package com.chervon.moudleContainer.panel.modules;

import com.chervon.moudleContainer.panel.data.entity.RnExtend;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;

import java.util.HashMap;

@JsonIgnoreProperties(ignoreUnknown = true)
public class RnInfo {

    public String eventtype;
    public String platform;
    public String pagesource;
    public String moduleid;
    public String pageid;
    public String modid;
    public String eleid;
    public String eventid;
    public String timestamp;
    @SerializedName("expand")
    public HashMap<String, Object> expand = new HashMap<>();


    public String getEventtype() {
        return eventtype;
    }

    public void setEventtype(String eventtype) {
        this.eventtype = eventtype;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPagesource() {
        return pagesource;
    }

    public void setPagesource(String pagesource) {
        this.pagesource = pagesource;
    }

    public String getModuleid() {
        return moduleid;
    }

    public void setModuleid(String moduleid) {
        this.moduleid = moduleid;
    }

    public String getPageid() {
        return pageid;
    }

    public void setPageid(String pageid) {
        this.pageid = pageid;
    }

    public String getModid() {
        return modid;
    }

    public void setModid(String modid) {
        this.modid = modid;
    }

    public String getEleid() {
        return eleid;
    }

    public void setEleid(String eleid) {
        this.eleid = eleid;
    }

    public String getEventid() {
        return eventid;
    }

    public void setEventid(String eventid) {
        this.eventid = eventid;
    }


    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public HashMap<String, Object> getExpand() {
        return expand;
    }

    public void setExpand(HashMap<String, Object> expand) {
        this.expand = expand;
    }
}
