/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * <p>
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.chervon.moudleContainer.react;

import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.ViewTreeObserver;
import androidx.appcompat.app.AppCompatActivity;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libDB.entities.BundleInfoEntry;
import com.chervon.libRouter.RouterConstants;
import com.chervon.libBase.model.DeviceStatus;
import com.chervon.moudleContainer.R;
import com.chervon.moudleContainer.panel.data.repository.PanelRepo;
import com.chervon.moudleContainer.panel.modules.PublicModule;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.bridge.CatalystInstance;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.modules.core.DefaultHardwareBackBtnHandler;
import com.facebook.react.modules.core.PermissionAwareActivity;
import com.facebook.react.modules.core.PermissionListener;
import com.chervon.moudleContainer.reactnative_multibundler.FileUtils;
import com.chervon.moudleContainer.reactnative_multibundler.RnBundle;
import com.chervon.moudleContainer.reactnative_multibundler.ScriptLoadUtil;
import com.chervon.moudleContainer.reactnative_multibundler.UpdateProgressListener;
import com.google.gson.Gson;
import java.io.File;
import java.util.ArrayList;
import javax.annotation.Nullable;
import io.reactivex.observers.DefaultObserver;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.AutoSizeCompat;

/**
 * 异步加载业务bundle的activity
 */
public abstract class AsyncReactActivity extends AppCompatActivity
        implements DefaultHardwareBackBtnHandler, PermissionAwareActivity {
    protected RnBundle mBundle = new RnBundle();
    private BundleInfoEntry mBundleInfoEntry;
    protected Handler mHandler;
    private int layoutCount;
    public String pageId;
    public String pageResource;

    public enum ScriptType {ASSET, FILE, NETWORK}

    private CountDownTimer mDownTimer;
    public PanelRepo panelRepo;
    private MyDelegate mDelegate;
    protected boolean bundleLoaded = false;
    protected Dialog mProgressDialog;
    protected DeviceInfo deviceInfo = null;
    protected String userId = null;
    public ReactContext reactContext;
    protected AsyncReactActivity() {
        //getBundle();
        mDelegate = createReactActivityDelegate();
    }

    /**
     * Returns the name of the main component registered from JavaScript.
     * This is used to schedule rendering of the component.
     * e.g. "MoviesApp"
     */
    @Nullable
    final private String getMainComponentNameInner() {
        try {
            if (!NetworkUtils.getMobileDataEnabled() && !NetworkUtils.getWifiEnabled()) {
                mBundle.scriptType = ScriptType.FILE;
            } else {
                mBundle.scriptType = ScriptType.NETWORK;
            }
        } catch (Exception e) {
            mBundle.scriptType = ScriptType.FILE;
        }

        if (!bundleLoaded && mBundle.scriptType == ScriptType.NETWORK) {
            return null;
        }
        return getMainComponentName();
    }

    @Nullable
    protected String getMainComponentName() {
        return deviceInfo.getProductId();
        // return getBundle().scriptPath;
    }

    /**
     * Called at construction time, override if you have a custom delegate implementation.
     */
    protected MyDelegate createReactActivityDelegate() {
        return new MyDelegate(this, getMainComponentNameInner());
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (null != savedInstanceState) {

            AutoSizeCompat.autoConvertDensityBaseOnHeight(super.getResources(), 1334);
            AutoSizeCompat.autoConvertDensityBaseOnWidth(super.getResources(), 750);
            savedInstanceState = null;
        }
        super.onCreate(savedInstanceState);
        mHandler = new Handler(getMainLooper());
        mProgressDialog = DialogUtil.showRnLoadingDialog(this);
        Bundle bundle = this.getIntent().getExtras();
        if (bundle != null) {
            deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
            userId = SoftRoomDatabase.getDatabase(this).userDao().getUser().getId();
            initTracePageSource();
            mDelegate.setDeviceInfo(deviceInfo);
            mDelegate.setUserId(userId);
        }
        panelRepo = new PanelRepo(this);
        final ReactInstanceManager manager = ((ReactApplication) getApplication()).getReactNativeHost().getReactInstanceManager();
        if (!manager.hasStartedCreatingInitialContext() || ScriptLoadUtil.getCatalystInstance(getReactNativeHost()) == null) {
            manager.addReactInstanceEventListener(new ReactInstanceManager.ReactInstanceEventListener() {
                @Override
                public void onReactContextInitialized(ReactContext context) {
                    getNetBundleInfoAndLoadScript();
                    manager.removeReactInstanceEventListener(this);
                }
            });
            ((ReactApplication) getApplication()).getReactNativeHost().getReactInstanceManager().createReactContextInBackground();
        } else {
            getNetBundleInfoAndLoadScript();
        }

        reactContext = getReactNativeHost().getReactInstanceManager().getCurrentReactContext();


    }

    private void initTracePageSource() {
        if (deviceInfo != null && !TextUtils.isEmpty(deviceInfo.getCommunicateMode())) {
            if ("BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                pageId = "116";
            } else if ("4g".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                pageId = "122";
            } else if ("wifi".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                pageId = "111";
            }
            initPageResource();
        }
    }

    private void getNetBundleInfoAndLoadScript() {

        TracePoints.clickDeviceListItem(this, UserInfo.get().getId(), new Gson().toJson(deviceInfo));


        loadScript(new LoadScriptListener() {
            @Override
            public void onLoadComplete(boolean success, String scriptPath) {
                bundleLoaded = success;
                LogUtils.d("RN_timestamp_onLoadComplete" + System.currentTimeMillis());
                if (success) {
                    runApp(scriptPath);
                }

            }
        });
    }

    private String getRnVersion(String productId) {
        BundleInfoEntry bundleInfoEntry = panelRepo.getRnBundleInfo(Long.parseLong(productId));
        if (bundleInfoEntry == null) {
            return "";
        }

        try {
            File bundleFile = new File(bundleInfoEntry.getRnBundleFile());
            if (bundleFile != null && bundleFile.exists()) {
                return bundleInfoEntry.getLastRnVersion();
            } else {
                return "";
            }
        } catch (Exception e) {
            return "";
        }


    }


    protected void runApp(String scriptPath) {
        if (scriptPath != null) {
            scriptPath = "file://" + scriptPath.substring(0, scriptPath.lastIndexOf(File.separator) + 1);
        }
        String finalScriptPath = scriptPath;
        final String path = finalScriptPath;
        final RnBundle bundle = mBundle;

        final ReactInstanceManager reactInstanceManager = ((ReactApplication) getApplication()).getReactNativeHost().getReactInstanceManager();
        //如果是网络加载的话，此时正在子线程
        if (bundle.scriptType == ScriptType.NETWORK) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    ScriptLoadUtil.setJsBundleAssetPath(
                            reactInstanceManager.getCurrentReactContext(),
                            path);
                    try {
                        mDelegate.loadApp(getMainComponentNameInner());
                    } catch (Exception e) {

                    }

                    layoutCount = 0;
                    try {
                        if (mDownTimer == null) {
                            mDownTimer = new CountDownTimer(2000, 1000) {

                                @Override
                                public void onTick(long millisUntilFinished) {

                                }

                                @Override
                                public void onFinish() {

                                }
                            };

                        }

                        getReactNativeHost().getReactInstanceManager().getCurrentReactContext().getCurrentActivity().
                                getWindow().getDecorView().getViewTreeObserver().
                                addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                                    @Override
                                    public void onGlobalLayout() {
                                        mDownTimer.start();
                                        String communicateMode = deviceInfo.getCommunicateMode();
                                        if (!TextUtils.isEmpty(communicateMode) && "BLE".equalsIgnoreCase(communicateMode)) {
                                            if (layoutCount > 3) {
                                                if (mProgressDialog != null) {
                                                    mProgressDialog.dismiss();
                                                    LogUtils.d("RN_timestamp_getReactInstanceManager_dismiss" + System.currentTimeMillis() + "layoutCount" + layoutCount);
                                                }
                                            }
                                        } else {

                                            if (mProgressDialog != null) {
                                                mProgressDialog.dismiss();
                                            }

                                        }

                                        layoutCount++;
                                        LogUtils.d("RN_timestamp_getReactInstanceManager" + System.currentTimeMillis() + "layoutCount" + layoutCount);
                                    }
                                });
                    } catch (Exception e) {

                    }

                }
            });
        } else {//主线程运行
            ScriptLoadUtil.setJsBundleAssetPath(
                    reactInstanceManager.getCurrentReactContext(),
                    path);
            initView();
            if (mProgressDialog != null) {
                mProgressDialog.dismiss();
            }
        }


    }


    protected void loadScript(final LoadScriptListener loadListener) {
//        LogUtils.d("ScriptType.NETWORK_0");
        //  mBundle.scriptType=ScriptType.NETWORK;
        final RnBundle bundle = mBundle;
        /** all buz module is loaded when in debug mode*/
        //当设置成debug模式时，所有需要的业务代码已经都加载好了
        if (ScriptLoadUtil.MULTI_DEBUG) {
            loadListener.onLoadComplete(true, null);
            return;
        }
        ScriptType pathType = bundle.scriptType;
        String scriptPath = bundle.scriptUrl;
        final CatalystInstance instance = ScriptLoadUtil.getCatalystInstance(getReactNativeHost());
        //  if(Utils.getAPNType(this)==-1){

        if (pathType == ScriptType.ASSET) {
            mBundle.scriptType = ScriptType.ASSET;
            String bundleName = mDelegate.getDeviceInfo().getRnBundleName();
            if (TextUtils.isEmpty(bundleName)) {
                BundleInfoEntry bundleInfoEntry = panelRepo.getRnBundleInfo(Long.parseLong(mDelegate.getDeviceInfo().getProductId()));
                bundleName = bundleInfoEntry.getRnBundleName();
            }
            scriptPath = mBundle.scriptUrl = bundleName + ".bundle";
            ScriptLoadUtil.loadScriptFromAsset(getApplicationContext(), instance, scriptPath, false);
            loadListener.onLoadComplete(true, null);


        } else if (pathType == ScriptType.FILE) {
            String bundleFilePath = "";
            String bundleName = mDelegate.getDeviceInfo().getRnBundleName();

            if (TextUtils.isEmpty(bundleName)) {
                BundleInfoEntry bundleInfoEntry = panelRepo.getRnBundleInfo(Long.parseLong(mDelegate.getDeviceInfo().getProductId()));
                //todo firebase  bundleInfoEntry==null
                if (bundleInfoEntry == null) {
                    getOutPage();
                } else {
                    bundleName = bundleInfoEntry.getRnBundleName();
                    bundleFilePath = getApplicationContext().getFilesDir() + File.separator + "bundles/" +
                            bundleName + File.separator + bundleName + ".bundle";
                }

            } else {
                bundleFilePath = getApplicationContext().getFilesDir() + File.separator + "bundles/" +
                        mDelegate.getDeviceInfo().getRnBundleName() + File.separator + mDelegate.getDeviceInfo().getRnBundleName() + ".bundle";
            }


            File scriptFile = new File(bundleFilePath);

            if (!scriptFile.exists()) {

                boolean accessFile = Utils.getAccessFileisExist(this, mDelegate.getDeviceInfo().getRnBundleName());
                if (accessFile) {
                    ScriptLoadUtil.loadScriptFromAsset(getApplicationContext(), instance, scriptPath, false);
                    loadListener.onLoadComplete(true, null);
                } else {
                    onBackPressed();
                }

            } else {
                scriptPath = scriptFile.getAbsolutePath();
                ScriptLoadUtil.loadScriptFromFile(scriptPath, instance, scriptPath, false);
                loadListener.onLoadComplete(true, scriptPath);
            }

        } else if (pathType == ScriptType.NETWORK) {
            LogUtils.d("ScriptType.NETWORK_1");
            initView();
            LogUtils.d("ScriptType.NETWORK_2");

            //由于downloadRNBundle里面的md5参数由组件名代替了，实际开发中需要用到md5校验的需要自己修改
            String finalScriptPath = scriptPath;
            //解决bug AsyncReactActivity.loadScript
            if (null == mDelegate) {
                return;
            }
            if (null == mDelegate.getDeviceInfo()) {
                return;
            }
            if (TextUtils.isEmpty(mDelegate.getDeviceInfo().getProductId())) {
                return;
            }

            //   deleteDeviceRegiterInfo();
            panelRepo.getDeviceStatus(deviceInfo).subscribe(new DefaultObserver<HttpResponse<DeviceStatus>>() {
                @Override
                public void onNext(HttpResponse<DeviceStatus> httpResponse) {
                    if (httpResponse.status) {
                        LogUtils.d("RN_timestamp_getDeviceStatus" + System.currentTimeMillis());
                        DeviceStatus deviceStatus = httpResponse.response;
                        if (deviceStatus.getDeviceCodeStatus() == 0 || deviceStatus.getDeviceStatus() == 0) {
                            deviceInfo.setIsOnline(2);
                            ToastUtils.showLong(LanguageStrings.containerDevicedonotuseToast());
                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                                    .withInt(KEY_FROM, R.layout.main_act_loadbundle).navigation();

                        } else {
                            loadBundleInfoFromNetwork(loadListener, instance, finalScriptPath);
                        }
                    } else {
                        loadBundleInfoFromNetwork(loadListener, instance, finalScriptPath);
                        ToastUtils.showLong(httpResponse.message);
                        //    httpResponse.setState(RESPONSE_FAIL);
                    }
                }

                @Override
                public void onError(Throwable e) {
                    loadBundleInfoFromNetwork(loadListener, instance, finalScriptPath);
                }

                @Override
                public void onComplete() {

                }
            });

        }
    }


    private void loadBundleInfoFromNetwork(LoadScriptListener loadListener, CatalystInstance instance, String finalScriptPath) {
        //增加空字符判断 修复 IOT-9594问题
        if (null == mDelegate.getDeviceInfo()) {
            return;
        }
        if (null == mDelegate.getDeviceInfo().getProductId()) {
            return;
        }
        LogUtils.d("RN_timestamp_getBundleInfo_start" + System.currentTimeMillis());
        LogUtils.i("RN加载相关--->", "查询接口--/app-hmac/rn/lastInfo");

        panelRepo.getBundleInfo(Long.parseLong(mDelegate.getDeviceInfo().getProductId()), getRnVersion(mDelegate.getDeviceInfo().getProductId())).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<BundleInfoEntry>>() {
            @Override
            public void onNext(HttpResponse<BundleInfoEntry> response) {
                LogUtils.i("RN加载相关--->", "接口响应--" + response.response);
                String bundleUrl = finalScriptPath;
                if (response.status) {
                    LogUtils.d("RN_timestamp_getBundleInfo" + System.currentTimeMillis());
                } else {
                    ToastUtils.showLong(response.message);
                }
                mBundleInfoEntry = response.response;
                boolean hasLast = false;
                String bundleName = "T";
                if (mBundleInfoEntry != null) {
                    hasLast = mBundleInfoEntry.isHasLast();
                    bundleName = mBundleInfoEntry.getRnBundleName() + ".bundle";
                }

                if (hasLast) {
                    mBundle.scriptType = ScriptType.NETWORK;
                    bundleUrl = mBundleInfoEntry.getLastRnUrl();
                    String finalBundleName = bundleName;
                    LogUtils.i("RN加载相关--->", "开始下载RN面板");

                    FileUtils.downloadRNBundle(hasLast, bundleName, AsyncReactActivity.this.getApplicationContext(), bundleUrl, mBundleInfoEntry.getRnBundleName(), new UpdateProgressListener() {
                        @Override
                        public void updateProgressChange(final int precent) {

                        }

                        @Override
                        public void complete(boolean success) {
                            new Thread(new Runnable() {
                                @Override
                                public void run() {
                                    LogUtils.i("RN加载相关--->", "面板下载完成");
                                    downLoadFileCommple(success, instance, loadListener, finalBundleName);
                                }
                            }).start();
                        }
                    });


                } else {

                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            loadBundleFromLocalDisk(instance, loadListener);
                        }
                    }).start();

                }
                LogUtils.d("ScriptType.NETWORK_hasLast");
                if (mBundleInfoEntry != null && mBundleInfoEntry.isHasLast()) {
                    TracePoints.clickDeviceListItem(AsyncReactActivity.this, UserInfo.get().getId(), new Gson().toJson(mBundleInfoEntry));

                    panelRepo.saveRnBundleInfo(mBundleInfoEntry);
                }
            }

            @Override
            public void onError(Throwable e) {
                // loadbundleWhenNetError(instance, loadListener);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        loadBundleFromLocalDisk(instance, loadListener);
                    }
                }).start();
            }

            @Override
            public void onComplete() {

            }
        });
    }

    private void downLoadFileCommple(boolean success, CatalystInstance instance, LoadScriptListener loadListener, String finalBundleName) {
        if (!success) {
            //      loadListener.onLoadComplete(false, null);
            String bundleName = mBundleInfoEntry.getRnBundleName() + ".bundle";
            loadBundleFromAsset(bundleName, instance, loadListener);
            return;
        }

        String info = FileUtils.getCurrentPackageMd5(getApplicationContext());
        String bundlePath = FileUtils.getPackageFolderPath(getApplicationContext(), info);
        String jsBundleFilePath = FileUtils.appendPathComponent(bundlePath, finalBundleName);
        File bundleFile = new File(jsBundleFilePath);
        if (bundleFile != null && bundleFile.exists()) {
            mBundleInfoEntry.setRnBundleFile(jsBundleFilePath);
            upDateBundleInfoEntry(mBundleInfoEntry);
            ScriptLoadUtil.loadScriptFromFile(jsBundleFilePath, instance, jsBundleFilePath, false);
        } else {
            success = false;

            getOutPage();
            return;
        }
        LogUtils.i("RN加载相关--->", "开始加载RN面板-path-" + jsBundleFilePath);

        loadListener.onLoadComplete(success, jsBundleFilePath);

    }

    private void getOutPage() {
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {

                ToastUtils.showLong(LanguageStrings.app_container_rnbundlenameerror_textview_text());


            }
        }, 500);

        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {

                //       finish();
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT).navigation();

            }
        }, 1000);
    }

    private void upDateBundleInfoEntry(BundleInfoEntry mBundleInfoEntry) {
        panelRepo.saveRnBundleInfo(mBundleInfoEntry);
    }

    private void loadBundleFromLocalDisk(CatalystInstance instance, LoadScriptListener loadListener) {
        String bundleName;
        BundleInfoEntry localBundleInfoEntry = panelRepo.getRnBundleInfo(Long.parseLong(deviceInfo.getProductId()));
        if (localBundleInfoEntry == null || (localBundleInfoEntry != null && TextUtils.isEmpty(localBundleInfoEntry.getRnBundleName()))) {
            getOutPage();
            return;
        }

        String bundlesPath = FileUtils.getPackageFolderPath(AsyncReactActivity.this, localBundleInfoEntry.getRnBundleName());
        String rnBundleName = "T";

        if (localBundleInfoEntry != null) {
            rnBundleName = localBundleInfoEntry.getRnBundleName() + ".bundle";
        }

        File bundleFile = new File(bundlesPath, rnBundleName);
        //   File bundleFile = new File(bundlesPath, mDelegate.getDeviceInfo().getRnBundleName()+ ".bundle");
        if (bundleFile.exists()) {
            ArrayList<Activity> collect = new ArrayList<Activity>();
            collect.add(AsyncReactActivity.this);
            mBundle.scriptPath = bundleFile.getAbsolutePath();
            ScriptLoadUtil.loadScriptFromFile(bundleFile.getAbsolutePath(), instance, bundleFile.getAbsolutePath(), false);
            loadListener.onLoadComplete(true, bundleFile.getAbsolutePath());

        } else {
            try {
                getOutPage();

            } catch (Exception e) {
                //  ScriptLoadUtil.loadScriptFromAsset(getApplicationContext(), instance, "index.android.bundle", false);
                loadListener.onLoadComplete(false, null);
            }

        }
    }

    private void loadbundleWhenNetError(CatalystInstance instance, LoadScriptListener loadListener) {
        LogUtils.d("ScriptType.NETWORK_hasLast");
        BundleInfoEntry localBundleInfoEntry = panelRepo.getRnBundleInfo(Long.parseLong(deviceInfo.getProductId()));
        String bundleName = localBundleInfoEntry.getRnBundleName() + ".bundle";
        loadBundleFromAsset(bundleName, instance, loadListener);
    }

    private void loadBundleFromAsset(String bundleName, CatalystInstance instance, LoadScriptListener loadListener) {
        if (!TextUtils.isEmpty(bundleName) && CommonUtils.isAssetsFileExists(AsyncReactActivity.this, bundleName)) {
            mBundle.scriptType = ScriptType.ASSET;
            bundleName = mBundle.scriptPath;
            ScriptLoadUtil.loadScriptFromAsset(getApplicationContext(), instance, bundleName, false);
            loadListener.onLoadComplete(true, null);
        } else {
            loadListener.onLoadComplete(false, null);
        }
    }


    protected void initView() {
        mDelegate.onCreate(null);
    }

    @Override
    protected void onResume() {
        super.onResume();

        try {
            mDelegate.onResume();
            if (!BluetoothConection.isConnected(deviceInfo.getMac())) {
                PublicModule.sendBlueState();
            }
        } catch (Exception e) {

        }
    }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    if (mProgressDialog != null) {
      mProgressDialog.dismiss();
    }

    try {
      mDelegate.getReactNativeHost().clear();

      mDelegate.onDestroy();
      getReactInstanceManager().destroy();
      ScriptLoadUtil.clearLoadedRecord();


    } catch (Throwable e) {

        }


    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (null == mDelegate) {
            return;
        }
        mDelegate.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (null == mDelegate) {
            return false;
        }
        return mDelegate.onKeyDown(keyCode, event) || super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        //适配 IOT-10677
        try {
            if (null == mDelegate) {
                return false;
            }
            return mDelegate.onKeyUp(keyCode, event) || super.onKeyUp(keyCode, event);
        } catch (Exception e) {
            return false;
        }

    }

    @Override
    public boolean onKeyLongPress(int keyCode, KeyEvent event) {
        if (null == mDelegate) {
            return false;
        }
        return mDelegate.onKeyLongPress(keyCode, event) || super.onKeyLongPress(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        if (null == mDelegate) {
            return;
        }
        try {
            if (!mDelegate.onBackPressed()) {
                super.onBackPressed();
            }
        } catch (Exception e) {
            finish();
        }

    }

    @Override
    public void invokeDefaultOnBackPressed() {
        super.onBackPressed();
    }

    @Override
    public void onNewIntent(Intent intent) {
        if (!mDelegate.onNewIntent(intent)) {
            super.onNewIntent(intent);
        }
    }

    @Override
    public void requestPermissions(
            String[] permissions,
            int requestCode,
            PermissionListener listener) {
        mDelegate.requestPermissions(permissions, requestCode, listener);
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onRequestPermissionsResult(
            int requestCode,
            String[] permissions,
            int[] grantResults) {
        mDelegate.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    protected final ReactNativeHost getReactNativeHost() {
        return mDelegate.getReactNativeHost();
    }

    protected final ReactInstanceManager getReactInstanceManager() {
        return mDelegate.getReactInstanceManager();
    }

    private void initPageResource() {
        if (!TextUtils.isEmpty(pageId)) {
            pageResource = ((BaseApplication) this.getApplication()).getCurrentPageResouce();
            pageResource = pageResource + "_" + pageId;
            ((BaseApplication) this.getApplication()).setCurrentPageResouce(pageResource);
        }

        LogUtils.d("PageResouce" + pageResource);
    }


}


