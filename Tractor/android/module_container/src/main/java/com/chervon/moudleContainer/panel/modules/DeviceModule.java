// ToastModule.java

package com.chervon.moudleContainer.panel.modules;

import static com.chervon.moudleContainer.reactnative_multibundler.ui.BuzActivity.BLE_TYPE;

import com.blankj.utilcode.util.ActivityUtils;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBluetooth.BlueToothUtilListener;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libNetwork.iot.event.AwsOfflineDataEvent;
import com.chervon.moudleContainer.reactnative_multibundler.ui.BuzActivity;
import com.facebook.react.bridge.Callback;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothGatt;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.pm.PermissionInfo;
import android.os.Build;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libBluetooth.data.DPData;
import com.chervon.libBluetooth.data.DPData1;
import com.chervon.libBluetooth.data.ParamData;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.LanguagePackageDao;
import com.chervon.libDB.entities.LanguagePackage;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.iot.data.AwsUpdateBean;
import com.chervon.libNetwork.iot.event.AwsUpdatePushEvent;
import com.chervon.moudleContainer.panel.data.api.PanelApi;
import com.chervon.moudleContainer.panel.data.entity.DevInfo;
import com.chervon.moudleContainer.panel.data.entity.GetUpdateTaskDetailBean;
import com.chervon.moudleContainer.panel.data.repository.PanelRepo;
import com.chervon.moudleContainer.panel.utils.QFRCTBasicUtil;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleRssiCallback;
import com.clj.fastble.callback.BleScanCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.observers.DefaultObserver;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleContainer.panel.modules
 * @ClassName: DeviceModule
 * @Description:Device management module
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:18
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:18
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class DeviceModule extends ReactContextBaseJavaModule implements BlueToothUtilListener {

    private static final String TAG = "RNLOG";
    private static final String DEVICE_TYPE_BLE = "BLE";
    private static ReactApplicationContext reactContext;

    private static final String DURATION_SHORT_KEY = "SHORT";
    private static final String DURATION_LONG_KEY = "LONG";
    //订阅超时
    private static final int TIME_OUT = 5;
    private long oldTime;
    private String mac;
    public static DeviceInfo deviceInfo;
    public BleDevice bleDevice;

    final String DATA_TYPE_RAW = "00";
    final String DATA_TYPE_BOOL = "01";
    final String DATA_TYPE_INT_VALUE = "02";
    final String DATA_TYPE_STRING = "03";
    final String DATA_TYPE_ENUM = "04";
    final String DATA_TYPE_PARAM = "05";
    final String DATA_TYPE_UNSIGNED_INT_06 = "06";
    final String DATA_TYPE_UNSIGNED_INT_07 = "07";
    final String DATA_TYPE_ARRAY = "08";
    final String DATA_TYPE_STRUCT = "09";
    final String DATA_TYPE_RESULT_FALSE = "00";

    final int DATA_TYPE_DPID_54 = 54;
    final int DATA_TYPE_DPID_52 = 52;

    public static DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public static void setDeviceInfo(DeviceInfo deviceInfo) {
        DeviceModule.deviceInfo = deviceInfo;
    }

    public DeviceModule(ReactApplicationContext context) {
        super(context);
        reactContext = context;
    }

    @NonNull
    @Override
    public String getName() {
        return "RNCallNativeDevice";
    }

    @Override
    public Map<String, Object> getConstants() {
        final Map<String, Object> constants = new HashMap<>();
        constants.put(DURATION_SHORT_KEY, Toast.LENGTH_SHORT);
        constants.put(DURATION_LONG_KEY, Toast.LENGTH_LONG);
        return constants;
    }

    /**
     * @param mac 设备唯一标识
     * @return void
     * @method requestBleConnect
     * @description 提供MAC连接蓝牙能力
     * @date: 2022/7/21 17:47
     * @author: langmeng
     */
    boolean isConnect;
    public static int connectStatus = 3;

    @ReactMethod
    public void requestBleConnect(String mac, Promise promise) {

        this.mac = mac;

        try {
            //不在RN面板的蓝牙连接直接return
            if (null == ActivityUtils.getTopActivity().getClass()) {
                return;
            }
            if (!ActivityUtils.getTopActivity().getClass().getName().equals(BuzActivity.class.getName())) {
                return;
            }

            LogUtils.d("BleConnectStateChange_requestBleConnect_entry_into");
            oldTime = System.currentTimeMillis();
            //mac不能为空
            if (TextUtils.isEmpty(mac)) {
                promise.resolve(false);
                QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, QFRCTBasicUtil.BLE_CONNECT_FAIL);
                return;
            }


            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }


            if (!BleManager.getInstance().isBlueEnable()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 0);
                LogUtils.d("wj-->", "sendDeviceBleConnectStateChange-->QFRCTBasicUtil.BLE_CONNECT_FAIL");
                QFRCTBasicUtil.sendBluetoothChange(reactContext, 0);

                return;
            }

            if (!requestPermisson()) {
                String event = "bluetoothChange";
                LogUtils.d("wj-->", "sendDeviceBleConnectStateChange-->QFRCTBasicUtil.BLE_CONNECT_FAIL");
                QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, QFRCTBasicUtil.BLE_CONNECT_FAIL);
                LogUtils.d("BleConnectStateChange_requestBleConnect_!requestPermisson()");
               return;
            }


            BluetoothConection.getInstance().setListener(this);
            BluetoothConection.getInstance().connectDeviceWithRN(reactContext, mac);

            promise.resolve(true);
        } catch (Throwable e) {

        }
    }

    public HashMap getMultiParamValue(int dpIp,ParamData[] paramData) {

        HashMap<String, Object> map = new HashMap<>();
        for (int i = 0; i < paramData.length; i++) {
            String key = paramData[i].getParam_id();
            String type = paramData[i].getParam_type();
            String value = paramData[i].getParam_data_value();


            switch (type) {
                //raw
                case DATA_TYPE_RAW:
                    map.put(ConvertUtils.hexString2Int(key) + "", value);
                    break;
                //bool
                case DATA_TYPE_BOOL:
                    if ("00".equals(value)) {
                        map.put(ConvertUtils.hexString2Int(key) + "", false);
                    } else {
                        map.put(ConvertUtils.hexString2Int(key) + "", true);
                    }
                    break;
                //value
                case DATA_TYPE_INT_VALUE:
                case DATA_TYPE_ENUM:
                case DATA_TYPE_UNSIGNED_INT_06:
                case DATA_TYPE_UNSIGNED_INT_07:
                    int length  = Integer.valueOf(paramData[i].getParam_data_len());
                    if (length >= 2) {
                        value = BluetoothConection.changeToBig(value);
                    }
                    map.put(ConvertUtils.hexString2Int(key) + "", ConvertUtils.hexString2Int(value));
                    break;
                //string
                case DATA_TYPE_STRING:
                    //创达适配R1 物模型为 52和54
                    if(dpIp == DATA_TYPE_DPID_52 || dpIp == DATA_TYPE_DPID_54 ) {
                        map.put(ConvertUtils.hexString2Int(key) + "", value);
                        break;
                    }
                    StringBuffer sb = new StringBuffer();
                    for (int index = 0; index < value.length() / 2; index++) {
                        String str = value.substring(index * 2, (index + 1) * 2);
                        int hexValue = Integer.parseInt(str, 16);
                        char character = (char) hexValue;
                        sb.append(character);
                    }
                    value = sb.toString();
                    map.put(ConvertUtils.hexString2Int(key) + "", value);
                    break;
                //enum
                default:
                    map.put(ConvertUtils.hexString2Int(key) + "", value);
                    break;
            }
        }
        return map;

    }


    /**
     * @param dpData 模型指令数据
     * @return void
     * @method setLocalDeviceData
     * @description 对设备指令下发
     * @date: 2022/7/21 18:24
     * @author: langmeng
     */
    @ReactMethod
    public void setLocalDeviceData1(String dpData, Promise promise) {

        try {

            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }
            if (!requestPermisson()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 2);
                return;
            }
            LogUtils.iTag(TAG, "RN Set Device Data: " + dpData);
            List<DPData> dpDataList = new Gson().fromJson(dpData, new TypeToken<List<DPData>>() {
            }.getType());

            String commandData = "";
            for (DPData data : dpDataList) {
                StringBuffer sb = new StringBuffer();
                sb.append(commandData).append(data.getDp_id()).append(data.getDp_type()).append(data.getDp_len()).append(data.getDp_data());
                commandData = sb.toString();
            }
            BluetoothConection.getInstance().writeDevice(BluetoothConection.getInstance().bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_SEND, commandData));

            promise.resolve(true);

        } catch (Throwable e) {

        }
    }

    /**
     * @param dpData 模型指令数据
     * @return void
     * @method setLocalDeviceData
     * @description 对设备指令下发
     * @date: 2022/7/21 18:24
     * @author: langmeng
     */
    @ReactMethod
    public void setLocalDeviceData(String dpData, Promise promise) {
        LogUtils.d("writeDevicetest_setLocalDeviceData");
        if (!BluetoothConection.getInstance().isBlceRealConnected(mac)) {
            LogUtils.d("writeDevicetest_isBlceRealConnected");
            return;
        }
        try {

            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }
            if (!requestPermisson()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 2);
                LogUtils.d("writeDevicetest_!requestPermisson()");
                return;
            }
            LogUtils.iTag(TAG, "RN Set Device Data: " + dpData);
            List<DPData1> dpDataList = new Gson().fromJson(dpData, new TypeToken<List<DPData1>>() {
            }.getType());

            String commandData = "";
            for (DPData1 data : dpDataList) {
                StringBuffer sb = new StringBuffer();
                sb.append(commandData).append(data.getDp_id()).append(data.getDp_type()).append(data.getDp_len());
                ParamData[] paramData = data.getDp_data();
                for (int i = 0; paramData != null && i < paramData.length; i++) {
                    sb.append(paramData[i].getParam_id()).append(paramData[i].getParam_type()).append(paramData[i].getParam_data_len()).append(paramData[i].getParam_data_value());
                }
                commandData = sb.toString();
            }

            String finalCommandData = commandData;


            SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                BluetoothConection.getInstance().writeDeviceTest(BluetoothConection.getInstance().bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_SEND, finalCommandData), finalCommandData);
            });


            promise.resolve(true);

        } catch (Throwable e) {

        }
    }


    @ReactMethod
    public void getDeviceData(String deviceId, Promise promise) {
        try {
            new PanelApi().getDeviceDetailInfo(deviceId).subscribe(new DefaultObserver<HttpResponse<DevInfo>>() {
                @Override
                public void onNext(HttpResponse<DevInfo> devInfoHttpResponse) {
                    promise.resolve(devInfoHttpResponse.response);
                }

                @Override
                public void onError(Throwable e) {
                    promise.reject(e);
                }

                @Override
                public void onComplete() {

                }
            });

        } catch (Throwable e) {

        }
    }

    /**
     * @param dpData dpdata
     * @return void
     * @method getLocalDeviceData
     * @description 获取DP数据
     * @date: 2022/7/22 16:00
     * @author: langmeng
     */
    @ReactMethod
    public void getLocalDeviceData(String dpData, Promise promise) {

        try {

            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }
            if (!requestPermisson()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 2);
                return;
            }

            LogUtils.iTag(TAG, "RN Get Device Data: " + dpData);
            if (dpData.isEmpty()) {
                getLocalAllDeviceData();
                return;
            }

            List<DPData> dpDataList = new Gson().fromJson(dpData, new TypeToken<List<DPData>>() {}.getType());

            String commandData = "";
            for (DPData data : dpDataList) {
                StringBuffer sb = new StringBuffer();
                sb.append(commandData).append(data.getDp_id()).append(data.getDp_type()).append("0000");
                commandData = sb.toString();
            }
            BluetoothConection.getInstance().writeDevice2(BluetoothConection.getInstance().bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_SEND, commandData));

            promise.resolve("");

        } catch (Throwable e) {

        }
    }

    /**
     * @return void
     * @method getLocalAllDeviceData
     * @description 查询所有DP数据
     * @date: 2022/7/22 17:47
     * @author: langmeng
     */
    @ReactMethod
    public void getLocalAllDeviceData() {

        try {


            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }
            if (!requestPermisson()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 2);
                return;
            }

            BluetoothConection.getInstance().writeDevice(BluetoothConection.getInstance().bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_GETALL, ""));

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public boolean isBleConnect(String id, Promise promise) {

        if (Build.VERSION.SDK_INT >= 31) {

        } else {
            permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
        }
        if (!requestPermisson()) {
            String event = "bluetoothChange";
            QFRCTBasicUtil.sendEvent(reactContext, event, 2);
            return true;
        }

        boolean isConnected = new PanelRepo().getBlceConnectState(id);
        promise.resolve(isConnected);


        return true;
    }

    @ReactMethod
    public boolean checkOTA() {
        try {
            if (getCurrentActivity() == null) {
                return true;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {

//          Toast.makeText(getReactApplicationContext(), "back", Toast.LENGTH_LONG).show();

                }
            });

        } catch (Throwable e) {

        }
        return true;
    }

    @ReactMethod
    public void getUpdateTasks(String deviceId, Promise promise) {

        try {

            LogUtils.iTag(TAG, "RN Get UpdateTasks: " + deviceId);

            Observer<IotModelEvent> observer = new Observer<IotModelEvent>() {
                @Override
                public void onChanged(IotModelEvent modelEvent) {

                    //监听到任务列表后返回给RN
                    if (modelEvent != null) {

                        String jobTopic = String.format(AwsMqttService.OTA_JOB_ACCEPTED_TOPIC, deviceId);
                        if (modelEvent.topic.equals(jobTopic)) {
                            promise.resolve(ConvertUtils.bytes2String(modelEvent.modelData));
                            AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_ACCEPTED_TOPIC, deviceId));
                            AwsMqttService.awsMessageLiveData.removeObserver(this);
                        }
                    }

                }
            };

            //设置超时关闭订阅
            Observable.timer(TIME_OUT, TimeUnit.SECONDS).doOnSubscribe(new Consumer<Disposable>() {
                @Override
                public void accept(Disposable disposable) throws Exception {
                    AwsMqttService.awsMessageLiveData.observeForever(observer);
                    AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_ACCEPTED_TOPIC, deviceId));
                    //获取任务列表
                    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_TOPIC, deviceId));
                }
            }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(AndroidSchedulers.mainThread()).subscribe(new io.reactivex.Observer<Long>() {
                @Override
                public void onSubscribe(Disposable d) {

                }

                @Override
                public void onNext(Long aLong) {
                    AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_ACCEPTED_TOPIC, deviceId));
                    AwsMqttService.awsMessageLiveData.removeObserver(observer);
                }

                @Override
                public void onError(Throwable e) {

                }

                @Override
                public void onComplete() {

                }
            });

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void getUpdateTaskDetail(String data, Promise promise) {

        try {
            LogUtils.iTag(TAG, "RN Get UpdateTaskDetail: " + data);

            GetUpdateTaskDetailBean getUpdateTaskDetailBean = new Gson().fromJson(data, GetUpdateTaskDetailBean.class);

            Observer<IotModelEvent> observer = new Observer<IotModelEvent>() {
                @Override
                public void onChanged(IotModelEvent modelEvent) {

                    //监听到任务列表后返回给RN
                    if (modelEvent != null) {
                        String jobDetailTopic = String.format(AwsMqttService.OTA_JOB_DETAIL_ACCEPTED_TOPIC, getUpdateTaskDetailBean.getDeviceId(), getUpdateTaskDetailBean.getJobId());
                        if (modelEvent.topic.equals(jobDetailTopic)) {
                            promise.resolve(ConvertUtils.bytes2String(modelEvent.modelData));
                            AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_DETAIL_ACCEPTED_TOPIC, getUpdateTaskDetailBean.getDeviceId(), getUpdateTaskDetailBean.getJobId()));
                            AwsMqttService.awsMessageLiveData.removeObserver(this);
                        }
                    }

                }
            };

            //设置超时关闭订阅
            Observable.timer(TIME_OUT, TimeUnit.SECONDS).doOnSubscribe(new Consumer<Disposable>() {
                @Override
                public void accept(Disposable disposable) throws Exception {
                    AwsMqttService.awsMessageLiveData.observeForever(observer);
                    AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_DETAIL_ACCEPTED_TOPIC, getUpdateTaskDetailBean.getDeviceId(), getUpdateTaskDetailBean.getJobId()));
                    //获取任务详情
                    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_DETAIL_TOPIC, getUpdateTaskDetailBean.getDeviceId(), getUpdateTaskDetailBean.getJobId()));
                }
            }).observeOn(AndroidSchedulers.mainThread()).subscribeOn(AndroidSchedulers.mainThread()).subscribe(new io.reactivex.Observer<Long>() {
                @Override
                public void onSubscribe(Disposable d) {

                }

                @Override
                public void onNext(Long aLong) {
                    //取消订阅
                    AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_DETAIL_ACCEPTED_TOPIC, getUpdateTaskDetailBean.getDeviceId(), getUpdateTaskDetailBean.getJobId()));
                    AwsMqttService.awsMessageLiveData.removeObserver(observer);
                }

                @Override
                public void onError(Throwable e) {

                }

                @Override
                public void onComplete() {

                }
            });

        } catch (Throwable e) {

        }
    }

    /**
     * @param topic MQTT主题
     * @param msg   发送消息体
     * @return void
     * @method topicPublish
     * @description 向MQTT发送消息
     * @date: 2022/9/13 14:54
     * @author: langmeng
     */
    @ReactMethod
    public void topicPublish(String topic, String msg) {
        LogUtils.iTag(TAG, "RN topicPublish", "topic: " + topic, "msg: " + msg);
        AwsMqttService.publishTopic(topic, msg);
    }

    /**
     * @param topic MQTT主题
     * @return void
     * @method subscribe
     * @description 主题订阅
     * @date: 2022/9/13 14:54
     * @author: langmeng
     */
    @ReactMethod
    public void subscribe(String topic) {
        LogUtils.iTag(TAG, "RN subscribe", "topic: " + topic);
        AwsMqttService.subscribeTopic(topic);
    }


    /**
     * @param topic MQTT主题
     * @param msg   发送消息体
     * @return void
     * @method topicPublish
     * @description 向MQTT发送消息
     * @date: 2022/9/13 14:54
     * @author: wh
     */
    @ReactMethod
    public void topicPublishWithQos(String topic, String msg, int qos) {
        LogUtils.iTag(TAG, "RN topicPublish", "topic: " + topic, "msg: " + msg);
        AwsMqttService.publishTopicQos(topic, msg, qos);
    }

    /**
     * @param topic MQTT主题
     * @return void
     * @method subscribe
     * @description 主题订阅
     * @date: 2022/9/13 14:54
     * @author: langmeng
     */
    @ReactMethod
    public void subscribeWithQos(String topic, int qos) {
        LogUtils.iTag(TAG, "RN subscribe", "topic: " + topic);
        AwsMqttService.subscribeTopicQos(topic, qos);
    }


    /**
     * @param topic MQTT主题
     * @return void
     * @method unsubscribe
     * @description 取消主题订阅
     * @date: 2022/9/13 14:54
     * @author: langmeng
     */
    @ReactMethod
    public void unsubscribe(String topic) {
        LogUtils.iTag(TAG, "RN unsubscribe", "topic: " + topic);
        AwsMqttService.unsubscribeTopic(topic);
    }

    /**
     * @param topic MQTT主题
     * @return void
     * @method editProperty
     * @description 修改物模型
     * @date: 2022/9/13 14:54
     * @author: langmeng
     */
    @ReactMethod
    public void editProperty(String topic, String dpState) {
        LogUtils.iTag(TAG, "RN editProperty", "topic: " + topic, "dpState: " + dpState);
        AwsMqttService.publishTopic(topic, dpState);
    }

    private String[] permissionsBlue = new String[]{Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_CONNECT};

    @ReactMethod
    public void readRSSI(String mac, Promise promise) {

        try {
            LogUtils.iTag(TAG, "RN readRSSI", "mac: " + mac);

            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }
            if (!requestPermisson()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 2);

                return;
            }


            if (BluetoothConection.getInstance().bleDevice != null && mac.equals(BluetoothConection.getInstance().bleDevice.getMac())) {

                BleManager.getInstance().readRssi(BluetoothConection.getInstance().bleDevice, new BleRssiCallback() {
                    @Override
                    public void onRssiFailure(BleException exception) {
                        if (BluetoothConection.getInstance().bleDevice != null) {
                            QFRCTBasicUtil.sendRSSI(getReactApplicationContext(), BluetoothConection.getInstance().bleDevice.getRssi() + "");
                            promise.resolve(BluetoothConection.getInstance().bleDevice.getRssi());
                        }


                    }

                    @Override
                    public void onRssiSuccess(int rssi) {
                        QFRCTBasicUtil.sendRSSI(getReactApplicationContext(), rssi + "");

                        promise.resolve(rssi);
                    }
                });

            } else {

                BleManager.getInstance().scan(new BleScanCallback() {
                    @Override
                    public void onScanFinished(List<BleDevice> scanResultList) {
                    }

                    @Override
                    public void onScanStarted(boolean success) {
                    }

                    @Override
                    public void onScanning(BleDevice bleDevice) {
                        if (bleDevice.getMac().equals(mac)) {

                            try {
                                BleManager.getInstance().cancelScan();
                            } catch (Exception e) {

                            }

                            BleManager.getInstance().readRssi(BluetoothConection.getInstance().bleDevice, new BleRssiCallback() {
                                @Override
                                public void onRssiFailure(BleException exception) {
                                    QFRCTBasicUtil.sendRSSI(getReactApplicationContext(), bleDevice.getRssi() + "");

                                    promise.resolve(bleDevice.getRssi());
                                }

                                @Override
                                public void onRssiSuccess(int rssi) {
                                    QFRCTBasicUtil.sendRSSI(getReactApplicationContext(), rssi + "");
                                    promise.resolve(rssi);
                                }
                            });
                        }
                    }
                });
            }


        } catch (Throwable e) {

        }
    }

    private final int MY_REQUEST_CODE = 1000;

    private boolean requestPermisson() {
        if (getActivity(reactContext) != null) {
            PackageManager packageManager = this.getCurrentActivity().getPackageManager();
            PermissionInfo permissionInfo = null;
//        PermissionGroupInfo permissionGroupInfo = null;

            for (int i = 0; i < permissionsBlue.length; i++) {
                try {
                    permissionInfo = packageManager.getPermissionInfo(permissionsBlue[i], 0);
//                permissionGroupInfo = packageManager.getPermissionGroupInfo(permissions[i], 0);
                } catch (PackageManager.NameNotFoundException e) {
                    e.printStackTrace();
                }
                CharSequence permissionName = permissionInfo.loadLabel(packageManager);
//            CharSequence permissionnGropName = permissionGroupInfo.loadLabel(packageManager);
                if (ContextCompat.checkSelfPermission(this.getCurrentActivity(), permissionsBlue[i]) != PackageManager.PERMISSION_GRANTED) {
                    // 未获取权限
                    if (ActivityCompat.shouldShowRequestPermissionRationale(this.getCurrentActivity(), permissionsBlue[i])) {
                        // 这是一个坑，某些手机弹出提示时没有永不询问的复选框，点击拒绝就默认勾上了这个复选框，而某些手机上即使勾选上了永不询问的复选框也不起作用
                        LogUtils.i(TAG, "您勾选了不再提示【" + permissionName + "】权限的申请");
                    } else {
                        ActivityCompat.requestPermissions(this.getCurrentActivity(), permissionsBlue, MY_REQUEST_CODE);
                    }
                    return false;
                } else {
                }
            }

            return true;
        } else {
            return false;
        }


    }


    public static Activity getActivity(Context context) {
        if (context instanceof Activity) {
            return (Activity) context;
        }

        if (context instanceof ReactContext) {
            ReactContext reactContext = ((ReactContext) context);
            return reactContext.getCurrentActivity();
        }
        return null;
    }

    /**
     * @param hexstring 指令报文
     * @param mac       蓝牙标识
     * @return void
     * @method sendCmd
     * @description 向蓝牙发送指令
     * @date: 2022/9/19 15:34
     * @author: langmeng
     */
    @ReactMethod
    public void sendCmd(String hexstring, String mac) {
        LogUtils.d(TAG, "BleManager.getInstance RN 发送指令-->" + hexstring);

        try {

            if (Build.VERSION.SDK_INT >= 31) {

            } else {
                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
            }
            if (!requestPermisson()) {
                String event = "bluetoothChange";
                QFRCTBasicUtil.sendEvent(reactContext, event, 2);
                return;
            }
            LogUtils.iTag(TAG, "RN sendCmd", "mac: " + mac, "hexstring: " + hexstring);
            //获取当前BLE连接
            List<BleDevice> allConnectedDevice = BleManager.getInstance().getAllConnectedDevice();
            if (allConnectedDevice != null && !allConnectedDevice.isEmpty()) {
                BleDevice bleDevice = null;
                //匹配MAC
                for (BleDevice device : allConnectedDevice) {
                    if (device.getMac().equals(mac)) {
                        bleDevice = device;
                        break;
                    }
                }
                //写入指令
                if (bleDevice != null) {
                    TracePoints.rnSendCmd(UserInfo.get().getId(),hexstring,deviceInfo.getDeviceId());
                    BluetoothConection.getInstance().writeDevice2(bleDevice, ConvertUtils.hexString2Bytes(hexstring));
                }
            }

        } catch (Throwable e) {
            TracePoints.rnSendCmdWithException(UserInfo.get().getId(),
                    deviceInfo.getDeviceId()+"----"+e.getMessage());
        }
    }

    /**
     * @param type 语言码
     * @return void
     * @method getLanguageData
     * @description 获取RN多语言文本
     * @date: 2022/10/21 10:30
     * @author: langmeng
     */
    @ReactMethod
    public void getLanguageData(String type, Promise promise) {
        LogUtils.iTag(TAG, "RN getLanguageData", "type: " + type);
        try {
            LanguagePackageDao languagePackageDao = SoftRoomDatabase.getDatabase(getReactApplicationContext()).languagePackageDao();
            LanguagePackage codeLanguage = languagePackageDao.getLanguageByCode(type);
            promise.resolve(GsonUtils.toJson(codeLanguage.getRn()));
        } catch (Throwable e) {

        }
    }


    /**
     * Sync UTC for device
     *
     * @param onConfirmed
     */
    @ReactMethod
    public void requestUTCDateResolveBlock(final Callback onConfirmed) {
        LogUtils.iTag(TAG, "RN requestUTCDateResolve---");
        try {
            //FH + VersionCode + Cmd + data_length
            String cmd = "55AA00D10007";
            final int formatYear = 2000;
            final int formatLength = 2;
            final int zeroArea = 0;
            final String zeroAreaCode = "00";
            final String westAreaCode = "0";
            final String eastAreaCode = "1";
            final int aHour = (1000 * 3600);

            Calendar c = Calendar.getInstance();
            int offset = c.get(Calendar.ZONE_OFFSET);
            c.add(Calendar.MILLISECOND, -offset);
            Long timeStampUTC = c.getTimeInMillis();

            //get UTC time
            String year = BluetoothConection.addZeroForNum(Integer.toHexString(c.get(Calendar.YEAR) - formatYear), formatLength);
            String month = BluetoothConection.addZeroForNum(Integer.toHexString(c.get(Calendar.MONTH) + 1), formatLength);
            String day = BluetoothConection.addZeroForNum(Integer.toHexString(c.get(Calendar.DAY_OF_MONTH)), formatLength);
            String hour = BluetoothConection.addZeroForNum(Integer.toHexString(c.get(Calendar.HOUR)), formatLength);
            String minute = BluetoothConection.addZeroForNum(Integer.toHexString(c.get(Calendar.MINUTE)), formatLength);
            String second = BluetoothConection.addZeroForNum(Integer.toHexString(c.get(Calendar.SECOND)), formatLength);

            //append cmd
            cmd = cmd + year + month + day + hour + minute + second;

            //get timeZone
            //0:westArea ，1:EastArea。ZeroArea:00
            Long timeStamp = new Date().getTime();
            Long timeZone = (timeStamp - timeStampUTC) / aHour;

            if (timeZone.intValue() == zeroArea) {
                cmd = cmd + zeroAreaCode;
            } else if (timeZone.intValue() > zeroArea) {
                cmd = cmd + eastAreaCode + Integer.toHexString(timeZone.intValue());
            } else {
                cmd = cmd + westAreaCode + Integer.toHexString(Math.abs(timeZone.intValue()));
            }

            String crc = BluetoothConection.getCRC(HexUtil.hexStringToBytes(cmd));

            cmd = cmd + crc;

            LogUtils.iTag(TAG, "RN sendrequestUTCDate", "cmd: " + cmd);
            onConfirmed.invoke(cmd);

        } catch (Throwable e) {

        }
    }

    @Override
    public void isBlueEnable(boolean enable) {

    }

    @Override
    public void onBleStartConnect() {
        connectStatus = QFRCTBasicUtil.BLE_CONNECTING;
        QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, connectStatus);
        LogUtils.d("onBleStartConnect");
    }

    @Override
    public void onBleConnectFail(BleDevice bleDevice, BleException exception, int time) {
        //删除本地连接超时机制，连接层面交给RN面板自身控制
        //if (time < BleManager.getInstance().getMaxConnectCount()) {
        connectStatus = QFRCTBasicUtil.BLE_CONNECT_FAIL;
        QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, connectStatus);
        if (getCurrentActivity() == null) {
            return;
        }
        getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ProgressHelper.hideProgressView(getCurrentActivity());
            }
        });
        // }

    }

    @Override
    public void onBleConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
        //不是蓝牙设备不建立长连接
        if (null != deviceInfo) {
            if (BLE_TYPE.equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                AwsMqttService.getInstance().initDeviceClient(deviceInfo);
            }
        }
    }

    @Override
    public void onMtuChanged(int mtu) {

    }

    @Override
    public void onSetMTUFailure(BleException exception) {

    }

    @Override
    public void onGattStartConnect() {

    }

    @Override
    public void onGattConnectFail(BleDevice bleDevice, BleException exception) {
        LogUtils.i("connect fail");
        QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, QFRCTBasicUtil.BLE_CONNECT_DISCONNECTED);
        if (getCurrentActivity() == null) {
            return;
        }
        getCurrentActivity().runOnUiThread(new Runnable() {
            @Override
            public void run() {
                ProgressHelper.hideProgressView(getCurrentActivity());
            }
        });

    }

    @Override
    public void onGattConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {

    }

    @Override
    public void onGattDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {
        QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, QFRCTBasicUtil.BLE_CONNECT_DISCONNECTED);

    }

    @Override
    public void onNotifySuccess(BleDevice bleDevice) {
        this.bleDevice = bleDevice;
        //身份校验指令写入
        BluetoothConection.getInstance().writeDevice2(bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_AUTHENTICATION, BluetoothConection.AUTHENTICATION_APP));

    }


    @Override
    public void onNotifyFailure(BleDevice bleDevice, BleException exception) {

        LogUtils.d(TAG, "BleManager.getInstance RN onNotifyFailure");


    }

    @Override
    public void onWriteSuccess(int current, int total, byte[] justWrite) {
        LogUtils.iTag(TAG, "wj--onWriteSuccess", current, total, justWrite);

    }


    @Override
    public void onCharacteristicChanged(byte[] data) {
        if (data == null) {
            return;
        }
        //解决转换String后异常问题
        if (null == HexUtil.formatHexString(data)) {
            return;
        }
        try {

        String dataHexString = HexUtil.formatHexString(data).toUpperCase();
        LogUtils.i(TAG, "BleManager.getInstance hex--->" + dataHexString);

        //对于设备端不合法数据处理
        if (dataHexString.length() < 9) {
            return;
        }

        String command = dataHexString.substring(BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length(), BluetoothConection.DATA_COMMAND_END_INDEX);


        //处理D5指令
        if (command.equalsIgnoreCase(BluetoothConection.COMMAND_ONLINE_STATUS)) {

            QFRCTBasicUtil.sendHostStatusChanged(reactContext, dataHexString);

        }


        if ("B0".equalsIgnoreCase(command)) {
            QFRCTBasicUtil.sendCmdNotify(reactContext, dataHexString);
            LogUtils.d("RevFromDeviceAndsendtoRN_command11" + dataHexString);
        }
        //将蓝牙通知数据透传给RN

        LogUtils.d("BleConnectStateChange_command" + command);
        //判断指令返回
        switch (command) {
            case BluetoothConection.COMMAND_AUTHENTICATION:
                QFRCTBasicUtil.sendDeviceBleConnectStateChange(reactContext, QFRCTBasicUtil.BLE_CONNECT_SUCCESS);

                break;

            case BluetoothConection.COMMAND_OBSERVER:
                //状态上报
                reportDeviceInfoToIotCore(dataHexString);
                break;
            case BluetoothConection.COMMAND_GET_DATA_OFFLINE:
                //离线数据上报
                reportDeviceOfflineDataToIotCore(dataHexString, deviceInfo);
                break;
        }
        } catch (Exception e) {
            LogUtils.e("DeviceModule is Exception----" + data);
        }
    }

    /**
     * 上报实时工况数据
     *
     * @param dataHexString
     */
    public void reportDeviceInfoToIotCore(String dataHexString) {
        if (TextUtils.isEmpty(dataHexString)) {
            return;
        }
        String dpString = dataHexString.substring(BluetoothConection.DATA_DATALENGTH_END_INDEX, dataHexString.length() - BluetoothConection.DATA_CRC_LENGTH);
        List<DPData> dpData;
        //解析数据
        try {
            dpData = BluetoothConection.dpString2dpDatas3(dpString);
            String dpJson = new Gson().toJson(dpData);
            //发送给RN
            QFRCTBasicUtil.sendLocalDeviceDataChange(reactContext, dpJson);
            //封装协议
            HashMap<Object, Object> dataMap = new HashMap<>();
            for (DPData dpDatum : dpData) {
                Object value = null;
                if (dpDatum.getDp_type().equals(DATA_TYPE_PARAM)) {

                    value = getMultiParamValue(ConvertUtils.hexString2Int(dpDatum.getDp_id()), (ParamData[]) dpDatum.getDp_data());
                    dataMap.put(ConvertUtils.hexString2Int(dpDatum.getDp_id()), value);
                } else {
                    switch (dpDatum.getDp_type()) {
                        //raw
                        case DATA_TYPE_RAW:
                            value = dpDatum.getDp_data();
                            break;
                        //bool
                        case DATA_TYPE_BOOL:
                            if (dpDatum.getDp_data().equals(DATA_TYPE_RESULT_FALSE)) {
                                value = false;
                            } else {
                                value = true;
                            }
                            break;
                        //value
                        case DATA_TYPE_INT_VALUE:
                            value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) dpDatum.getDp_data())).order(ByteOrder.LITTLE_ENDIAN).getInt();
                            break;
                        //string
                        case DATA_TYPE_STRING:
                            StringBuffer stringBuffer = new StringBuffer();
                            String contentData = (String) dpDatum.getDp_data();
                            for (int index = 0; index < contentData.length() / 2; index++) {
                                String str = contentData.substring(index * 2, (index + 1) * 2);
                                int hexValue = Integer.parseInt(str, 16);
                                char character = (char) hexValue;
                                stringBuffer.append(character);
                            }
                            value = stringBuffer.toString();
                            break;
                        //enum
                        case DATA_TYPE_ENUM:
                            value = ConvertUtils.hexString2Int((String) dpDatum.getDp_data());
                            break;
                        //param
                        case DATA_TYPE_PARAM:
                            value = dpDatum.getDp_data();
                            break;
                        case DATA_TYPE_UNSIGNED_INT_06:
                            value = ConvertUtils.hexString2Int((String) dpDatum.getDp_data());
                            LogUtils.e(TAG, "INT_06--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                            break;
                        case DATA_TYPE_UNSIGNED_INT_07:
                            value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) dpDatum.getDp_data()))
                                    .order(ByteOrder.LITTLE_ENDIAN)
                                    .getShort() & 0xFFFF;
                            ;
                            LogUtils.e(TAG, "INT_07--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                            break;

                        /**
                         * array类型
                         */
                        case DATA_TYPE_ARRAY:
                            if (dpDatum.getDp_data() != null) {
                                if (dpDatum.getDp_id().equalsIgnoreCase("0837")){
                                    String content = (String) dpDatum.getDp_data();
                                    value = dealArrayType(content);
                                }
                            }
                            break;
                        /**
                         * struct类型
                         */
                        case DATA_TYPE_STRUCT:
                            if (dpDatum.getDp_data() != null) {
                                String content = (String) dpDatum.getDp_data();
                                ArrayList<String> source = new ArrayList<>();
                                getDataSource(content, source);
                                HashMap<String, Object> map = new HashMap<>();
                                for (int i = 0; i < source.size(); i++) {
                                    String everyData = source.get(i);
                                    String structType = everyData.substring(0, 2);
                                    String structData = everyData.substring(2);
                                    switch (structType) {
                                        //bool
                                        case DATA_TYPE_BOOL:
                                            if (structData.equals(DATA_TYPE_RESULT_FALSE)) {
                                                value = false;
                                            } else {
                                                value = true;
                                            }
                                            break;
                                        //value
                                        case DATA_TYPE_INT_VALUE:
                                            value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) structData)).order(ByteOrder.LITTLE_ENDIAN).getInt();
                                            break;
                                        //string
                                        case DATA_TYPE_STRING:
                                            StringBuffer sb = new StringBuffer();
                                            for (int index = 0; index < structData.length() / 2; index++) {
                                                String str = structData.substring(index * 2, (index + 1) * 2);
                                                int hexValue = Integer.parseInt(str, 16);
                                                char character = (char) hexValue;
                                                sb.append(character);
                                            }
                                            value = sb.toString();
                                            break;
                                        //enum
                                        case DATA_TYPE_ENUM:
                                            value = ConvertUtils.hexString2Int((String) structData);
                                            break;
                                        //param
                                        case DATA_TYPE_PARAM:
                                            value = structData;
                                            break;
                                        // unit8类型
                                        case DATA_TYPE_UNSIGNED_INT_06:
                                            value = ConvertUtils.hexString2Int((String) structData);
                                            LogUtils.e(TAG, "INT_06--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                                            break;
                                        // unit16类型
                                        case DATA_TYPE_UNSIGNED_INT_07:
                                            value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) structData))
                                                    .order(ByteOrder.LITTLE_ENDIAN)
                                                    .getShort() & 0xFFFF;
                                            ;
                                            LogUtils.e(TAG, "INT_07--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                                            break;
                                        default:
                                            value = structData;
                                            break;
                                    }
                                    map.put((i + 1) + "", value);
                                }
                                value = map;
                            }
                            break;
                        default:
                            value = dpDatum.getDp_data();
                            break;
                    }
                    dataMap.put(ConvertUtils.hexString2Int(dpDatum.getDp_id()), value);
                }
            }


            //如果没有deviecId取消上报
            if (deviceInfo == null) {
                return;
            }
            LogUtils.e("RNLOGdpString--" + dpString + "_" + UserInfo.get().getId() + "/" + deviceInfo.getDeviceId() + ".ble");
            //设置网关拓扑关系
            dataMap.put("route", UserInfo.get().getId() + "/" + deviceInfo.getDeviceId() + ".ble");
            AwsUpdateBean awsUpdateData = new AwsUpdateBean();
            AwsUpdateBean.StateDTO stateDTO = new AwsUpdateBean.StateDTO();
            stateDTO.setReported(dataMap);
            awsUpdateData.setState(stateDTO);
            AwsUpdatePushEvent awsUpdatePushEvent = new AwsUpdatePushEvent(deviceInfo.getDeviceId(), awsUpdateData);
            //发送上报数据至IOT CORE
            AwsMqttService.deviceModelUpdate(awsUpdatePushEvent);

        } catch (Exception e) {
            LogUtils.e(TAG, "reportDeviceInfoToIotCore--->" + e.getMessage());
        }
    }


    /**
     * 上报离线数据到IotCore
     *
     * @param dataHexString
     */
    private void reportDeviceOfflineDataToIotCore(String dataHexString, DeviceInfo deviceInfo) {

        LogUtils.e(TAG, "reportDeviceOfflineDataToIotCore---" + dataHexString);
        if (null == deviceInfo) {
            return;
        }

        if (TextUtils.isEmpty(deviceInfo.getMac())) {
            return;
        }

        if (TextUtils.isEmpty(dataHexString)) {
            return;
        }

        String dpString = dataHexString.substring(BluetoothConection.DATA_DATALENGTH_END_INDEX, dataHexString.length() - BluetoothConection.DATA_CRC_LENGTH);
        if (TextUtils.isEmpty(dpString)) {
            return;
        }
        //解析数据

        List<DPData> dpData;
        dpData = BluetoothConection.dpString2dpDatas3(dpString);
        String dpJson = new Gson().toJson(dpData);
        //发送给RN
        QFRCTBasicUtil.sendLocalDeviceDataChange(reactContext, dpJson);
        //封装协议
        HashMap<Object, Object> dataMap = new HashMap<>();
        for (DPData dpDatum : dpData) {
            Object value = null;
            if (dpDatum.getDp_type().equals(DATA_TYPE_PARAM)) {
                value = getMultiParamValue(ConvertUtils.hexString2Int(dpDatum.getDp_id()), (ParamData[]) dpDatum.getDp_data());
                dataMap.put(ConvertUtils.hexString2Int(dpDatum.getDp_id()), value);
            } else {
                switch (dpDatum.getDp_type()) {
                    //raw
                    case DATA_TYPE_RAW:
                        value = dpDatum.getDp_data();
                        break;
                    //bool
                    case DATA_TYPE_BOOL:
                        if (dpDatum.getDp_data().equals(DATA_TYPE_RESULT_FALSE)) {
                            value = false;
                        } else {
                            value = true;
                        }
                        break;
                    //value
                    case DATA_TYPE_INT_VALUE:
                        value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) dpDatum.getDp_data())).order(ByteOrder.LITTLE_ENDIAN).getInt();
                        break;
                    //string
                    case DATA_TYPE_STRING:
                        StringBuffer stringBuffer = new StringBuffer();
                        String contentData = (String) dpDatum.getDp_data();
                        for (int index = 0; index < contentData.length() / 2; index++) {
                            String str = contentData.substring(index * 2, (index + 1) * 2);
                            int hexValue = Integer.parseInt(str, 16);
                            char character = (char) hexValue;
                            stringBuffer.append(character);
                        }
                        value = stringBuffer.toString();
                        break;
                    //enum
                    case DATA_TYPE_ENUM:
                        value = ConvertUtils.hexString2Int((String) dpDatum.getDp_data());
                        break;
                    //param
                    case DATA_TYPE_PARAM:
                        value = dpDatum.getDp_data();
                        break;
                    // unit8类型
                    case DATA_TYPE_UNSIGNED_INT_06:
                        value = ConvertUtils.hexString2Int((String) dpDatum.getDp_data());
                        LogUtils.e(TAG, "INT_06--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                        break;
                    // unit16类型
                    case DATA_TYPE_UNSIGNED_INT_07:
                        value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) dpDatum.getDp_data()))
                                .order(ByteOrder.LITTLE_ENDIAN)
                                .getShort() & 0xFFFF;
                        ;
                        LogUtils.e(TAG, "INT_07--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                        break;

                    /**
                     * array类型
                     */
                    case DATA_TYPE_ARRAY:
                        if (dpDatum.getDp_data() != null) {
                            String content = (String) dpDatum.getDp_data();
                            value = dealArrayType(content);
                        }
                        break;
                    /**
                     * struct类型
                     */
                    case DATA_TYPE_STRUCT:
                        if (dpDatum.getDp_data() != null) {
                            String content = (String) dpDatum.getDp_data();
                            ArrayList<String> source = new ArrayList<>();
                            getDataSource(content, source);
                            HashMap<String, Object> map = new HashMap<>();
                            for (int i = 0; i < source.size(); i++) {
                                String everyData = source.get(i);
                                String structType = everyData.substring(0, 2);
                                String structData = everyData.substring(2);
                                switch (structType) {
                                    //bool
                                    case DATA_TYPE_BOOL:
                                        if (structData.equals(DATA_TYPE_RESULT_FALSE)) {
                                            value = false;
                                        } else {
                                            value = true;
                                        }
                                        break;
                                    //value
                                    case DATA_TYPE_INT_VALUE:
                                        value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) structData)).order(ByteOrder.LITTLE_ENDIAN).getInt();
                                        break;
                                    //string
                                    case DATA_TYPE_STRING:
                                        StringBuffer sb = new StringBuffer();
                                        for (int index = 0; index < structData.length() / 2; index++) {
                                            String str = structData.substring(index * 2, (index + 1) * 2);
                                            int hexValue = Integer.parseInt(str, 16);
                                            char character = (char) hexValue;
                                            sb.append(character);
                                        }
                                        value = sb.toString();
                                        break;
                                    //enum
                                    case DATA_TYPE_ENUM:
                                        value = ConvertUtils.hexString2Int((String) structData);
                                        break;
                                    //param
                                    case DATA_TYPE_PARAM:
                                        value = structData;
                                        break;
                                    // unit8类型
                                    case DATA_TYPE_UNSIGNED_INT_06:
                                        value = ConvertUtils.hexString2Int((String) structData);
                                        LogUtils.e(TAG, "INT_06--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                                        break;
                                    // unit16类型
                                    case DATA_TYPE_UNSIGNED_INT_07:
                                        value = ByteBuffer.wrap(ConvertUtils.hexString2Bytes((String) structData))
                                                .order(ByteOrder.LITTLE_ENDIAN)
                                                .getShort() & 0xFFFF;
                                        LogUtils.e(TAG, "INT_07--" + "id---" + dpDatum.getDp_id(), "data--->" + value);
                                        break;
                                    default:
                                        value = structData;
                                        break;
                                }
                                map.put((i + 1) + "", value);
                            }
                            value = map;
                        }
                        break;
                    default:
                        value = dpDatum.getDp_data();
                        break;
                }
                dataMap.put(ConvertUtils.hexString2Int(dpDatum.getDp_id()), value);
            }
        }

        //设置网关拓扑关系
        dataMap.put("route", UserInfo.get().getId() + "/" + deviceInfo.getDeviceId() + ".ble");
        AwsUpdateBean awsUpdateData = new AwsUpdateBean();
        AwsUpdateBean.StateDTO stateDTO = new AwsUpdateBean.StateDTO();
        stateDTO.setReported(dataMap);
        awsUpdateData.setState(stateDTO);

        AwsOfflineDataEvent awsUpdatePushEvent = new AwsOfflineDataEvent(deviceInfo.getDeviceId(), awsUpdateData);

        AwsMqttService.getInstance().updateOfflineData(awsUpdatePushEvent);

        if (null == bleDevice) {
            return;
        }
        //发送给设备指令
        BluetoothConection.getInstance().writeToDevice(bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_SEND_REPORT_OFFLINE_DATA_SUCCESS, "01"));
        LogUtils.e(TAG + "有离线数据了", "reportDeviceOfflineDataToIotCore");
    }

    private void getDataSource(String content, ArrayList<String> sources) {
        String structType = content.substring(0, 2);
        String structLength = content.substring(2, 4);
        int length = ConvertUtils.hexString2Int(structLength);
        String structData = content.substring(4, 4 + length * 2);

        sources.add(structType + structData);
        int firstLength = 4 + length * 2;
        if (content.length() > firstLength) {
            getDataSource(content.substring(firstLength, content.length()), sources);
        }
    }


    private HashMap<Object, Object> dealArrayType(String content) {
        String boolValueFalse = "00";
        if (TextUtils.isEmpty(content) || content.length() < 4) {
            return null;
        }
        //创达遗留-bool--01、int--02、enum--04、uint8-06、uint16--07

        HashMap<Object, Object> map = new HashMap<>();
        String paramType = content.substring(0, 2);
//        int paramNumber = Integer.parseInt(content.substring(2, 4), 16);

        String value = content.substring(4);

        switch (paramType) {
            case DATA_TYPE_BOOL:

                //取出bool数组
                String[] split = splitStringByTwoChars(value);
                for (int i = 0; i <= split.length-1; i++) {
                    map.put(i + 1, !boolValueFalse.equals(split[i]));
                }

                break;
            case DATA_TYPE_INT_VALUE:

                //取出int 数组
                String[] splitInt = splitStringByEightChars(value);
                for (int i = 0; i <= splitInt.length-1; i++) {
                    map.put(i + 1, ByteBuffer.wrap(ConvertUtils.hexString2Bytes(splitInt[i])).order(ByteOrder.LITTLE_ENDIAN).getInt());
                }

                break;
            case DATA_TYPE_ENUM:

                String[] splitEnum = splitStringByTwoChars(value);
                for (int i = 0; i <= splitEnum.length-1; i++) {
                    map.put(i + 1, String.valueOf(ConvertUtils.hexString2Int(splitEnum[i])));
                }

                break;

            case DATA_TYPE_UNSIGNED_INT_06:

                String[] split_UNSIGNED_INT_06 = splitStringByTwoChars(value);
                for (int i = 0; i <= split_UNSIGNED_INT_06.length-1; i++) {
                    map.put(i + 1, ConvertUtils.hexString2Int(split_UNSIGNED_INT_06[i]));
                }

                break;
            case DATA_TYPE_UNSIGNED_INT_07:

                String[] split_UNSIGNED_INT_07 = splitStringByFourChars(value);
                for (int i = 0; i <= split_UNSIGNED_INT_07.length-1; i++) {

                    int valueInt16 = ByteBuffer.wrap(ConvertUtils.hexString2Bytes(split_UNSIGNED_INT_07[i]))
                            .order(ByteOrder.LITTLE_ENDIAN)
                            .getShort() & 0xFFFF;

                    map.put(i + 1, valueInt16);
                }

                break;
        }


        return map;
    }


    /**
     * 将字符串按两位分割成数组
     * @param str
     * @return
     */
    public static String[] splitStringByTwoChars(String str) {
        int len = str.length() / 2;
        String[] result = new String[len];
        for (int i = 0; i < len; i++) {
            result[i] = str.substring(i * 2, i * 2 + 2);
        }
        return result;
    }

    /**
     * 将字符串按四位分割成数组
     * @param str
     * @return
     */
    public static String[] splitStringByFourChars(String str) {
        int len = str.length() / 4;
        String[] result = new String[len];
        for (int i = 0; i < len; i++) {
            result[i] = str.substring(i * 4, i * 4 + 4);
        }
        return result;
    }

    /**
     * 将字符串按四位分割成数组
     * @param str
     * @return
     */
    public static String[] splitStringByEightChars(String str) {
        int len = str.length() / 8;
        String[] result = new String[len];
        for (int i = 0; i < len; i++) {
            result[i] = str.substring(i * 8, i * 8 + 8);
        }
        return result;
    }

}
