package com.chervon.moudleContainer.reactnative_multibundler.ui;


import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.CVNNetworkUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.FireBaseUtils;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.BundleInfoEntry;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleContainer.R;
import com.chervon.moudleContainer.panel.data.repository.PanelRepo;
import com.chervon.moudleContainer.reactnative_multibundler.CheckVersionService;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceManager;
import com.tbruyelle.rxpermissions2.RxPermissions;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;


@Route(path = RouterConstants.ACTIVITY_URL_PANEL)
public class PanelActivity extends AppCompatActivity {
    private DeviceInfo mDeviceInfo;
    private String[] permissionsBlue = new String[]{Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_CONNECT};
    PanelRepo panelRepo;
    private boolean market = false;
    private Dialog loadingDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.main_act_loadbundle);
        panelRepo = new PanelRepo(this);
        ReactInstanceManager reactInstanceManager = ((ReactApplication) getApplication()).getReactNativeHost().getReactInstanceManager();
        // if (!reactInstanceManager.hasStartedCreatingInitialContext()) {
        reactInstanceManager.createReactContextInBackground();//这里会先加载基础包platform.android.bundle，也可以不加载
        // }
        //事先加载基础包可以减少后面页面加载的时间，但相应的会增加内存使用


        Bundle bundle = this.getIntent().getExtras();


        if (bundle != null) {
            String messageContent = bundle.getString("messageDataJson");
            String deviceId = bundle.getString("deviceId");
            if (!TextUtils.isEmpty(messageContent) || !TextUtils.isEmpty(deviceId)) {
                mDeviceInfo = panelRepo.getDeviceFromDb(deviceId);
                if (mDeviceInfo == null) {
                    mDeviceInfo = new DeviceInfo();
                    mDeviceInfo.setDeviceId(deviceId);
                }
                bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
                getIntent().putExtras(bundle);
            } else {
                mDeviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
            }
            //设置当前操作设备
            BaseApplication.getInstance().setCurrentDeviceId(null != mDeviceInfo ? mDeviceInfo.getDeviceId() : "");

        }

        checkNet();

        try {
            if (loadingDialog == null) {
                LogUtils.d("MobileLog_showLoading_buz");
                loadingDialog = DialogUtil.showRnLoadingDialog(this);
            } else {
                LogUtils.d("MobileLog_showLoading_buz");
                loadingDialog.show();
            }
        } catch (Throwable e) {

        }

    }

    private void checkNet() {
        //对于PID为空做退出面板处理
        if (null == mDeviceInfo) {
            toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
            return;
        }
        if (TextUtils.isEmpty(mDeviceInfo.getProductId())) {
            toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
            return;
        }


        if (CVNNetworkUtils.getNetworkType() != CVNNetworkUtils.NETWORK_UNLINK) {
            BundleInfoEntry bundleInfo = SoftRoomDatabase.getDatabase(this).rnBundleInfoDao().getBundleInfo(Long.parseLong(mDeviceInfo.getProductId()));
            if (TextUtils.isEmpty(mDeviceInfo.getProductId())) {
                toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
                return;
            }
            try {
                if (null != bundleInfo) {
                    mDeviceInfo.setRnBundleName(bundleInfo.getRnBundleName());
                    mDeviceInfo.setVersion(bundleInfo.getLastRnVersion());
                }
            } catch (Exception e) {
                toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
            }

        } else {
            toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
        }
    }

    @Override
    protected void onStart() {
        super.onStart();

        BluetoothService.getInstance().stopPostDevice();

        getLastVersion();


    }

    /**
     * 获取RN面板最新版本
     * 判断是容器版本是否满足加载
     */
    @SuppressLint("CheckResult")
    private void getLastVersion() {

        if (market) {
            finish();
        }
        if (null == mDeviceInfo) {
            return;
        }
        if (TextUtils.isEmpty(mDeviceInfo.getProductId())) {
            return;
        }
        String deviceId = mDeviceInfo.getDeviceId();
        String mac = mDeviceInfo.getMac();
        panelRepo.getDeviceInfo(deviceId).subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<HttpResponse<DeviceInfo>>() {
            @Override
            public void accept(HttpResponse<DeviceInfo> deviceInfoResponse) throws Exception {
                if (deviceInfoResponse.status) {
                    mDeviceInfo = deviceInfoResponse.response;
                    //CRM功能缺失mac地址---会延迟更新-----手动更新mac地址，补齐mac地址
                    if (!TextUtils.isEmpty(mac)&&TextUtils.isEmpty(mDeviceInfo.getMac())){
                        mDeviceInfo.setMac(mac);
                    }
                    if (null != mDeviceInfo) {
                        panelRepo.upDateDevice(mDeviceInfo);
                        String rnBundleName = mDeviceInfo.getRnBundleName();
                        if (TextUtils.isEmpty(rnBundleName)) {
                            openUpgradeDialog();
                        } else {
                            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
                                permissionsBlue = new String[]{Manifest.permission.ACCESS_COARSE_LOCATION};
                            }
                            if (!checkBluePermission()) {
                                toastAndgoHome(LanguageStrings.app_networkconfig_bluetooth_permission_tip());
                            } else {
                                if (null == mDeviceInfo.getProductId()) {
                                    finish();
                                }
                                if (TextUtils.isEmpty(mDeviceInfo.getProductId()) || TextUtils.isEmpty(mDeviceInfo.getCommunicateMode())) {
                                    finish();
                                } else {
                                    if (null!=loadingDialog){
                                        loadingDialog.dismiss();
                                    }
                                    setFirebaseTag();
                                    Intent intent = getIntent();
                                    Bundle bundle = intent.getExtras();
                                    bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
                                    intent.putExtras(bundle);
                                    intent.setClass(PanelActivity.this, BuzActivity.class);
                                    intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
                                    startActivity(intent);

                                }
                            }
                        }
                    } else {
                        toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
                    }
                } else {
                    toastAndgoHome(LanguageStrings.app_base_nonetwork_textview_text());
                }
            }
        });

    }

    /**
     * 打开提示升级强制弹窗
     */
    private void openUpgradeDialog() {
        CheckVersionService.getInstance().simpleConfirmDialog(PanelActivity.this, new CheckVersionService.DialogListener() {
            @Override
            public void upgradeNow() {
                intentGoogleMarket();
            }

            @Override
            public void upgradeLater() {
                finish();
            }

            @Override
            public void backKeyPress() {
                finish();
            }
        });
    }

    private void intentGoogleMarket() {
        try {
            market = true;
            String packageNameNa = "com.chervon.connect.na";
            String packageNameEu = "com.chervon.connect.eu";
            String packageName = BuildConfig.EVN.equalsIgnoreCase(Utils.NA_TAG) ? packageNameNa : packageNameEu;
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("market://details?id=" + packageName));
            intent.setPackage("com.android.vending");
            if (intent.resolveActivity(this.getPackageManager()) != null) {
                startActivity(intent);
            } else {
                Intent intent2 = new Intent(Intent.ACTION_VIEW);
                intent2.setData(Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
                if (intent2.resolveActivity(getPackageManager()) != null) {
                    startActivity(intent2);
                } else {
                    finish();
                }
            }
        } catch (ActivityNotFoundException activityNotFoundException1) {
            LogUtils.e("GoogleMarket Intent not found");
            finish();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).navigation();
        }
        return false;

    }


    // 蓝牙层面动态申请权限
    private boolean checkBluePermission() {
        RxPermissions rxPermissions = new RxPermissions(PanelActivity.this);
        if (ContextCompat.checkSelfPermission(PanelActivity.this, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {

            BluetoothService.getInstance().PERMISSION_GRANTED = true;

        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                String[] bluetoothPermission = new String[]{Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_SCAN};
                rxPermissions.request(bluetoothPermission).subscribe(new io.reactivex.Observer<Boolean>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        LogUtils.e("");
                    }

                    @Override
                    public void onNext(@NonNull Boolean aBoolean) {
                        BluetoothService.getInstance().PERMISSION_GRANTED = aBoolean;
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        LogUtils.e("");
                    }

                    @Override
                    public void onComplete() {
                        LogUtils.e("");
                    }
                });

            } else {
                if (ContextCompat.checkSelfPermission(PanelActivity.this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                    BluetoothService.getInstance().PERMISSION_GRANTED = true;
                } else {
                    BluetoothService.getInstance().PERMISSION_GRANTED = false;
                }

            }
        }

        return BluetoothService.getInstance().PERMISSION_GRANTED;

    }


    private void toastAndgoHome(String result) {
        findViewById(R.id.btn_go_buz2).postDelayed(new Runnable() {
            @Override
            public void run() {
                ToastUtils.showLong(result);
            }
        }, 500);
        findViewById(R.id.btn_go_buz2).postDelayed(new Runnable() {
            @Override
            public void run() {
                if (null!=loadingDialog){
                    loadingDialog.dismiss();
                }
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                        .withInt(KEY_FROM,R.layout.main_act_loadbundle).navigation();

            }
        }, 1000);
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null!=loadingDialog){
            loadingDialog.dismiss();
        }
    }

    private void setFirebaseTag() {
        final String RN_Bundle_Name = "RN_Bundle_Name";
        final String evn_key = "APP_EVN";
        final String user_email_key = "USER_EMAIL";
        String userEmail = SoftRoomDatabase.getDatabase(this).userDao().getUser().getEmail();
        Bundle bundle = new Bundle();
        bundle.putString(RN_Bundle_Name, mDeviceInfo.getRnBundleName());
        bundle.putString(evn_key, BuildConfig.EVN + BuildConfig.FLAVOR);
        bundle.putString(user_email_key, TextUtils.isEmpty(userEmail)?"":userEmail);
        FireBaseUtils.addPanelCrashLogEvent(bundle);

    }
}
