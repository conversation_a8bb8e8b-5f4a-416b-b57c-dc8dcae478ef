package com.chervon.moudleContainer.panel.modules;


import static android.os.Build.VERSION.RELEASE;
import static android.os.Looper.getMainLooper;
import static com.blankj.utilcode.util.ActivityUtils.startActivity;
import static com.chervon.libBase.utils.APPConstants.IS_RESET_WIFI_CONNECTION;
import static com.chervon.libBase.utils.Utils.SOURCE_NAME;
import static com.chervon.libBase.utils.Utils.sendTrace;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_FEEDBACK_QUESTION;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE_REGISTED_DETAIL;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_MESSAGE_LIST;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEICON;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEID;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICENAME;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_CONFIG;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_MORE_SOLUTIONS;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_JOB_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_MOUNT_DEVICE;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRIVACY_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_SINGLEMCU;
import static com.chervon.libRouter.RouterConstants.PARAMETER_UPGRADE_MODEL;
import static com.chervon.libRouter.RouterConstants.WIFI_OFFLINE;


import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.location.Location;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.adapter.DialogBottomListAdapter;
import com.chervon.libBase.ui.widget.RnCustomDatePicker;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.ObjectListCompressor;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.request.LanguageV2Request;
import com.chervon.libNetwork.http.model.result.LanguageV2Res;
import com.chervon.libNetwork.http.model.result.MessageListBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleContainer.R;
import com.chervon.moudleContainer.panel.data.entity.RNConstants;
import com.chervon.moudleContainer.panel.data.entity.RnExtend;
import com.chervon.moudleContainer.panel.utils.QFRCTBasicUtil;
import com.chervon.moudleContainer.reactnative_multibundler.ui.BuzActivity;
import com.chervon.moudleContainer.widget.RNTimeDatePicker;
import com.chervon.trace.bean.EventBean;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONObject;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleContainer.panel.modules
 * @ClassName: Mobile
 * @Description:Mobile module
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:18
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/8/9
 * @UpdateRemark: 新增设备分享路由
 * @Version: 1.1
 */
public class Mobile extends ReactContextBaseJavaModule {

    private static final String TAG = "Mobile-----";
    public static final String UUID = "uuid";
    private static final String PATH_MESSAGE_DETAIL = "/MessageCenter/MessageDetail";
    private static final String PATH_DECICE_AGREEMENT_DETAIL = "/DeviceManage/privacyAgreementDetail";
    private static final String PATH_DECICE_REGIST = "/DeviceManage/deviceRegist";
    private static final String PATH_DECICE_REGIST_DETAIL = "/DeviceManage/deviceRegistedInfo";

    private static final String PATH_OTA_UPDATE = "/DeviceManage/OTAInfo";
    private static final String PATH_DECICE_AGREEMENT = "/DeviceManage/privacyAgreement";

    private static final String PATH_RESET_WIFI_INFO = "/ConfigNet/wifiConnection";

    private static final String PATH_SHARE_DEVICE_DETAIL= "/UserCenter/shareDeviceDetail";

    //适配R wangheng 2023/9/20
    private static final String PATH_DEALER_LOCATION = "/DeviceManage/productDealers";

    private static final String PARAMETER_PANEL_PRIVACY_ID = "deviceKeyId";


    private static ReactApplicationContext reactContext;

    private static final String DURATION_SHORT_KEY = "SHORT";
    private static final String DURATION_LONG_KEY = "LONG";
    private static final String PATH_PRODUCT_HELP = "/DeviceManage/productHelp";
    private static final String PATH_HELP_CENTER = "/UserCenter/helpCenter";

    private static final String PATH_PRODUCT_FITTING = "/DeviceManage/productFittings";


    //more solutiosn 页面
    private static final String PATH_WIFI_MORE_SOLUTIONS = "/NetworkConfig/moreSolutions";
    private static final String PARE_DEVICEID = "deviceId";
    private static final String PARE_PRODUCTID = "productId";

    private static final String TIME_FORMAT_24 = "24";
    private static final String PACKAGE_NAME_GOOGLE_MAP = "com.google.android.apps.maps";


    public static boolean canExitRN = false;

    public Mobile(ReactApplicationContext context) {
        super(context);
        reactContext = context;
    }

    @NonNull
    @Override
    public String getName() {
        return "RNCallNativeMobile";
    }

    @Override
    public Map<String, Object> getConstants() {
        final Map<String, Object> constants = new HashMap<>();
        constants.put(DURATION_SHORT_KEY, Toast.LENGTH_SHORT);
        constants.put(DURATION_LONG_KEY, Toast.LENGTH_LONG);
        return constants;
    }


    @ReactMethod
    public void back() {
        try {
            DeviceZ6Module.setBackClick(true);
            if (!canExitRN) {
                return;
            }
            canExitRN = false;

            Handler mHandler = new Handler(getMainLooper());
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    AwsMqttService.getInstance().disConnectWithDevice();
                    BluetoothConection.getInstance().disConnectDevice();
                    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM,R.layout.main_act_loadbundle).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT).navigation();
                }
            }, 1000);
        } catch (Throwable e) {
            //出现异常退出面板
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM,R.layout.main_act_loadbundle).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT).navigation();
        }
    }

    @ReactMethod
    public void bottomListDialog(String[] itemList, String selected, final Callback onConfirmed) {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    new DialogUtil().showBottomListDialog(Mobile.this.getCurrentActivity(), "", new DialogBottomListAdapter.OnItemClickListener() {

                        @Override
                        public void onClick(View var1, String itemName) {
                            onConfirmed.invoke(itemName);
                            // bottomSheetDialog.hide();
                        }
                    }, itemList);
                }
            });
        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void disablePopGesture() {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                }
            });

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void enablePopGesture() {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                }
            });
        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void getMobileInfo(Callback callback) {
        try {
            Map<String, Object> mobileMap = new HashMap();
            mobileMap.put("lat", BaseApplication.getInstance().latitude);
            mobileMap.put("lon", BaseApplication.getInstance().longitude);
            mobileMap.put("os", "Android");
            mobileMap.put("osSystem", RELEASE);
            mobileMap.put("lang", MyLang.getCurrentLanguageCode());
            mobileMap.put("ele", "");
            mobileMap.put("platform", Build.MODEL);
            mobileMap.put("countryCode", Utils.getCountryCode(this.getReactApplicationContext(), ""));
            User user = UserInfo.get();
            if (user != null) {
                mobileMap.put("phoneCode", user.getPhone());
            }
            mobileMap.put("appRnVersion", AppUtils.getAPPRNVersion());
            mobileMap.put("t", System.currentTimeMillis());
            TimeZone timeZone = TimeZone.getDefault();
            if (timeZone != null) {
                mobileMap.put("timezoneId", timeZone.getID());
            }
            callback.invoke(new Object[]{QFRCTBasicUtil.panelConfigToWritableMap(mobileMap)});
        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void getNetworkState(Callback callback) {
        try {
            @SuppressLint("MissingPermission") NetworkUtils.NetworkType networkType = NetworkUtils.getNetworkType();
            if (networkType == NetworkUtils.NetworkType.NETWORK_WIFI) {
                callback.invoke("WIFI");
            } else {
                callback.invoke("GPRS");
            }
        } catch (Throwable e) {

        }
    }


    @ReactMethod
    public void showLoading() {

        LogUtils.d("MobileLog_showLoading");
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            ((BuzActivity) getCurrentActivity()).showProgress();
        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void hideLoading() {

        LogUtils.d("MobileLog_hideLoading");
        try {
            if (getCurrentActivity() == null) {
                return;
            }
        } catch (Throwable e) {

        }
        LogUtils.d("MobileLog_hideLoading_hideProgress");
        ((BuzActivity) getCurrentActivity()).hideProgress();

    }

    @ReactMethod
    public void is24Hour(Promise promise) {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            ContentResolver cv = getCurrentActivity().getContentResolver();
            String strTimeFormat = android.provider.Settings.System.getString(cv,
                    android.provider.Settings.System.TIME_12_24);
            if (strTimeFormat != null && strTimeFormat.equals(TIME_FORMAT_24)) {
                promise.resolve(true);
            } else {
                promise.resolve(false);
            }

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void getLanguageData(Promise promise) {
        try {
            String sysCode = "rn";
            ArrayList<String> codeList = new ArrayList<>();
            codeList.add(MyLang.getCurrentLanguageCode());
            LanguageV2Request req = new LanguageV2Request(codeList,sysCode);

            ApiService.instance().getLanguagePackageV2(req).subscribe(new HttpObserver<LanguageV2Res>() {
                @RequiresApi(api = Build.VERSION_CODES.O)
                @Override
                protected void Next(LanguageV2Res entity) {
                    if (entity.isStatus()) {
                        if (null == entity.getEntry()) {
                            promise.resolve("");
                            return;
                        }
                        if (TextUtils.isEmpty(entity.getEntry().getData())) {
                            promise.resolve("");
                            return;
                        } else {
                            try {
                                String decompress = ObjectListCompressor.decompress(Base64.getDecoder().decode(entity.getEntry().getData()));
                                List<LanguageV2Res.LanguageModel.LanguageItem> langList = new Gson().fromJson(decompress, new TypeToken<List<LanguageV2Res.LanguageModel.LanguageItem>>() {
                                }.getType());
                                entity.getEntry().setDecompressData(langList);
                            } catch (IOException e) {
                                e.printStackTrace();
                                promise.resolve("");
                            }
                        }
                        //转换成旧的面板可以使用的json对象
                        HashMap<String, String> rnMap = new HashMap();
                        for (LanguageV2Res.LanguageModel.LanguageItem item : entity.getEntry().getDecompressData()) {
                            rnMap.put(item.getLangCode(), item.getContent());
                        }

                        promise.resolve(GsonUtils.toJson(rnMap));
                    } else {
                        promise.resolve("");
                    }
                }
            });

        } catch (Throwable e) {
            promise.resolve("");
        }
    }

    @ReactMethod
    public void jumpTo(String url, Callback callback) {
        try {
            //如果RN面板失去网络连接，跳转交互提示网络异常 IOT-10910
            if (!NetworkUtils.isConnected()) {
                ToastUtils.showShort(LanguageStrings.appBaseHaveNotNetWork());
                return;
            }
            Uri content_url = Uri.parse(url);
            LogUtils.i("lesly", "leslyjumpTo: url = " + url);
            String path = content_url.getPath();
            LogUtils.i("lesly", "leslyjumpTo: path = " + path);

            if (ACTIVITY_URL_MESSAGE_LIST.equals(path)) {
                // /MessageCenter/MessageList?sourceType=RN&messageType=2&deviceld=1234567
                String KEY_SOURCE_TYPE = "sourceType";
                String KEY_MESSAGE_TYPE = "messageType";
                String KEY_PRODUCTID = "productId";
                String KEY_DEVICENAME = "deviceName";
                int DEFAULT_MESSAGE_TYPE = -1;
                String deviceId = content_url.getQueryParameter(PARAMETER_DEVICD_ID);
                String sourceType = content_url.getQueryParameter(KEY_SOURCE_TYPE);
                //默认消息类型给-1  如果未-1不跳转。防止RN测不传递参数
                int messageType = DEFAULT_MESSAGE_TYPE;
                try {
                    messageType = Integer.parseInt(content_url.getQueryParameter(KEY_MESSAGE_TYPE));
                } catch (Exception e) {
                    LogUtils.e("messageType is exception");
                }

                String productId = content_url.getQueryParameter(KEY_PRODUCTID);
                String deviceName = "";
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                if (bundle != null) {
                    DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                    productId = deviceInfo.getProductId();
                    deviceName = deviceInfo.getNickName();
                    if (TextUtils.isEmpty(productId)) {
                        return;
                    }
                    if (TextUtils.isEmpty(deviceName)) {
                        return;
                    }
                }

                if (TextUtils.isEmpty(deviceId)) {
                    return;
                }
                if (TextUtils.isEmpty(sourceType)) {
                    return;
                }

                if (DEFAULT_MESSAGE_TYPE == messageType) {
                    return;
                }

                ARouter.getInstance().build(ACTIVITY_URL_MESSAGE_LIST)
                        .withString(PARAMETER_DEVICD_ID, deviceId)
                        .withString(KEY_SOURCE_TYPE, sourceType)
                        .withInt(KEY_MESSAGE_TYPE, messageType)
                        .withString(KEY_PRODUCTID, productId)
                        .withString(KEY_DEVICENAME, deviceName)
                        .navigation();

            } else if (PATH_DECICE_REGIST.equals(path)) {
                //未注册
                String deviceId = content_url.getQueryParameter(PARAMETER_DEVICD_ID);
                if (getCurrentActivity() == null) {
                    return;
                }
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                if (bundle != null) {
                    DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                    ARouter.getInstance().build(ACTIVITY_URL_DEVICE)
                            .withSerializable(KEY_PREV_DATA, deviceInfo)
                            .withInt(KEY_PREV_FRAGMENT, 1234)
                            .navigation();
                }
            } else if (PATH_DECICE_REGIST_DETAIL.equals(path)) {
                //注册 registration
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                ARouter.getInstance().build(ACTIVITY_URL_DEVICE_REGISTED_DETAIL)
                        .withSerializable(KEY_PREV_DATA, deviceInfo).withInt(KEY_PREV_FRAGMENT, 0)
                        .navigation();
            } else if (PATH_OTA_UPDATE.equals(path)) {
                String deviceId = content_url.getQueryParameter(PARAMETER_DEVICD_ID);
                String jobId = content_url.getQueryParameter(PARAMETER_JOB_ID);
                boolean singleMcu = content_url.getBooleanQueryParameter(PARAMETER_SINGLEMCU, false);
                String upgradeModelQueryParameter = content_url.getQueryParameter(PARAMETER_UPGRADE_MODEL);
                int upgradeModel = 0;
                if (upgradeModelQueryParameter != null && !upgradeModelQueryParameter.isEmpty()) {
                    upgradeModel = Integer.parseInt(upgradeModelQueryParameter);
                }
                boolean mountDevice = content_url.getBooleanQueryParameter(PARAMETER_MOUNT_DEVICE, false);
                ARouter.getInstance().build(ACTIVITY_URL_DEVICE_UPGRADE)
                        .withString(PARAMETER_DEVICD_ID, deviceId)
                        .withString(PARAMETER_JOB_ID, jobId)
                        .withBoolean("singleMcu", singleMcu)
                        .withBoolean("mountDevice", mountDevice)
                        .withInt("upgradeModel", upgradeModel)
                        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                TracePoints.clickRnRouteHistory( UserInfo.get().getId(),jobId,deviceId);

            } else if (PATH_DECICE_AGREEMENT.equals(path)) {
                String productId = content_url.getQueryParameter(PARAMETER_PRODUCT_ID);
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                productId = deviceInfo.getProductId();
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRIVACY).withInt(KEY_FROM, R.id.controlPanel)
                        .withString(KEY_PREV_DATA, productId)
                        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

            } else if (PATH_DECICE_AGREEMENT_DETAIL.equals(path)) {
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                String productId = deviceInfo.getProductId();
                String privacyID = content_url.getQueryParameter(PARAMETER_PANEL_PRIVACY_ID);
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.controlPanel)
                        .withString(KEY_PREV_DATA, productId)
                        .withString(PARAMETER_PRIVACY_ID, privacyID)
                        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
            } else if (PATH_PRODUCT_HELP.equals(path)) {
                //产品百科点击时
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                String productId = content_url.getQueryParameter(PARAMETER_PRODUCT_ID);
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL).withSerializable(KEY_PREV_DATA, deviceInfo).navigation();

            } else if (PATH_HELP_CENTER.equals(path)) {
                //跳转到帮助中心
                ARouter.getInstance().build(RouterConstants.ACTIVITY_HELP_CENTER).navigation();

            } else if (PATH_PRODUCT_FITTING.equals(path)) {
                //Parts点击时
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                String deviceId = content_url.getQueryParameter(PARAMETER_DEVICD_ID);
                String productId = content_url.getQueryParameter(PARAMETER_PRODUCT_ID);

                LogUtils.i(" productId = " + deviceId);
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS)
                        .withString(PARAMETER_PRODUCT_ID,productId)
                        .withString(PARAMETER_DEVICD_ID, deviceId).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();

            } else if (PATH_MESSAGE_DETAIL.equals(path)) {

                //MessageDetail
                try {
                    Bundle bundle = getCurrentActivity().getIntent().getExtras();
                    String productId = content_url.getQueryParameter(PARAMETER_PRODUCT_ID);
                    String deviceId = content_url.getQueryParameter(PARAMETER_DEVICD_ID);
                    String createTime = content_url.getQueryParameter("createTime");
                    String uuid = content_url.getQueryParameter(UUID);
                    String messageType = content_url.getQueryParameter("messageType");
                    String systemMessageId = content_url.getQueryParameter("systemMessageId");
                    LogUtils.i(" productId = " + productId);

                    MessageListBean.EntryDTO.ListDTO messageData = new MessageListBean.EntryDTO.ListDTO();
                    messageData.setDeviceId(deviceId);
                    messageData.setCreateTime(Long.parseLong(createTime));
                    messageData.setMessageType(Integer.parseInt(messageType));
                    messageData.setProductId(productId);
                    messageData.setUuid(uuid);
                    messageData.setSystemMessageId(systemMessageId);
                    MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = new MessageListBean.EntryDTO.ListDTO.PayloadDataDTO();
                    payloadDataDTO.setUuid(uuid);
                    payloadDataDTO.setProductId(productId);
                    payloadDataDTO.setMessageType(messageData.getMessageType() + "");
                    payloadDataDTO.setCreateTime(messageData.getCreateTime() + "");
                    payloadDataDTO.setDeviceId(deviceId);
                    messageData.setPayloadData(payloadDataDTO);


                    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                            .withSerializable(KEY_PREV_DATA, messageData).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();

                } catch (Exception e) {
                    LogUtils.d(e.toString());
                }


            } else if (PATH_RESET_WIFI_INFO.equals(path)) {
//TODU 72002
                try {
                    BluetoothConection.getInstance().disConnectDevice();
                } catch (Exception e) {
                }
//---
                ArrayList<DeviceInfo> list = new ArrayList();
                Bundle bundle = getCurrentActivity().getIntent().getExtras();
                if (bundle != null) {
                    DeviceInfo deviceInfo = (DeviceInfo) bundle.get(PARAMETER_DEVICD);
                    boolean offline = Boolean.parseBoolean(content_url.getQueryParameter(WIFI_OFFLINE));
                    deviceInfo = SoftRoomDatabase.getDatabase(getCurrentActivity()).deviceDao().getDeviceById(deviceInfo.getDeviceId());
                    list.add(deviceInfo);
                    if (deviceInfo.getIsOnline() == 0 || offline) {
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                                .withSerializable(PARAMETER_DEVICD, list)
                                .withInt(KEY_PREV_FRAGMENT, MOBLIE_WIFI_CONFIG)
                                .withInt(KEY_FROM, MOBLIE_WIFI_CONFIG)
                                .withInt(KEY_TO, com.chervon.libOTA.R.id.deviceGuidePageOneFragment)
                                .withInt(IS_RESET_WIFI_CONNECTION, 1)
                                .navigation();

                    } else {
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                                .withSerializable(PARAMETER_DEVICD, list)
                                .withInt(KEY_FROM, com.chervon.libOTA.R.id.activity_device_upgrade)
                                .withInt(KEY_TO, com.chervon.libOTA.R.id.deviceGuidePageOneFragment)
                                .withInt(IS_RESET_WIFI_CONNECTION, 1)
                                .navigation();
                    }
                }
            }
            //适配R wangheng 2023/9/20
            else if (PATH_DEALER_LOCATION.equals(path)) {

                ARouter.getInstance().build(RouterConstants.ACTIVITY_DEALER_LOCATION).navigation();


            } else if (PATH_WIFI_MORE_SOLUTIONS.equals(path)) {

                String productId = content_url.getQueryParameter(PARAMETER_PRODUCT_ID);
                String deviceId = content_url.getQueryParameter(PARAMETER_DEVICD_ID);
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                        .withString(PARAMETER_PRODUCT_ID, productId)
                        .withString(PARAMETER_DEVICD_ID, deviceId)
                        .withInt(KEY_FROM, MOBLIE_WIFI_MORE_SOLUTIONS)
                        .withInt(KEY_TO, com.chervon.libOTA.R.id.wifiMoreSolutionsFragment)
                        .withInt(IS_RESET_WIFI_CONNECTION, 1)
                        .navigation();

            } else if (ACTIVITY_FEEDBACK_QUESTION.equals(path)) {
                String productId = content_url.getQueryParameter(RouterConstants.KEY_PRODUCTID);
                String deviceId = content_url.getQueryParameter(RouterConstants.KEY_DEVICEID);
                String nickName = content_url.getQueryParameter(RouterConstants.KEY_NICKNAME);
                String commodityModel = content_url.getQueryParameter(RouterConstants.KEY_COMMODITYMODEL);
                String sn = content_url.getQueryParameter(RouterConstants.KEY_SN);
                String sourceType = "RN";
                ARouter.getInstance().build(ACTIVITY_FEEDBACK_QUESTION).withSerializable(KEY_FROM, sourceType).withSerializable(RouterConstants.KEY_PRODUCTID, productId).withSerializable(RouterConstants.KEY_DEVICEID, deviceId).withSerializable(RouterConstants.KEY_NICKNAME, nickName).withSerializable(RouterConstants.KEY_COMMODITYMODEL, commodityModel).withSerializable(RouterConstants.KEY_SN, sn).navigation();

            } else if(PATH_SHARE_DEVICE_DETAIL.equals(path)) {
                String deviceId = content_url.getQueryParameter(KEY_DEVICEID);
                String deviceName = content_url.getQueryParameter(KEY_DEVICENAME);
                String deviceIcon = content_url.getQueryParameter(KEY_DEVICEICON);
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_SHARE_DEVICE_DETAIL)
                        .withString(KEY_DEVICEID,deviceId)
                        .withString(KEY_DEVICENAME,deviceName)
                        .withString(KEY_DEVICEICON,deviceIcon)
                        .navigation();
            }else {
                Intent intent = new Intent();
                intent.setAction("android.intent.action.VIEW");
                intent.setData(content_url);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                ActivityUtils.startActivity(intent);
            }

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void showEditDialog(String title, String editString, Callback onConfirmed, Callback onCanceled) {
        try {
            Activity context = getCurrentActivity();
            if (context != null) {

                context.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        DialogUtil.showEditDialog(context, title, editString, new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                onConfirmed.invoke(view.getTag());
                            }
                        }, new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                onCanceled.invoke();
                            }
                        });
                    }
                });


            }
        } catch (Throwable e) {

        }
    }


    @ReactMethod
    public void showPromptDialog(String confirmText,
                                 String cancelText,
                                 String title,
                                 String message,
                                 String defaultValue,
                                 Callback onConfirmed, Callback onCanceled) {
        try {


            if (getCurrentActivity() == null) {
                return;
            }

            getCurrentActivity().runOnUiThread(new Runnable() {
                                                   @Override
                                                   public void run() {
                                                       if (getCurrentActivity() != null && !getCurrentActivity().isFinishing()) {


                                                           DialogUtil.showPromptDialog(reactContext, confirmText,
                                                                   cancelText,
                                                                   title,
                                                                   message,
                                                                   defaultValue, new View.OnClickListener() {
                                                                       @Override
                                                                       public void onClick(View view) {
                                                                           onConfirmed.invoke();
                                                                       }
                                                                   }, new View.OnClickListener() {
                                                                       @Override
                                                                       public void onClick(View view) {
                                                                           onCanceled.invoke();
                                                                       }
                                                                   });
                                                       }
                                                   }
                                               }
            );


        } catch (Throwable e) {

        }

    }

    @ReactMethod
    public void simpleDialog(String title, String message, String cancelText, String confirmText, final Callback onConfirmed, Callback onCanceled) {
        LogUtils.d("simpleDialog" + title + "_" + message + "_" + confirmText);
        try {
            if (getCurrentActivity() == null) {
                return;
            }

            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (getCurrentActivity() != null && !getCurrentActivity().isFinishing()) {

                        //--适配强制升级
                        DialogUtil.simpleConfirmDialog(getCurrentActivity(),
                                title,
                                message, cancelText,confirmText,view -> onConfirmed.invoke(), view -> onCanceled.invoke());

                    }
                }
            });
        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void simpleForcedConfirmDialog(String title, String message, final Callback onConfirmed, Callback onCanceled) {
        try {
            if (getCurrentActivity() == null) {
                return;
            }

            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (getCurrentActivity() != null && !getCurrentActivity().isFinishing()) {
                        DialogUtil.simpleConfirmDialogRN(getCurrentActivity(),
                                title,
                                message,
                                view -> onConfirmed.invoke(),
                                view -> onCanceled.invoke());
                    }
                }
            });
        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void simpleConfirmDialog(String title, String message, final Callback onConfirmed, Callback onCanceled) {
        LogUtils.d("simpleConfirmDialog");
        try {
            if (getCurrentActivity() == null) {
                return;
            }

            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (getCurrentActivity() != null && !getCurrentActivity().isFinishing()) {
                        View.OnClickListener confirmClickListener = new ConfirmClickListener(onConfirmed);
                        View.OnClickListener cancelClickListener = new CancelClickListener(onCanceled);
                        if (TextUtils.isEmpty(title)) {
                            //无标题弹窗--适配强制升级
                            DialogUtil.simpleConfirmDialog(getCurrentActivity(),
                                    title,
                                    message, confirmClickListener, cancelClickListener);
                        } else {
                            //有弹窗--适配删除设备 ' '
                            if (TextUtils.isEmpty(title.trim())) {
                                //mark------适配RN面板 空格字段' '
                                DialogUtil.simpleConfirmDialogRN(getCurrentActivity(),
                                        title,
                                        message,
                                        confirmClickListener, cancelClickListener);
                            } else {
                                DialogUtil.simpleConfirmDialog3(getCurrentActivity(),
                                        title,
                                        message,
                                        LanguageStrings.app_devicelist_deletedevicealertok_button_text(),
                                        LanguageStrings.app_setting_clearcachecancle_button_text(),
                                        confirmClickListener,
                                        cancelClickListener, "");
                            }
                        }
                    }
                }
            });
        } catch (Exception e) {
            LogUtils.d(TAG, "simpleConfirmDialog error : " + e.getMessage().toString());
        }
    }

    private class ConfirmClickListener implements View.OnClickListener {

        private Callback callback;

        public ConfirmClickListener(Callback callback) {
            this.callback = callback;
        }

        private boolean hasConfirmClicked = false;

        @Override
        public void onClick(View v) {
            if (callback != null && !hasConfirmClicked) {
                hasConfirmClicked = true;
                callback.invoke();
            }
        }
    }

    private class CancelClickListener implements View.OnClickListener {

        private Callback callback;

        public CancelClickListener(Callback callback) {
            this.callback = callback;
        }

        private boolean hasCancelClicked = false;

        @Override
        public void onClick(View v) {
            if (callback != null && !hasCancelClicked) {
                hasCancelClicked = true;
                callback.invoke();
            }
        }
    }


    @ReactMethod
    public void closeSimpleConfirmDialog() {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            LogUtils.e("QFRCT-->>closeSimpleConfirmDialog");


            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (getCurrentActivity() != null && !getCurrentActivity().isFinishing()) {
                        DialogUtil.closeSimpleDialog();
                    }
                }
            });

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void simpleTipDialog(String message, Callback onConfirmed) {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                                                   @Override
                                                   public void run() {
                                                       DialogUtil.simpleTipDialog(getCurrentActivity(),
                                                               message, new View.OnClickListener() {
                                                                   @Override
                                                                   public void onClick(View view) {
                                                                       //适配R wangheng 2023/9/20
                                                                       try {
                                                                           onConfirmed.invoke();
                                                                       } catch (Throwable e) {

                                                                       }

                                                                   }
                                                               });
                                                   }
                                               }
            );

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void toast(String message, int duration) {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    ToastUtils.showShort(message);
                }
            });

        } catch (Throwable e) {

        }
    }

    @ReactMethod
    public void sendCmd(String hexstring, String mac) {
        //LogUtils.iTag("TAG", "RN sendCmd", "mac: " + mac, "hexstring: " + hexstring);
        //获取当前BLE连接

    }


    @ReactMethod
    public void track(String str) {
        try {
            if (TextUtils.isEmpty(str)) {
                return;
            }
            String SOURCE_NAME = "RNMobile";
            RnInfo rnInfo = GsonUtils.fromJson(str, RnInfo.class);
            EventBean eventBean = new EventBean();
            eventBean.setTimestamp(rnInfo.getTimestamp());
            eventBean.setEventid(rnInfo.getEventid());
            eventBean.setPagesource(rnInfo.getPagesource());
            eventBean.setPlatform(rnInfo.getPlatform());
            eventBean.setEleid(rnInfo.getEleid());
            eventBean.setPageid(rnInfo.getPageid());
            eventBean.setModuleid(rnInfo.getModuleid());
            eventBean.setModid(rnInfo.getModid());
            eventBean.setEventtype(rnInfo.getEventtype());
            eventBean.setEventid(rnInfo.getEventid());
            EventBean.Expand expand = new EventBean.Expand();
            expand.map = new HashMap<>();
            expand.map.put(SOURCE_NAME, SOURCE_NAME);

            if (!rnInfo.getExpand().isEmpty()){
                expand.map.putAll(rnInfo.getExpand());
            }

            sendTrace(eventBean, expand, getCurrentActivity(), rnInfo.getEventtype());
        } catch (Throwable e) {
            LogUtils.e(TAG, "track is error:" + e.getMessage());
        }
    }


    @ReactMethod
    public void updateLocation(String lat, String lng) {

    }

    @ReactMethod
    public void handlePushNotification() {

    }


    @Override
    public void onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy();
    }

    private RNTimeDatePicker rnTimeDatePicker;

    @RequiresApi(api = Build.VERSION_CODES.O)
    @ReactMethod
    public void openDateTimePicker(ReadableMap params, Callback onConfirm, Callback onClose) {
        try {
            LogUtils.e(TAG, "openDateTimePicker is openDateTimePicker");

            Bundle argumentsBundle = createFragmentArguments(params);
            LogUtils.i(TAG, params);
            String outRNDateFormat = "yyyy-MM-dd HH:mm";

            if (getCurrentActivity() == null) {
                return;
            }

            if (!TextUtils.isEmpty(argumentsBundle.getString(RNConstants.ARG_MODE))) {
                LogUtils.e(TAG, "openDateTimePicker is mode:" + argumentsBundle.getString(RNConstants.ARG_MODE));
            }

            if (!TextUtils.isEmpty(argumentsBundle.getString(RNConstants.ARG_VALUE))) {
                //处理value
                LogUtils.e(TAG, "openDateTimePicker is value:" + argumentsBundle.getString(RNConstants.ARG_VALUE));
            }
            LogUtils.e(TAG, "openDateTimePicker is disabled:" + argumentsBundle.getBoolean(RNConstants.ARG_DISABLED));

            if (!TextUtils.isEmpty(argumentsBundle.getString(RNConstants.ARG_MINIMUMDATE))) {
                LogUtils.e(TAG, "openDateTimePicker is minimumDate:" + argumentsBundle.getString(RNConstants.ARG_MINIMUMDATE));
            }
            if (!TextUtils.isEmpty(argumentsBundle.getString(RNConstants.ARG_MAXIMUMDATE))) {
                LogUtils.e(TAG, "openDateTimePicker is maximumDate:" + argumentsBundle.getString(RNConstants.ARG_MAXIMUMDATE));
            }

            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    LogUtils.e(TAG,"");
                    rnTimeDatePicker = new RNTimeDatePicker(getCurrentActivity(), new RNTimeDatePicker.ResultHandler() {
                        @RequiresApi(api = Build.VERSION_CODES.O)
                        @Override
                        public void handle(String time, long timeStamp) {
                            String isEmptyo8601DateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(outRNDateFormat)).format(DateTimeFormatter.ISO_DATE_TIME);
                            String iso8601DateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(outRNDateFormat))
                                    .atZone(ZoneId.systemDefault())  // 先指定为本地时区
                                    .withZoneSameInstant(ZoneOffset.UTC)  // 转换到 UTC 时区，会自动调整时间
                                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"));
                            LogUtils.e(TAG, "openDateTimePicker is iso8601DateTime:" + iso8601DateTime);

                            if (null != onConfirm) {
                                onConfirm.invoke(iso8601DateTime);
                            }

                        }

                        @Override
                        public void handleDismiss() {
                            if (null != onClose) {
                                onClose.invoke();
                            }
                        }
                    }, argumentsBundle);

                    switch (argumentsBundle.getString(RNConstants.ARG_MODE)) {
                        case RNConstants.ARG_MODEL_DATETIME:
                            rnTimeDatePicker.showSpecificTime(true);
                            break;
                        case RNConstants.ARG_MODEL_DATE:
                            rnTimeDatePicker.showSpecificTime(false);
                            break;
                        case RNConstants.ARG_MODEL_MONTH:
                            rnTimeDatePicker.showSpecificTime(false);
                            break;
                        case RNConstants.ARG_MODEL_TIME:
                            //时:分 格式  区分12小时制和24小时制
                            rnTimeDatePicker.showSpecificTime(true);
                            break;
                        default:
                            break;
                    }
                    rnTimeDatePicker.setIsLoop(false);
                    rnTimeDatePicker.show(argumentsBundle.getString(RNConstants.ARG_VALUE));

                }
            });

        } catch (Throwable e) {
            LogUtils.e(TAG, "openDateTimePicker is error:" + e.getMessage());
        }
    }

    @ReactMethod
    public void closeDateTimePicker() {
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (null != rnTimeDatePicker) {
                        rnTimeDatePicker.dismissDialog();
                    }
                }
            });

        } catch (Throwable e) {
            LogUtils.e(TAG, "simpleTipDialog1 is error:" + e.getMessage());
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    private Bundle createFragmentArguments(ReadableMap options) {

        Bundle args = new Bundle();
        String defaultStartTime = String.valueOf(ZonedDateTime.of(2000, 1, 1, 0, 0, 0, 0, ZoneId.of("Z")));
        String defaultEndTime = String.valueOf(ZonedDateTime.of(2099, 1, 1, 0, 0, 0, 0, ZoneId.of("Z")));
        ;
        int defaultMinuteInterval = 1;
        String defaultTitle = "Date picker";
        try {
            if (options.hasKey(RNConstants.ARG_TITLE) && !options.isNull(RNConstants.ARG_TITLE)) {
                args.putString(RNConstants.ARG_TITLE, TextUtils.isEmpty(options.getString(RNConstants.ARG_TITLE)) ? defaultTitle : options.getString(RNConstants.ARG_TITLE));
            } else {
                args.putString(RNConstants.ARG_TITLE, defaultTitle);
            }

            if (options.hasKey(RNConstants.ARG_MODE) && !options.isNull(RNConstants.ARG_MODE)) {
                args.putString(RNConstants.ARG_MODE, TextUtils.isEmpty(options.getString(RNConstants.ARG_MODE)) ? RNConstants.ARG_MODEL_DATE : options.getString(RNConstants.ARG_MODE));
            }

            if (options.hasKey(RNConstants.ARG_VALUE) && !options.isNull(RNConstants.ARG_VALUE)) {

//                String value = LocalDateTime.parse(options.getString(RNConstants.ARG_VALUE), DateTimeFormatter.ISO_DATE_TIME).format(customFormatter);
                String value = isoDateTimeToLocalTime(options.getString(RNConstants.ARG_VALUE));
                args.putString(RNConstants.ARG_VALUE, value);
            }

            if (options.hasKey(RNConstants.ARG_DISABLED) && !options.isNull(RNConstants.ARG_DISABLED)) {
                args.putBoolean(RNConstants.ARG_DISABLED, options.getBoolean(RNConstants.ARG_DISABLED));
            } else {
                args.putBoolean(RNConstants.ARG_DISABLED, false);

            }

            if (options.hasKey(RNConstants.ARG_MINIMUMDATE) && !options.isNull(RNConstants.ARG_MINIMUMDATE)) {
//                defaultStartTime = LocalDateTime.parse(options.getString(RNConstants.ARG_MINIMUMDATE), DateTimeFormatter.ISO_DATE_TIME).format(customFormatter);
                defaultStartTime = isoDateTimeToLocalTime(options.getString(RNConstants.ARG_MINIMUMDATE));
                args.putString(RNConstants.ARG_MINIMUMDATE, defaultStartTime);
            } else {
                args.putString(RNConstants.ARG_MINIMUMDATE, isoDateTimeToLocalTime(defaultStartTime));
            }

            if (options.hasKey(RNConstants.ARG_MAXIMUMDATE) && !options.isNull(RNConstants.ARG_MAXIMUMDATE)) {
//                defaultEndTime = LocalDateTime.parse(options.getString(RNConstants.ARG_MAXIMUMDATE), DateTimeFormatter.ISO_DATE_TIME).format(customFormatter);
                defaultEndTime = isoDateTimeToLocalTime(options.getString(RNConstants.ARG_MAXIMUMDATE));
                args.putString(RNConstants.ARG_MAXIMUMDATE, defaultEndTime);
            } else {
                args.putString(RNConstants.ARG_MAXIMUMDATE, isoDateTimeToLocalTime(defaultEndTime));
            }

            if (options.hasKey(RNConstants.ARG_IS12HOURFORMAT) && !options.isNull(RNConstants.ARG_IS12HOURFORMAT)) {
                args.putBoolean(RNConstants.ARG_IS12HOURFORMAT, options.getBoolean(RNConstants.ARG_IS12HOURFORMAT));
            } else {
                args.putBoolean(RNConstants.ARG_IS12HOURFORMAT, false);
            }
            if (options.hasKey(RNConstants.ARG_MINUTEINTERVAL) && !options.isNull(RNConstants.ARG_MINUTEINTERVAL)) {
                args.putInt(RNConstants.ARG_MINUTEINTERVAL, options.getInt(RNConstants.ARG_MINUTEINTERVAL));
            } else {
                args.putInt(RNConstants.ARG_MINUTEINTERVAL, defaultMinuteInterval);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "createFragmentArguments is error:" + e.getMessage());
        }


        return args;
    }


    @RequiresApi(api = Build.VERSION_CODES.O)
    private String isoDateTimeToLocalTime(String isoDateTimeString) {
        DateTimeFormatter customFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        if (TextUtils.isEmpty(isoDateTimeString)) {
            return "";
        }
        try {
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(isoDateTimeString, DateTimeFormatter.ISO_OFFSET_DATE_TIME);

            ZoneId currentZoneId = ZoneId.systemDefault();

            ZonedDateTime currentZonedDateTime = offsetDateTime.atZoneSameInstant(currentZoneId);
            return currentZonedDateTime.format(customFormatter);
        } catch (Exception e) {
            LogUtils.e(TAG, "isoDateTimeToLocalTime is error-->" + e.getMessage());
            return "";
        }


    }

    @ReactMethod
    public void openMapWithCoordinates(String startLocationStr, String destinationLocationStr, Callback callback) {
        try {
            JSONObject jsonObject = new JSONObject(destinationLocationStr);
            double destinationLatitude = (Double) jsonObject.get("latitude");
            double destinationLongitude =  (Double) jsonObject.get("longitude");
            String destinationAddress = destinationLatitude + "," + destinationLongitude;
            if (new BigDecimal(destinationLatitude).compareTo(new BigDecimal(0.0)) == 0
                    || new BigDecimal(destinationLongitude).compareTo(new BigDecimal(0.0)) == 0) {
                if (callback != null) {
                    callback.invoke();
                }
                return;
            }
            if (isAvailable(BaseApplication.getInstance(), PACKAGE_NAME_GOOGLE_MAP)) {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("google.navigation:q=" + destinationAddress));
                intent.setPackage("com.google.android.apps.maps");
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                BaseApplication.getInstance().startActivity(intent);
            } else {
                LocationServices.getFusedLocationProviderClient(BaseApplication.getInstance()).getLastLocation().addOnCompleteListener(new OnCompleteListener<Location>() {
                    @Override
                    public void onComplete(@NonNull Task<Location> task) {
                    }
                }).addOnSuccessListener(new OnSuccessListener<Location>() {
                    @Override
                    public void onSuccess(Location location) {
                        if (null != location) {
                            double startLatitude = location.getLatitude();
                            double startLongitude = location.getLongitude();

                            try {
                                launchGoogleMapsForRoute(startLatitude, startLongitude, destinationAddress);

                            } catch (Exception e) {
                                if (callback != null) {
                                    callback.invoke(e.getMessage());
                                }
                                LogUtils.d("openMapWithCoordinates", "run error:" + e.getMessage());
                            }
                        } else {
                            if (callback != null) {
                                callback.invoke();
                            }
                        }
                    }
                }).addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(@NonNull Exception e) {
                        if (callback != null) {
                            callback.invoke(e.getMessage());
                        }
                    }
                });
            }
        } catch (Exception e) {
            LogUtils.d("openMapWithCoordinates", "run error:" + e.getMessage());
            if (callback != null) {
                callback.invoke(e.getMessage());
            }
        }
    }

    public static boolean isAvailable(Context context, String packageName) {
        // 获取packagemanager
        final PackageManager packageManager = context.getPackageManager();
        // 获取所有已安装程序的包信息
        List<PackageInfo> packageInfos = packageManager.getInstalledPackages(0);
        // 用于存储所有已安装程序的包名
        List<String> packageNames = new ArrayList<String>();
        // 从pinfo中将包名字逐一取出，压入pName list中
        if (packageInfos != null) {
            for (int i = 0; i < packageInfos.size(); i++) {
                String packName = packageInfos.get(i).packageName;
                packageNames.add(packName);
            }
        }
        // 判断packageNames中是否有目标程序的包名，有TRUE，没有FALSE
        try {
            return packageNames.contains(packageName) && context.getPackageManager().getApplicationInfo(packageName, 0).enabled;
        } catch (PackageManager.NameNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private void launchGoogleMapsForRoute(double startLatitude, double startLongitude, String destinationAddress)  {
        String uri = "http://maps.google.com/maps?" +
                "saddr=" + startLatitude + "," + startLongitude +
                "&daddr=" +destinationAddress /*destinationLat + "," + destinationLng*/;
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(uri));
        startActivity(intent);
    }

    @ReactMethod
    public void showDatePicker(String minDateStr,String maxDateStr,String defaultTime,String title,Callback  onConfirmed,Callback onCanceled) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
        //获取当前时间
        try {
            if (getCurrentActivity() == null) {
                return;
            }
            getCurrentActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if(customDatePicker==null){
                        datePicker();
                    }
                    String  now = sdf.format(new Date());
                    if(!TextUtils.isEmpty(defaultTime)){
                        now= defaultTime.replace("/","-");
                        if(!TextUtils.isEmpty(minDateStr)){
                            startTime= defaultTime.replace("/","-");
                        }
                        if(!TextUtils.isEmpty(maxDateStr)){
                            endYear= defaultTime.replace("/","-");
                        }
                    }

                    customDatePicker.show(now);
                    customDatePicker.setTitle(title);
                    customDatePicker.setmOnDateClickListener(new RnCustomDatePicker.OnDateClickListener() {
                        @Override
                        public void onConfirmClick(String date) {
                            onConfirmed.invoke(date);
                        }

                        @Override
                        public void onCancelClick(String date) {
                            onCanceled.invoke();
                        }
                    });
                }
            });

        } catch (Throwable e) {
        }
    }

    private void datePicker() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
        //获取当前时间

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());   //设置当前时间
        String  endTime = sdf.format(cal.getTime());

        String now = sdf.format(new Date());
        if(getCurrentActivity()!=null){
            if(TextUtils.isEmpty(startTime)){
                startTime="2000-01-01 00:00";
            }
            if(TextUtils.isEmpty(endYear)){
                endYear=endTime;
            }
            //tvElectricalTime.setText(now.split(" ")[0]);
            customDatePicker = new RnCustomDatePicker(getCurrentActivity(),
                    (time, timeStamp) -> {

                    },startTime ,endYear , now); // 初始化日期格式请用：yyyy-MM-dd HH:mm，否则不能正常运行
            customDatePicker.showSpecificTime(false); // 不显示时和分
            customDatePicker.setIsLoop(false); // 不允许循环滚动
        }
    }


    private RnCustomDatePicker customDatePicker;
    private String startTime;
    private String endYear;
}
