package com.chervon.moudleOobe.ui;

import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.ToastUtils;

import static com.blankj.utilcode.util.ColorUtils.getColor;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.moudleOobe.data.repository.LoginRepo.NETWORK_ECEPTION;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.LoginFragment.LOGIN_SUCCESS;
import static com.chervon.moudleOobe.ui.RegisterFragment.ERROR_EMAIL;
import static com.chervon.moudleOobe.ui.RegisterFragment.REGISTER_DATA;
import static com.chervon.moudleOobe.ui.RegisterFragment.REGISTER_SUCCESS;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.ui.widget.VerifyEditText;
import com.chervon.libBase.utils.ClipboardChervonUtil;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.BuildConfig;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentCheckEmailBinding;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.state.LoginUiState;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.ForgetPasswordViewModel;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: ForgetPasswordFragment
 * @Description: Login module's forgotten password acquisition verification code interface
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/18
 * @UpdateRemark: 适配欧洲注册流程
 * @Version: 1.1
 */
public class CheckVerifyCodeFragment extends BaseEnhanceFragment<ForgetPasswordViewModel, MoudleOobeFragmentCheckEmailBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "CheckVerifyCodeFragment";
  public static final int GET_VERIFICATION_CODE_SUCCESS = 11;
  public static final int RESPONSE_FAIL = 12;
  public static final int VERIFICATION_ERROR = 1000022010;
  public static final int VALID_FREQUENT = 1000022016;
  public static final int VERIFICATION_SUCCESS = 10;
  private CountDownTimer mDownTimer;

  private LoginUiState registerUiSate;
  private int finalFragmentId;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initDownTimer();
    Bundle bundle = getArguments();
    ForgetPasswordUiSate uiSate;
    if (null != bundle) {
      uiSate = (ForgetPasswordUiSate) bundle.getSerializable(KEY_PREV_DATA);
    } else {
      uiSate = new ForgetPasswordUiSate();
    }
    uiSate = initBindingData(bundle, uiSate);
     finalFragmentId = uiSate.getFrom();
    observeLiveData(bundle, finalFragmentId);
    setListenner();
    showEmailHasSended(uiSate.getAccount());
    mViewDataBinding.tvGetVerifyCode.setClickable(false);
    mViewDataBinding.tvGetVerifyCode.setEnabled(false);

    ProgressHelper.showProgressView(getActivity(), 0, true);

  }

  private void setListenner() {
    mViewDataBinding.etVerify.addInputCompleteListener(new VerifyEditText.InputCompleteListener() {
      @Override
      public void complete(String content) {

        mViewDataBinding.getUser().setVerificationCode(content);
        mViewDataBinding.btnNext.setClickable(true);
        mViewDataBinding.btnNext.setEnabled(true);


      }

      @Override
      public void onPauseFromView(String content) {

        mViewDataBinding.etVerify.setDefaultContent(content);

      }

      @Override
      public void process() {
        mViewDataBinding.btnNext.setEnabled(false);
        mViewDataBinding.btnNext.setClickable(false);
      }
    });
  }

  @NonNull
  private ForgetPasswordUiSate initBindingData(Bundle bundle, ForgetPasswordUiSate uiSate) {
    if (uiSate == null) {
      uiSate = new ForgetPasswordUiSate();
    }
    int fragmentId = 0;
    if (bundle != null) {
      fragmentId = bundle.getInt(KEY_PREV_FRAGMENT);
      uiSate.setFrom(fragmentId);
    }
    if (fragmentId == R.id.registerFragment || fragmentId == R.id.registerEuThreeFragment) {
      mViewDataBinding.tvTitle.setText(LanguageStrings.getSignup());
      RegisterUiState registerUiSate = (RegisterUiState) bundle.getSerializable(REGISTER_DATA);
      uiSate.setAccount(registerUiSate.getEmail());
      mViewDataBinding.btnNext.setText(LanguageStrings.getRegistverifycodedone());
      showRegisterUi();
      initRegisterPageTrace();
    } else if (fragmentId == R.id.loginFragment) {

      mViewDataBinding.tvTitle.setText(LanguageStrings.app_accountverification_title_textview_text());
      registerUiSate = (LoginUiState) bundle.getSerializable(REGISTER_DATA);
      LoginUiState loginUiState = new LoginUiState();
      loginUiState.setAccount(registerUiSate.getAccount());
      loginUiState.setPassword(registerUiSate.getPassword());

      mViewModel.mLoginLiveData.setValue(loginUiState);
      uiSate.setAccount(registerUiSate.getAccount());
      mViewDataBinding.btnNext.setText(LanguageStrings.getRegistverifycodedone());
      showRegisterUiFromLogin();
      initLoginChanelPageTrace();
    } else {
      initForgetPageTrace();
      mViewDataBinding.btnNext.setText(LanguageStrings.getForgetpasswordNext());
      mViewDataBinding.tvTitle.setText(LanguageStrings.getForgetpassword());
    }
    mViewDataBinding.setUser(uiSate);
    mViewDataBinding.setPresenter(this);
    return uiSate;
  }

  private void initLoginChanelPageTrace() {
    stayEleId = "3";
    pageId = "168";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  private void initRegisterPageTrace() {
    stayEleId = "4";
    pageId = "8";
    mouduleId = "4";
    pageResouce = "1_7_8";
    nextButtoneleid = "2";
  }

  private void initForgetPageTrace() {
    stayEleId = "3";
    pageId = "4";
    mouduleId = "3";
    pageResouce = "1_2_3_4";
    nextButtoneleid = "2";
  }

  private void observeLiveData(Bundle bundle, int finalFragmentId) {

    mViewModel.mLoginLiveData.observe(this, new Observer<LoginUiState>() {
      @Override
      public void onChanged(LoginUiState loginUiState) {
        if (loginUiState.getLoginState() == LOGIN_SUCCESS) {
          sendBaseTraceClick("4");
          ((BaseApplication) (CheckVerifyCodeFragment.this.getActivity().getApplication())).setLoginSuccess(true);
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

        } else if (loginUiState.getLoginState() != INIT_STATE) {
          sendBaseTraceClick("5");
          if (!TextUtils.isEmpty(loginUiState.getErrorMsg())) {
            ToastUtils.showLong(loginUiState.getErrorMsg());
          }
        }
        loginUiState.setLoginState(INIT_STATE);
      }
    });


    mViewModel.mForgetPasswordLiveData.observe(this, new Observer<ForgetPasswordUiSate>() {
      @Override
      public void onChanged(ForgetPasswordUiSate forgetPasswordUiSate) {
        ProgressHelper.hideProgressView(CheckVerifyCodeFragment.this.getActivity());
        if (forgetPasswordUiSate.getState() == VERIFICATION_SUCCESS) {
          if (finalFragmentId == R.id.registerFragment || finalFragmentId == R.id.registerEuThreeFragment) {
            mViewModel.register((RegisterUiState) bundle.getSerializable(REGISTER_DATA), mViewDataBinding.getUser().getVerificationCode());
          }else if (finalFragmentId == R.id.loginFragment) {
            mViewModel.login();
          } else {
            ((LoginAndRegisterActivity) CheckVerifyCodeFragment.this.getActivity()).gotoPassWordConfirm(mViewDataBinding.getUser());
          }
        } else if (forgetPasswordUiSate.getState() == GET_VERIFICATION_CODE_SUCCESS) {

        } else if (forgetPasswordUiSate.getState() == ERROR_EMAIL) {
          ToastUtils.showLong(LanguageStrings.app_base_email_format_text());
        } else if (forgetPasswordUiSate.getState() == VERIFICATION_ERROR) {
          // ToastUtils.showLong(R.layout.base_toast_success);
          //   DialogUtil.showWarningDialog(getContext(), getString(R.string.verify_error));
          //  mViewDataBinding.etVerify.setInvalidColor();
          DialogUtil.showWarningDialog(getContext(), forgetPasswordUiSate.getRespondMessage());
          if (finalFragmentId == R.id.registerFragment || finalFragmentId == R.id.registerEuThreeFragment) {
            sendBaseTraceClick("6");
          } else if (finalFragmentId == R.id.loginFragment) {

            sendBaseTraceClick("5");
          }
          //ToastUtils.showLong(getString(R.string.verify_error));
        } else if (forgetPasswordUiSate.getState() == NETWORK_ECEPTION) {
          ToastUtils.showLong(LanguageStrings.app_base_servererror_textview_text());
        } else if (forgetPasswordUiSate.getState() != INIT_STATE) {
          if (!TextUtils.isEmpty(forgetPasswordUiSate.getRespondMessage())) {
            getActivity().runOnUiThread(new Runnable() {
              @Override
              public void run() {
                if (forgetPasswordUiSate.getState() == VALID_FREQUENT) {
                  if (mDownTimer != null) {
                    mDownTimer.cancel();
                    mDownTimer.onFinish();
                    mDownTimer = null;
                  }
                } else {

                }
                ToastUtils.showLong(forgetPasswordUiSate.getRespondMessage());


              }
            });
          }

        }
        forgetPasswordUiSate.setState(INIT_STATE);
      }
    });

    mViewModel.mRegisterLiveData.observe(this, new Observer<RegisterUiState>() {
      @Override
      public void onChanged(RegisterUiState registerUiSate) {
        if (registerUiSate.getState() == REGISTER_SUCCESS) {
          sendBaseTraceClick("5");
          goToHomePage();

        } else if (registerUiSate.getState() != INIT_STATE) {
          sendBaseTraceClick("6");
          ToastUtils.showLong(registerUiSate.getRespondMessage());
        }
        registerUiSate.setState(INIT_STATE);
      }
    });
  }


  private void initDownTimer() {

    if (mDownTimer == null) {
      mDownTimer = new CountDownTimer(60000, 1000) {

        @Override
        public void onTick(long millisUntilFinished) {
          if (mViewDataBinding != null && mViewDataBinding.tvDownCount != null) {
            mViewDataBinding.tvDownCount.setText(("(" + millisUntilFinished / 1000) + "s)");
            mViewDataBinding.tvGetVerifyCode.setClickable(false);
            mViewDataBinding.tvGetVerifyCode.setEnabled(false);
            mViewDataBinding.tvGetVerifyCode.setBackground(null);
          }

        }

        @Override
        public void onFinish() {
          if (mViewDataBinding != null) {
            mViewDataBinding.tvGetVerifyCode.setClickable(true);
            mViewDataBinding.tvGetVerifyCode.setEnabled(true);
            //IOT-12888 10/15去除下划线
//            mViewDataBinding.tvGetVerifyCode.setBackgroundResource(R.drawable.base_tv_underline);
            mViewDataBinding.tvDownCount.setText("");
          }

        }

      };

      mViewDataBinding.etVerify.postDelayed(new Runnable() {
        @Override
        public void run() {
          if (mDownTimer != null) {
            mDownTimer.start();
          }

        }
      }, 500);
    }


  }

  private void showEmailHasSended(String email) {
    if (email == null) {
      return;
    }

    String text = LanguageStrings.app_registverifycode_havesendfirst_textview_text() + " "+ email +LanguageStrings.appRegistverifycodeSecondText();

    int firstPartIndex = text.indexOf(email);
    Spannable span = new SpannableString(text);
    span.setSpan(new ForegroundColorSpan(getColor(com.chervon.libBase.R.color.colorsecond)), 0, firstPartIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    span.setSpan(new ForegroundColorSpan(getColor(com.chervon.libBase.R.color.colorTitle)), firstPartIndex, firstPartIndex + email.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    span.setSpan(new ForegroundColorSpan(getColor(com.chervon.libBase.R.color.colorsecond)), firstPartIndex + email.length(), text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    mViewDataBinding.tvEmailSendTip.setText(span);
  }

  @SuppressLint("ResourceType")
  private void goToHomePage() {
    DialogUtil.showSuccessDialog(getContext(), LanguageStrings.app_setting_success_textview_text());
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();


  }

  private void showRegisterUiFromLogin() {
    mViewDataBinding.page1.setVisibility(View.GONE);
    mViewDataBinding.page2.setVisibility(View.GONE);
    mViewDataBinding.page3.setVisibility(View.GONE);
    mViewDataBinding.page4.setVisibility(View.GONE);
    mViewDataBinding.tvPageNum.setVisibility(View.GONE);
    //mViewDataBinding.tvPageNum.setText(getString(R.string.two_seconds));
  }

  private void showRegisterUi() {
    mViewDataBinding.page1.setVisibility(View.VISIBLE);
    mViewDataBinding.page2.setVisibility(View.VISIBLE);
    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA)) {
      mViewDataBinding.page3.setVisibility(View.GONE);
      mViewDataBinding.page4.setVisibility(View.GONE);
      mViewDataBinding.tvPageNum.setVisibility(View.VISIBLE);
      mViewDataBinding.tvPageNum.setText(getString(R.string.two_seconds));
    } else if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
      mViewDataBinding.page3.setBackground(getResources().getDrawable(R.color.colorButtonNormal,null));
      mViewDataBinding.page3.setVisibility(View.VISIBLE);
      mViewDataBinding.page4.setVisibility(View.VISIBLE);
      mViewDataBinding.tvPageNum.setVisibility(View.VISIBLE);
      mViewDataBinding.tvPageNum.setText(getString(R.string.four_quarter));
    }

  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_check_email;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding.btnNext.setEnabled(false);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
    ProgressHelper.hideProgressView(CheckVerifyCodeFragment.this.getActivity());
  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends ForgetPasswordViewModel> getViewModelClass() {
    return ForgetPasswordViewModel.class;
  }

  public void onClickEvent(ForgetPasswordUiSate uiSate) {
    sendButtonClick();
    if (isAdded()) {
      ProgressHelper.showProgressView(this.getActivity(), 0);
    }
    mViewModel.onClickEvent(uiSate);
  }

  public void getVerycode(ForgetPasswordUiSate uiSate) {
    if (isAdded()) {
      ProgressHelper.showProgressView(this.getActivity(), 0);
    }


    if (mDownTimer != null) {
      mViewDataBinding.tvGetVerifyCode.setClickable(false);
      mViewDataBinding.tvGetVerifyCode.setEnabled(false);
      mDownTimer.start();
    }

    mViewModel.getVerycode(uiSate);
    ClipboardChervonUtil.clear();

    //1、从注册页面&当用户倒计时到60秒---sendAgain再次亮起--埋点触发
    String tvDownCount = mViewDataBinding.tvDownCount.getText().toString();
    if (TextUtils.isEmpty(tvDownCount)&& finalFragmentId == R.id.registerFragment){
      sendVerfycodeAgainClick();
    }

  }


  @Override
  public void onStart() {
    super.onStart();
    //延迟1s获取粘贴板内容
    Observable.just(1).delay(1, TimeUnit.SECONDS)
      .observeOn(AndroidSchedulers.mainThread())
      .subscribe(new Consumer<Object>() {
        @SuppressLint("CheckResult")
        @Override
        public void accept(Object o) throws Exception {

          KeyboardUtils.hideSoftInput(getActivity());

          getClipboardText();
        }
      });

  }


  /**
   * copy text from clipboard
   */
  protected void getClipboardText() {

    int vCodeLength = 6;

    String regex = "\\d{6}";

    String clipString = ClipboardChervonUtil.getText().toString();

    if (TextUtils.isEmpty(clipString)) {
      return;
    }

    if (clipString.length() != vCodeLength){
      return;
    }

    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(clipString);
    if (matcher.find()) {

      DialogUtil.simpleConfirmCopyDialog(getActivity(), "", clipString, new View.OnClickListener() {
        @Override
        public void onClick(View v) {
          //设置给输入框
          mViewDataBinding.etVerify.setDefaultContent(clipString);
          ClipboardChervonUtil.clear();
        }
      }, new View.OnClickListener() {
        @Override
        public void onClick(View v) {

        }
      });

    }
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  private void sendButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  private void sendVerfycodeAgainClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "3", "1");
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    ClipboardChervonUtil.clear();
    return false;
  }


  @Override
  public void onDestroyView() {
    super.onDestroyView();
  }




}
