package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.KEY_URL;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.PasswordConfirmFragment.RESPONSE_SUCCESS;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.ConfirmAlertWithTitleDialog;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeActivityRevokingAuthorizationBinding;
import com.chervon.moudleOobe.ui.state.AgreementUiState;
import com.chervon.moudleOobe.ui.viewmodel.RevokingAuthorizationViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: PermissionManageActivity
 * @Description: PermissionManage
 * @Author: wangheng
 * @CreateDate: 2022/8/25 17:25
 * @UpdateUser:
 * @UpdateDate: 2022/8/25 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_REVOKING_AUTHORIZATION)
public class RevokingAuthorizationActivity extends BaseActivity<RevokingAuthorizationViewModel> {
    private MoudleOobeActivityRevokingAuthorizationBinding binding;
    public static final int DEVIVE_WITHDRAW_SUCCESS = 5;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_oobe_activity_revoking_authorization;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (MoudleOobeActivityRevokingAuthorizationBinding) viewDataBinding;


    }

    @Override
    protected void initData(Bundle savedInstanceState) {

      Bundle bundle = getIntent().getExtras();
      if (bundle != null) {
        int fragmentId = getIntent().getExtras().getInt(KEY_PREV_FRAGMENT);
        int from = getIntent().getExtras().getInt(KEY_FROM);
        int pageTo = getIntent().getExtras().getInt(KEY_TO);
        String url = bundle.getString(KEY_URL);
        if (from == R.id.controlPanel) {
          mViewModel. setFromPanel(true);
          initTrace();
          String productId = bundle.getString(KEY_PREV_DATA);
          mViewModel.setProductId(productId);
        }else{
          initSettingRevokingTrace();
        }

      }
        mViewModel.toolbarData.setValue(new ToolbarData(
                LanguageStrings.privacy_agreement_revoke_title(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                      sendBackTrace();
                      finish();
                    }
                }));
      mViewModel.mRevokingClick.observe(this, new Observer<Boolean>() {
        @Override
        public void onChanged(Boolean aBoolean) {
          if(aBoolean){
            sendNextButtonClick();
            if(mViewModel.isFromPanel){
              ConfirmAlertWithTitleDialog. initTrace("3","83","9",pageResouce+"_83","2","1");
            }else{
              ConfirmAlertWithTitleDialog. initTrace("3","101","11",pageResouce+"_101","2","1");

            }
          }
        }
      });
        mViewModel.setFragmentManager(getSupportFragmentManager());
        binding.setPrezenter(this);
        binding.setUserCenterSettingModel(mViewModel);
        mViewModel.mLiveData.observe(this, new Observer<AgreementUiState>() {
            @Override
            public void onChanged(AgreementUiState uiState) {
                if (uiState.getState() == RESPONSE_SUCCESS) {
                    UserInfo.clear();
                } else if (uiState.getState() == DEVIVE_WITHDRAW_SUCCESS) {
                    goToHome();
                } else if (uiState.getState() != RESPONSE_FAIL) {
                    if (!TextUtils.isEmpty(uiState.getMessage())) {
                        ToastUtils.showLong(uiState.getMessage());
                    }
                }
                uiState.setState(INIT_STATE);
            }
        });



    }

    private void goToHome() {
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();


    }

    @Override
    protected Class<? extends RevokingAuthorizationViewModel> getViewModelClass() {
        return RevokingAuthorizationViewModel.class;
    }

    public void cancel() {
      sendCancelClickButtonClick();
//        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LEGAL_INFO)
//                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        finish();
    }




  private void initTrace() {
    stayEleId = "4";
    pageId = "82";
    mouduleId = "9";
    nextButtoneleid = "2";
  }
  private void initSettingRevokingTrace() {
    stayEleId = "4";
    pageId = "100";
    mouduleId = "11";
    nextButtoneleid = "2";
  }
  public void sendCancelClickButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendNextButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
  }



  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }


}
