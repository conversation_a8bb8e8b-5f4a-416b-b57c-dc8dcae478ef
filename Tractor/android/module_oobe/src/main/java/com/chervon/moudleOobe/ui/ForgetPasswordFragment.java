package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_EMAIL;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.moudleOobe.data.repository.LoginRepo.NETWORK_ECEPTION;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.GET_VERIFICATION_CODE_SUCCESS;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.VERIFICATION_SUCCESS;
import static com.chervon.moudleOobe.ui.RegisterFragment.ERROR_EMAIL;
import static com.chervon.moudleOobe.ui.RegisterFragment.REGISTER_DATA;
import static com.chervon.moudleOobe.ui.RegisterFragment.REGISTER_SUCCESS;
import static com.chervon.moudleOobe.ui.RegisterFragment.VERIFYCODE_SEND_TOO_MUCH;
import com.blankj.utilcode.util.ToastUtils;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentPasswordSettingBinding;
import com.chervon.moudleOobe.ui.state.ChangePasswordUiState;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.ForgetPasswordViewModel;
import com.chervon.libBase.ui.widget.InlineTipEditText;
import com.chervon.moudleOobe.utils.InlineVerificationHelper;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: ForgetPasswordFragment
 * @Description: Login module's forgotten password acquisition verification code interface
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:00
 * @UpdateRemark:
 * @Version: 1.0
 */
public class ForgetPasswordFragment extends BaseEnhanceFragment<ForgetPasswordViewModel, MoudleOobeFragmentPasswordSettingBinding> implements   BaseEnhanceFragment.OnkeyBackListener {
    public static final String TAG = "ForgetPasswordFragment";
    public static final String CHANGE_PASSWORD_PAGE_NUM = "1/2";
    public static final String FORGETPASSWORD_PAGE_FIRST = "1/3";
    public static final int CHECK_OLDE_PASSWORD_SUCCESS = 118;
    public static final int INIT_STATE = 0;
    public static final String LABEL_CHANGE_PASSWORD = "changepassword";
    private InlineVerificationHelper mInlineVerificationHelper;
    @Override
    protected void initDatas(Bundle savedInstanceState) {
        mInlineVerificationHelper = new InlineVerificationHelper(mViewDataBinding.btnNext);
        ForgetPasswordUiSate uiSate= mViewDataBinding.getUser();
        Bundle bundle = getArguments();
        int fragmentId = 0;
        if(uiSate==null){
            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
            controller.getCurrentDestination().setLabel("ForgetPasswordFragment");
            uiSate = setDefaultUiData();
            if (bundle != null) {
                fragmentId = bundle.getInt(KEY_PREV_FRAGMENT);
                uiSate.setFrom(fragmentId);
            }
            mViewDataBinding.btnNext.setText(LanguageStrings. getForgetpasswordNext());

            if (fragmentId == R.id.registerFragment) {
                mViewDataBinding.tvTitle.setText(LanguageStrings.app_forget_confirm_email_text());
                RegisterUiState registerUiSate = (RegisterUiState) bundle.getSerializable(REGISTER_DATA);
                uiSate.setAccount(registerUiSate.getEmail());
                mInlineVerificationHelper.buildEmailChecker(mViewDataBinding.ietInput);
            } else if (fragmentId == R.id.changepassword) {
                initChangPasswordPageTrace();
                uiSate = new ChangePasswordUiState();
                uiSate.setTitle( LanguageStrings. appcheckpasswordtitle() );
                uiSate.setHintAccount(LanguageStrings. getOriginalPassword());
                uiSate.setButtonName( LanguageStrings.app_checkpassword_done_button_text());
              if (bundle != null) {
                String email = bundle.getString(KEY_EMAIL);
                uiSate.setEmail(email);
              }
                uiSate.setPageNum(CHANGE_PASSWORD_PAGE_NUM);
                mViewDataBinding.tvEmailTip.setVisibility(View.GONE);
                controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
                controller.getCurrentDestination().setLabel(LABEL_CHANGE_PASSWORD);
                mInlineVerificationHelper .buildPasswordChecker(mViewDataBinding.ietInput);
                //mViewDataBinding.ietInput.setPassWordType();
                mViewDataBinding.btnNext.setText( LanguageStrings. getChangePassword());
            } else {
               initForgetPasswordPageTrace();
              if (bundle != null) {
                String email = bundle.getString(KEY_EMAIL);
                uiSate.setEmail(email);
                uiSate.setAccount(email);
                uiSate.setTitle(LanguageStrings.app_forgetpasswordsuccess_title_textview_text());
              }
                mInlineVerificationHelper.buildEmailChecker(mViewDataBinding.ietInput);
            }
            final int finalFragmentId = fragmentId;
            oBserveLivedata(bundle, finalFragmentId);
        }else{
          if(uiSate.getFrom()==0){
              uiSate.setPageNum(FORGETPASSWORD_PAGE_FIRST);
          }else if(uiSate.getFrom()== R.id.changepassword){
              uiSate.setPageNum(CHANGE_PASSWORD_PAGE_NUM);
          }
        }
        mViewDataBinding.setUser(uiSate);
        mViewDataBinding.setPresenter(this);

        mViewDataBinding.ietInput.setmTextChangedListenner(new InlineTipEditText.TextChangedListenner() {
            @Override
            public void textChanged(String s) {
                ForgetPasswordUiSate uiState = mViewDataBinding.getUser();
                uiState.setAccount(s);
                mInlineVerificationHelper.checkInline();
            }
        });
    }

    private void oBserveLivedata(Bundle bundle, int finalFragmentId) {
        mViewModel.mForgetPasswordLiveData.observe(this, new Observer<ForgetPasswordUiSate>() {
            @Override
            public void onChanged(ForgetPasswordUiSate forgetPasswordUiSate) {
                ProgressHelper.hideProgressView(ForgetPasswordFragment.this.getContext());
                if (forgetPasswordUiSate.getState() == VERIFICATION_SUCCESS) {
                    if (finalFragmentId == R.id.registerFragment) {
                        mViewModel.register((RegisterUiState) bundle.getSerializable(REGISTER_DATA),mViewDataBinding.getUser().getVerificationCode());
                    } else {
                        forgetPasswordUiSate.setFrom(finalFragmentId);
                        ((LoginAndRegisterActivity) ForgetPasswordFragment.this.getActivity()).gotoPassWordConfirm(forgetPasswordUiSate);
                        forgetPasswordUiSate.setState(INIT_STATE);
                    }
                }else if(forgetPasswordUiSate.getState() == GET_VERIFICATION_CODE_SUCCESS){
                    forgetPasswordUiSate.setState(INIT_STATE);
                    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
                    Bundle bundle = new Bundle();
                    ForgetPasswordUiSate passwordUiSate=  mViewDataBinding.getUser();
                    bundle.putSerializable(KEY_PREV_DATA, passwordUiSate);
                    controller.navigate(R.id.action_forgetPasswordFragment_to_checkVerifyCodeFragment, bundle);
                }else if(forgetPasswordUiSate.getState() == CHECK_OLDE_PASSWORD_SUCCESS){
                    goNextPage((ChangePasswordUiState) forgetPasswordUiSate);
                }else if (forgetPasswordUiSate.getState() == ERROR_EMAIL) {
                    ToastUtils.showLong(LanguageStrings.app_base_email_format_text());
                }  else if (forgetPasswordUiSate.getState() == NETWORK_ECEPTION) {
                    ToastUtils.showLong(LanguageStrings.app_base_servererror_textview_text());
                }else if (forgetPasswordUiSate.getState() == VERIFYCODE_SEND_TOO_MUCH) {
                    ToastUtils.showLong(LanguageStrings.app_verifycode_sendtoomuch_text());
                } else if(forgetPasswordUiSate.getState() !=INIT_STATE){
                    ToastUtils.showLong(forgetPasswordUiSate.getRespondMessage());
                }

                forgetPasswordUiSate.setState(INIT_STATE);
            }
        });

        mViewModel.mRegisterLiveData.observe(this, new Observer<RegisterUiState>() {
            @Override
            public void onChanged(RegisterUiState registerUiSate) {
                if (registerUiSate.getState() == REGISTER_SUCCESS) {
                    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).navigation();
                } else {
                    ToastUtils.showLong(registerUiSate.getRespondMessage());
                }
                registerUiSate.setState(INIT_STATE);
            }
        });
    }

    @NonNull
    private ForgetPasswordUiSate setDefaultUiData() {
        ForgetPasswordUiSate uiSate = new ForgetPasswordUiSate();
        uiSate.setTitle(       LanguageStrings.getForgetpassword() );
        uiSate.setHintAccount(  LanguageStrings. getEmailaddressForgetpassword() );
        uiSate.setButtonName( LanguageStrings. getForgetpasswordNext() );
        uiSate.setPageNum(FORGETPASSWORD_PAGE_FIRST);
        uiSate.setFrom(R.id.forgetPasswordFragment);
        return uiSate;
    }

    @Override
    protected int getLayoutId() {
        return com.chervon.moudleOobe.R.layout.moudle_oobe_fragment_password_setting;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding.clContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                v.clearFocus();
            }
        });

    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class<? extends ForgetPasswordViewModel> getViewModelClass() {
        return ForgetPasswordViewModel.class;
    }

    public void goNextPage(ForgetPasswordUiSate uiSate) {
      sendBaseTraceClick(nextButtoneleid);

        if(isAdded()){
            ProgressHelper.showProgressView(this.getActivity(), 0);
        }
        if (uiSate instanceof ChangePasswordUiState) {
            mViewModel.checkOldPassword((ChangePasswordUiState) uiSate);
        } else {
            mViewModel.getVerycode(uiSate);
        }

    }

    public void goNextPage(ChangePasswordUiState uiSate) {
        sendForgetNextButtonClick();
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        uiSate.setPageNum(getString(R.string.two_seconds));
        uiSate.setOldPassword(uiSate.getAccount());
        bundle.putSerializable(KEY_PREV_DATA, uiSate);
        controller.navigate(R.id.action_forgetPasswordFragment_to_passwordConfirmFragment, bundle);
    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }


  private void sendForgetNextButtonClick() {
    sendBaseTraceClick("2");
  }



  @Override
  public void onPause() {
    super.onPause();

  }


  @Override
  public void onDestroyView() {
    mInlineVerificationHelper.clear();
    mInlineVerificationHelper=null;
    if(mViewDataBinding!=null&& mViewDataBinding.clContainer!=null){
      mViewDataBinding.clContainer.setOnClickListener(null);
    }
    super.onDestroyView();


  }

  private void initChangPasswordPageTrace() {
    stayEleId="3";
    pageId="157";
    mouduleId="11";
    nextButtoneleid ="2";
  }



  private void initForgetPasswordPageTrace() {
    stayEleId="3";
    pageId="3";
    mouduleId="3";
    nextButtoneleid ="2";
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, "1","1");
    return false;
  }

}
