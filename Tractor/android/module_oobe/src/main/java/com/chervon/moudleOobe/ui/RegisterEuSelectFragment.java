package com.chervon.moudleOobe.ui;

import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_TO_COMMERCIAL_ELE_ID;
import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_TO_DOMESTIC_ELE_ID;

import android.os.Bundle;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentRegisterEuSelectBinding;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.RegisterSelectViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: RegisterEuSelectFragment
 * @Description: Account from Eu select registration page
 * @Author: wuxd
 * @CreateDate: 2024/6/12 上午11:30
 * @UpdateUser: wuxd
 * @UpdateDate:
 * @UpdateRemark:
 * @UpdateRemark
 * @Version: 1.0
 */
public class RegisterEuSelectFragment extends BaseEnhanceFragment<RegisterSelectViewModel, MoudleOobeFragmentRegisterEuSelectBinding> {
  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    RegisterUiState registerUiState = new RegisterUiState();
    mViewDataBinding.setUiState(registerUiState);
    mViewDataBinding.setController(this);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_register_eu_select;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {}

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends RegisterSelectViewModel> getViewModelClass() {
    return RegisterSelectViewModel.class;
  }

  public void goToDomesticRegister() {
    sendBaseTraceClick(TRACE_REGISTER_EU_TO_DOMESTIC_ELE_ID);
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.registerEuSelectFragment);
    boolean jumpStatus = Navigation.findNavController(requireView()).popBackStack(R.id.registerEuOneFragment, false);
    if(!jumpStatus) {
      controller.navigate(R.id.registerEuOneFragment, bundle);
    }
  }

  public void goToCommercialRegister() {
    sendBaseTraceClick(TRACE_REGISTER_EU_TO_COMMERCIAL_ELE_ID);
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.registerEuSelectFragment);
    controller.navigate(R.id.registerEuWebFragment, bundle);
  }

  private void initTrace() {
    stayEleId = "4";
    pageId = "7";
    mouduleId = "4";
    pageResouce = "1_4_7";
  }
}
