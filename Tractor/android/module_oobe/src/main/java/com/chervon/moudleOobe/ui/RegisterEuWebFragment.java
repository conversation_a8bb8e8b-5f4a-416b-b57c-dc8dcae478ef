package com.chervon.moudleOobe.ui;


import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_BACK_ELE_ID;
import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_TO_WEB_ELE_ID;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentRegisterEuWebBinding;
import com.chervon.moudleOobe.ui.viewmodel.RegisterEuWebViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: RegisterCommercialFragment
 * @Description: Account check registration commercial page
 * @Author: wuxd
 * @CreateDate: 2024/6/12 上午11:30
 * @UpdateUser: wuxd
 * @UpdateDate:
 * @UpdateRemark:
 * @UpdateRemark
 * @Version: 1.0
 */
public class RegisterEuWebFragment extends BaseEnhanceFragment<RegisterEuWebViewModel, MoudleOobeFragmentRegisterEuWebBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    mViewDataBinding.setController(this);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_register_eu_web;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding.tvUrl.setText(getString(R.string.app_registcommercialtype_link_textview_text));
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends RegisterEuWebViewModel> getViewModelClass() {
    return RegisterEuWebViewModel.class;
  }

  public void goToWeb() {
    sendBaseTraceClick(TRACE_REGISTER_EU_TO_WEB_ELE_ID);
     Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.app_registcommercialtype_linkurl_textview_text)));
     requireActivity().startActivity(intent);
  }

  public void goToLink() {
    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.app_registcommercialtype_link_textview_text)));
    requireActivity().startActivity(intent);
  }

  private void initTrace() {
    stayEleId = "2";
    pageId = "449";
    mouduleId = "4";
    pageResouce = "1_4_449";
  }

  @Override
  public boolean OnkeyBack() {
    sendBaseTraceClick(TRACE_REGISTER_EU_BACK_ELE_ID);
    return false;
  }
}
