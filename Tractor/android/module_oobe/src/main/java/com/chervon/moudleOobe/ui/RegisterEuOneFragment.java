package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.moudleOobe.utils.Constants.EMPTY;
import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_BACK_ELE_ID;


import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;


import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.chervon.libBase.model.DictItem;
import com.chervon.libBase.model.DictNode;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentRegisterEuOneBinding;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.RegisterEuViewModel;

import java.util.ArrayList;

import me.jessyan.autosize.AutoSize;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: RegisterEuOneFragment
 * @Description: 欧洲第一步
 * @Author: wuxd
 * @CreateDate: 2024/6/12
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @UpdateRemark
 * @Version: 1.0
 */
public class RegisterEuOneFragment extends BaseEnhanceFragment<RegisterEuViewModel, MoudleOobeFragmentRegisterEuOneBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "RegisterEuOneFragment";
  public static final String REGISTER_DATA = "register_data";
  public static final String APP_REGISTER_EU_COUNTRY_CODE = "app_register_eu_country_code";
  private DialogUtil mCountryDialog;
  private String[] mCountryItem;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    RegisterUiState registerUiState = mViewDataBinding.getUiState();
    if (registerUiState == null) {
      registerUiState = new RegisterUiState();
      registerUiState.setEmail(EMPTY);
      registerUiState.setFirstName(EMPTY);
      registerUiState.setLastName(EMPTY);
      registerUiState.setPhone(EMPTY);
      registerUiState.setPassword(EMPTY);
      registerUiState.setConfirmPassword(EMPTY);
    }
    mViewModel.mRegisterUiState.setValue(registerUiState);
    mViewDataBinding.setUiState(registerUiState);
    mViewDataBinding.setPresenter(mViewModel);
    mViewDataBinding.setController(this);
    showLoading();
    mViewModel.getCountry();
  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_register_eu_one;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding.clContainer.setOnClickListener(v -> {
      v.clearFocus();
      KeyboardUtils.hideSoftInput(RegisterEuOneFragment.this.getActivity());
    });
    mViewDataBinding.etCountry.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
      }

      @Override
      public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
      }

      @Override
      public void afterTextChanged(Editable editable) {
        RegisterUiState uiState = mViewDataBinding.getUiState();
        uiState.setCountry(editable.toString());
        if(!editable.toString().isEmpty()) {
          mViewDataBinding.btnNext.setEnabled(true);
        } else {
          mViewDataBinding.btnNext.setEnabled(false);
        }
      }
    });

    initObserver();
  }

  private void initObserver() {
    mViewModel.mDictLiveData.observe(this, dictEntry -> {
      dismissLoading();
      if (dictEntry != null) {
        DictItem[] dictItems = dictEntry.getEntry();
        for (int i = 0; dictItems != null && i < dictItems.length; i++) {
          if (APP_REGISTER_EU_COUNTRY_CODE.equalsIgnoreCase(dictItems[i].getDictName())) {
            ArrayList<String> countryList = new ArrayList<>();
            for (DictNode dictNode : dictItems[i].getNodes()) {
              countryList.add(dictNode.getLabel());
            }
            mCountryItem = countryList.toArray(new String[0]);
            if(null == mViewDataBinding.getUiState().getCountry() && mCountryItem.length>0) {
              mViewDataBinding.getUiState().setCountry(mCountryItem[0]);
            }
          }
        }
      }
    });
  }

  private void goToStepTwo(RegisterUiState uiSate) {
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.registerEuOneFragment);
    bundle.putSerializable(REGISTER_DATA, uiSate);
    controller.navigate(R.id.registerEuTwoFragment, bundle);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends RegisterEuViewModel> getViewModelClass() {
    return RegisterEuViewModel.class;
  }

  public void goToLoginPage(RegisterUiState uiSate) {
    sendBaseTraceClick(TRACE_REGISTER_EU_BACK_ELE_ID);
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    controller.navigate(R.id.loginFragment);
  }

  public void onClickEvent(RegisterUiState uiSate) {
    goToStepTwo(uiSate);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  @Override
  public void onResume() {
    super.onResume();
    AutoSize.autoConvertDensityOfGlobal(ActivityUtils.getTopActivity());
  }

  private void initTrace() {
    stayEleId = "4";
    pageId = "7";
    mouduleId = "4";
    pageResouce = "1_4_7";
  }

  /**
   * 新需求默认选中第一个
   */
  private int selectPosition = 0;
  public void choiceCountry(RegisterUiState registerUistate) {
    if (mCountryDialog == null) {
      mCountryDialog = new DialogUtil();
    }
    mCountryDialog.showBottomListDialog2WithSelectPosition(this.getContext(),
      LanguageStrings.app_registselectcountry_selectcountry_textview_text(),
      (view, itemName) -> {
        selectPosition = (int) view.getTag();
        if(null != mViewDataBinding && null != mViewDataBinding.getUiState()) {
          mViewDataBinding.getUiState().setCountry(itemName);
        }
      }, mCountryItem,selectPosition);

  }

  private void showLoading() {
    if (isAdded()) {
      ProgressHelper.showProgressView(this.getActivity(), 0, true);
    }
  }

  private void dismissLoading() {
    if(null == getActivity()) {
      return;
    }
    if(getActivity().isFinishing()) {
      return;
    }
    ProgressHelper.hideProgressView(getActivity());
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, TRACE_REGISTER_EU_BACK_ELE_ID, "1");
    return false;
  }
}
