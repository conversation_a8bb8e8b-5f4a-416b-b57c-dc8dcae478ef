package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.KEY_URL;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.GET_VERIFICATION_CODE_SUCCESS;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.RESPONSE_FAIL;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.VALID_FREQUENT;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.LoginFragment.LOGIN_PASSWORD_DIFF;
import static com.chervon.moudleOobe.ui.LoginFragment.LOGIN_PASSWORD_ERROR;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.UnderlineSpan;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.InlineTipEditTextRegist;
import com.chervon.libBase.ui.widget.InlineVerificationHelperRegist;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.BuildConfig;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentRegisterBinding;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.RegisterViewModel;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTip;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTipView;

import me.jessyan.autosize.AutoSize;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: RegisterFragment
 * @Description: Account registration page
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: hyman
 * @UpdateDate: 2023/12/4
 * @UpdateRemark: 迭代用户注册  jackson需求---涉及范围：UI改版，文案修改，提示逻辑修改-重构提示弹出自定义组件---需求1.1
 * @UpdateRemark FirstName 和LastName增加--需求1.2
 * @Version: 1.2
 */
public class RegisterFragment extends BaseEnhanceFragment<RegisterViewModel, MoudleOobeFragmentRegisterBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "RegisterFragment";
  public static final int REGISTER_SUCCESS = 1;
  public static final int REGISTER_FAIL = 2;
  public static final int OTHER_STATE = -1;
  public static final int ERROR_EMAIL = 5;
  //邮箱已经存在
  public static final int ACCOUNT_CHECK_EXIST = **********;
  //邮箱邮箱格式异常
  public static final int ACCOUNT_CHECK_FORMAT_ERROR = **********;
  //邮箱验证通过-App自定义
  public static final int ACCOUNT_CHECK_RIGHT = 100133;

  public static final int VERIFYCODE_SEND_TOO_MUCH = *********;
  public static final String REGISTER_DATA = "register_data";
  private ToolTipView mRedToolTipView;
  private InlineVerificationHelperRegist mInlineVerificationHelper;
  private int mClickNum;
  private Dialog loadingDialog;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    RegisterUiState registerUiSate = mViewDataBinding.getUiState();
    if (registerUiSate == null) {
      registerUiSate = new RegisterUiState();
    }
    mViewDataBinding.setUiState(registerUiSate);
    mViewDataBinding.setPresenter(mViewModel);
    mViewDataBinding.setControl(this);
    mViewModel.policyAndAgereen.postValue(false);

    mViewDataBinding.ietFirstName.setmTextChangedListenner(new InlineTipEditTextRegist.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        RegisterUiState uiState = mViewDataBinding.getUiState();
        uiState.setFirstName(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });

    mViewDataBinding.ietLastName.setmTextChangedListenner(new InlineTipEditTextRegist.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        RegisterUiState uiState = mViewDataBinding.getUiState();
        uiState.setLastName(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }

      }
    });


    mViewDataBinding.ietTmail.setmTextChangedListenner(new InlineTipEditTextRegist.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        RegisterUiState uiState = mViewDataBinding.getUiState();
        uiState.setEmail(s);
        LogUtils.i(TAG,"ietTmail--->"+s);
        LogUtils.i(TAG,"焦点状态----->"+mViewDataBinding.ietTmail.hasFocus());
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.setEmail(s);
          mInlineVerificationHelper.checkInline();
          if (CommonUtils.checkEmail(s)&&!mViewDataBinding.ietTmail.hasFocus()){
            mViewModel.checkEmailExist(s);
          }
        }
      }
    });

    mViewDataBinding.ietTmail.setCvOnFocusChangeListener(new InlineTipEditTextRegist.CVOnFocusChangeListener() {
      @Override
      public void checkEmailWithNet(View v, boolean hasFocus, String editText) {
        LogUtils.e(TAG,"checkEmailWithNet-hasFocus->"+hasFocus);
        loadingDialog = ProgressHelper.progressViewRnShow(getActivity(), 0, false);

        String mail = mViewDataBinding.ietTmail.getInlineTipEditTextString();
        mViewModel.checkEmailExist(mail);
      }
    });


    mViewDataBinding.ietPassword.setmTextChangedListenner(new InlineTipEditTextRegist.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        RegisterUiState uiState = mViewDataBinding.getUiState();
        uiState.setPassword(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.setEditPassword(s);
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(new InlineTipEditTextRegist.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        RegisterUiState uiState = mViewDataBinding.getUiState();
        uiState.setConfirmPassword(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_register;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mInlineVerificationHelper = new InlineVerificationHelperRegist(mViewDataBinding.btnNext);

    //北美注册
    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA)) {
      mInlineVerificationHelper
        .buildFirstName(mViewDataBinding.ietFirstName)
        .buildLastName(mViewDataBinding.ietLastName)
        .buildEmail(mViewDataBinding.ietTmail)
        .buildPasswordChecker(mViewDataBinding.ietPassword)
        .buildPasswordCheckerWithSameChecker2(mViewDataBinding.ietConfirmPassword, mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
//        .buildPasswordSameChecker(mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
        .buildAgreementChecker(mViewDataBinding.rbRead);
    } else if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
      mInlineVerificationHelper
        .buildEmail(mViewDataBinding.ietTmail)
        .buildPasswordChecker(mViewDataBinding.ietPassword)
        .buildPasswordCheckerWithSameChecker2(mViewDataBinding.ietConfirmPassword, mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
//        .buildPasswordSameChecker(mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
        .buildAgreementChecker(mViewDataBinding.rbRead);
    }


    mViewDataBinding.clContainer.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        v.clearFocus();
        KeyboardUtils.hideSoftInput(RegisterFragment.this.getActivity());
      }
    });


    addRedToolTipView();

    mViewDataBinding.rbRead.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
      @Override
      public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
          mViewDataBinding.activityMainTooltipframelayout.removeAllViews();
          sendAgreementClick();
        } else {
          addRedToolTipView();
        }
        mViewDataBinding.getUiState().setAgreementChecked(isChecked);
        mViewDataBinding.clContainer.clearFocus();
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });

    mViewModel.mSendEmailLivedata.observe(this, new Observer<ForgetPasswordUiSate>() {
      @Override
      public void onChanged(ForgetPasswordUiSate uiSate) {
        if (uiSate.getState() == GET_VERIFICATION_CODE_SUCCESS) {
          goToCheckVerifyCode(mViewDataBinding.getUiState());
          uiSate.setState(OTHER_STATE);
        } else if (uiSate.getState() == VERIFYCODE_SEND_TOO_MUCH) {
          ToastUtils.showLong(LanguageStrings.app_verifycode_sendtoomuch_text());
        } else if (uiSate.getState() == RESPONSE_FAIL || uiSate.getState() == VALID_FREQUENT ) {
          if (!TextUtils.isEmpty(uiSate.getRespondMessage())) {
            ToastUtils.showLong(uiSate.getRespondMessage());
          }
        }

      }
    });
    mViewModel.mRegisterUiSate.observe(this, new Observer<RegisterUiState>() {
      @Override
      public void onChanged(RegisterUiState uiState) {
        if (loadingDialog != null) {
          loadingDialog.dismiss();
        }
        if (uiState.getState() == ERROR_EMAIL) {
          ToastUtils.showLong(LanguageStrings.app_base_email_format_text());
        } else if (uiState.getState() == LOGIN_PASSWORD_ERROR) {
          //ToastUtils.showLong(getString(com.chervon.libBase.R.string.password_range));
        } else if (uiState.getState() == LOGIN_PASSWORD_DIFF) {
          ToastUtils.showLong(LanguageStrings.app_password_comfirm_mismatch_text());
        } else if (uiState.getState() == ACCOUNT_CHECK_EXIST) {
          if (mInlineVerificationHelper == null) {
          return;
          }
          mInlineVerificationHelper.buildEmailWornTip(mViewDataBinding.ietTmail, uiState.isEmailError(),
                  LanguageStrings.app_regist_emailisexists_textview_text());
          mInlineVerificationHelper.setEmailCheckResult(false);
          mInlineVerificationHelper.checkInline();
          uiState.setRegister(false);
        } else if (uiState.getState() == ACCOUNT_CHECK_FORMAT_ERROR) {
          if (mInlineVerificationHelper == null) {
            return;
          }
          uiState.setRegister(false);
          if (!TextUtils.isEmpty(uiState.getMessage())) {
            mInlineVerificationHelper.buildEmailWornTip(mViewDataBinding.ietTmail, uiState.isEmailError(), uiState.getMessage());
            mInlineVerificationHelper.setEmailCheckResult(false);
          }
          mInlineVerificationHelper.checkInline();
        } else if (uiState.getState() == ACCOUNT_CHECK_RIGHT) {
          if (mInlineVerificationHelper == null) {
            return;
          }
          mInlineVerificationHelper.setEmailCheckResult(true);
          mInlineVerificationHelper.buildEmailWornTip(mViewDataBinding.ietTmail, uiState.isEmailError(), "");
          mInlineVerificationHelper.checkInline();
          if (uiState.isRegister()){
            onClickEvent(uiState);
          }

        } else if (uiState.getState() != INIT_STATE) {
          if (!TextUtils.isEmpty(uiState.getRespondMessage())) {
            ToastUtils.showLong(uiState.getRespondMessage());
          }
        }
        uiState.setState(INIT_STATE);
      }
    });
    initAgreementView();
  }

  private void goToCheckVerifyCode(RegisterUiState uiSate) {
    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.registerFragment);
    bundle.putSerializable(REGISTER_DATA, uiSate);
    controller.navigate(R.id.checkVerifyCodeFragment, bundle);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends RegisterViewModel> getViewModelClass() {
    return RegisterViewModel.class;
  }

  private void addRedToolTipView() {
    ToolTip toolTip = new ToolTip()
      .withText(LanguageStrings.getLoginAgreementprivacy())
      .withColor(Color.WHITE)
      .withShadow();
    mRedToolTipView = mViewDataBinding.activityMainTooltipframelayout.showToolTipForView(toolTip, mViewDataBinding.rbRead);

  }

  public void goToLoginPage(RegisterUiState uiSate) {
//    goToNextFragment(R.id.action_registerFragment_to_loginFragment);
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    if (mFrom == R.id.loginFragment){
      controller.popBackStack();
    }else {
      controller.navigate(R.id.loginFragment);
    }


  }

  public void goToUserAgreement(RegisterUiState uiState) {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.tvUserAgreement)
      .withInt(KEY_TO, R.id.userAgreementFragment)
      .withString(KEY_URL, "https://egopowerplus.com")
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

  }

  public void goToUsePolicy(RegisterUiState uiState) {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.tvPrivacvPolicy)
      .withInt(KEY_TO, R.id.userAgreementFragment)
      .withString(KEY_URL, "https://egopowerplus.com")
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

  }

  public void onClickEvent(RegisterUiState uiState) {
    sendButtonClick();
    if (mClickNum == 0) {
      if (null==mInlineVerificationHelper){
        return;
      }
      //先检验邮箱是否合格
      if (!mInlineVerificationHelper.isEmailCheckResult()) {
        uiState.setRegister(true);
        mViewModel.checkEmailExist(uiState.getEmail());
      } else {
        mViewModel.onClickEvent(uiState);
      }

      mClickNum = 1;
    }

    mViewDataBinding.rbRead.postDelayed(new Runnable() {
      @Override
      public void run() {
        mClickNum = 0;
      }
    }, 2000);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  private void initAgreementView() {
    String andStr = " " + LanguageStrings.getUseragreementAnd() + " ";
    SpannableStringBuilder style = new SpannableStringBuilder();
    style.append(LanguageStrings.getUseragreementPart());
    style.append(LanguageStrings.getUseragreement());
    style.append(andStr);
    style.append(LanguageStrings.getPrivacyPolicy());


    ClickableSpan clickableSpan = new ClickableSpan() {
      @Override
      public void onClick(@NonNull View view) {
        goToUserAgreement(mViewDataBinding.getUiState());
      }
    };


    ClickableSpan clickableSpan2 = new ClickableSpan() {
      @Override
      public void onClick(@NonNull View view) {
        goToUsePolicy(mViewDataBinding.getUiState());

      }
    };


    style.setSpan(clickableSpan2, style.length() - LanguageStrings.getPrivacyPolicy().length(), style.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(clickableSpan, LanguageStrings.getUseragreementPart().length(), LanguageStrings.getUseragreementPart().length()
      + LanguageStrings.getUseragreement().length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    style.setSpan(new UnderlineSpan() {
      @Override
      public void updateDrawState(@NonNull TextPaint ds) {
        super.updateDrawState(ds);
        ds.setColor(getResources().getColor(R.color.colorButtonNormal));
        ds.setUnderlineText(false);//去掉下划线
      }
    }, LanguageStrings.getUseragreementPart().length(), LanguageStrings.getUseragreementPart().length()
      + LanguageStrings.getUseragreement().length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(new UnderlineSpan() {
      @Override
      public void updateDrawState(@NonNull TextPaint ds) {
        super.updateDrawState(ds);
        ds.setColor(getResources().getColor(R.color.colorButtonNormal));
        ds.setUnderlineText(false);//去掉下划线
      }
    }, style.length() - LanguageStrings.getPrivacyPolicy().length(), style.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    mViewDataBinding.tvAgreement.setMovementMethod(LinkMovementMethod.getInstance());
    mViewDataBinding.tvAgreement.setText(style);
  }

  @Override
  public void onResume() {
    super.onResume();
    AutoSize.autoConvertDensityOfGlobal(ActivityUtils.getTopActivity());
  }

  private void initTrace() {
    stayEleId = "4";
    pageId = "7";
    mouduleId = "4";
    pageResouce = "1_4_7";
    nextButtoneleid = "3";
  }

  private void sendAgreementClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "2", "1");
  }

  private void sendButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    return false;
  }


  @Override
  public void onStop() {
    super.onStop();
//    if (mInlineVerificationHelper != null) {
//      mInlineVerificationHelper.clear();
//      mInlineVerificationHelper = null;
//    }
//    mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(null);
//    mViewDataBinding.ietConfirmPassword.setEditTextOnFocusChangeListener(null);
//    mViewDataBinding.ietConfirmPassword.clear();
//
//
//    mViewDataBinding.ietPassword.setmTextChangedListenner(null);
//    mViewDataBinding.ietPassword.setEditTextOnFocusChangeListener(null);
//    mViewDataBinding.ietPassword.clear();
//
//    mViewDataBinding.ietTmail.setmTextChangedListenner(null);
//    mViewDataBinding.ietTmail.setEditTextOnFocusChangeListener(null);
//    mViewDataBinding.ietTmail.clear();
  }


  @Override
  public void onDestroy() {
    super.onDestroy();
    try {

    if (null!=loadingDialog){
      loadingDialog.dismiss();
      loadingDialog = null;
    }
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.clear();
        mInlineVerificationHelper = null;
      }

      mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(null);
      mViewDataBinding.ietConfirmPassword.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietConfirmPassword.clear();

      mViewDataBinding.ietPassword.setmTextChangedListenner(null);
      mViewDataBinding.ietPassword.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietPassword.clear();

      mViewDataBinding.ietTmail.setmTextChangedListenner(null);
      mViewDataBinding.ietTmail.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietTmail.setCvOnFocusChangeListener(null);
      mViewDataBinding.ietTmail.clear();
    } catch (Exception e) {
      LogUtils.i(TAG, "error");
    }

  }
}
