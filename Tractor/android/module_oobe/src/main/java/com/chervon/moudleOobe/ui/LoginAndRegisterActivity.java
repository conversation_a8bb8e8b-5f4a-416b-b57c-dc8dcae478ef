package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_CHECK_PASSWORD;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_FORGET_PASSWORD;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_REGIST;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_ROUTER_PATH;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.LABEL_CHANGE_PASSWORD;
import static com.chervon.moudleOobe.ui.PasswordConfirmFragment.FORGET_PASSWORD_CONFIRM;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.navigation.NavBackStackEntry;
import androidx.navigation.NavController;
import androidx.navigation.NavDestination;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.BuildConfig;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeActivityLoginRegisterBinding;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.viewmodel.LoginAndRegisterViewModel;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: LoginAndRegisterActivity
 * @Description: Main activity of login module
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/18
 * @UpdateRemark: 适配欧洲注册流程
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_URL_LOGIN)
public class LoginAndRegisterActivity extends BaseActivity<LoginAndRegisterViewModel> {
  public static final String USER_NAME = "forget_user_name";
  private ToolbarData mToolbarData;
  private MoudleOobeActivityLoginRegisterBinding mBinding;

  private static final String RESET_PASSWORD_SUCCESS_FRAGMENT = "ResetPasswordSuccessFragment";

  @Override
  protected void onUnRegister() {
  }

  @Override
  protected void onRegister() {

  }


  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    //  AutoSize.cancelAdapt(this);
    //     AutoSize.checkAndInit(this.getApplication());
    super.onCreate(savedInstanceState);


  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_oobe_activity_login_register;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    AutoSizeCompat.autoConvertDensityBaseOnHeight(super.getResources(), 1334);
    AutoSizeCompat.autoConvertDensityBaseOnWidth(super.getResources(), 750);
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      int fragmentId = getIntent().getExtras().getInt(KEY_PREV_FRAGMENT);
      String routerPath = getIntent().getExtras().getString(KEY_ROUTER_PATH);
      if (fragmentId == R.id.changepassword || (!TextUtils.isEmpty(routerPath) && ACTIVITY_CHECK_PASSWORD.equals(routerPath))) {
        NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        controller.navigate(R.id.forgetPasswordFragment, bundle);
      } else if (fragmentId == R.id.registerFragment || fragmentId == R.id.registerEuSelectFragment || (!TextUtils.isEmpty(routerPath) && ACTIVITY_URL_REGIST.equals(routerPath))) {
        NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        NavBackStackEntry navBackStackEntry;
        //欧洲进入和北美进入区分
        if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
          controller.setGraph(R.navigation.nav_graph_register_eu);
          navBackStackEntry = controller.getBackStackEntry(R.id.registerEuSelectFragment);
        } else {
          controller.setGraph(R.navigation.nav_graph_register);
          navBackStackEntry = controller.getBackStackEntry(R.id.registerFragment);
        }
        controller.getBackStack().clear();
        controller.getBackStack().add(navBackStackEntry);
      }else if ((!TextUtils.isEmpty(routerPath) && ACTIVITY_URL_FORGET_PASSWORD.equals(routerPath))) {
        NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        //欧洲进入和北美进入区分
        if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
          controller.setGraph(R.navigation.nav_graph_register_eu);
        } else {
          controller.setGraph(R.navigation.nav_graph_register);
        }
        NavBackStackEntry navBackStackEntry = controller.getBackStackEntry(R.id.forgetPasswordFragment);
        controller.getBackStack().clear();
        controller.getBackStack().add(navBackStackEntry);
      }
    }

    mBinding = (MoudleOobeActivityLoginRegisterBinding) viewDataBinding;
    mBinding.toolbar.setNavigationOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });
    mToolbarData = new ToolbarData("", new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });
    mBinding.setToolbarData(mToolbarData);
    NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);

    navController.addOnDestinationChangedListener(new NavController.OnDestinationChangedListener() {
      @Override
      public void onDestinationChanged(@NonNull NavController controller, @NonNull NavDestination destination, @Nullable Bundle arguments) {
        setKeyBackListener(null);
        if (R.id.forgetResetSuccessFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.app_forgetpassword_textview_text());
          mBinding.setToolbarData(mToolbarData);
        }else {
          mToolbarData.setTitle("");
          mBinding.setToolbarData(mToolbarData);

        }
      }
    });
  }

  @SuppressLint("RestrictedApi")
  private boolean goBack() {
    if (mkeyBackListener != null) {
      if (mkeyBackListener.OnkeyBack()) {
        mkeyBackListener = null;
      }
    }

    NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);

    if (R.id.forgetPasswordFragment == navController.getCurrentDestination().getId()) {
      sendForgetReturnButtonClick();
    }
    String currentLabel = navController.getCurrentDestination().getLabel().toString();
    if (LABEL_CHANGE_PASSWORD.equals(currentLabel)) {
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_USER_INFO).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
      finish();
      return false;
    }
    if (navController.getPreviousBackStackEntry() != null && navController.getPreviousBackStackEntry().getDestination() != null
      && !TextUtils.isEmpty(navController.getPreviousBackStackEntry().getDestination().getLabel())) {
      if (RESET_PASSWORD_SUCCESS_FRAGMENT.equals(currentLabel)) {
        navController.getBackStack().clear();
        navController.navigate(R.id.loginFragment);
        return false;
      }
    }


    if (FORGET_PASSWORD_CONFIRM.equals(currentLabel)) {
      navController.popBackStack(R.id.checkVerifyCodeFragment, true);
      //.navigate(R.id.forgetPasswordFragment);
      return false;
    }


    int size = navController.getBackStack().size();
    boolean isOnlyRegistPage = (size == 1 && navController.getBackStack().getFirst().getDestination().getId() == R.id.registerFragment
    );
    if (size == 0 || isOnlyRegistPage) {
      finish();
      return true;
    } else if ((size <= 2)) {
      //  navController.popBackStack();
      finish();
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_WELCOME).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP).navigation();

      return true;
    } else {
      navController.navigateUp();
    }

    return false;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {


  }

  @Override
  protected Class getViewModelClass() {
    return LoginAndRegisterViewModel.class;
  }


  public void gotoRegister(View view) {
    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA)) {
      //北美进入
      NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
      Bundle bundle = new Bundle();
      bundle.putInt(KEY_FROM,R.id.loginFragment);
      controller.navigate(R.id.registerFragment,bundle);
    } else if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
      //欧洲进入
      NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
      controller.navigate(R.id.registerEuSelectFragment);
    }
    sendRegistrationButtonClick();
  }


  public void gotoPassWordConfirm(ForgetPasswordUiSate uiSate) {
    NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    uiSate.setPageNum("3/3");
    bundle.putSerializable(KEY_PREV_DATA, uiSate);
    controller.navigate(R.id.action_checkVerifyCodeFragment_to_passwordConfirmFragment, bundle);
  }


  @Override
  public boolean onKeyDown(int keyCode, KeyEvent event) {
    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
      if (goBack()) {
        return super.onKeyDown(keyCode, event);
      }
    }
    return false;
  }

  @Override
  protected void onPause() {
    super.onPause();
    ProgressHelper.hideProgressView(this);
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
  }

  @Override
  protected void onResume() {
    super.onResume();


//    BluetoothConection.getInstance().disConnectDevice();
  }

  private void sendForgetReturnButtonClick() {
    sendClickTrace(this, "3", "3", "1_2_3", "1", "");
  }

  private void sendRegistrationButtonClick() {
    sendClickTrace(this, "2", "2", "1_2", "3", "1");
  }

}
