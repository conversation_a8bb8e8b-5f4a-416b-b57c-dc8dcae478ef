package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libRouter.RouterConstants.KEY_EMAIL;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.KEY_URL;
import static com.chervon.moudleOobe.data.repository.LoginRepo.LOGIN_STATE_MORE_CHANNEL;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.GET_VERIFICATION_CODE_SUCCESS;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.RegisterFragment.OTHER_STATE;
import static com.chervon.moudleOobe.ui.RegisterFragment.REGISTER_DATA;
import static com.chervon.moudleOobe.ui.RegisterFragment.VERIFYCODE_SEND_TOO_MUCH;

import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.ToastUtils;

import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.UnderlineSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.ButtonClickUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.BuildConfig;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentLoginBinding;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.state.LoginUiState;
import com.chervon.moudleOobe.ui.viewmodel.LoginViewModel;
import com.chervon.libBase.ui.widget.InlineTipEditText;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTip;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTipView;
import com.chervon.moudleOobe.utils.InlineVerificationHelper;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.HashMap;
import java.util.Map;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: LoginAndRegisterActivity
 * @Description: Login main page
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/19
 * @UpdateRemark: 欧洲登录显示crm提示语
 * @Version: 1.1
 */
public class LoginFragment extends BaseEnhanceFragment<LoginViewModel, MoudleOobeFragmentLoginBinding> implements ToolTipView.OnToolTipViewClickedListener {
  public static final String TAG = "LoginFragment";
  public static final int LOGIN_SUCCESS = 1;
  public static final int LOGIN_FAIL = 112;
  public static final int AGREEMENT_CHECK_SUCCESS = 3;
  public static final int LOGIN_PASSWORD_ERROR = 800501003;
  public static final int LOGIN_PASSWORD_DIFF = 5;
  private ToolTipView mRedToolTipView;
  private InlineVerificationHelper mInlineVerificationHelper;
  private final String NET_RESULT_LOGIN_SUCCESS_TRACE = "1";
  private final String NET_RESULT_LOGIN_FAIL_TRACE = "0";

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    getEmailFromIntent(mViewModel.mLoginLiveData.getValue());
    initViewBinding(mViewModel.mLoginLiveData.getValue());
    liveDatasObserver();

  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_login;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    ((BaseApplication) this.getActivity().getApplication()).setLoginSuccess(false);
    initInlineVerifyHelper();
    addAgreementTipView();
    setListener();
    initAgreementView();
    initLink();
  }

  private void initInlineVerifyHelper() {
    if (mInlineVerificationHelper == null) {
      mInlineVerificationHelper = new InlineVerificationHelper(mViewDataBinding.btnLogin);
      mInlineVerificationHelper.buildEmailChecker(mViewDataBinding.ietEmail, new InlineVerificationHelper.EmailInputComplete() {
        @Override
        public boolean complete(String email) {
          mViewModel.haseAgreePricy(email);
          return false;
        }
      }).buildPasswordChecker(mViewDataBinding.llPassword).buildAgreementChecker(mViewDataBinding.rbRead);
    }

  }

  /**
   * 欧洲crm需求
   * 添加提示语
   */
  private void initLink() {
    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
      mViewDataBinding.tvLoginEu.setVisibility(View.VISIBLE);
      SpannableStringBuilder text = new SpannableStringBuilder(LanguageStrings.app_logineu_description_textview_text());
      Pattern pattern = Pattern.compile(LanguageStrings.app_logineu_link_textview_text());
      Matcher matcher = pattern.matcher(text);
      while (matcher.find()) {
        text.setSpan(new CrmWebClickableSpan(),matcher.start(),matcher.end(),Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
      }
      mViewDataBinding.tvLoginEu.setText(text);
      mViewDataBinding.tvLoginEu.setMovementMethod(LinkMovementMethod.getInstance());
    }
  }

  private void initAgreementView() {
    String andStr = " " + LanguageStrings.getUseragreementAnd() + " ";
    SpannableStringBuilder style = new SpannableStringBuilder();
    style.append(LanguageStrings.getUseragreementPart());
    style.append(LanguageStrings.getUseragreement());
    style.append(andStr);
    style.append(LanguageStrings.getPrivacyPolicy());


   style.setSpan(new UserPricyClickableSpan(), style.length() - LanguageStrings.getPrivacyPolicy().length(),
     style.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(new UserAgreementClickableSpan(), LanguageStrings.getUseragreementPart().length(), LanguageStrings.getUseragreementPart().length()
      + LanguageStrings.getUseragreement().length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);


    style.setSpan(new UnderlineSpan(){
      @Override
      public void updateDrawState(@NonNull TextPaint ds) {
        super.updateDrawState(ds);
        ds.setColor(getResources().getColor(R.color.colorButtonNormal));//设置颜色
        ds.setUnderlineText(false);//去掉下划线
      }
    }, LanguageStrings.getUseragreementPart().length(), LanguageStrings.getUseragreementPart().length()
      + LanguageStrings.getUseragreement().length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

   style.setSpan(new UnderlineSpan(){
                   @Override
                   public void updateDrawState(@NonNull TextPaint ds) {
                     super.updateDrawState(ds);
                     ds.setColor(getResources().getColor(R.color.colorButtonNormal));//设置颜色
                     ds.setUnderlineText(false);//去掉下划线
                   }
                 }, style.length() - LanguageStrings.getPrivacyPolicy().length(), style.length(),
     Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);


    mViewDataBinding.tvAgreement.setMovementMethod(LinkMovementMethod.getInstance());
    mViewDataBinding.tvAgreement.setText(style);
  }

  private void liveDatasObserver() {

    mViewModel.mSendEmailLivedata.observe(this, new Observer<ForgetPasswordUiSate>() {
      @Override
      public void onChanged(ForgetPasswordUiSate uiState) {

        if (uiState.getState() == GET_VERIFICATION_CODE_SUCCESS) {
          goToCheckVerifyCode(mViewModel.mLoginLiveData.getValue());
          uiState.setState(OTHER_STATE);
        } else if (uiState.getState() == VERIFYCODE_SEND_TOO_MUCH) {
          ToastUtils.showShort(LanguageStrings.app_verifycode_sendtoomuch_text());
        } else if (uiState.getState() != INIT_STATE) {
          if (!TextUtils.isEmpty(uiState.getRespondMessage())) {
            ToastUtils.showShort(uiState.getRespondMessage());
          }
        }
        uiState.setRespondMessage("");
        uiState.setState(INIT_STATE);
      }
    });

    mViewModel.mLoginLiveData.observe(this, new Observer<LoginUiState>() {
      @Override
      public void onChanged(LoginUiState loginUiState) {
        ProgressHelper.hideProgressView(LoginFragment.this.getActivity());
        if (loginUiState.getLoginState() == LOGIN_SUCCESS) {
          ((BaseApplication) (LoginFragment.this.getActivity().getApplication())).setLoginSuccess(true);
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
          trace_login_button_click_result(NET_RESULT_LOGIN_SUCCESS_TRACE);
        }  else if (loginUiState.getState() == LOGIN_FAIL){

          if (!TextUtils.isEmpty(loginUiState.getErrorMsg())) {
            ToastUtils.showLong(loginUiState.getErrorMsg());
          }
          trace_login_button_click_result(NET_RESULT_LOGIN_FAIL_TRACE);

        }else if (loginUiState.getLoginState() == AGREEMENT_CHECK_SUCCESS) {
          mViewDataBinding.rbRead.setChecked(loginUiState.isAgreementChecked());
        } else if (loginUiState.getLoginState() == LOGIN_STATE_MORE_CHANNEL) {
          mViewModel.getVerificationCode();
        } else if (loginUiState.getLoginState() != INIT_STATE) {
          if (!TextUtils.isEmpty(loginUiState.getErrorMsg())) {
            ToastUtils.showLong(loginUiState.getErrorMsg());
          }
        }
        loginUiState.setLoginState(INIT_STATE);
      }
    });


    mViewModel.mAgreeMentLiveData.observe(this, new Observer<BaseUistate>() {
      @Override
      public void onChanged(BaseUistate uistate) {
        if (uistate.getState() == LOGIN_SUCCESS) {
          mViewModel.login();
        } else if (uistate.getState() != INIT_STATE) {
          if (!TextUtils.isEmpty(uistate.getMessage())) {
            ToastUtils.showLong(uistate.getMessage());
          }
        }
        uistate.setState(INIT_STATE);
      }
    });
  }

  private void initViewBinding(LoginUiState loginUiState) {
    mViewDataBinding.setPresenter(mViewModel);
    mViewDataBinding.setController(this);
  }

  private void getEmailFromIntent(LoginUiState loginUiState) {
    Bundle bundle = getArguments();
    if (bundle != null) {
      String email = bundle.getString(KEY_EMAIL);
      if (email != null) {
        loginUiState.setAccount(email);
      }
    }
  }


  private void setListener() {
    mViewDataBinding.rbRead.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
      @Override
      public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
          mViewDataBinding.activityMainTooltipframelayout.removeAllViews();
          sendAgreementButtonClickClick();
        } else {
          addAgreementTipView();
        }
        //   mViewDataBinding.clContainer.clearFocus();
        mViewModel.mLoginLiveData.getValue().setAgreementChecked(isChecked);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.clContainer.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        v.clearFocus();
        KeyboardUtils.hideSoftInput(LoginFragment.this.getActivity());
      }
    });
    mViewDataBinding.ietEmail.setmTextChangedListenner(new InlineTipEditText.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        mViewModel.mLoginLiveData.getValue().setAccount(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }


      }
    });


    mViewDataBinding.llPassword.setmTextChangedListenner(new InlineTipEditText.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        mViewModel.mLoginLiveData.getValue().setPassword(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }

      }
    });

  }

  private void addAgreementTipView() {
    ToolTip toolTip = new ToolTip()
      .withText(LanguageStrings.getLoginCheckagreement())
      .withColor(Color.WHITE)
      .withShadow();
    mRedToolTipView = mViewDataBinding.activityMainTooltipframelayout.showToolTipForView(toolTip, mViewDataBinding.rbRead);
    mRedToolTipView.setOnToolTipViewClickedListener(this);

  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }


  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends LoginViewModel> getViewModelClass() {
    return LoginViewModel.class;
  }


  @Override
  public void onToolTipViewClicked(ToolTipView toolTipView) {

  }

  public void gotoForgetPassword(LoginUiState uiState) {
    sendForgetButtonClick();
    Bundle bundle = new Bundle();
    bundle.putString(KEY_EMAIL, uiState.getAccount());
    goToNextFragment(R.id.forgetPasswordFragment, bundle);
  }

  public static void goToUserAgreement() {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.tvUserAgreement)
      .withInt(KEY_TO, R.id.userAgreementFragment)
      .withString(KEY_URL, "https://egopowerplus.com")
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

  }

  public static void goToUserPolicy() {

    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.tvPrivacvPolicy)
      .withInt(KEY_TO, R.id.userAgreementFragment)
      .withString(KEY_URL, "https://egopowerplus.com")
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

  }


  @Override
  public void onResume() {
    super.onResume();
    initInlineVerifyHelper();
  }

  public void onClickEvent(LoginUiState loginUiState) {
    if (ButtonClickUtils.isFastClick()) {
      return;
    }
    if (isAdded()) {
      ProgressHelper.showProgressView(this.getActivity(), 0, true);
    }
    sendLoginButtonClick();
    mViewModel.onClickEvent(loginUiState);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  public void onDetach() {
    super.onDetach();
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
    AutoSizeCompat.autoConvertDensityBaseOnHeight(super.getResources(), 1334);
    AutoSizeCompat.autoConvertDensityBaseOnWidth(super.getResources(), 750);
    return super.onCreateView(inflater, container, savedInstanceState);
  }

  private void sendAgreementButtonClickClick() {
    sendClickTrace(this.getContext(), "2", "2", pageResouce, "1", "1");
  }

  private void sendLoginButtonClick() {
    sendClickTrace(this.getContext(), "2", "2", pageResouce, "2", "1");

  }

  private void sendForgetButtonClick() {
    sendBaseTraceClick("4");
  }

  @Override
  public void onStop() {
    super.onStop();
    try {
      mInlineVerificationHelper.clear();
      mInlineVerificationHelper = null;


      mViewDataBinding.ietEmail.setmTextChangedListenner(null);
      mViewDataBinding.ietEmail.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietEmail.clear();


      mViewDataBinding.llPassword.setmTextChangedListenner(null);
      mViewDataBinding.llPassword.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.llPassword.clear();
    } catch (Exception e) {

    }
  }


  public static class UserAgreementClickableSpan extends ClickableSpan {
    @Override
    public void onClick(@NonNull View view) {
      goToUserAgreement();
    }
  }



  public static class UserPricyClickableSpan extends ClickableSpan {
    @Override
    public void onClick(@NonNull View view) {
      goToUserPolicy();
    }
  }

  public class CrmWebClickableSpan extends ClickableSpan {
    @Override
    public void onClick(@NonNull View view) {
      goToCrmWeb();
    }

    @Override
    public void updateDrawState(@NonNull TextPaint ds) {
      super.updateDrawState(ds);
      ds.setColor(getResources().getColor(R.color.colorButtonNormal));
      ds.setUnderlineText(false);
    }
  }

  private void goToCrmWeb() {
    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(getString(R.string.app_logineu_linkurl_textview_text)));
    requireActivity().startActivity(intent);
  }



  private void goToCheckVerifyCode(LoginUiState uiSate) {
    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.loginFragment);
    bundle.putSerializable(REGISTER_DATA, uiSate);
    controller.navigate(R.id.checkVerifyCodeFragment, bundle);
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    mRedToolTipView = null;
  }

  private void initTrace() {
    stayEleId = "5";
    pageId = "2";
    mouduleId = "2";
    nextButtoneleid = "2";
  }

  private void trace_login_button_click_result(String result){
    String module_id = "2";
    String page_id = "2";
    String ele_id = "6";
    String key_status = "status";
    Map<String,String> expand = new HashMap();
    expand.put(key_status,result);

    sendClickTraceNew(this.getContext(), module_id, page_id, pageResouce, ele_id, expand);
  }
}
