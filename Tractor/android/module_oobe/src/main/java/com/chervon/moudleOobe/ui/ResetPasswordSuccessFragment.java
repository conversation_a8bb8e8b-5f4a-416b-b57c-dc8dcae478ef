package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_EMAIL;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.blankj.utilcode.util.KeyboardUtils;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentPasswordResetSuccessBinding;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.viewmodel.ForgetPasswordViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: ForgetPasswordFragment
 * @Description: Login module's forgotten password acquisition verification code interface
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:00
 * @UpdateRemark:
 * @Version: 1.0
 */
public class ResetPasswordSuccessFragment extends BaseEnhanceFragment<ForgetPasswordViewModel, MoudleOobeFragmentPasswordResetSuccessBinding> implements  BaseEnhanceFragment.OnkeyBackListener {
    public static final String TAG = "ForgetPasswordFragment";


    @Override
    protected void initDatas(Bundle savedInstanceState) {
      initTrace();
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.getCurrentDestination().setLabel("ResetPasswordSuccessFragment");
        KeyboardUtils.hideSoftInput(ResetPasswordSuccessFragment.this.getActivity());
        ForgetPasswordUiSate uiState = new ForgetPasswordUiSate();
        Bundle bundle = getArguments();
        if (null!=bundle){
            String email = bundle.getString(KEY_EMAIL);
            uiState.setAccount(email);
        }
        mViewDataBinding.setUser(uiState);
        mViewDataBinding.setPresenter(this);
        mViewDataBinding.tvTitle.setText(LanguageStrings.appForgetpasswordsuccessMaintext());
    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_oobe_fragment_password_reset_success;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding = (MoudleOobeFragmentPasswordResetSuccessBinding) viewDataBinding;

    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class<? extends ForgetPasswordViewModel> getViewModelClass() {
        return ForgetPasswordViewModel.class;
    }

    @SuppressLint("RestrictedApi")
    public void goLoginPage(ForgetPasswordUiSate uiState) {
       sendButtonClick();
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        bundle.putString(KEY_EMAIL, uiState.getAccount());
        controller.navigate(R.id.action_forgetResetSuccessFragment_to_loginFragment, bundle);


    }

  private void initTrace() {
    stayEleId="3";
    pageId="6";
    mouduleId="3";
    nextButtoneleid ="2";
  }


  private void sendButtonClick() {
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, nextButtoneleid,"1");
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, "1","1");
    return false;
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }
}
