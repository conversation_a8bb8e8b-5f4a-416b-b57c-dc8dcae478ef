package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.KEY_URL;
import static com.chervon.moudleOobe.ui.CheckVerifyCodeFragment.GET_VERIFICATION_CODE_SUCCESS;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.LoginFragment.LOGIN_PASSWORD_DIFF;
import static com.chervon.moudleOobe.ui.LoginFragment.LOGIN_PASSWORD_ERROR;
import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_TO_LOGIN_ELE_ID;
import static com.chervon.moudleOobe.utils.Constants.URL_USER_PRIVACY_AND_AGREEMENT;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.UnderlineSpan;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.InlineVerificationHelperEu;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentRegisterEuThreeBinding;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.RegisterEuViewModel;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTip;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTipView;

import me.jessyan.autosize.AutoSize;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: RegisterEuOneFragment
 * @Description: 欧洲第三步
 * @Author: wuxd
 * @CreateDate: 2024/6/12
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @UpdateRemark
 * @Version: 1.0
 */
public class RegisterEuThreeFragment extends BaseEnhanceFragment<RegisterEuViewModel, MoudleOobeFragmentRegisterEuThreeBinding> {
  public static final String TAG = "RegisterEuThreeFragment";
  public static final int REGISTER_SUCCESS = 1;
  public static final int REGISTER_FAIL = 2;
  public static final int OTHER_STATE = -1;
  public static final int ERROR_EMAIL = 5;
  public static final int ACCOUNT_REGISTED = *********;
  public static final int VERIFYCODE_SEND_TOO_MUCH = *********;
  public static final String REGISTER_DATA = "register_data";
  private ToolTipView mRedToolTipView;
  private InlineVerificationHelperEu mInlineVerificationHelper;
  private int mClickNum;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    Bundle bundle = getArguments();
    RegisterUiState registerUiState;
    if(null != bundle){
      registerUiState = (RegisterUiState) bundle.getSerializable(REGISTER_DATA);
    } else {
      registerUiState = new RegisterUiState();
    }
    if (registerUiState==null) {
      registerUiState = new RegisterUiState();
    }
    registerUiState.setAgreementChecked(false);
    mViewModel.mRegisterUiState.setValue(registerUiState);
    mViewDataBinding.setUiState(registerUiState);
    mViewDataBinding.setPresenter(mViewModel);
    mViewDataBinding.setController(this);
    mViewDataBinding.ietStreet.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      uiState.setStreet(s);
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.checkInlineAccount();
      }
    });
    mViewDataBinding.ietCity.setmTextChangedListenner(s -> {
      RegisterUiState uiSate = mViewDataBinding.getUiState();
      uiSate.setCity(s);
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.checkInlineAccount();
      }
    });
    mViewDataBinding.ietCounty.setmTextChangedListenner(s -> {
      RegisterUiState uiSate = mViewDataBinding.getUiState();
      uiSate.setCounty(s);
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.checkInlineAccount();
      }
    });
    mViewDataBinding.ietPostCode.setmTextChangedListenner(s -> {
      RegisterUiState uiSate = mViewDataBinding.getUiState();
      uiSate.setPostcode(s);
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.checkInlineAccount();
      }
    });
  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_register_eu_three;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mInlineVerificationHelper = new InlineVerificationHelperEu(mViewDataBinding.btnNext);
    mInlineVerificationHelper
      .buildStreet(mViewDataBinding.ietStreet)
      .buildCity(mViewDataBinding.ietCity)
      .buildCounty(mViewDataBinding.ietCounty)
      .buildPostcode(mViewDataBinding.ietPostCode)
      .buildAgreementChecker(mViewDataBinding.rbRead);;
    mViewDataBinding.clContainer.setOnClickListener(v -> {
      v.clearFocus();
      KeyboardUtils.hideSoftInput(RegisterEuThreeFragment.this.getActivity());
    });
    addRedToolTipView();

    mViewDataBinding.rbRead.setOnCheckedChangeListener((buttonView, isChecked) -> {
      if (isChecked) {
        mViewDataBinding.activityMainTooltipframelayout.removeAllViews();
        sendAgreementClick();
      } else {
        addRedToolTipView();
      }
      mViewDataBinding.getUiState().setAgreementChecked(isChecked);
      mViewDataBinding.clContainer.clearFocus();
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.checkInlineAccount();
      }
    });

    mViewModel.mSendEmailLivedata.observe(this, uiSate -> {
      ProgressHelper.hideProgressView(RegisterEuThreeFragment.this.getActivity());
      if (uiSate.getState() == GET_VERIFICATION_CODE_SUCCESS) {
        goToCheckVerifyCode(mViewDataBinding.getUiState());
      } else if (uiSate.getState() == VERIFYCODE_SEND_TOO_MUCH) {
        ToastUtils.showLong(LanguageStrings.app_verifycode_sendtoomuch_text());
      } else if (uiSate.getState() != INIT_STATE) {
        if (!TextUtils.isEmpty(uiSate.getRespondMessage())) {
          DialogUtil.showToastDialog(RegisterEuThreeFragment.this.getContext(), R.layout.toast_layout, uiSate.getRespondMessage(), ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
      }
      uiSate.setState(INIT_STATE);

    });
    mViewModel.mRegisterUiState.observe(this, uiSate -> {
      if (uiSate.getState() == ERROR_EMAIL) {
        ToastUtils.showLong(LanguageStrings.app_base_email_format_text());
      } else if (uiSate.getState() == LOGIN_PASSWORD_ERROR) {

      } else if (uiSate.getState() == LOGIN_PASSWORD_DIFF) {
        ToastUtils.showLong(LanguageStrings.app_password_comfirm_mismatch_text());
      } else if (uiSate.getState() != INIT_STATE) {
        if (!TextUtils.isEmpty(uiSate.getRespondMessage())) {
          ToastUtils.showLong(uiSate.getRespondMessage());
        }
      }

      uiSate.setState(INIT_STATE);
    });

    initAgreementView();
  }

  private void goToCheckVerifyCode(RegisterUiState uiSate) {
    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.registerEuThreeFragment);
    bundle.putSerializable(REGISTER_DATA, uiSate);
    controller.navigate(R.id.checkVerifyCodeFragment, bundle);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends RegisterEuViewModel> getViewModelClass() {
    return RegisterEuViewModel.class;
  }

  private void addRedToolTipView() {
    ToolTip toolTip = new ToolTip()
      .withText(LanguageStrings.getLoginAgreementprivacy())
      .withColor(Color.WHITE)
      .withShadow();
    mRedToolTipView = mViewDataBinding.activityMainTooltipframelayout.showToolTipForView(toolTip, mViewDataBinding.rbRead);

  }

  public void goToLoginPage(RegisterUiState uiSate) {
    sendBaseTraceClick(TRACE_REGISTER_EU_TO_LOGIN_ELE_ID);
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    controller.navigate(R.id.loginFragment);
  }

  public void goToUserAgreement(RegisterUiState uiState) {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.tvUserAgreement)
      .withInt(KEY_TO, R.id.userAgreementFragment)
      .withString(KEY_URL, URL_USER_PRIVACY_AND_AGREEMENT)
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
  }

  public void goToUserPricy(RegisterUiState uiState) {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.tvPrivacvPolicy)
      .withInt(KEY_TO, R.id.userAgreementFragment)
      .withString(KEY_URL, URL_USER_PRIVACY_AND_AGREEMENT)
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

  }

  public void onClickEvent(RegisterUiState uiSate) {
    sendButtonClick();
    if (mClickNum == 0) {
      mViewModel.onClickEvent(uiSate);
      if (isAdded()) {
        ProgressHelper.showProgressView(this.getActivity(), 0);
      }
      mClickNum = 1;
    }

    mViewDataBinding.rbRead.postDelayed(new Runnable() {
      @Override
      public void run() {
        mClickNum = 0;
      }
    }, 2000);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  private void initAgreementView() {
    String andStr = " " + LanguageStrings.getUseragreementAnd() + " ";
    SpannableStringBuilder style = new SpannableStringBuilder();
    style.append(LanguageStrings.getUseragreementPart());
    style.append(LanguageStrings.getUseragreement());
    style.append(andStr);
    style.append(LanguageStrings.getPrivacyPolicy());


    ClickableSpan clickableSpan = new ClickableSpan() {
      @Override
      public void onClick(@NonNull View view) {
        goToUserAgreement(mViewDataBinding.getUiState());
      }
    };


    ClickableSpan clickableSpan2 = new ClickableSpan() {
      @Override
      public void onClick(@NonNull View view) {
        goToUserPricy(mViewDataBinding.getUiState());

      }
    };


    style.setSpan(clickableSpan2, style.length() - LanguageStrings.getPrivacyPolicy().length(), style.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(clickableSpan, LanguageStrings.getUseragreementPart().length(), LanguageStrings.getUseragreementPart().length()
      + LanguageStrings.getUseragreement().length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
    style.setSpan(new UnderlineSpan() {
      @Override
      public void updateDrawState(@NonNull TextPaint ds) {
        super.updateDrawState(ds);
        ds.setColor(getResources().getColor(R.color.colorButtonNormal));
        ds.setUnderlineText(false);//去掉下划线
      }
    }, LanguageStrings.getUseragreementPart().length(), LanguageStrings.getUseragreementPart().length()
      + LanguageStrings.getUseragreement().length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(new UnderlineSpan() {
      @Override
      public void updateDrawState(@NonNull TextPaint ds) {
        super.updateDrawState(ds);
        ds.setColor(getResources().getColor(R.color.colorButtonNormal));
        ds.setUnderlineText(false);//去掉下划线
      }
    }, style.length() - LanguageStrings.getPrivacyPolicy().length(), style.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    mViewDataBinding.tvAgreement.setMovementMethod(LinkMovementMethod.getInstance());
    mViewDataBinding.tvAgreement.setText(style);
  }

  @Override
  public void onResume() {
    super.onResume();
    AutoSize.autoConvertDensityOfGlobal(ActivityUtils.getTopActivity());
  }

  private void initTrace() {
    stayEleId = "4";
    pageId = "7";
    mouduleId = "4";
    pageResouce = "1_4_7";
    nextButtoneleid = "3";
  }

  private void sendAgreementClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "2", "1");
  }

  private void sendButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }


  @Override
  public void onDestroy() {
    try {
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.clear();
        mInlineVerificationHelper = null;
      }
      mViewDataBinding.ietStreet.setmTextChangedListenner(null);
      mViewDataBinding.ietStreet.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietStreet.clear();


      mViewDataBinding.ietCounty.setmTextChangedListenner(null);
      mViewDataBinding.ietCounty.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietCounty.clear();

      mViewDataBinding.ietCity.setmTextChangedListenner(null);
      mViewDataBinding.ietCity.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietCity.clear();

      mViewDataBinding.ietPostCode.setmTextChangedListenner(null);
      mViewDataBinding.ietPostCode.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietPostCode.clear();
    } catch (Exception e) {
      LogUtils.i(TAG, "error");
    }
    super.onDestroy();
  }
}
