package com.chervon.moudleOobe.ui;

import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.utils.Constants.EMPTY;
import static com.chervon.moudleOobe.utils.Constants.TRACE_REGISTER_EU_TO_LOGIN_ELE_ID;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.inputmethod.EditorInfo;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.InlineTipEditTextEu;
import com.chervon.libBase.ui.widget.InlineVerificationHelperEu;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentRegisterEuTwoBinding;
import com.chervon.moudleOobe.ui.state.RegisterUiState;
import com.chervon.moudleOobe.ui.viewmodel.RegisterEuViewModel;
import com.chervon.moudleOobe.ui.widget.supertooltip.ToolTipView;

import me.jessyan.autosize.AutoSize;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: RegisterEuOneFragment
 * @Description: 欧洲第二步
 * @Author: wuxd
 * @CreateDate: 2024/6/12
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @UpdateRemark
 * @Version: 1.0
 */
public class RegisterEuTwoFragment extends BaseEnhanceFragment<RegisterEuViewModel, MoudleOobeFragmentRegisterEuTwoBinding> {
  public static final String TAG = "RegisterEuTwoFragment";
  public static final int REGISTER_SUCCESS = 1;
  public static final int REGISTER_FAIL = 2;
  public static final int OTHER_STATE = -1;
  public static final int ERROR_EMAIL = 5;
  public static final int ACCOUNT_REGISTED = *********;
  public static final int VERIFYCODE_SEND_TOO_MUCH = *********;
  /**
   *  邮箱已经存在
   */
  public static final int ACCOUNT_CHECK_EXIST = **********;
  /**
   * 邮箱邮箱格式异常
   */
  public static final int ACCOUNT_CHECK_FORMAT_ERROR = **********;
  /**
   * 邮箱验证通过-App自定义
   */
  public static final int ACCOUNT_CHECK_RIGHT = 100133;
  public static final String REGISTER_DATA = "register_data";
  private ToolTipView mRedToolTipView;
  private InlineVerificationHelperEu mInlineVerificationHelper;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    Bundle bundle = getArguments();
    RegisterUiState registerUiState;
    if(null != bundle){
      registerUiState = (RegisterUiState) bundle.getSerializable(REGISTER_DATA);
    } else {
      registerUiState = new RegisterUiState();
    }
    if (registerUiState==null) {
      registerUiState = new RegisterUiState();
      registerUiState.setEmail(EMPTY);
      registerUiState.setFirstName(EMPTY);
      registerUiState.setLastName(EMPTY);
      registerUiState.setPhone(EMPTY);
      registerUiState.setPassword(EMPTY);
      registerUiState.setConfirmPassword(EMPTY);
    }
    registerUiState.setCity(EMPTY);
    registerUiState.setStreet(EMPTY);
    registerUiState.setCounty(EMPTY);
    registerUiState.setPostcode(EMPTY);
    mViewModel.mRegisterUiState.setValue(registerUiState);
    mViewDataBinding.setUiState(registerUiState);
    mViewDataBinding.setPresenter(mViewModel);
    mViewDataBinding.setController(this);
    initObserver();
    mInlineVerificationHelper = new InlineVerificationHelperEu(mViewDataBinding.btnNext);
    mInlineVerificationHelper
            .buildPasswordChecker(mViewDataBinding.ietPassword)
            .buildPasswordCheckerWithSameChecker2(mViewDataBinding.ietConfirmPassword, mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
//            .buildPasswordSameChecker(mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
            .buildFirstName(mViewDataBinding.ietFirstName)
            .buildLastName(mViewDataBinding.ietLastName)
            .buildEmailExistChecker();
    mInlineVerificationHelper.setEmailCheckResult(registerUiState.isRegister());
    if(!TextUtils.isEmpty(registerUiState.getEmail())) {
      mViewDataBinding.btnNext.setEnabled(true);
    } else {
      mViewDataBinding.btnNext.setEnabled(false);
    }
    mViewDataBinding.ietFirstName.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      if(!s.equals(uiState.getFirstName())) {
        uiState.setFirstName(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.ietFirstName.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietFirstName.clearFocus();
      }
      return true;
    });

    mViewDataBinding.ietLastName.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      if(!s.equals(uiState.getLastName())){
        uiState.setLastName(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.ietLastName.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietLastName.clearFocus();
      }
      return true;
    });

    mViewDataBinding.ietTmail.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.setEmail(s);
        if(!s.equals(uiState.getEmail())) {
          mInlineVerificationHelper.setEmailCheckResult(false);
          uiState.setRegister(false);
          uiState.setEmail(s);
          mInlineVerificationHelper.checkInline();
        }
      }
    });

    mViewDataBinding.ietTmail.setEditTextOnFocusChangeListener((view, b) -> {
      if(!b) {
        if (!CommonUtils.checkEmail(mViewDataBinding.ietTmail.getInlineTipEditTextString())) {
          mViewDataBinding.ietTmail.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, LanguageStrings.getEnterCorrectMailbox(), "", "");
        } else {
          mViewModel.checkEmailExist(mViewDataBinding.ietTmail.getInlineTipEditTextString());
        }
      }
    });
    mViewDataBinding.ietTmail.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietTmail.clearFocus();
      }
      return true;
    });

    mViewDataBinding.ietPhone.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      if(!s.equals(uiState.getPhone())) {
        uiState.setPhone(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.ietPhone.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietPhone.clearFocus();
      }
      return true;
    });
    mViewDataBinding.ietPassword.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      if(!s.equals(uiState.getPassword())) {
        uiState.setPassword(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.ietPassword.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietPassword.clearFocus();
      }
      return true;
    });
    mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(s -> {
      RegisterUiState uiState = mViewDataBinding.getUiState();
      if(!s.equals(uiState.getConfirmPassword())){
        uiState.setConfirmPassword(s);
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInline();
        }
      }
    });
    mViewDataBinding.ietConfirmPassword.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietConfirmPassword.clearFocus();
      }
      return true;
    });
  }

  private void initObserver() {
    mViewModel.mRegisterUiState.observe(this, uiState -> {
      if (uiState.getState() == ACCOUNT_CHECK_EXIST) {
        if (mInlineVerificationHelper == null) {
          return;
        }
        uiState.setRegister(false);
        mInlineVerificationHelper.setEmailCheckResult(false);
        mViewDataBinding.ietTmail.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, LanguageStrings.app_regist_emailisexists_textview_text(), "", "");
        mInlineVerificationHelper.checkInline();
      } else if (uiState.getState() == ACCOUNT_CHECK_FORMAT_ERROR) {
        if (mInlineVerificationHelper == null) {
          return;
        }
        uiState.setRegister(false);
        mInlineVerificationHelper.setEmailCheckResult(false);
        if (!TextUtils.isEmpty(uiState.getMessage())) {
          mViewDataBinding.ietTmail.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, uiState.getMessage(), "", "");
        }
        mInlineVerificationHelper.checkInline();
      } else if (uiState.getState() == ACCOUNT_CHECK_RIGHT) {
        if (mInlineVerificationHelper == null) {
          return;
        }
        uiState.setRegister(true);
        mInlineVerificationHelper.setEmailCheckResult(true);
        mViewDataBinding.ietTmail.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, "", "", "");
        mInlineVerificationHelper.checkInline();

      } else if(INIT_STATE != uiState.getState()){
        if (!TextUtils.isEmpty(uiState.getRespondMessage())) {
          ToastUtils.showLong(uiState.getRespondMessage());
        }
      }
      uiState.setState(INIT_STATE);
    });
  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_register_eu_two;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding.clContainer.setOnClickListener(v -> {
      v.clearFocus();
      KeyboardUtils.hideSoftInput(RegisterEuTwoFragment.this.getActivity());
    });
    mViewDataBinding.clContent.setOnFocusChangeListener((v, hasFocus) -> {
      if(hasFocus && null != getActivity()) {
        KeyboardUtils.hideSoftInput(getActivity());
      }
    });
  }

  private void goToStepThree(RegisterUiState uiSate) {
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_PREV_FRAGMENT, R.id.registerEuTwoFragment);
    bundle.putSerializable(REGISTER_DATA, uiSate);
    controller.navigate(R.id.registerEuThreeFragment, bundle);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends RegisterEuViewModel> getViewModelClass() {
    return RegisterEuViewModel.class;
  }

  public void goToLoginPage(RegisterUiState uiSate) {
    sendBaseTraceClick(TRACE_REGISTER_EU_TO_LOGIN_ELE_ID);
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    controller.navigate(R.id.loginFragment);
  }

  public void onClickEvent(RegisterUiState uiSate) {
    goToStepThree(uiSate);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  public void onResume() {
    super.onResume();
    AutoSize.autoConvertDensityOfGlobal(ActivityUtils.getTopActivity());
  }

  private void initTrace() {
    stayEleId = "4";
    pageId = "7";
    mouduleId = "4";
    pageResouce = "1_4_7";
  }

  @Override
  public void onDestroy() {
    try {
      if (mInlineVerificationHelper != null) {
        mInlineVerificationHelper.clear();
        mInlineVerificationHelper = null;
      }
      mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(null);
      mViewDataBinding.ietConfirmPassword.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietConfirmPassword.clear();


      mViewDataBinding.ietPassword.setmTextChangedListenner(null);
      mViewDataBinding.ietPassword.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietPassword.clear();

      mViewDataBinding.ietTmail.setmTextChangedListenner(null);
      mViewDataBinding.ietTmail.setEditTextOnFocusChangeListener(null);
      mViewDataBinding.ietTmail.clear();
    } catch (Exception e) {
      LogUtils.i(TAG, "error");
    }
    super.onDestroy();
  }

  public void clearFocus(){
    mViewDataBinding.ietTmail.clearFocus();
  }
}
