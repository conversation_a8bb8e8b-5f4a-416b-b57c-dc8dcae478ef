package com.chervon.moudleOobe.ui;


import static com.chervon.libBase.utils.APPConstants.MESSAGE_DATA_JSON;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_KEY_CREATETIME;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_KEY_MESSAGE_TYPE;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_KEY_UUID;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_RUTE_PATH;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_DEVICE;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_FEEDBACK;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_OFFER;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_SHARE_DEVICE;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_SYSTEM;
import static com.chervon.libBase.utils.APPConstants.DEVICE_SHARE_TYPE_ACCEPT;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_MAIN_REMOVE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_MAIN_SHARE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_SUB_ACCEPT;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_SUB_DELETE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_SUB_REMOVE;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_REGIST;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEID;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_CREATE_TIME;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_MESSAGETYPE;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_UUID;
import static com.chervon.libRouter.RouterConstants.KEY_ROUTER_PATH;
import static com.chervon.libRouter.RouterConstants.KEY_SHARE_SELECT_POSITION;
import static com.chervon.libRouter.RouterConstants.MESSAGE_DATA_KEY;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.GsonUtils;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.view.WindowCompat;
import androidx.databinding.ViewDataBinding;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.AppExitHelper;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.result.MessageListBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.BuildConfig;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentLoginHomeBinding;
import com.chervon.moudleOobe.ui.viewmodel.LoginHomePageViewModel;

import java.util.HashMap;
import java.util.List;

import io.reactivex.observers.DefaultObserver;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: LoginHomePageActivity
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/6/9 下午2:59
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/6/9 下午2:59
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_LOGIN_WELCOME)
public class LoginHomePageActivity extends BaseActivity<LoginHomePageViewModel> {
    private final String TAG = "LoginHomePageActivity";
    private MoudleOobeFragmentLoginHomeBinding mViewDataBinding;
    private AppExitHelper mAppExitHelper;
    private static final String MESSAGE_TYPE = "messageType";
    private static final String SHARE_OPERATE_TYPE = "shareOperateType";
    private static final String DEVICE_ID = "deviceId";
    private static final String UUID = "uuid";
    private static final String CREATE_TIME = "createTime";
    private static final String DEFAULT_NUM = "0";

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_oobe_fragment_login_home;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);


    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding = (MoudleOobeFragmentLoginHomeBinding) viewDataBinding;
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        //设置欧洲底部不显示文案----IOT-11094
        if (!BuildConfig.EVN.equals(Utils.NA_TAG)) {
            mViewDataBinding.oobeFragmentTvLoginguide.setVisibility(View.GONE);
        } else {
            mViewDataBinding.oobeFragmentTvLoginguide.setVisibility(View.VISIBLE);
        }

    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        mAppExitHelper = new AppExitHelper();
        mViewDataBinding.setPresenter(this);
        User user = UserInfo.getDataOnly();
        if (user != null) {
            String token = UserInfo.getDataOnly().getAccessToken();
            if (!TextUtils.isEmpty(token)) {
                Bundle bundle = this.getIntent().getExtras();
                if (bundle != null) {
                    dealWithTombstone(bundle);
                } else {
                    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                    finish();
                }
            }
        }

        mViewDataBinding.bunLogin.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goToLoginPage(view);
            }
        });

    }

    /**
     * 处理墓碑消息
     *
     * @param bundle
     */
    private void dealWithTombstone(Bundle bundle) {

        try {
            String messageContent = bundle.getString("messageDataJson");
            List<Activity> activityList = ActivityUtils.getActivityList();
            String currentActivity = "";
            if (activityList != null) {
                if (activityList.size() > 1) {
                    if (activityList.get(1) != null) {
                        currentActivity = activityList.get(1).getClass().toString();
                    }
                }
            }
            //根据消息类型去处理墓碑消息
            String messageType = bundle.getString(MESSAGE_KEY_MESSAGE_TYPE);

            String rutePath = bundle.getString(MESSAGE_RUTE_PATH);

            String createTime = bundle.getString(MESSAGE_KEY_CREATETIME);

            String uuid = bundle.getString(MESSAGE_KEY_UUID);

            //消息类型为空进首页
            if (TextUtils.isEmpty(messageType)) {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                return;
            }

            switch (messageType) {
                case MESSAGE_TYPE_SYSTEM:
                case MESSAGE_TYPE_OFFER:
                    //系统消息
                    if (!TextUtils.isEmpty(uuid)) {
                        MessageListBean.EntryDTO.ListDTO messageData = new MessageListBean.EntryDTO.ListDTO();

                        messageData.setCreateTime(Long.parseLong(createTime));
                        messageData.setMessageType(Integer.parseInt(messageType));
                        messageData.setUuid(uuid + "");
                        MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = new MessageListBean.EntryDTO.ListDTO.PayloadDataDTO();
                        payloadDataDTO.setCreateTime(messageData.getCreateTime() + "");
                        payloadDataDTO.setMessageType(messageData.getMessageType() + "");
                        payloadDataDTO.setUuid(messageData.getUuid());
                        messageData.setPayloadData(payloadDataDTO);

                        if (!TextUtils.isEmpty(rutePath)) {
                            tellServiceMessageRead(messageData);
                            getSystemWebView(rutePath);
                        } else {
                            //无外链去消息详情 ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL).withSerializable(KEY_PREV_DATA, messageData).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                            finish();
                        }

                    } else {
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                        finish();
                    }


                    break;
                case MESSAGE_TYPE_DEVICE:
                    //设备消息
                    if (!TextUtils.isEmpty(bundle.getString(KEY_DEVICEID))) {
                        // deviceId存在
                        if (!TextUtils.isEmpty(currentActivity) && currentActivity.contains("BuzActivity")) {
                            //在RN面板不做任何处理
                        } else {
                            //不在RN面板的跳转到RN面板
                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL).withString(KEY_DEVICEID, bundle.getString(KEY_DEVICEID)).withString(MESSAGE_DATA_JSON, messageContent).navigation();
                        }
                    } else {
                        // deviceId不存在--跳转消息详情
                        if (!TextUtils.isEmpty(bundle.getString(MESSAGE_KEY_UUID))) {
                            MessageListBean.EntryDTO.ListDTO messageData = new MessageListBean.EntryDTO.ListDTO();
                            messageData.setCreateTime(Long.parseLong(bundle.getString(MESSAGE_KEY_CREATETIME)));
                            messageData.setMessageType(Integer.parseInt(bundle.getString(MESSAGE_KEY_MESSAGE_TYPE)));
                            messageData.setUuid(bundle.getString(MESSAGE_KEY_UUID) + "");
                            MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = new MessageListBean.EntryDTO.ListDTO.PayloadDataDTO();
                            payloadDataDTO.setCreateTime(messageData.getCreateTime() + "");
                            payloadDataDTO.setMessageType(messageData.getMessageType() + "");
                            payloadDataDTO.setUuid(messageData.getUuid());
                            messageData.setPayloadData(payloadDataDTO);
                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL).withSerializable(KEY_PREV_DATA, messageData).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                        } else {
                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                        }
                    }
                    finish();
                    break;
                case MESSAGE_TYPE_FEEDBACK:
                    //反馈消息
                    MessageListBean.EntryDTO.ListDTO messageData = new MessageListBean.EntryDTO.ListDTO();
                    messageData.setCreateTime(Long.parseLong(createTime));
                    messageData.setMessageType(Integer.parseInt(messageType));
                    messageData.setUuid(uuid);
                    MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadFeedBack = new MessageListBean.EntryDTO.ListDTO.PayloadDataDTO();
                    payloadFeedBack.setUuid(uuid);
                    payloadFeedBack.setRutePath(rutePath);
                    payloadFeedBack.setMessageType(messageData.getMessageType() + "");
                    payloadFeedBack.setCreateTime(messageData.getCreateTime() + "");
                    messageData.setPayloadData(payloadFeedBack);
                    if (!TextUtils.isEmpty(currentActivity)) {
                        String FEED_BACK_ID = "feedbackId";
                        String payloadRutePath = payloadFeedBack.getRutePath();
                        String feedbackId = Uri.parse(payloadRutePath).getQueryParameter(FEED_BACK_ID);
                        String path = Uri.parse(rutePath).getPath();
                        ARouter.getInstance().build(path).withString(KEY_PREV_DATA, feedbackId).withString(KEY_PREV_CREATE_TIME, payloadFeedBack.getCreateTime()).withString(KEY_PREV_MESSAGETYPE, payloadFeedBack.getMessageType()).withString(KEY_PREV_UUID, uuid).navigation();
                    } else {
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withString(MESSAGE_DATA_KEY, GsonUtils.toJson(messageData)).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).withString(KEY_PREV_CREATE_TIME, payloadFeedBack.getCreateTime()).withString(KEY_PREV_MESSAGETYPE, payloadFeedBack.getMessageType()).withString(KEY_PREV_UUID, uuid).navigation();
                    }
                    finish();
                    break;
                case MESSAGE_TYPE_SHARE_DEVICE:
                  if (SHARE_OPERATE_TYPE_MAIN_SHARE.equals(bundle.getString(SHARE_OPERATE_TYPE)) ||
                          SHARE_OPERATE_TYPE_SUB_ACCEPT.equals(bundle.getString(SHARE_OPERATE_TYPE)) ||
                          SHARE_OPERATE_TYPE_SUB_DELETE.equals(bundle.getString(SHARE_OPERATE_TYPE)) ||
                          SHARE_OPERATE_TYPE_SUB_REMOVE.equals(bundle.getString(SHARE_OPERATE_TYPE)) ||
                          SHARE_OPERATE_TYPE_MAIN_REMOVE.equals(bundle.getString(SHARE_OPERATE_TYPE))) {
                    //消息详情
                    if (!TextUtils.isEmpty(bundle.getString(UUID))) {
                          MessageListBean.EntryDTO.ListDTO deviceShareMessageData = new MessageListBean.EntryDTO.ListDTO();
                          deviceShareMessageData.setCreateTime(Long.parseLong(null != createTime ? createTime : DEFAULT_NUM));
                          deviceShareMessageData.setMessageType(Integer.parseInt(null != messageType ? messageType : DEFAULT_NUM));
                          deviceShareMessageData.setUuid(bundle.getString(UUID) + "");
                          MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = new MessageListBean.EntryDTO.ListDTO.PayloadDataDTO();
                          payloadDataDTO.setCreateTime(deviceShareMessageData.getCreateTime() + "");
                          payloadDataDTO.setMessageType(deviceShareMessageData.getMessageType() + "");
                          payloadDataDTO.setUuid(deviceShareMessageData.getUuid());
                          payloadDataDTO.setRutePath(rutePath);
                          deviceShareMessageData.setPayloadData(payloadDataDTO);
                          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                                  .withSerializable(KEY_PREV_DATA, deviceShareMessageData).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                  .navigation();
                      } else {
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                      }
                    } else {
                      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                    }
                    finish();
                    break;
                default:
                    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                    finish();
                    break;
            }
        } catch (Exception e) {
            LogUtils.e(TAG, e.getMessage());
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withInt(KEY_FROM, R.id.loginFragment).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        }
    }

    private void tellServiceMessageRead( MessageListBean.EntryDTO.ListDTO messageData) {
        HashMap<String, Object> params = new HashMap<>();
        String messageType;
//        String MESSAGE_DEVICE = "2";
        params.put("createTime", messageData.getCreateTime());
        messageType = messageData.getMessageType() + "";
        if (null != messageData.getPayloadData() && null != messageData.getPayloadData().getMessageType()) {
            messageType = messageData.getPayloadData().getMessageType();
        }
        params.put("messageType", messageType);
        params.put("uuid", messageData.getUuid());

        ApiService.instance().getMessageDetail(params).subscribe(new DefaultObserver<HttpResponse<MessageListBean.EntryDTO.ListDTO>>() {
            @Override
            public void onNext(HttpResponse<MessageListBean.EntryDTO.ListDTO> messageModel) {
                if (messageModel.status){
                    LogUtils.i("消息已读成功");
                }
            }

            @Override
            public void onError(Throwable e) {
                LogUtils.i("消息已读失败");
            }

            @Override
            public void onComplete() {

            }
        });
    }


    @Override
    protected Class getViewModelClass() {
        return LoginHomePageViewModel.class;
    }

    public void goToLoginPage(View view) {
        sendBaseTraceClick("1");
        mViewDataBinding.bunLogin.postDelayed(new Runnable() {
            @Override
            public void run() {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN).withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP).navigation();

            }
        }, 200);
    }

    private void getSystemWebView(String rutePath){
        try {
            Uri webpage = Uri.parse(rutePath);
            Intent intent = new Intent(Intent.ACTION_VIEW, webpage);
            // 添加category来明确指定这是浏览器意图
            intent.addCategory(Intent.CATEGORY_BROWSABLE);
            // 设置包类型为浏览器
            intent.setDataAndType(webpage, "text/html");

            // 检查是否有应用可以处理此Intent
            if (intent.resolveActivity(getPackageManager()) != null) {
                startActivity(intent);
                // 启动浏览器后结束当前Activity
                finish();
            } else {
                // 如果没有找到可以处理的应用，回退到默认处理
                LogUtils.e(TAG, "No browser found to handle URL");
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                        .withInt(KEY_FROM, R.id.loginFragment)
                        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
                finish();
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "Error opening URL: " + e.getMessage());
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                    .withInt(KEY_FROM, R.id.loginFragment)
                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation();
            finish();
        }
    }

    public void goToSignUpPage(View view) {
        sendBaseTraceClick("2");
        mViewDataBinding.bunLogin.postDelayed(new Runnable() {
            @Override
            public void run() {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN).withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP).withString(KEY_ROUTER_PATH, ACTIVITY_URL_REGIST).navigation();
            }
        }, 200);
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            mAppExitHelper.appExit(this);
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            UiHelper.hideSystemUI(getWindow());
        }
    }

    private void initTrace() {
        stayEleId = "3";
        pageId = "159";
        mouduleId = "2";
        nextButtoneleid = "1";
    }

}
