package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.APPConstants.HTML_CONTAINTER;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.KEY_URL;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRIVACY_ID;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.PasswordConfirmFragment.RESPONSE_SUCCESS;

import android.content.res.Resources;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.data.model.UserAgreementDetail;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentUserAgreementBinding;
import com.chervon.moudleOobe.ui.state.AgreementUiState;
import com.chervon.moudleOobe.ui.viewmodel.UserAgreementViewModel;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: UserAgreementActivity
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/6/9 下午5:47
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/6/9 下午5:47
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT)
public class UserAgreementActivity extends BaseActivity<UserAgreementViewModel> {
  private MoudleOobeFragmentUserAgreementBinding mViewDataBinding;


  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_oobe_fragment_user_agreement;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (MoudleOobeFragmentUserAgreementBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    ProgressHelper.showProgressView(this, 0);
    mViewDataBinding.setPresenter(mViewModel);
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.appPrivacyTitle(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          finish();
          sendBackTrace();
        }
      }));
   /*     {
            Bundle bundle = getIntent().getExtras();
            if (bundle != null) {
                String url = bundle.getString(KEY_URL);
                if (!TextUtils.isEmpty(url)) {
                    mViewDataBinding.wvUserAgreement.loadUrl(url);
                }
            }
        }*/
    mViewModel.mLiveData.observe(this, new Observer<AgreementUiState>() {
      @Override
      public void onChanged(AgreementUiState uiState) {
        if (uiState.getState() == RESPONSE_SUCCESS) {
          ProgressHelper.hideProgressView(UserAgreementActivity.this);
          UserAgreementDetail detail = uiState.getUserAgreementDetail();
          //   String content = uiState.getContent();
          //  mViewDataBinding.wvUserAgreement.loadDataWithBaseURL(null,  test,"text/html;charset=utf-8","UTF-8",null);
          if (detail != null) {
            String content = detail.getContent();
            if (!TextUtils.isEmpty(content)) {
              String html = HTML_CONTAINTER.replace("#{content}", detail.getContent());
              runOnUiThread(new Runnable() {
                @Override
                public void run() {
                  mViewDataBinding.wvUserAgreement.loadDataWithBaseURL(null, html, "text/html;charset=utf-8", "UTF-8", null);
                  ;
                  //  mViewDataBinding.wvUserAgreement.loadUrl("https://www.baidu.com/");
                  //      mViewDataBinding.wvUserAgreement.requestLayout();
                  //     mViewDataBinding.wvUserAgreement.setWebViewClient(new WebViewClient());
                }
              });

            }
            mViewDataBinding.tvTitle.setText(detail.getTitle());

            String dataStr = "";
            if (detail.getUpdateTime() != null && detail.getUpdateTime().contains("-")) {
              dataStr = detail.getUpdateTime();
            } else {
              if (detail.getUpdateTime() != null) {
                String pattern = BuildConfig.EVN.contains(Utils.NA_TAG)?"MM/dd/yyyy":"dd/MM/yyyy";

                SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.CHINA);
                dataStr = sdf.format(new Date(Long.parseLong(detail.getUpdateTime())));
              }
            }

            int from = getIntent().getExtras().getInt(KEY_FROM);


            if (from == R.id.tvUserAgreement) {
              mViewDataBinding.tvDate.setText(LanguageStrings.privacyagreementuserDatetext() + " " + dataStr);
            } else if (from == R.id.tvPrivacvPolicy) {
              mViewDataBinding.tvDate.setText(LanguageStrings.privacyAgreementDetailDate() + " " + dataStr);
            } else if (from == R.id.usercenterLegalInfoPricy || from == R.id.usercenterLegalInfoUserAgreement) {
              Bundle bundle = getIntent().getExtras();
              String url = bundle.getString(KEY_URL);
              try {
                if ("PrivacyAgreement".equalsIgnoreCase(url)) {
                  mViewDataBinding.tvDate.setText(LanguageStrings.privacyAgreementDetailDate() + " " + dataStr);
                } else if ("UserAgreement".equalsIgnoreCase(url)) {
                  mViewDataBinding.tvDate.setText(LanguageStrings.privacyagreementuserDatetext() + " " + dataStr);

                }
              } catch (Exception e) {

              }


            }


            //   mViewDataBinding.tvDate.setText(Html.fromHtml(detail.getContent()));
          }
        } else if (uiState.getState() != RESPONSE_FAIL) {
          ProgressHelper.hideProgressView(UserAgreementActivity.this);
          if (!TextUtils.isEmpty(uiState.getMessage())) {
            ToastUtils.showLong(uiState.getMessage());
          }
        }

        uiState.setState(INIT_STATE);
      }
    });


    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      int fragmentId = getIntent().getExtras().getInt(KEY_PREV_FRAGMENT);
      int from = getIntent().getExtras().getInt(KEY_FROM);
      int pageTo = getIntent().getExtras().getInt(KEY_TO);
      String url = bundle.getString(KEY_URL);
      if (from == R.id.controlPanel) {
        pageId = "81";
        mouduleId = "9";
        String productId = bundle.getString(KEY_PREV_DATA);
        String prrivacyId = bundle.getString(PARAMETER_PRIVACY_ID);
        mViewModel.getDetail(productId, prrivacyId);
      } else if (from == R.id.tvUserAgreement || from == R.id.tvPrivacvPolicy) {
        mViewModel.getLastAgreement(from);
      } else if (from == R.id.usercenterLegalInfoPricy || from == R.id.usercenterLegalInfoUserAgreement) {
        if ("PrivacyAgreement".equalsIgnoreCase(url)) {
          initLegalInfoTraceData();
        } else if ("UserAgreement".equalsIgnoreCase(url)) {

          initAgreeMentTraceData();
        }

        mViewModel.getAgreedAgreement(from);
      }

    }
  }

  private void initLegalInfoTraceData() {
    stayEleId = "2";
    pageId = "98";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  private void initAgreeMentTraceData() {
    stayEleId = "2";
    pageId = "99";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }


  @Override
  protected Class<? extends UserAgreementViewModel> getViewModelClass() {
    return UserAgreementViewModel.class;
  }

  @Override
  public void onStop() {
    super.onStop();

  }

  @Override
  public void onDestroy() {
    super.onDestroy();

  }

  @Override
  public Resources getResources() {
    //需要升级到 v1.1.2 及以上版本才能使用 AutoSizeCompat
    AutoSizeCompat.autoConvertDensityOfGlobal((super.getResources()));
    return super.getResources();
  }


  private void initTrace() {
    stayEleId = "5";
    pageId = "80";
    mouduleId = "9";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }


}
