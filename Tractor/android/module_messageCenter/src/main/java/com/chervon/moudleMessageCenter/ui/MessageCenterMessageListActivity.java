package com.chervon.moudleMessageCenter.ui;

import static com.blankj.utilcode.util.ActivityUtils.startActivity;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libRouter.RouterConstants.REQUEST_CODE;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.ui.widget.refresh.MessageListPullRefreshLayout;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.result.BaseResult;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libNetwork.http.model.result.MessageListBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleMessageCenter.R;
import com.chervon.moudleMessageCenter.databinding.MessagecenterActivityMessagelistBinding;
import com.chervon.moudleMessageCenter.databinding.MessagecenterActivityMessagelistItemBinding;
import com.chervon.moudleMessageCenter.ui.viewmodel.MessageCenterMessageListViewModel;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.disposables.Disposable;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.ui
 * @ClassName: MessageCenterMessageListActivity
 * @Description: 消息通知列表页
 * @Author: langmeng
 * @CreateDate: 2022/9/1 13:59
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/1 13:59
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_MESSAGE_LIST)
public class MessageCenterMessageListActivity extends BaseActivity<MessageCenterMessageListViewModel> {

    private static final String TAG = "MessageCenterMessageListActivity";

    private static final int DETAIL_RESULT_CODE = 2;
    public static final String POSITION_MESSAGE = "message_postion";
    private static final int REQUEST_CODE_MESSAGE_LIST_TO_MESSAGE_DETAIL = 1;
    @Autowired
    public int messageType;
    @Autowired
    public String deviceId;
    @Autowired
    public String productId;
    @Autowired
    public String deviceName;
    private MessagecenterActivityMessagelistBinding binding;
    private List<MessageListBean.EntryDTO.ListDTO> dataList = new ArrayList<>();
    private LoadMoreAdapter mLoadMoreAdapter;
    private WeakReference<LoadingDialog> loadingDialogWeakReference;
    private long mCurrentPageTimepageTime;
    //0系统消息，1营销消息, 2设备消息 3反馈
    private final int SYSTEM_MESSAGE_TYPE = 0;
    private final int MARKING_MESSAGE_TYPE = 1;
    private final int DEVICE_MESSAGE_TYPE = 2;
    private final int CUSTOMER_MESSAGE_TYPE = 3;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.messagecenter_activity_messagelist;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (MessagecenterActivityMessagelistBinding) viewDataBinding;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        String title = "";

        switch (messageType) {
            //系统消息
            case 0:
                title = LanguageStrings.app_messagecenter_systemmessage_textview_text();
                break;
            //营销消息
            case 1:
                title = LanguageStrings.app_messagecenter_marketingmessage_textview_text();
                break;
            //设备消息
            default:
                title = deviceName;
                break;
        }
        mViewModel.toolbarData.setValue(new ToolbarData(
                title,
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        sendBackTrace();
                        finish();
                    }
                }));


        binding.setMessageCenterMessageListViewModel(mViewModel);

        initAdapter();


        binding.messagePullRefresh.setOnRefreshListener(new MessageListPullRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                binding.messagePullRefresh.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        binding.messagePullRefresh.setRefreshing(false);
                    }
                }, 1000);
            }
        });
        binding.messagePullRefresh.setOnLoadListener(new MessageListPullRefreshLayout.OnLoadListener() {
            @Override
            public void onLoad() {
                binding.messagePullRefresh.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        binding.messagePullRefresh.setLoading(false);
                    }
                }, 1000);
            }
        });

        getMessagesFirstPage();
    }

    private void getMessagesFirstPage() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("messageType", messageType);
        if (messageType == 2) {
            params.put("deviceId", deviceId);
            params.put("productId", productId);
        }
        dataList.clear();

        loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));
        getMessages(params);
    }

    private void getMessages(HashMap<String, Object> params) {
        ApiService.instance().getMessageList(params).subscribe(new HttpObserver<MessageListBean>() {
            @Override
            protected void Next(MessageListBean entity) {
                mLoadMoreAdapter.setLoadState(mLoadMoreAdapter.LOADING_COMPLETE);
                if (entity.getEntry().getList() != null && entity.getEntry().getList().size() > 0) {

                    dataList.addAll(entity.getEntry().getList());
                    mViewModel.dataListLiveData.setValue(dataList);

                } else if ((entity.getEntry().getList() != null && entity.getEntry().getList().size() == 0) || entity.getEntry().getList() == null) {
                    mLoadMoreAdapter.setLoadState(mLoadMoreAdapter.LOADING_END);
                }
                if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
                    loadingDialogWeakReference.get().dismiss();
                }
                mLoadMoreAdapter.notifyDataSetChanged();
            }

            @Override
            public void Error(ErrorBean errorBean) {
                super.Error(errorBean);
                if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
                    loadingDialogWeakReference.get().dismiss();
                }
                mLoadMoreAdapter.setLoadState(mLoadMoreAdapter.LOADING_COMPLETE);
                mLoadMoreAdapter.notifyDataSetChanged();
                //         ToastUtils.showShort(errorBean.getErrorMsg());
            }
        });
    }

    @Override
    protected Class<? extends MessageCenterMessageListViewModel> getViewModelClass() {
        return MessageCenterMessageListViewModel.class;
    }

    /**
     * @return void
     * @method initAdapter
     * @description init adapter
     * @date: 2022/6/20 15:45
     * @author: LangMeng
     */
    private void initAdapter() {

        mLoadMoreAdapter = new LoadMoreAdapter(dataList);

        //init recyclerview
        LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
        binding.messagecenterActivityMessageListRecycler.setLayoutManager(linearLayoutManager);
        binding.messagecenterActivityMessageListRecycler.setAdapter(mLoadMoreAdapter);

        // 设置加载更多监听
        binding.messagecenterActivityMessageListRecycler.addOnScrollListener(new EndlessRecyclerOnScrollListener() {
            @Override
            public void onLoadMore() {
                mLoadMoreAdapter.setLoadState(mLoadMoreAdapter.LOADING);

                // if (dataList.size() < 800) {
                long pageTime = dataList.get(dataList.size() - 1).getCreateTime();
                if (pageTime == mCurrentPageTimepageTime) {
                    mLoadMoreAdapter.setLoadState(mLoadMoreAdapter.LOADING_END);
                    return;
                }
                // 模拟获取网络数据，延时1s
                HashMap<String, Object> params = new HashMap<>();
                params.put("messageType", messageType);
                params.put("createTime", dataList.get(dataList.size() - 1).getCreateTime());
                if (messageType == 2) {
                    params.put("deviceId", deviceId);
                    params.put("productId", productId);
                }
                getMessages(params);
                mCurrentPageTimepageTime = pageTime;

            }
        });


    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        sendBackTrace();
    }


    private void initTrace() {

        if (messageType == 0) {
            stayEleId = "5";
            pageId = "67";
            pageResouce = "1_9_65_67";
        } else if (messageType == 1) {
            stayEleId = "2";
            pageId = "68";
            pageResouce = "1_9_65_68";
        } else {
            stayEleId = "2";
            pageId = "66";
            pageResouce = "1_9_65_66";
        }

        mouduleId = "8";

        nextButtoneleid = "2";
    }

    /**
     * 点击设备消息详情埋点
     */
    private void traceDeviceMessageDetail() {
        String module_id = "8";
        String page_id = "66";
        String ele_id = "3";
        String pageRes = pageResouce;
        String key_deviceId = "deviceId";
        Map<String, String> expand = new HashMap();
        expand.put(key_deviceId, deviceId);
        sendClickTraceNew(MessageCenterMessageListActivity.this, module_id, page_id, pageRes, ele_id, expand);
    }

    /**
     * 点击系统消息详情埋点
     */
    private void traceSystemMessageDetail() {
        String module_id = "8";
        String page_id = "67";
        String ele_id = "3";
        String pageRes = pageResouce;
        String mod_id = "0";
        sendClickTraceNew(MessageCenterMessageListActivity.this, module_id, page_id, pageRes, ele_id, mod_id);
    }

    /**
     * 点击系统消息详情埋点
     */
    private void traceMarketingMessageDetail() {
        String module_id = "8";
        String page_id = "68";
        String ele_id = "3";
        String pageRes = pageResouce;
        String mod_id = "0";
        sendClickTraceNew(MessageCenterMessageListActivity.this, module_id, page_id, pageRes, ele_id, mod_id);
    }

    public class LoadMoreAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

        private List<MessageListBean.EntryDTO.ListDTO> dataList;

        // 普通布局
        private final int TYPE_ITEM = 1;
        // 脚布局
        private final int TYPE_FOOTER = 2;
        // 当前加载状态，默认为加载完成
        private int loadState = 2;
        // 正在加载
        public final int LOADING = 1;
        // 加载完成
        public final int LOADING_COMPLETE = 2;
        // 加载到底
        public final int LOADING_END = 3;

        public final int MESSAGE_READ = 1;
        public final int MESSAGE_UNREAD = 0;

        public LoadMoreAdapter(List<MessageListBean.EntryDTO.ListDTO> dataList) {
            this.dataList = dataList;
        }

        @Override
        public int getItemViewType(int position) {
            // 最后一个item设置为FooterView
            if (position + 1 == getItemCount()) {
                return TYPE_FOOTER;
            } else {
                return TYPE_ITEM;
            }
        }

        @Override
        public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            //进行判断显示类型，来创建返回不同的View
            if (viewType == TYPE_ITEM) {
                LayoutInflater inflater = LayoutInflater.from(parent.getContext());
                MessagecenterActivityMessagelistItemBinding viewDataBinding =
                        DataBindingUtil.inflate(inflater, R.layout.messagecenter_activity_messagelist_item, parent, false);
                return new ViewHolder(viewDataBinding);

            } else if (viewType == TYPE_FOOTER) {
                View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.base_progress_loading_messagelist_bottom, parent, false);
                TextView tvLoading = view.findViewById(R.id.tvLoading);
                tvLoading.setText(LanguageStrings.app_base_refreshheaderloading_textview_text());
                return new FootViewHolder(view);
            }
            return null;
        }

        @Override
        public void onBindViewHolder(RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position) {
            if (holder instanceof ViewHolder) {
                {
                    MessageListBean.EntryDTO.ListDTO listDTO = dataList.get(position);

                    ((ViewHolder) holder).viewDataBinding.setMessageListData(listDTO);
                    //消息时间格式化
                    if (listDTO.getCreateTime() != 0) {
                        TextView time = holder.itemView.findViewById(R.id.messagecenter_messagelist_item_time);
                        time.setText(Utils.getTimeForHour(listDTO.getCreateTime()));
                    }
                    holder.itemView.findViewById(R.id.redPoint).setVisibility(listDTO.getIfRead() == MESSAGE_UNREAD ? View.VISIBLE : View.GONE);
                    // ImageView imageView=    holder.itemView.findViewById(R.id.ivMessageWarn);
                    TextView tv_code = holder.itemView.findViewById(R.id.tvMessageWarn);
                    MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = listDTO.getPayloadData();
                    if (payloadDataDTO != null) {
                        String suggestionTitle = payloadDataDTO.getErrorCode();
                        if (TextUtils.isEmpty(suggestionTitle) || listDTO.getMessageType() != 2) {
                            tv_code.setVisibility(View.GONE);
                        } else {

                            if (TextUtils.isEmpty(payloadDataDTO.getErrorCode())) {
                                tv_code.setText("");
                            } else {
                                Spanned textCodeHtml = Html.fromHtml(
                                        "<font color=\"#666666\">" + LanguageStrings.app_messagedetail_error_textview_text() + "</font>" +
                                                "<font color=\"#666666\">" + payloadDataDTO.getErrorCode() + "</font>");
                                tv_code.setText(textCodeHtml);

                            }
                            tv_code.setVisibility(View.VISIBLE);
                        }
                    }


                    //删除消息
                    holder.itemView.findViewById(R.id.messagecenter_messagelist_item_delete)
                            .setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    HashMap<String, Object> params = new HashMap<>();
                                    params.put("messageType", listDTO.getMessageType());
                                    params.put("uuid", listDTO.getUuid());
                                    params.put("createTime", listDTO.getCreateTime());
                                    if (messageType == 2) {
                                        params.put("deviceId", listDTO.getDeviceId());
                                        params.put("productId", listDTO.getProductId());
                                    }
                                    WeakReference<LoadingDialog> loadingDialogWeakReferenceHolder = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));

                                    ApiService.instance().messageDelete(params).subscribe(new HttpObserver<BaseResult>() {
                                        @Override
                                        public void onSubscribe(Disposable d) {
                                            super.onSubscribe(d);

                                        }

                                        @Override
                                        protected void Next(BaseResult entity) {
                                            if (loadingDialogWeakReferenceHolder != null && loadingDialogWeakReferenceHolder.get() != null) {
                                                loadingDialogWeakReferenceHolder.get().dismiss();
                                            }
                                            LogUtils.d("messageDelete_dataList" + dataList.size() + "  position" + position);
                                            try {
                                                dataList.remove(position);
                                                notifyItemRemoved(position);
                                                LogUtils.d(TAG, "position = " + position + "  dataList.size() = " + dataList.size());
                                                notifyItemRangeChanged(position >= 1 ? position - 1 : position, dataList.size());
                                                mViewModel.dataListLiveData.setValue(dataList);
                                            } catch (Exception e) {
                                                LogUtils.d(TAG, "remove error : " + e.getMessage());
                                            }
                                        }

                                        @Override
                                        public void Error(ErrorBean errorBean) {
                                            super.Error(errorBean);
                                            if (loadingDialogWeakReferenceHolder != null && loadingDialogWeakReferenceHolder.get() != null) {
                                                loadingDialogWeakReferenceHolder.get().dismiss();
                                            }
                                            ToastUtils.showShort(errorBean.getErrorMsg());
                                        }
                                    });
                                }
                            });

                    //进入消息详情
                    holder.itemView.findViewById(R.id.messagecenter_messagelist_item_contentitem)
                            .setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View view) {
                                    dataList.get(position).setIfRead(MESSAGE_READ);
                                    notifyItemChanged(position);
                                    if (listDTO.getMessageType() == SYSTEM_MESSAGE_TYPE || listDTO.getMessageType() == MARKING_MESSAGE_TYPE) {
                                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                                                .withString("messageDataJson", GsonUtils.toJson(listDTO))
                                                .withInt(POSITION_MESSAGE, position)
                                                .withInt(REQUEST_CODE, REQUEST_CODE_MESSAGE_LIST_TO_MESSAGE_DETAIL)
                                                .navigation();
                                    } else {
                                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                                                .withString("messageDataJson", GsonUtils.toJson(listDTO))
                                                .withInt(POSITION_MESSAGE, position)
                                                .withInt(REQUEST_CODE, REQUEST_CODE_MESSAGE_LIST_TO_MESSAGE_DETAIL)
                                                .navigation();
                                    }
                                    //埋点相关-- 0系统消息，1营销消息, 2设备消息 3反馈
                                    if (SYSTEM_MESSAGE_TYPE == messageType || CUSTOMER_MESSAGE_TYPE == messageType) {
                                        //系统消息
                                        traceSystemMessageDetail();
                                    } else if (MARKING_MESSAGE_TYPE == messageType) {
                                        traceMarketingMessageDetail();
                                    } else if (DEVICE_MESSAGE_TYPE == messageType) {
                                        traceDeviceMessageDetail();
                                    }
                                }
                            });
                }

            } else if (holder instanceof FootViewHolder) {
                FootViewHolder footViewHolder = (FootViewHolder) holder;
                switch (loadState) {
                    case LOADING: // 正在加载
                        footViewHolder.pbLoading.setVisibility(View.VISIBLE);
                        footViewHolder.tvLoading.setVisibility(View.VISIBLE);
                        // footViewHolder.tvEndLine.setVisibility(View.GONE);
                        break;

                    case LOADING_COMPLETE: // 加载完成
                        footViewHolder.pbLoading.setVisibility(View.INVISIBLE);
                        footViewHolder.tvLoading.setVisibility(View.INVISIBLE);
                        //  footViewHolder.tvEndLine.setVisibility(View.GONE);
                        break;

                    case LOADING_END: // 加载到底
                        if (getItemCount() > 6) {
                            footViewHolder.pbLoading.setVisibility(View.GONE);
                            footViewHolder.tvLoading.setVisibility(View.GONE);
                            // footViewHolder.tvEndLine.setVisibility(View.VISIBLE);
                        } else {
                            footViewHolder.pbLoading.setVisibility(View.GONE);
                            footViewHolder.tvLoading.setVisibility(View.GONE);
                            // footViewHolder.tvEndLine.setVisibility(View.GONE);
                        }

                        break;

                    default:
                        break;
                }
            }
        }

        @Override
        public int getItemCount() {
            return dataList.size() + 1;
        }


        /**
         * 设置上拉加载状态
         *
         * @param loadState 0.正在加载 1.加载完成 2.加载到底
         */
        public void setLoadState(int loadState) {
            this.loadState = loadState;
            notifyDataSetChanged();
        }


    }
    public static class ViewHolder extends RecyclerView.ViewHolder {

        MessagecenterActivityMessagelistItemBinding viewDataBinding;

        public ViewHolder(@NonNull MessagecenterActivityMessagelistItemBinding itemView) {
            super(itemView.getRoot());
            viewDataBinding = itemView;
        }
    }

    private class FootViewHolder extends RecyclerView.ViewHolder {

        ProgressBar pbLoading;
        TextView tvLoading;


        FootViewHolder(View itemView) {
            super(itemView);
            pbLoading = (ProgressBar) itemView.findViewById(R.id.pb2);
            tvLoading = (TextView) itemView.findViewById(R.id.tvLoading);
        }
    }

    public abstract class EndlessRecyclerOnScrollListener extends RecyclerView.OnScrollListener {

        //用来标记是否正在向上滑动
        private boolean isSlidingUpward = false;

        @Override
        public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
            super.onScrollStateChanged(recyclerView, newState);
            LinearLayoutManager manager = (LinearLayoutManager) recyclerView.getLayoutManager();
            // 当不滑动时
            if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                //获取最后一个完全显示的itemPosition
                int lastItemPosition = manager.findLastCompletelyVisibleItemPosition();
                int itemCount = manager.getItemCount();

                // 判断是否滑动到了最后一个item，并且是向上滑动
                if (lastItemPosition == (itemCount - 1) && isSlidingUpward) {
                    //加载更多
                    onLoadMore();
                }
            }
        }

        @Override
        public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
            super.onScrolled(recyclerView, dx, dy);
            // 大于0表示正在向上滑动，小于等于0表示停止或向下滑动
            isSlidingUpward = dy > 0;
        }

        /**
         * 加载更多回调
         */
        public abstract void onLoadMore();
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            Bundle bundle = data.getExtras();
            if (bundle != null) {
                int messagePostion = bundle.getInt(POSITION_MESSAGE, -1);
                if (messagePostion != -1) {
                    try {
                        dataList.get(messagePostion).setIfRead(1);
                        mLoadMoreAdapter.notifyDataSetChanged();
                    } catch (Exception e) {

                    }

                }
            }

        }
    }
}
