package com.chervon.moudleMessageCenter.ui;

import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_DEVICE;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_FEEDBACK;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_OFFER;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_SHARE_DEVICE;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_SYSTEM;
import static com.chervon.libBase.utils.CommonUtils.goToWebPage;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_SHARE_SELECT_POSITION;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.REQUEST_CODE;
import static com.chervon.libRouter.RouterConstants.REQUEST_CODE_MESSAGE_LIST_TO_MESSAGE_DETAIL;
import static com.chervon.libRouter.RouterConstants.TAG;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.result.MessageListBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleMessageCenter.R;
import com.chervon.moudleMessageCenter.databinding.MessagecenterActivityMessagedetailBinding;
import com.chervon.moudleMessageCenter.ui.viewmodel.MessageCenterMessageDetailViewModel;

import net.nightwhistler.htmlspanner.HtmlSpanner;

import java.lang.ref.WeakReference;
import java.util.HashMap;

import io.reactivex.observers.DefaultObserver;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.ui
 * @ClassName: MessageCenterMessageDetailActivity
 * @Description: 消息通知详情页
 * @Author: langmeng
 * @CreateDate: 2022/9/1 17:07
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/1 17:07
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
public class MessageCenterMessageDetailActivity extends BaseActivity<MessageCenterMessageDetailViewModel> {

  @Autowired
  public String messageDataJson;
  private MessagecenterActivityMessagedetailBinding binding;
  private MessageListBean.EntryDTO.ListDTO messageData;
  private WeakReference<LoadingDialog> loadingDialogWeakReference;
  private String messageType;
  private boolean isRead;
  public final String MESSAGE_DEVICE = "2";
  private final static String SHARE_LIST_TYPE = "shareListType";

  public int request_code = 0;
  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    ARouter.getInstance().inject(this);
    super.onCreate(savedInstanceState);
  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.messagecenter_activity_messagedetail;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (MessagecenterActivityMessagedetailBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {

    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_messagecenter_messagedetailtitle_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          if (request_code==REQUEST_CODE_MESSAGE_LIST_TO_MESSAGE_DETAIL){
            Intent intent = MessageCenterMessageDetailActivity.this.getIntent();
            MessageCenterMessageDetailActivity.this.setResult(RESULT_OK, intent);
          }
          finish();
         // sendBackTrace();
        }
      }));
    String extras = getIntent().getExtras().getString("messageDataJson");
    if (extras != null && !extras.isEmpty()) {
      this.messageDataJson = getIntent().getExtras().getString("messageDataJson");
    }
    Object messageListDtoObeject = getIntent().getSerializableExtra(KEY_PREV_DATA);
    request_code = getIntent().getIntExtra(REQUEST_CODE,0);
    if (messageListDtoObeject != null) {
      messageData = (MessageListBean.EntryDTO.ListDTO) messageListDtoObeject;
    } else {
      messageData = GsonUtils.fromJson(this.messageDataJson, MessageListBean.EntryDTO.ListDTO.class);
    }
    binding.tvMessageWarn.setVisibility(View.GONE);
    binding.tvQuestionTitle.setVisibility(View.GONE);
    binding.tvSuggestionContent.setVisibility(View.GONE);
    binding.tvViewMore.setVisibility(View.GONE);
    binding.ivMessageWarn.setVisibility(View.GONE);
    //设置显示数据
    if (messageData != null) {
      reFreshUi();
    } else {
      messageData = (MessageListBean.EntryDTO.ListDTO) getIntent().getExtras().getSerializable(KEY_PREV_DATA);
    }
    binding.setMessageCenterMessageDetailViewModel(mViewModel);
    HashMap<String, Object> params = new HashMap<>();
    params.put("createTime", messageData.getCreateTime());
    messageType = messageData.getMessageType() + "";
    if (null != messageData.getPayloadData() && null != messageData.getPayloadData().getMessageType()) {
      messageType = messageData.getPayloadData().getMessageType();
    }
    params.put("messageType", messageType);
    params.put("uuid", messageData.getUuid());
    params.put("systemMessageId",messageData.getSystemMessageId());

    if (MESSAGE_DEVICE.equalsIgnoreCase(messageType)) {
      if (!TextUtils.isEmpty(messageData.getDeviceId())) {
        params.put("deviceId", messageData.getDeviceId());
        params.put("productId", messageData.getProductId());
      } else if (messageData.getPayloadData() != null) {
        params.put("deviceId", messageData.getPayloadData().getDeviceId());
        params.put("productId", messageData.getPayloadData().getProductId());
      }

    }
    //将消息设置为已读
    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));
    ApiService.instance().getMessageDetail(params).subscribe(new DefaultObserver<HttpResponse<MessageListBean.EntryDTO.ListDTO>>() {
      @Override
      public void onNext(HttpResponse<MessageListBean.EntryDTO.ListDTO> messageListBean) {
        if (messageListBean.status && messageListBean.response != null) {
          isRead = true;
          messageData = messageListBean.response;
          reFreshUi();
        }
        LogUtils.d("");
        if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
          loadingDialogWeakReference.get().dismiss();
        }
      }

      @Override
      public void onError(Throwable e) {
        LogUtils.d("");
        if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
          loadingDialogWeakReference.get().dismiss();
        }
      }

      @Override
      public void onComplete() {

      }
    });
    initTrace();
  }

  private void reFreshUi() {

    try {
      mViewModel.messageTitle.set(messageData.getTitle());
      mViewModel.messageTime.set(Utils.getTimeForHour(messageData.getCreateTime()));

      mViewModel.messageContent.set(messageData.getContent());

    } catch (Exception e) {

    }

    MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = messageData.getPayloadData();
    if (payloadDataDTO != null) {
      String suggestionTitle = payloadDataDTO.getSuggestionExtra();
      String rutePath = payloadDataDTO.getRutePath();
      if (!MESSAGE_DEVICE.equalsIgnoreCase(payloadDataDTO.getMessageType())) {
        binding.tvMessageWarn.setVisibility(View.GONE);
        binding.tvQuestionTitle.setVisibility(View.GONE);
        binding.tvSuggestionContent.setVisibility(View.GONE);
        binding.tvViewMore.setVisibility(TextUtils.isEmpty(rutePath) ? View.GONE : View.VISIBLE);
        binding.ivMessageWarn.setVisibility(TextUtils.isEmpty(rutePath) ? View.GONE : View.VISIBLE);

      } else {
        String suggestionContent = payloadDataDTO.getSuggestionContent();
        if (TextUtils.isEmpty(suggestionContent)) {
          binding.tvViewMore.setVisibility(View.GONE);
          binding.ivMessageWarn.setVisibility(View.GONE);
        } else {
          if (TextUtils.isEmpty(suggestionContent)) {
//            binding.tvSuggestionContent.setText("");
          } else {
//            binding.tvSuggestionContent.setText(Html.fromHtml(payloadDataDTO.getSuggestionContent()));
            binding.tvSuggestionContent.loadData(payloadDataDTO.getSuggestionContent(), "text/html", "UTF-8");
          }
          binding.tvQuestionTitle.setVisibility(View.VISIBLE);
          binding.tvQuestionTitle.setText(LanguageStrings.getAppMessagedetailSuggestiontitleTextviewText());
          binding.tvSuggestionContent.setVisibility(View.VISIBLE);
          binding.tvViewMore.setVisibility(View.VISIBLE);
          binding.ivMessageWarn.setVisibility(View.VISIBLE);
          if (TextUtils.isEmpty(payloadDataDTO.getSuggestionExtra())) {
            binding.tvViewMore.setVisibility(View.GONE);
            binding.ivMessageWarn.setVisibility(View.GONE);
          } else {
            binding.tvViewMore.setVisibility(View.VISIBLE);
            binding.ivMessageWarn.setVisibility(View.VISIBLE);
          }
        }


        if (TextUtils.isEmpty(payloadDataDTO.getErrorCode())) {
          binding.tvMessageWarn.setText("");
          binding.tvMessageWarn.setVisibility(View.GONE);
        } else {
          //需要修改
          Spanned textCodeHtml = Html.fromHtml(
            "<font color=\"#666666\">" + LanguageStrings.app_messagedetail_error_textview_text() + "</font>" +
              "<font color=\"#666666\">" + payloadDataDTO.getErrorCode() + "</font>");
          binding.tvMessageWarn.setText(textCodeHtml);
          binding.tvMessageWarn.setVisibility(View.VISIBLE);
        }
      }


    }
  }

  @Override
  protected Class<? extends MessageCenterMessageDetailViewModel> getViewModelClass() {
    return MessageCenterMessageDetailViewModel.class;
  }

  public void viewMore(View w) {
    try {
      String FEED_BACK_ID = "feedbackId";
      MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payloadDataDTO = messageData.getPayloadData();
      String suggestionExtraUrl = payloadDataDTO.getSuggestionExtra();
      String rutePath = payloadDataDTO.getRutePath();
      String messageType = messageData.getPayloadData().getMessageType();
      if (TextUtils.isEmpty(messageType)){
        return;
      }
      switch (messageType){
        case MESSAGE_TYPE_SYSTEM:
        case MESSAGE_TYPE_OFFER:
          if (!TextUtils.isEmpty(rutePath)){
            goToWebPage(this, rutePath);
          }
          break;
        case MESSAGE_TYPE_DEVICE:
          if (!TextUtils.isEmpty(suggestionExtraUrl)){
            goToWebPage(this, suggestionExtraUrl);
          }
          break;
        case MESSAGE_TYPE_FEEDBACK:
          //跳转路由 ChervonIoT://EGO/UserCenter/feedbackHistoryDetail?feedbackId=${feedbackId}
          if (!TextUtils.isEmpty(rutePath)){
            String path = Uri.parse(rutePath).getPath();
            String feedbackId = Uri.parse(rutePath).getQueryParameter(FEED_BACK_ID);
            if (TextUtils.isEmpty(path)||TextUtils.isEmpty(feedbackId)){
              return;
            }
            ARouter.getInstance().build(path).withString(KEY_PREV_DATA, feedbackId).navigation();
          }
          break;
        case MESSAGE_TYPE_SHARE_DEVICE:
          if(!TextUtils.isEmpty(rutePath)){
            String path = Uri.parse(rutePath).getPath();
            String shareListType = Uri.parse(rutePath).getQueryParameter(SHARE_LIST_TYPE);
            if (TextUtils.isEmpty(path)||TextUtils.isEmpty(shareListType)){
              return;
            }
            try {
              int type = Integer.parseInt(shareListType);
              ARouter.getInstance().build(path).withInt(KEY_SHARE_SELECT_POSITION, type).navigation();
            } catch (NumberFormatException e) {
              LogUtils.i(TAG,"shareListType is error--->"+e.getMessage());
            }
          }
          break;
        default:
          break;
      }
    } catch (Exception e) {
      LogUtils.i(TAG,"viewMore is error--->"+e.getMessage());
    }
  }


  @Override
  public void onBackPressed() {
    // super.onBackPressed();
    if (request_code == REQUEST_CODE_MESSAGE_LIST_TO_MESSAGE_DETAIL){
      Intent intent = MessageCenterMessageDetailActivity.this.getIntent();
      MessageCenterMessageDetailActivity.this.setResult(RESULT_OK, intent);
    }
    sendBackTrace();
    finish();
  }


  private void initTrace() {
    //0系统消息，1营销消息, 2设备消息 3反馈
     final String System_MESSAGE_TYPE = "0";
     final String MARKING_MESSAGE_TYPE = "1";
     final String DEVICE_MESSAGE_TYPE = "2";
     final String CUSTOMER_MESSAGE_TYPE = "3";
    //埋点相关
    if (System_MESSAGE_TYPE.equals(messageType)|| CUSTOMER_MESSAGE_TYPE.equals(messageType)) {
    //系统消息或者客服消息埋点
      pageId = "414";
      pageResouce = "1_9_65_66_414";
    }else if (MARKING_MESSAGE_TYPE.equals(messageType)){
     //营销消息埋点
      pageId = "415";
      pageResouce = "1_9_65_66_415";
    }else if (DEVICE_MESSAGE_TYPE.equals(messageType)){
      //设备消息埋点
      pageId = "451";
      pageResouce = "1_9_65_66_451";
    }
    stayEleId = "1";
    mouduleId = "8";
    nextButtoneleid = "2";
  }
}
