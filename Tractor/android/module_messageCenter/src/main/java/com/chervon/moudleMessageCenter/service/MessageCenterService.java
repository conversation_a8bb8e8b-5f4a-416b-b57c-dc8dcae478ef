package com.chervon.moudleMessageCenter.service;

import static com.chervon.libBase.BaseApplication.isShowDialogMessage;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_SHARE_DEVICE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_MAIN_REMOVE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_MAIN_SHARE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_SUB_ACCEPT;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_SUB_DELETE;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_SUB_REMOVE;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.APPConstants.MESSAGE_TYPE_DEVICE;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_CREATE_TIME;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_MESSAGETYPE;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_UUID;

import android.content.Intent;
import android.net.Uri;
import android.os.Handler;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleService;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.dialog.MessageAlertDialog;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.result.MessageListBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libNetwork.iot.data.MqttNotificationData;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleMessageCenter.ui.dialog.BannerMessageDialog;
import com.chervon.moudleMessageCenter.ui.dialog.BannerMessageDialogFragment;
import com.chervon.moudleMessageCenter.ui.dialog.BannerMessageHorizontalDialogFragment;
import com.chervon.libNetwork.event.ShareUpdateEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;

import io.reactivex.functions.Consumer;
import io.reactivex.observers.DefaultObserver;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.service
 * @ClassName: MessageCenterService
 * @Description: 消息服务，用于处理各种消息逻辑
 * @Author: langmeng
 * @CreateDate: 2022/9/14 15:36
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/14 15:36
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class MessageCenterService extends LifecycleService {

    private static final String TAG = "MessageCenterService";
    private static final String BANNER_TRACE_MODULE_ID = "87";
    private static final String BANNER_TRACE_PAGE_ID = "468";
    private static final String BANNER_TRACE_EVENT_CLICK = "1";
    private static final String CLASS_NAME_BUZ_ACTIVITY = "com.chervon.moudleContainer.reactnative_multibundler.ui.BuzActivity";
    private static final String CLASS_NAME_HOME_ACTVITY = "com.chervon.moudleDeviceManage.ui.HomeActivity";
    private static final String CLASS_NAME_DEVICE_INFO_REGISTERED_ACTIVITY = "com.chervon.moudleDeviceManage.ui.DeviceInfoRegistedActivity";
    private static final String CLASS_NAME_DEVICE_UPGRADE_ACTIVITY = "com.chervon.libOTA.ui.DeviceUpgradeActivity";
    private static final String CLASS_NAME_PRODUCT_ENCYCLOPEDIAS_ACTIVITY = "com.chervon.moudleDeviceManage.ui.ProductEncyclopediasActivity";
    private static final String CLASS_NAME_RN_PARTS_ACTIVITY = "com.chervon.moudleDeviceManage.ui.RnPartsActivity";
    private static final String CLASS_NAME_DEVICE_UPGRADE_PROCESS_ACTIVITY = "com.chervon.libOTA.ui.DeviceUpgradeProcessActivity";
    private static final String CLASS_NAME_DEVICE_UPGRADE_HISTORY_ACTIVITY = "com.chervon.libOTA.ui.DeviceUpgradeHistoryActivity";
    private static final String CLASS_NAME_CONFIG_NET_ACTIVITY = "com.chervon.moudleConfigNet.ui.ConfigNetActivity";
    private static final String CLASS_NAME_PDF_RENDERER_ACTIVITY = "com.chervon.moudleDeviceManage.pdf.PdfRendererActivity";
    private static final String CLASS_NAME_RN_PART_DETAIL_ACTIVITY = "com.chervon.moudleDeviceManage.ui.RnPartDetailActivity";
    private static final String CONSTANTS_MESSAGE_DATA_JSON="messageDataJson";


    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
    }


    @Override
    public int onStartCommand(@Nullable Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);


    }

    @Nullable
    @Override
    public IBinder onBind(@NonNull Intent intent) {
        LogUtils.d("mqttService_MessageCenterService_onBind");
        return super.onBind(intent);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LogUtils.d("mqttService_MessageCenterService_onDestroy");
        EventBus.getDefault().unregister(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = false)
    public void onMQTTMessageEvent(IotModelEvent iotModelEvent) {

        //消息通知
        String messageTopic = String.format(AwsMqttService.APP_MESSAGE_NOTIFICATION, UserInfo.get().getId());
        if (iotModelEvent.topic.equals(messageTopic)) {

            User user = UserInfo.getDataOnly();
            if (user != null && TextUtils.isEmpty(user.getAccessToken())) {
                return;
            }
            //消息数据
            MqttNotificationData mqttNotificationData = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), MqttNotificationData.class);
            LogUtils.d("messageMqttNotificationData" + ConvertUtils.bytes2String(iotModelEvent.modelData));
            //设置时间戳
            if (mqttNotificationData.getPayloadData().getCreateTime() != null) {
                mqttNotificationData.setCreateTime(Long.parseLong(mqttNotificationData.getPayloadData().getCreateTime()));
            }

            if (!AppUtils.isAppForeground()) {
                LogUtils.e(TAG, "get Message from network but app is forgound");
                return;
            }
            //消息弹窗
            if (mqttNotificationData.getPushTypes().contains(1)) {
                try {
                    FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();

                    if (
                            "com.chervon.moudleContainer.reactnative_multibundler.ui.BuzActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleDeviceManage.ui.HomeActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleDeviceManage.ui.DeviceInfoRegistedActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.libOTA.ui.DeviceUpgradeActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleDeviceManage.ui.ProductEncyclopediasActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleDeviceManage.ui.RnPartsActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleMessageCenter.ui.MessageCenterMessageDetailActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.libOTA.ui.DeviceUpgradeProcessActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.libOTA.ui.DeviceUpgradeHistoryActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleConfigNet.ui.ConfigNetActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleDeviceManage.pdf.PdfRendererActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleUserCenter.uiFeedBackDetailActivity".equals(topActivity.getClass().getName()) ||
                                    "com.chervon.moudleDeviceManage.ui.RnPartDetailActivity".equals(topActivity.getClass().getName()) ||

                                    isShowDialogMessage()

                    ) {
                        //如果栈顶activity是RN面板则不弹窗
                    } else {
                        MessageAlertDialog.initTrace("3",
                                "70",
                                "8",
                                "1_9_11_14_15",
                                "1", "2");
                        switch (mqttNotificationData.getMessageType()) {
                            //设备消息跳转至设备面板
                            case 2:

                                MessageAlertDialog.show(topActivity.getSupportFragmentManager(),
                                        mqttNotificationData.getTitle(),
                                        mqttNotificationData.getContent(),
                                        LanguageStrings.getAppMessagecenterDialogButtonClose(),
                                        LanguageStrings.app_messagealert_tosee_button_text(), new Consumer<Boolean>() {
                                            @Override
                                            public void accept(Boolean aBoolean) throws Exception {
                                                if (aBoolean) {
                                                    //设备面板
                                                    DeviceDao deviceDao = SoftRoomDatabase.getDatabase(getApplication()).deviceDao();
                                                    DeviceInfo deviceInfo = deviceDao.getDeviceById(mqttNotificationData.getPayloadData().getDeviceId());
                                                    if (mqttNotificationData.getMessageType() == 0 || mqttNotificationData.getMessageType() == 1) {
                                                        String rutepath = mqttNotificationData.getPayloadData().getRutePath();

                                                        if (TextUtils.isEmpty(rutepath)) {
                                                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL)
                                                                    .withSerializable(RouterConstants.PARAMETER_DEVICD, deviceInfo)
                                                                    .navigation();
                                                        } else {
                                                            try {
                                                                tellServiceMessageReaded(deviceInfo, mqttNotificationData);
                                                                jumptoWeb(rutepath);
                                                            } catch (Exception e) {

                                                            }
                                                        }

                                                    } else {
                                                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL)
                                                                .withSerializable(RouterConstants.PARAMETER_DEVICD, deviceInfo)
                                                                .navigation();
                                                    }
                                                }
                                            }
                                        });
                                break;
                            default:
                                //其他消息跳转至消息详情
                                MessageAlertDialog.show(topActivity.getSupportFragmentManager(),
                                        mqttNotificationData.getTitle(),
                                        mqttNotificationData.getContent(),
                                        LanguageStrings.getAppMessagecenterDialogButtonClose(),
                                        LanguageStrings.app_messagealert_tosee_button_text(), new Consumer<Boolean>() {
                                            @Override
                                            public void accept(Boolean aBoolean) throws Exception {
                                                if (aBoolean) {
                                                    DeviceDao deviceDao = SoftRoomDatabase.getDatabase(getApplication()).deviceDao();
                                                    DeviceInfo deviceInfo = deviceDao.getDeviceById(mqttNotificationData.getPayloadData().getDeviceId());
                                                    if (mqttNotificationData.getMessageType() == 0 || mqttNotificationData.getMessageType() == 1) {
                                                        String rutepath = mqttNotificationData.getPayloadData().getRutePath();

                                                        if (TextUtils.isEmpty(rutepath)) {
                                                            //消息详情
                                                            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                                                                    .withString(CONSTANTS_MESSAGE_DATA_JSON, GsonUtils.toJson(mqttNotificationData)).navigation();

                                                        } else {
                                                            try {
                                                                tellServiceMessageReaded(deviceInfo, mqttNotificationData);
                                                                jumptoWeb(rutepath);
                                                            } catch (Exception e) {

                                                            }
                                                        }

                                                    } else {
                                                        //消息详情
                                                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                                                                .withString(CONSTANTS_MESSAGE_DATA_JSON, GsonUtils.toJson(mqttNotificationData)).navigation();

                                                    }

                                                }
                                            }
                                        });
                                break;
                        }
                    }
                } catch (ClassCastException e) {
                    e.fillInStackTrace();
                }
            }

            LogUtils.i(TAG, "----Banner收到消息");
            //banner
            if (mqttNotificationData.getPushTypes().contains(2)) {

                //设备面板
                FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
                if ("com.chervon.moudleMessageCenter.ui.MessageCenterMessageDetailActivity".equals(topActivity.getClass().getName())) {
                    return;
                }

                //如果是横屏
                if (ScreenUtils.isLandscape()) {
                    BannerMessageHorizontalDialogFragment bannerMessageHorizontalDialogFragment = BannerMessageHorizontalDialogFragment
                            .show(topActivity.getSupportFragmentManager(), mqttNotificationData);
                    bannerMessageHorizontalDialogFragment.setOnBannerNotificationClickListener(new BannerMessageDialog.OnBannerNotificationClick() {
                        @Override
                        public void onClick() {
                            bannerMessageHorizontalDialogFragment.dismiss();
                            bannerMessage(mqttNotificationData, topActivity);
                            //加入埋点
                            sendClickTrace(getApplication(), BANNER_TRACE_MODULE_ID, BANNER_TRACE_PAGE_ID,
                                    ((BaseApplication) getApplication()).getCurrentPageResouce(), BANNER_TRACE_EVENT_CLICK, "1");
                        }

                    });

                } else {
                    BannerMessageDialogFragment bannerMessageDialogFragment = BannerMessageDialogFragment
                            .show(topActivity.getSupportFragmentManager(), mqttNotificationData);
                    bannerMessageDialogFragment.setOnBannerNotificationClickListener(new BannerMessageDialog.OnBannerNotificationClick() {
                        @Override
                        public void onClick() {
                            bannerMessageDialogFragment.dismiss();
                            bannerMessage(mqttNotificationData, topActivity);
                            //加入埋点
                            sendClickTrace(getApplication(), BANNER_TRACE_MODULE_ID, BANNER_TRACE_PAGE_ID,
                                    ((BaseApplication) getApplication()).getCurrentPageResouce(), BANNER_TRACE_EVENT_CLICK, "1");
                        }
                    });

                }
                //设备分享消息更新
                if(MESSAGE_TYPE_SHARE_DEVICE.equals(Integer.toString(mqttNotificationData.getMessageType()))) {
                  EventBus.getDefault().post(new ShareUpdateEvent(mqttNotificationData.getPayloadData().getDeviceId(),
                          mqttNotificationData.getPayloadData().getShareOperateType()));
                  //子账户操作时被主账户删除退出
                  if(SHARE_OPERATE_TYPE_MAIN_REMOVE.equals(mqttNotificationData.getPayloadData().getShareOperateType()) &&
                          BaseApplication.getInstance().isCurrentDevice(mqttNotificationData.getPayloadData().getDeviceId())) {
                    if (CLASS_NAME_BUZ_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_HOME_ACTVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_DEVICE_INFO_REGISTERED_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_DEVICE_UPGRADE_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_PRODUCT_ENCYCLOPEDIAS_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_RN_PARTS_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_DEVICE_UPGRADE_PROCESS_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_DEVICE_UPGRADE_HISTORY_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_CONFIG_NET_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_PDF_RENDERER_ACTIVITY.equals(topActivity.getClass().getName()) ||
                            CLASS_NAME_RN_PART_DETAIL_ACTIVITY.equals(topActivity.getClass().getName())
                    ) {
                      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                              .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                              .navigation();
                      //延迟1s后在主界面弹窗提示
                      try {
                        Handler handler = new Handler(getMainLooper());
                        handler.postDelayed(() -> {
                          FragmentActivity mainActivity = (FragmentActivity) ActivityUtils.getTopActivity();
                          if (ScreenUtils.isLandscape()) {
                            BannerMessageHorizontalDialogFragment bannerMessageHorizontalDialogFragment = BannerMessageHorizontalDialogFragment
                                    .show(mainActivity.getSupportFragmentManager(), mqttNotificationData);
                            bannerMessageHorizontalDialogFragment.setOnBannerNotificationClickListener(() -> {
                              bannerMessageHorizontalDialogFragment.dismiss();
                              bannerMessage(mqttNotificationData, mainActivity);
                              //加入埋点
                              sendClickTrace(getApplication(), BANNER_TRACE_MODULE_ID, BANNER_TRACE_PAGE_ID,
                                      ((BaseApplication) getApplication()).getCurrentPageResouce(), BANNER_TRACE_EVENT_CLICK, "1");
                            });
                          } else {
                            BannerMessageDialogFragment bannerMessageDialogFragment = BannerMessageDialogFragment
                                    .show(mainActivity.getSupportFragmentManager(), mqttNotificationData);
                            bannerMessageDialogFragment.setOnBannerNotificationClickListener(() -> {
                              bannerMessageDialogFragment.dismiss();
                              bannerMessage(mqttNotificationData, mainActivity);
                              //加入埋点
                              sendClickTrace(getApplication(), BANNER_TRACE_MODULE_ID, BANNER_TRACE_PAGE_ID,
                                      ((BaseApplication) getApplication()).getCurrentPageResouce(), BANNER_TRACE_EVENT_CLICK, "1");
                            });
                          }
                        },1000);
                      }catch (Exception e){
                        e.printStackTrace();
                      }
                    }
                  }
                }
            }
        }
    }

    private void bannerMessage(MqttNotificationData mqttNotificationData, FragmentActivity topActivity) {
        if (null == mqttNotificationData) {
            return;
        }
        String pannelActivity = "com.chervon.moudleContainer.reactnative_multibundler.ui.BuzActivity";

        DeviceDao deviceDao = SoftRoomDatabase.getDatabase(getApplication()).deviceDao();
        DeviceInfo deviceInfo = deviceDao.getDeviceById(mqttNotificationData.getPayloadData().getDeviceId());

        //0系统消息 1营销消息 2设备消息 3 客服消息
        if (mqttNotificationData.getMessageType() == 2) {
            if (!topActivity.getClass().getName().equals(pannelActivity)) {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL)
                        .withSerializable(RouterConstants.PARAMETER_DEVICD, deviceInfo)
                        .navigation();
            } else {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                        .withString(CONSTANTS_MESSAGE_DATA_JSON, GsonUtils.toJson(mqttNotificationData)).navigation();

            }
        } else if (mqttNotificationData.getMessageType() == 3) {
            MqttNotificationData.PayloadDataDTO payload = mqttNotificationData.getPayloadData();
            if (null != payload) {
                String FEED_BACK_ID = "feedbackId";
                String rutePath = payload.getRutePath();
                if (!TextUtils.isEmpty(rutePath)) {
                    String path = Uri.parse(rutePath).getPath();
                    String feedbackId = Uri.parse(rutePath).getQueryParameter(FEED_BACK_ID);
                    String uuid = payload.getUuid();
                    if (TextUtils.isEmpty(path) || TextUtils.isEmpty(feedbackId)) {
                        return;
                    }
                    ARouter.getInstance().build(path).
                            withString(KEY_PREV_DATA, feedbackId)
                            .withString(KEY_PREV_CREATE_TIME, payload.getCreateTime())
                            .withString(KEY_PREV_MESSAGETYPE, payload.getMessageType())
                            .withString(KEY_PREV_UUID, uuid).navigation();
                }
            }
        } else if (mqttNotificationData.getMessageType() == 0 || mqttNotificationData.getMessageType() == 1) {
            String rutepath = mqttNotificationData.getPayloadData().getRutePath();

            if (TextUtils.isEmpty(rutepath)) {
                //消息详情
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                        .withString(CONSTANTS_MESSAGE_DATA_JSON, GsonUtils.toJson(mqttNotificationData)).navigation();
            } else {
                try {
                    tellServiceMessageReaded(deviceInfo, mqttNotificationData);
                    jumptoWeb(rutepath);
                } catch (Exception e) {

                }
            }

        } else if (MESSAGE_TYPE_SHARE_DEVICE.equals(Integer.toString(mqttNotificationData.getMessageType()))) {
          if(SHARE_OPERATE_TYPE_MAIN_SHARE.equals(mqttNotificationData.getPayloadData().getShareOperateType()) ||
                  SHARE_OPERATE_TYPE_SUB_ACCEPT.equals(mqttNotificationData.getPayloadData().getShareOperateType()) ||
                  SHARE_OPERATE_TYPE_SUB_DELETE.equals(mqttNotificationData.getPayloadData().getShareOperateType()) ||
                  SHARE_OPERATE_TYPE_SUB_REMOVE.equals(mqttNotificationData.getPayloadData().getShareOperateType()) ||
                  SHARE_OPERATE_TYPE_MAIN_REMOVE.equals(mqttNotificationData.getPayloadData().getShareOperateType())
          ) {
            //消息详情
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                    .withString(CONSTANTS_MESSAGE_DATA_JSON, GsonUtils.toJson(mqttNotificationData)).navigation();
          }
        } else {
            //消息详情
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_DETAIL)
                    .withString("messageDataJson", GsonUtils.toJson(mqttNotificationData)).navigation();

        }
    }

    private void tellServiceMessageReaded(DeviceInfo deviceInfo, MqttNotificationData mqttNotificationData) {

        String key_uuid = "uuid";
        String key_createTime = "createTime";
        String key_messageType = "messageType";
        HashMap<String, Object> params = new HashMap<>();
        params.put(key_createTime, mqttNotificationData.getCreateTime());
        params.put(key_messageType, mqttNotificationData.getMessageType());
        params.put(key_uuid, mqttNotificationData.getUuid());
        if (mqttNotificationData.getMessageType() == Integer.parseInt(MESSAGE_TYPE_DEVICE)) {
            params.put("deviceId", deviceInfo.getDeviceId());
            params.put("productId", deviceInfo.getProductId());
        }

        ApiService.instance().getMessageDetail(params).subscribe(new DefaultObserver<HttpResponse<MessageListBean.EntryDTO.ListDTO>>() {
            @Override
            public void onNext(HttpResponse<MessageListBean.EntryDTO.ListDTO> messageListBean) {
                if (messageListBean.status) {
                    LogUtils.i(TAG, "消息被已读");
                }
            }

            @Override
            public void onError(Throwable e) {
                LogUtils.e(TAG, "message detail is error --->" + e.getMessage());

            }

            @Override
            public void onComplete() {

            }
        });

    }

    private void jumptoWeb(String rutepath) {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri content_url = Uri.parse(rutepath);
        intent.setData(content_url);
        startActivity(intent);
    }
}
