package com.chervon.moudleMessageCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;

import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BR;
import com.chervon.libBase.BaseDataBindingAdapter;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.result.MessageLastBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleMessageCenter.R;
import com.chervon.moudleMessageCenter.data.DeviceMessageData;
import com.chervon.moudleMessageCenter.databinding.ActivityMessageCenterBinding;
import com.chervon.moudleMessageCenter.ui.viewmodel.MessageCenterViewModel;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.ui
 * @ClassName: MessageSettingActivity
 * @Description: 消息设置页面
 * @Author: langmeng
 * @CreateDate: 2022/8/29 15:49
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/8/29 15:49
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_MESSAGE_CENTER)
public class MessageCenterActivity extends BaseActivity<MessageCenterViewModel> {

  private ActivityMessageCenterBinding binding;
  private List<DeviceMessageData> dataList = new ArrayList<>();
  private BaseDataBindingAdapter adapter;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.activity_message_center;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (ActivityMessageCenterBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_messagecenter_title_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));
    mViewModel.messageSettingClick.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        sendBottonClick();
      }
    });
    binding.setMessageCenterViewModel(mViewModel);

    initAdapter();
  }

  @Override
  protected void onResume() {
    super.onResume();
    refreshData();
    EventBus.getDefault().register(this);
  }

  @Override
  protected void onPause() {
    super.onPause();
    EventBus.getDefault().unregister(this);
  }

  @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
  public void onDeviceListDataEvent(IotModelEvent iotModelEvent) {
    refreshData();
  }

  private void refreshData() {
    dataList.clear();
    //插入设备列表数据
    DeviceDao deviceDao = SoftRoomDatabase.getDatabase(this).deviceDao();
    List<DeviceInfo> devices = deviceDao.getDevices();
    for (DeviceInfo device : devices) {
      DeviceMessageData deviceMessageData = new DeviceMessageData();
      deviceMessageData.setIfRead(1);
      deviceMessageData.setDeviceInfo(device);
      deviceMessageData.setMessageType(2);
      deviceMessageData.setMessageContent(LanguageStrings.app_messagecenter_devicemessagecontent_textview_text());
      dataList.add(deviceMessageData);
    }

    //插入设备消息数据
    ApiService.instance().getMessageLast().subscribe(new HttpObserver<MessageLastBean>() {
      @Override
      protected void Next(MessageLastBean entity) {

        //显示系统消息角标
        if (entity.getEntry().getSystemMessage() != null) {
          mViewModel.systemMessageMark.set(true);
        } else {
          mViewModel.systemMessageMark.set(false);
        }
        //显示营销消息角标
        if (entity.getEntry().getMarketingMessage() != null) {
          mViewModel.marketingMessageMark.set(true);
        } else {
          mViewModel.marketingMessageMark.set(false);
        }
        //wangheng 修改消息中心
        for (int i = 0;dataList!=null&& i < dataList.size(); i++) {
          try {
            //todo 初始化时间  处理 清空后 消息时间 还存在
            dataList.get(i).setMessageTime(0);
          } catch (Exception e) {
            if (e != null) {
              LogUtils.e(e.getMessage());
            }

          }

        }


        if (entity.getEntry().getDevicesMessage() != null) {
          for (MessageLastBean.EntryDTO.DevicesMessageDTO devicesMessageDTO : entity.getEntry().getDevicesMessage()) {
            for (int i = 0; i < dataList.size(); i++) {
              DeviceMessageData deviceMessageData = dataList.get(i);
              try {
                if (devicesMessageDTO.getDeviceId() != null && devicesMessageDTO.getDeviceId().equals(deviceMessageData.getDeviceInfo().getDeviceId())) {
                  deviceMessageData.setMessageTitle(devicesMessageDTO.getTitle());
                  deviceMessageData.setMessageContent(devicesMessageDTO.getContent());
                  deviceMessageData.setMessageTime(devicesMessageDTO.getCreateTime());
                  deviceMessageData.setMessageType(devicesMessageDTO.getMessageType());
                  deviceMessageData.setIfRead(0);
                  dataList.remove(deviceMessageData);
                  dataList.add(i, deviceMessageData);
                }
              } catch (Exception e) {
                if (e != null) {
                  LogUtils.e(e.getMessage());
                }

              }

            }
          }
        }

        adapter.notifyDataSetChanged();
      }
    });
  }

  @Override
  protected Class<? extends MessageCenterViewModel> getViewModelClass() {
    return MessageCenterViewModel.class;
  }

  /**
   * @return void
   * @method initAdapter
   * @description init adapter
   * @date: 2022/6/20 15:45
   * @author: LangMeng
   */
  private void initAdapter() {

    adapter = new BaseDataBindingAdapter(
      this,
      R.layout.message_center_device_message_item,
      dataList,
      BR.deviceMessageData,
      new BaseDataBindingAdapter.itemCallBack() {
        @Override
        public void onBindViewHolder(BaseDataBindingAdapter.ViewHolder holder,
                                     int position,
                                     RecyclerView.Adapter<BaseDataBindingAdapter.ViewHolder> adapter) {
          DeviceMessageData deviceMessageData = dataList.get(position);

          //消息图标
          ImageView icon = holder.itemView.findViewById(R.id.message_center_device_message_item_icon);
          Glide.with(icon).load(deviceMessageData.getDeviceInfo().getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(icon);
          TextView time = holder.itemView.findViewById(R.id.message_center_device_message_item_time);
          //消息时间格式化
          if (deviceMessageData.getMessageTime() != 0) {
            time.setText(Utils.getTimeForHour(deviceMessageData.getMessageTime()));
          }else{
            //todo wangheng 初始化时间  处理 清空后 消息时间 还存在
            time.setText("");
          }

          holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
              //进入设备详情
              mViewModel.inMessageList(
                deviceMessageData.getMessageType(),
                deviceMessageData.getDeviceInfo().getDeviceId(),
                deviceMessageData.getDeviceInfo().getProductId(),
                deviceMessageData.getDeviceInfo().getNickName());
            }
          });
        }
      });

    //init recyclerview
    LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
    binding.messagecenterActivityMessageCenterDevicemessageRecycler.setLayoutManager(linearLayoutManager);
    binding.messagecenterActivityMessageCenterDevicemessageRecycler.setAdapter(adapter);
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }


  private void initTrace() {
    stayEleId = "3";
    pageId = "65";
    mouduleId = "8";
    pageResouce = "1_9_65";
    nextButtoneleid = "2";
  }

  public void sendBottonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

}
