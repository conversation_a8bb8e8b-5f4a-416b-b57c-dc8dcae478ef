package com.chervon.moudleDeviceManage.data.api;

import android.net.Uri;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.UriUtils;
import com.chervon.libBase.model.ProductEntry;
import com.chervon.libBase.model.ProductsParams;
import com.chervon.libBase.model.SnScanEntry;
import com.chervon.libBase.utils.Utils;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.PartDetail;
import com.chervon.libDB.entities.PartsInfo;
import com.chervon.libDB.entities.ProductEncyclopediasInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libNetwork.BuildConfig;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.RetrofitMethod;
import com.chervon.moudleDeviceManage.data.model.DeviceAgreeMentRespone;
import com.chervon.moudleDeviceManage.data.model.DeviceRegisterParam;
import com.chervon.libBase.model.DeviceRegisterRes;
import com.chervon.moudleDeviceManage.data.model.DeviceSortParam;
import com.chervon.moudleDeviceManage.data.model.DeviceStatus;
import com.chervon.libBase.model.DictItem;
import com.chervon.libBase.model.QuestionPageData;
import com.chervon.moudleDeviceManage.data.model.LastMessageRespone;
import com.chervon.moudleDeviceManage.data.model.ReNameParam;
import com.chervon.moudleDeviceManage.data.model.UploadInfoEntry;
import com.chervon.moudleDeviceManage.data.model.UploadInfoParam;
import com.chervon.moudleDeviceManage.ui.state.DeviceRegistedDetailUistate;
import com.chervon.moudleDeviceManage.ui.state.HomeDeviceUiState;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Url;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.data.api
 * @ClassName: DeviceApi
 * @Description: the Application Program Interface of DeviceInfo moudle
 * @Author: wangheng
 * @CreateDate: 2022/4/25 下午5:37
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/25 下午5:37
 * @UpdateRemark: wangheng
 * @Version: 1.0
 */
public class DeviceApi extends RetrofitMethod {
  private PostService service;

  private static final String DEVICE_ID = "deviceId";

  private static final String PARTS_ID = "partsId";

  private static final String PRODUCT_ID = "productId";

  private static final String PRODUCT_PARTS_INSTANCE_ID = "productPartsInstanceId";

  public DeviceApi() {
    service = createService(PostService.class);
  }

  public Observable<HttpResponse<com.chervon.libDB.entities.DeviceInfo>> getDeviceInfo(String deviceId) {

    String body = "{ \"req\": \"" + deviceId + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getDeviceDetail(requestBody);
  }

  public Observable<HttpResponse<DeviceInfo>> getDeviceDetailBysn(String sn) {

    String body = "{ \"req\": \"" + sn + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getDeviceDetailBysn(requestBody);
  }


  public Observable<HttpResponse<DeviceRegisterRes>> registerDevice2(DeviceRegisterParam registerParam) {

    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {

      elementStr = objectMapper.writeValueAsString(registerParam);

    } catch (Exception e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.registerDevice(requestBody);
  }

  public Observable<HttpResponse<List<com.chervon.libDB.entities.DeviceInfo>>> getDeviceList() {
    // String body = "{ \"pageSize\": \"" + 1 + "\", \"pageSize\": \"" + 1 + "\"}";
    String body = "";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getDeviceList(requestBody);
  }

  public Observable<String> upLoadReceipImage(String url, File receipFile) {

    Uri uri = UriUtils.file2Uri(FileUtils.getFileByPath(receipFile.getAbsolutePath()));
    if (uri == null) {
      uri = Uri.parse(BuildConfig.BASE_URL);
    }
    RequestBody requestBody = RequestBody.create(
      MediaType.parse("image/jpeg"),
      Utils.uriToByteArray(ActivityUtils.getTopActivity(), uri)
    );

    return service.upLoadReceipImage(url, requestBody);
  }


  public Observable<HttpResponse<UploadInfoEntry>> getUploadInfo(String deviceId, String fileName) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    RequestBody requestBody = null;
    try {
//            List<UploadInfoParam> list=new ArrayList<UploadInfoParam>();
//            list.add(new UploadInfoParam(deviceId,fileName));
      elementStr = objectMapper.writeValueAsString(new UploadInfoParam(deviceId, fileName));
      requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    return service.getUploadInfo(requestBody);
  }

  public Observable<HttpResponse> deleteDevice(String deviceId) {
    String body = "{ \"deviceId\": \"" + deviceId + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.deleteDevice(requestBody);
  }

  public Observable<HttpResponse> reOrderDevice(String[] ids) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    RequestBody requestBody = null;
    try {
      elementStr = objectMapper.writeValueAsString(new DeviceSortParam(ids));

      requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }


    return service.sortDevice(requestBody);
  }


  public Observable<HttpResponse<ProductEntry>> getProducts(ProductsParams params) {

    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.getProducts(requestBody);
  }

  public Observable<HttpResponse> commitQuestion(QuestionPageData questionPageData) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      elementStr = objectMapper.writeValueAsString(questionPageData);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.commitQuestion(requestBody);
  }


  public Observable<HttpResponse<DeviceStatus>> getDeviceStatus(String deviceId) {

    return Observable.create(new ObservableOnSubscribe<HttpResponse<DeviceStatus>>() {
      @Override
      public void subscribe(ObservableEmitter<HttpResponse<DeviceStatus>> emitter) {
        try {
          HttpResponse<DeviceStatus> httpResponse = new HttpResponse<DeviceStatus>();
          DeviceStatus deviceStatus = new DeviceStatus();
          deviceStatus.setDeviceStatus(1);
          deviceStatus.setDeviceCodeStatus(1);
          httpResponse.status = true;
          httpResponse.response = deviceStatus;
          emitter.onNext(httpResponse);
        } catch (Exception e) {

        }

      }
    });


//        String body = "{ \"deviceId\": \"" + deviceId + "\"}";
//        RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
//        return service.getDeviceStatus(requestBody);
  }

  public Observable<HttpResponse<DictItem[]>> getDictNames(String info) {
    //    String body = "{ \"info\": [\"appDeviceRegisterReceiptInformation\", \"appDeviceRegisterPurchasePlace\",\"appDeviceRegisterApplyWith\",\"warrantyQuestionnaireCountry\"]}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), info);
    return service.getDictNames(requestBody);
  }

  public Observable<HttpResponse<ProductInfo>> getProductDetail(String productId) {
    String body = "{ \"req\": \"" + productId + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getProductDetail(requestBody);
  }

  public Observable<HttpResponse<DeviceAgreeMentRespone[]>> getNewAgreement(String productId) {
    String body = "{ \"productId\": \"" + productId + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getNewAgreement(requestBody);
  }

  public Observable<HttpResponse<DeviceRegistedDetailUistate>> getDeviceRegistedDetail(String deviceId) {
    String body = "{ \"deviceId\": \"" + deviceId + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getDeviceRegistedDetail(requestBody);

  }


  public Observable<HttpResponse<SnScanEntry>> getProductBySn(String sn) {
    String body = "{ \"req\": \"" + sn + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getProductBySn(requestBody);
  }


  public Observable<HttpResponse<LastMessageRespone>> getLastMessage() {
    return service.getLastMessage();
  }

  //产品百科
  public Observable<HttpResponse<ProductEncyclopediasInfo>> getProductEncyclopedias(String productId) {
    String body = "{ \"req\": \"" + productId + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getProductEncyclopedias(requestBody);
  }

  //设备附件
  public Observable<HttpResponse<PartsInfo[]>> getEquipmentAccessor(String productId, String deviceId) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = "";
    try {
      HashMap params = new HashMap();
      params.put(DEVICE_ID, deviceId);
      params.put(PRODUCT_ID, productId);
      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.getEquipmentAccessor(requestBody);
  }

  public Observable<HttpResponse<PartDetail>> getPartDetail(String partId, String deviceId) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = "";
    try {
      HashMap params = new HashMap();
      params.put(DEVICE_ID, deviceId);
      params.put(PARTS_ID, partId);
      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.getPartDetail(requestBody);
  }

  public Observable<HttpResponse> partReset(String deviceId, String partId) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      HashMap params = new HashMap();
      params.put(DEVICE_ID, deviceId);
      params.put(PRODUCT_PARTS_INSTANCE_ID, partId);

      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.partReset(requestBody);
  }


  public Observable<HttpResponse> reNameDevice(String name, String deviceId) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      elementStr = objectMapper.writeValueAsString(new ReNameParam(deviceId, name));
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.reNameDevice(requestBody);

  }

  public Observable<HttpResponse> resetDevicePassword(String deviceId,boolean online) {
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      HashMap params = new HashMap();
      params.put(DEVICE_ID, deviceId);
      params.put("identifier", "24006");

      HashMap valueMap = new HashMap();
      valueMap.put("1",true);
      params.put("value", valueMap);

      if (!online) {
        params.put("shadowName", "cache");
      }

      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.thingModelUpdate(requestBody);

  }

  public Observable<HttpResponse<List<DeviceInfo>>> getDebugList() {

    String body = "";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getDebugList(requestBody);
  }

  //获取经销商信息
  /*public  Observable<HttpResponse<DealerInfo>>  getDealerInfo(DealerParams params){
    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.getDealerInfo(requestBody);
  }*/

  public Observable<HttpResponse> validateSn(String sn) {
    ObjectMapper objectMapper = new ObjectMapper();
    HashMap params = new HashMap();
    params.put("req",sn);
    String elementStr = null;
    try {
      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);
    return service.validate(requestBody);
  }


  interface PostService {
    @POST("/app-hmac/log/uploadStatus")
    Observable<HttpResponse<HomeDeviceUiState>> getDevicesInfo(@Body RequestBody body);

    @POST("/app-hmac/device/list")
    Observable<HttpResponse<List<DeviceInfo>>> getDeviceList(@Body RequestBody body);


    @POST("/app-hmac/device/debug/list")
    Observable<HttpResponse<List<DeviceInfo>>> getDebugList(@Body RequestBody body);


    @POST("/app-hmac/device/detail")
    Observable<HttpResponse<DeviceInfo>> getDeviceDetail(@Body RequestBody body);

    @POST("/app-hmac/device/detailBySn")
    Observable<HttpResponse<DeviceInfo>> getDeviceDetailBysn(@Body RequestBody body);

    @POST("/app-hmac/device/info/add")
    Observable<HttpResponse<DeviceRegisterRes>> registerDevice(@Body RequestBody requestBody);


    @PUT
    Observable<String> upLoadReceipImage(@Url String url, @Body RequestBody body);

//        @Multipart
//        @POST
//        Observable<HttpResponse > upLoadReceipImage(@Header("Authorization") String token,@Url String url,@Part MultipartBody.Part body);


    @POST("/app-hmac/device/info/upload")
    Observable<HttpResponse<UploadInfoEntry>> getUploadInfo(@Body RequestBody body);

    @POST("/app-hmac/device/unbind")
    Observable<HttpResponse> deleteDevice(@Body RequestBody body);

    @POST("/app-hmac/device/sort")
    Observable<HttpResponse> sortDevice(@Body RequestBody body);

    @POST("/app-hmac/product/list")
    Observable<HttpResponse<ProductEntry>> getProducts(@Body RequestBody params);

    @POST("/app-hmac/product/detail/pid")
    Observable<HttpResponse<ProductInfo>> getProductDetail(@Body RequestBody params);

    @POST("/app-hmac/device/warranty/questionnaire/add")
    Observable<HttpResponse> commitQuestion(@Body RequestBody params);

    @POST("/app-hmac/device/get/status")
    Observable<HttpResponse<DeviceStatus>> getDeviceStatus(@Body RequestBody body);

    @POST("/app-hmac/dict/list/by/dict/name")
    Observable<HttpResponse<DictItem[]>> getDictNames(@Body RequestBody body);

    /**
     * modifyPassword
     *
     * @param body RequestBody
     * @return Observable
     */
    @POST("/app-hmac/device/agreement/list")
    Observable<HttpResponse<DeviceAgreeMentRespone[]>> getNewAgreement(@Body RequestBody body);

    @POST("/app-hmac/device/info/detail")
    Observable<HttpResponse<DeviceRegistedDetailUistate>> getDeviceRegistedDetail(@Body RequestBody body);

    @POST("/app-hmac/device/detailBySn")
    Observable<HttpResponse<SnScanEntry>> getProductBySn(@Body RequestBody sn);

    @POST("/app-hmac/message/get/last/message")
    Observable<HttpResponse<LastMessageRespone>> getLastMessage();

    //产品百科信息
    @POST("/app-hmac/postSale/productWiki")
    Observable<HttpResponse<ProductEncyclopediasInfo>> getProductEncyclopedias(@Body RequestBody params);

    //设备附件
    @POST("/app-hmac/device/parts/listV2")
    Observable<HttpResponse<PartsInfo[]>> getEquipmentAccessor(@Body RequestBody params);


    //设备附件详情
    @POST("/app-hmac/device/parts/detailV2")
    Observable<HttpResponse<PartDetail>> getPartDetail(@Body RequestBody params);

    //设备附件详情
    @POST("/app-hmac/device/parts/reset")
    Observable<HttpResponse> partReset(@Body RequestBody params);


    //经销商信息
   /*   @POST("/app-hmac/dealer/list")
      Observable<HttpResponse<DealerInfo>>  getDealerInfo(@Body RequestBody params);*/

    //设备附件详情
    @POST("/app-hmac/device/edit")
    Observable<HttpResponse> reNameDevice(@Body RequestBody params);

    @POST("/app-hmac/device/info/validateSn")
    Observable<HttpResponse> validate(@Body RequestBody body);

    @POST("/app-hmac/device/shadow/thing/model/update")
    Observable<HttpResponse> thingModelUpdate(@Body RequestBody params);
  }

}
