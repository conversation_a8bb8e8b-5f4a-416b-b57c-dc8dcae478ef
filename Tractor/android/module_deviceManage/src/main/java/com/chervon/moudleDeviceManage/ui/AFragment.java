package com.chervon.moudleDeviceManage.ui;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.TextView;

import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.libBase.model.DictNode;
import com.chervon.libBase.model.QuestionPageData;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.adapter.DialogBottomListAdapter;
import com.chervon.libBase.ui.adapter.DialogBottomListAdapter2;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.moudleDeviceManage.R;
import com.chervon.libBase.model.DeviceDictEntry;
import com.chervon.libBase.model.DictItem;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentChildAnswerOneBinding;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterViewModel;


public class AFragment extends BaseEnhanceFragment<DeviceRegisterViewModel,MoudleDevicemanageFragmentChildAnswerOneBinding> implements TextWatcher {
  private DialogUtil mCountryDialog;
  private DialogUtil mPrivonceDialog;
  private DictNode[] mContryItem;
  private QuestionPageDialog mPageDialog;
  private TextView tvA;
  private DictNode[] mProvinceItem;
  MoudleDevicemanageFragmentChildAnswerOneBinding mViewDataBinding;

  public AFragment() {
  }

  public QuestionPageDialog getmPageDialog() {
    return mPageDialog;
  }

  public void setmPageDialog(QuestionPageDialog mPageDialog) {
    this.mPageDialog = mPageDialog;
  }

  public AFragment(QuestionPageDialog pageDialog , DeviceRegisterUistate uistate) {
    mPageDialog =pageDialog;
  }

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    DeviceRegisterUistate deviceRegisterUistate = mPageDialog.getmUistate();
    mViewDataBinding.setUiState(deviceRegisterUistate);
    mViewDataBinding.setPresenter(this);
    checkQuestionPage();

    mViewModel.mDictLiveData.observe(this, new Observer<DeviceDictEntry>() {
      @Override
      public void onChanged(DeviceDictEntry deviceDictEntry) {
        if (deviceDictEntry != null) {
          DictItem[] dictItems = deviceDictEntry.getEntry();
          for (int i = 0; dictItems != null && i < dictItems.length; i++) {
            if ("appDeviceRegisterPurchasePlace".equalsIgnoreCase(dictItems[i].getDictName())) {
              //    mPlaceItem = dictItems[i].getNodes();
            } else if ("appDeviceRegisterReceiptInformation".equalsIgnoreCase(dictItems[i].getDictName())) {
              //  mReceiptItem = dictItems[i].getNodes();
            } else if ("appDeviceRegisterApplyWith".equalsIgnoreCase(dictItems[i].getDictName())) {
              //    mPurposeItem = dictItems[i].getNodes();
            } else if ("warrantyQuestionnaireCountry".equalsIgnoreCase(dictItems[i].getDictName())) {
              mContryItem = dictItems[i].getNodes();
            }
          }
        }
      }
    });

    mViewModel.mProvinseLiveData.observe(this, new Observer<DeviceDictEntry>() {
      @Override
      public void onChanged(DeviceDictEntry deviceDictEntry) {
        ProgressHelper.hideProgressView(getActivity());
        if (deviceDictEntry != null) {

          DictItem[] dictItems = deviceDictEntry.getEntry();
          for (int i = 0; dictItems != null && i < dictItems.length; i++) {

            mProvinceItem = dictItems[i].getNodes();

          }
        }
      }
    });
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_devicemanage_fragment_child_answer_one;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (MoudleDevicemanageFragmentChildAnswerOneBinding) viewDataBinding;
    mViewDataBinding.dtFirstName.addTextChangedListener(this);
    mViewDataBinding.tvLastname.addTextChangedListener(this);
    mViewDataBinding.etAddress.addTextChangedListener(this);
    mViewDataBinding.etCity.addTextChangedListener(this);
    mViewDataBinding.etPostcode.addTextChangedListener(this);

  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends DeviceRegisterViewModel> getViewModelClass() {
    return DeviceRegisterViewModel.class;
  }


  public void gotoNextPage() {
    DeviceRegisterUistate deviceRegisterUistate = mViewDataBinding.getUiState();

    mPageDialog.setmUistate(deviceRegisterUistate);
    Fragment bFragment = mPageDialog.getFragmentList().get(1);
    if (bFragment == null) {
      bFragment = new BFragment(mPageDialog, deviceRegisterUistate);
    }
    Fragment fragment =mPageDialog. getChildFragmentManager().findFragmentByTag("a");
    if (fragment == null) {
      mPageDialog.getChildFragmentManager().beginTransaction().hide(fragment).add(com.chervon.libBase.R.id.content_fragment, bFragment).addToBackStack(null).commitAllowingStateLoss();
    } else {
      mPageDialog.getChildFragmentManager().beginTransaction().replace(com.chervon.libBase.R.id.content_fragment, bFragment).addToBackStack(null).commitAllowingStateLoss();
    }
    // 切换
    // 防止实例
  }

  public void dissMiss() {
    mPageDialog.dismiss();
  }


  public void choiceProvince(DeviceRegisterUistate registerUistate) {

    mPrivonceDialog = new DialogUtil();
    int delay = 0;
    if (mProvinceItem == null) {
      delay = 1000;
    }
    mViewDataBinding.etProvince.postDelayed(new Runnable() {
      @Override
      public void run() {
        AFragment.this.getActivity().runOnUiThread(new Runnable() {
          @Override
          public void run() {
            mPrivonceDialog.showBottomListDialog2(AFragment.this.getContext(), LanguageStrings.getDeviceRegistProvinceTitle(),
              new DialogBottomListAdapter2.OnItemClickListener() {
              @Override
              public void onClick(View view, String itemName) {
                int position = (int) view.getTag();
                QuestionPageData questionPageData = registerUistate.getQuestionPageData();
                questionPageData.setDeviceId(registerUistate.getDeviceid());
                questionPageData.setProvince(itemName);
                registerUistate.setQuestionPageData(questionPageData);
                mViewDataBinding.setUiState(registerUistate);
                checkQuestionPage();
              }
            }, mProvinceItem);
          }
        });

      }
    }, delay);

  }

  public void choiceCountry(DeviceRegisterUistate registerUistate) {
    if (mCountryDialog == null) {
      mCountryDialog = new DialogUtil();
    }
    mCountryDialog.showBottomListDialog2(this.getContext(), LanguageStrings.getDeviceRegistCountryTitle(),
      new DialogBottomListAdapter2.OnItemClickListener() {
      @Override
      public void onClick(View view, String itemName) {
        int position = (int) view.getTag();
        QuestionPageData questionPageData = registerUistate.getQuestionPageData();
        questionPageData.setCountry(itemName);
        questionPageData.setProvince("");
        registerUistate.setQuestionPageData(questionPageData);
        mViewDataBinding.setUiState(registerUistate);
        checkQuestionPage();
        try {
          String label = mContryItem[position].getLabel();
          mViewModel.getProvinseInfo(label);
          ProgressHelper.showProgressView(getActivity(), 0);
        } catch (Exception e) {

        }

      }
    }, mContryItem);
  }


  public void checkQuestionPage() {

    QuestionPageData questionPageData = mViewDataBinding.getUiState().getQuestionPageData();
    String firstName = questionPageData.getFirstName();
    String lastName = questionPageData.getLastName();
    String city = questionPageData.getCity();
    String zipCode = questionPageData.getZipCode();
    String country = questionPageData.getCountry();
    String province = questionPageData.getProvince();
    if (!TextUtils.isEmpty(lastName) &&!TextUtils.isEmpty(firstName) && !TextUtils.isEmpty(city) &&
      !TextUtils.isEmpty(zipCode) &&
      !TextUtils.isEmpty(country) &&
      !TextUtils.isEmpty(province)) {
      disableBtnClick(true);
    } else {
      disableBtnClick(false);
    }


  }

  private void disableBtnClick(boolean b) {
    mViewDataBinding.btnNext.setEnabled(b);
    mViewDataBinding.btnNext.setClickable(b);
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

  }

  @Override
  public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

  }

  @Override
  public void afterTextChanged(Editable editable) {
checkQuestionPage();
  }
}

