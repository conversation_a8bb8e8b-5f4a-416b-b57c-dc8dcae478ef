package com.chervon.moudleDeviceManage.ui;


import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.DeviceRegisterRes;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.dialog.RecommendationDialog;
import com.chervon.libBase.utils.Utils;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.BuildConfig;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceRegisterBinding;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceRegisterInfoBinding;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterInfoViewModel;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: DeviceRegisterInfoFragment
 * @Description:
 * @Author:
 * @CreateDate:
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/20
 * @UpdateRemark: 欧洲注册结果界面显示修改
 * @Version: 1.1
 */
public class DeviceRegisterInfoFragment extends BaseEnhanceFragment<DeviceRegisterInfoViewModel, MoudleDevicemanageFragmentDeviceRegisterInfoBinding> implements BaseEnhanceFragment.OnkeyBackListener {
    public static final String DEVICE_REGISTER_UI_DATA = "device_register_data";
    public static final String REGIST_MORE = "RegistMore";
    private static final String COMPLETE_BUTTON_CLICK = "2";
    private static final String REGISTER_MORE_DEVICES_BUTTON_CLICK = "3";
    private static final String ADDRESS_OTHER = "Other";
    private DeviceInfo mDeviceInfo;
    private boolean isRegistMore;
    private DeviceRegisterRes deviceRecommendParts;

    @Override
    protected void initDatas(Bundle savedInstanceState) {
        initTrace();
        Bundle bundle = getArguments();
        if (bundle != null) {
            DeviceRegisterUistate deviceRegisterUistate = (DeviceRegisterUistate) bundle.getSerializable(DEVICE_REGISTER_UI_DATA);
            deviceRecommendParts = deviceRegisterUistate.getDeviceRegisterRes();
            mDeviceInfo = (DeviceInfo) bundle.getSerializable(PARAMETER_DEVICD);
            isRegistMore = deviceRegisterUistate.isRegistMore();
            String sn = deviceRegisterUistate.getSn().toUpperCase();
            deviceRegisterUistate.setSn(sn);

            if (!TextUtils.isEmpty(sn) && sn.contains("SN.")) {
                deviceRegisterUistate.setSn(sn.replace("SN.", ""));
            }
            //判断是否是Other
            if (null != deviceRegisterUistate) {
                String addressType = deviceRegisterUistate.getAddressType();
                if (!TextUtils.isEmpty(addressType)) {
                    if (addressType.equals(ADDRESS_OTHER)) {
                        deviceRegisterUistate.setAddress(TextUtils.isEmpty(deviceRegisterUistate.getPurchasePlaceOther()) ? "" : deviceRegisterUistate.getPurchasePlaceOther());
                    }
                }
            }

            mViewDataBinding.setUiState(deviceRegisterUistate);
            mViewDataBinding.setPresenter(this);
            if (TextUtils.isEmpty(mDeviceInfo.getDeviceIcon())) {
                Glide.with(this).load(mDeviceInfo.getIconUrl()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
            } else {
                Glide.with(this).load(mDeviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
            }
            //北美是否显示推荐弹窗
            if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA)) {
                if (null != deviceRecommendParts) {
                    RecommendationDialog.show(getActivity().getSupportFragmentManager(),
                            TextUtils.isEmpty(deviceRecommendParts.partName) ? "" : deviceRecommendParts.partName,
                            TextUtils.isEmpty(deviceRecommendParts.getImageUrl()) ? "" : deviceRecommendParts.getImageUrl(),
                            TextUtils.isEmpty(deviceRecommendParts.getShopUrl()) ? "" : deviceRecommendParts.getShopUrl(),
                            TextUtils.isEmpty(deviceRecommendParts.getPartModel()) ? "" : deviceRecommendParts.getPartModel()
                            , new RecommendationDialog.RecommendClickListener() {
                                @Override
                                public void closeDialog() {

                                }

                                @Override
                                public void shopNow() {
                                    jumpToWeb(TextUtils.isEmpty(deviceRecommendParts.getShopUrl()) ? "" : deviceRecommendParts.getShopUrl());
                                }
                            },pageResouce
                    );
                }
            }
        }
    }

    private void jumpToWeb(String routePath) {
        if (TextUtils.isEmpty(routePath)) {
            return;
        }
        try {
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_VIEW);
            // 移除FLAG_ACTIVITY_NEW_TASK，使用FLAG_ACTIVITY_NO_HISTORY确保web页面不会加入回退栈
            intent.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY);
            Uri content_url = Uri.parse(routePath);
            intent.setData(content_url);
            startActivity(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_devicemanage_fragment_device_register_info;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        //欧洲页面有点差别
        if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
            mViewDataBinding.tvTitlePurpose.setVisibility(View.GONE);
            mViewDataBinding.tvPurpose.setVisibility(View.GONE);
            mViewDataBinding.divid2.setVisibility(View.GONE);
            mViewDataBinding.divide4.setVisibility(View.GONE);
            mViewDataBinding.tvTitlePlace.setVisibility(View.GONE);
            mViewDataBinding.tvPlace.setVisibility(View.GONE);
            mViewDataBinding.tvKitWorn.setVisibility(View.GONE);
            mViewDataBinding.btnConfirm.setVisibility(View.GONE);
        }
    }

    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class<? extends DeviceRegisterInfoViewModel> getViewModelClass() {
        return DeviceRegisterInfoViewModel.class;
    }

    public void registerComplete(DeviceRegisterUistate uistate) {
        sendBaseTraceClick(COMPLETE_BUTTON_CLICK);
        if (isRegistMore) {
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP) .withInt(KEY_FROM,R.layout.moudle_devicemanage_fragment_device_register_info).navigation();
        } else if (1234 == uistate.getFrom()) {
//           ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL).withInt(KEY_PREV_FRAGMENT,12345)
//                   .navigation();
            getActivity().finish();
        } else if (!isRegistMore) {
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL)
                    .withSerializable(PARAMETER_DEVICD, mDeviceInfo).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    .navigation();
        } else {
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME) .withInt(KEY_FROM,R.layout.moudle_devicemanage_fragment_device_register_info).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        }

    }

    public void registeMore(DeviceRegisterUistate uistate) {
        if (null != uistate) {
            String addressType = uistate.getAddressType();
            if (!TextUtils.isEmpty(addressType)) {
                if (addressType.equals(ADDRESS_OTHER)) {
                    uistate.setAddress(ADDRESS_OTHER);
                }
            }
        }
        sendBaseTraceClick(REGISTER_MORE_DEVICES_BUTTON_CLICK);
        uistate.setRegistMore(true);
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        //手动把sn置为空后再跳转
        uistate.setSn("");
        uistate.setShowlostReceipt(false);
        uistate.setHasQuestionPage(false);
        uistate.setShowQuestionPage(false);
        uistate.setCbAnswer(false);
        uistate.setDeviceInfo(null);
        uistate.setShowEnterSN(true);
        bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uistate);
        bundle.putInt(KEY_PREV_FRAGMENT, R.id.deviceRegisterInfoFragment);
        controller.navigate(R.id.deviceRegisterFragment, bundle);
    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }

    @Override
    public boolean OnkeyBack() {
        sendBackTrace();
        DeviceRegisterUistate uistate = mViewDataBinding.getUiState();
        if (null == uistate) {
            if (isAdded()) {
                this.getActivity().finish();
            }
        }
        if (1234 == uistate.getFrom()) {
            if (isAdded()) {
                this.getActivity().finish();
            }
            return false;
        } else {
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                    .withInt(KEY_FROM,R.layout.moudle_devicemanage_fragment_device_register_info).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
            return true;
        }
    }


    private void initTrace() {
        stayEleId = "4";
        pageId = "106";
        mouduleId = "12";
        pageResouce = "1_9_105_106";
        nextButtoneleid = "2";
    }

}
