package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.model.AppConstants.PERMISSION_ALBUM_UNAUTHORIZED;
import static com.chervon.libBase.model.AppConstants.PERMISSION_CAMERA_UNAUTHORIZED;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_SN_FAIL;
import static com.chervon.libBase.utils.CameraUtil.CODE_GALLERY;
import static com.chervon.libBase.utils.CameraUtil.CODE_TAKE_PHOTO;
import static com.chervon.libBase.utils.CameraUtil.TYPE_TAKE_PHOTO;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.APP_DEVICE_INFO_ALREADY_REGISTERED;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_COMMIT_QUESTION_FAIL;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_COMMIT_QUESTION_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_ERROR;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_INIT;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RECEIP_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.VALIDATE_SN_FAIL;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.VALIDATE_SN_SUCCESS;
import static com.chervon.moudleDeviceManage.ui.DeviceRegisterInfoFragment.DEVICE_REGISTER_UI_DATA;
import static com.chervon.moudleDeviceManage.ui.HomeActivity.PICK_PDF_AND_WORD_REQUEST;

import android.Manifest;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.AppConstants;
import com.chervon.libBase.model.DeviceRegistStatusModel;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.ui.adapter.DialogBottomListAdapter2;
import com.chervon.libBase.ui.widget.CustomDatePicker;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.libBase.model.DeviceDictEntry;
import com.chervon.libBase.model.DictItem;
import com.chervon.libBase.model.DictNode;
import com.chervon.libBase.model.QuestionPageData;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceRegisterBinding;
import com.chervon.moudleDeviceManage.ui.adapter.ReceiptAdapter;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterViewModel;
import com.chervon.moudleDeviceManage.utils.DeviceManagerConstants;
import com.google.gson.Gson;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.engine.UriToFileTransformEngine;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.SandboxTransformUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: DeviceRegisterFragment
 * @Description:
 * @Author: hyman
 * @CreateDate: 2024/12/23 上午10:14
 * @UpdateUser: hyman
 * @UpdateDate: 2024/12/23
 * @UpdateRemark: 增加sn预校验
 * @Version: 1.1.2
 */
public class DeviceRegisterFragment extends BaseEnhanceFragment<DeviceRegisterViewModel, MoudleDevicemanageFragmentDeviceRegisterBinding> implements ItemClick, View.OnFocusChangeListener, BaseEnhanceFragment.OnkeyBackListener, QuestionPageDialog.DialogListenner {

    private static final String TAG = "DeviceRegisterFragment";
    public static final String FILE_UPLOAD_BUTTON_CLICK = "2";
    private static final String COMPLETE_BUTTON_CLICK = "3";
    final String DEVICE_HAS_REGISTED = "1";
    private List<String> url = new ArrayList<>();
    private ReceiptAdapter mReceiptAdapter;
    private DictNode[] mReceiptItem = new DictNode[]{};
    private DictNode[] mPlaceItem = new DictNode[]{};
    private DictNode[] mPurposeItem = new DictNode[]{};
    private DeviceInfo mDeviceInfo;
    private DialogUtil mReceiptDialog;
    private DialogUtil mPurchanceAddrDialog;
    private DialogUtil mPurposeDialog;

    int prevFragment = 0;
    private String editSn;
    private boolean isRegistedMorePage;
    private boolean isQuestionPageShow;
    public List<Fragment> fragmentList;
    protected Dialog mProgressDialog;
    /**
     * 用户选择了 i have my receipt
     */
    private static final int HAVE_RECEIPT = 1;
    /**
     * 用户选择了 i lost my receipt
     */
    private static final int LOST_RECEIPT = 2;
    /**
     * 用户选择了 product was gift
     */
    private static final int PRODUCT_WAS_A_GIFT = 3;
    /**
     * 用户选择住宅
     */
    private static final int PURPOSE_RESIDENTIAL_POSITION = 1;

    private DeviceRegisterUistate deviceRegisterUistate;

    private static final String PURPOSE_OTHER = "Other";
    private final String space_language = " ";
    private Drawable etShapeNormal;
    private Drawable etShapeFocus;
    private Drawable etShapeError;

    protected void initDataInView() {
        // 添加空值检查
        if (mViewDataBinding == null || !isAdded()) {
            return;
        }

        HomeActivity homeActivity = (HomeActivity) getActivity();
        etShapeFocus = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_focus);
        etShapeNormal = getResources().getDrawable(com.chervon.libBase.R.drawable.module_login_et_shape);
        etShapeError = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_error);
        mViewDataBinding.linearEditSn.setBackground(etShapeNormal);

        if (null == deviceRegisterUistate) {
            deviceRegisterUistate = new DeviceRegisterUistate();
            deviceRegisterUistate.setModelNo("");
            deviceRegisterUistate.setName("");
            deviceRegisterUistate.setSn("");
        }


        if (homeActivity != null) {
            DeviceInfo deviceInfo = null;
            Bundle bundle = getArguments();
            if (bundle == null) {
                bundle = homeActivity.getIntent().getExtras();
            }
            if (bundle != null) {
                prevFragment = (int) bundle.get(KEY_PREV_FRAGMENT);
                if (prevFragment == R.id.scanNearbyDevicesFragment || prevFragment == 1234 || prevFragment == 12345) {
                    //1234页面是非IOT设备立即注册或者非iot面板跳转
                    // 12345是WIFI设备立即注册跳转
                    deviceRegisterUistate.setFrom(prevFragment);
                    Object object = bundle.get(KEY_PREV_DATA);
                    if (object != null) {
                        deviceInfo = (DeviceInfo) object;
                    }
                } else if (prevFragment == R.id.controlPanel) {
                    deviceRegisterUistate.setFrom(prevFragment);
                    String deviceId = bundle.getString(KEY_PREV_DATA);
                    if (deviceId != null) {
                        ProgressHelper.showProgressView(getContext(), 0, true);
                        mViewModel.getDeviceInfo(deviceId);
                    }
                } else if (prevFragment == R.id.scanCodeFragment) {
                    //来自扫描页面 或 扫描的二级页面
                    deviceRegisterUistate = (DeviceRegisterUistate) bundle.get(KEY_PREV_DATA);
                    if (deviceRegisterUistate.isRegistMore()) {
                        mViewDataBinding.clTopContainer.setVisibility(View.GONE);
                        editSn = TextUtils.isEmpty(deviceRegisterUistate.getSn()) ? "" : deviceRegisterUistate.getSn();
                        mViewDataBinding.etSn.setText(editSn);
                        deviceRegisterUistate.setShowQuestionPage(false);
                    } else {
                        Object object = bundle.get(PARAMETER_DEVICD);
                        if (object != null) {
                            deviceInfo = (DeviceInfo) object;
                            deviceInfo.setDeviceId(TextUtils.isEmpty(deviceRegisterUistate.getDeviceid()) ? "" : deviceRegisterUistate.getDeviceid());
                            deviceRegisterUistate.setCheckSnHasRegistered(false);
                            deviceRegisterUistate.setShowSNRegisteredTips(false);
                            editSn = deviceInfo.getSn();
                            mViewDataBinding.etSn.setText(editSn);
                            deviceRegisterUistate.setShowQuestionPage(false);
                        }
                    }

                } else if (prevFragment == R.id.deviceRegisterInfoFragment) {
                    //来自注册结果页面
                    Object object = bundle.get(PARAMETER_DEVICD);
                    if (object != null) {
                        deviceInfo = (DeviceInfo) object;
                        editSn = deviceInfo.getSn();
                        mViewDataBinding.etSn.setText(editSn);
                        mViewDataBinding.clTopContainer.setVisibility(View.VISIBLE);
                        ProgressHelper.showProgressView(getContext(), 0, true);
                        mViewModel.getProductInfo(deviceInfo.getProductId());
                    } else {
                        mViewDataBinding.clTopContainer.setVisibility(View.GONE);
                    }
                    deviceRegisterUistate = (DeviceRegisterUistate) bundle.getSerializable(DEVICE_REGISTER_UI_DATA);
                    url = deviceRegisterUistate.getUrl();
                    if (deviceRegisterUistate.getReceiptType() == 1) {
                        mViewDataBinding.tvUpLoadReceip.setVisibility(View.VISIBLE);
                        mViewDataBinding.rvReceip.setVisibility(View.VISIBLE);
                    }
                    deviceRegisterUistate.setRegistMore(true);


                }
            }

            if (deviceInfo != null) {
                //做sn处理
                if (TextUtils.isEmpty(deviceInfo.getSn())) {
                    String sn = TextUtils.isEmpty(deviceInfo.getSn()) ? "--" : deviceInfo.getSn();
                    deviceRegisterUistate.setShowSN(LanguageStrings.appDeviceregistDevicesnText() + space_language + sn);
                    deviceRegisterUistate.setShowEnterSN(true);
                } else {
                    //如果输入框已经展示那么就不隐藏--如果是registerMore不走此逻辑
                    deviceRegisterUistate.setSn(deviceInfo.getSn());
                    if (!deviceRegisterUistate.isShowEnterSN() && !deviceRegisterUistate.isRegistMore()) {
                        deviceRegisterUistate.setShowSN(LanguageStrings.appDeviceregistDevicesnText() + space_language + deviceInfo.getSn());
                        deviceRegisterUistate.setSn(deviceInfo.getSn());
                        deviceRegisterUistate.setShowEnterSN(false);
                    }
                }


                deviceRegisterUistate.setDeviceid(deviceInfo.getDeviceId());

                mDeviceInfo = deviceInfo;
                if (null == mProgressDialog) {
                    mProgressDialog = DialogUtil.showRnLoadingDialog(getActivity());
                }
                //如果从SN或者SN更深一级的输入SN
                if (prevFragment == R.id.scanCodeFragment) {
                    if (TextUtils.isEmpty(mDeviceInfo.getSn())) {
                        mProgressDialog.dismiss();
                    } else {
                        mViewModel.validateSn(mDeviceInfo.getSn());
                    }
                } else {
                    if (TextUtils.isEmpty(mDeviceInfo.getDeviceId())) {
                        mProgressDialog.dismiss();
                    } else {
                        mViewModel.getDeviceInfo(mDeviceInfo.getDeviceId());
                    }

                }


                if (TextUtils.isEmpty(mDeviceInfo.getDeviceIcon()) && TextUtils.isEmpty(mDeviceInfo.getIconUrl())) {
                    deviceRegisterUistate.setModelNo("");
                    deviceRegisterUistate.setName("");
                } else {
                    if (!TextUtils.isEmpty(mDeviceInfo.getDeviceIcon())) {
                        Glide.with(this).load(mDeviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
                    } else {
                        Glide.with(this).load(mDeviceInfo.getIconUrl()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
                    }
                }
            } else {
                //注册更多
                if (prevFragment == R.id.scanCodeFragment && deviceRegisterUistate.isRegistMore()) {
                    String sn = deviceRegisterUistate.getSn();
                    mViewModel.validateSn(sn);
                }
            }
        }

        mViewDataBinding.setUiState(deviceRegisterUistate);

        mViewModel.userInfoLiveData = UserInfo.getUserLiveData();

        mViewModel.userInfoLiveData.observe(this, new Observer<User>() {
            @Override
            public void onChanged(User user) {
                DeviceRegisterUistate finalDeviceRegisterUistate = mViewDataBinding.getUiState();
                QuestionPageData questionPageData = finalDeviceRegisterUistate.getQuestionPageData();
                if (questionPageData == null) {
                    questionPageData = new QuestionPageData();
                }
                if (user != null) {
                    initUserInfo(questionPageData);
                }
                finalDeviceRegisterUistate.setQuestionPageData(questionPageData);
                isRegistedMorePage = finalDeviceRegisterUistate.isRegistMore();
                mViewDataBinding.setUiState(finalDeviceRegisterUistate);
            }
        });

        mViewDataBinding.setPresenter(this);


        mViewModel.mLiveData.observe(this, new Observer<DeviceRegisterUistate>() {
            @Override
            public void onChanged(DeviceRegisterUistate deviceRegisterUistate) {

                if (deviceRegisterUistate.getState() == RECEIP_SUCCESS) {
                    //发票上传成功后，执行设备注册

                    if (deviceRegisterUistate.getRecipKey() != null && deviceRegisterUistate.getRecipKey().size() == url.size()) {
                        DeviceRegisterUistate registerUiState = mViewDataBinding.getUiState();
                        registerUiState.setDeviceid(mDeviceInfo.getDeviceId());
                        registerUiState.setRecipKey(deviceRegisterUistate.getRecipKey());
                        mViewModel.registerDevice(registerUiState);
                    }

                } else if (deviceRegisterUistate.getState() == APP_DEVICE_INFO_ALREADY_REGISTERED) {
                    if (null != mProgressDialog) {
                        mProgressDialog.dismiss();
                    }

                    if (!TextUtils.isEmpty(deviceRegisterUistate.getMessage())) {
                        ToastUtils.showShort(deviceRegisterUistate.getMessage());
                    }
                    gotoErrPage(deviceRegisterUistate);
                } else if (deviceRegisterUistate.getState() == DEVICE_REGISTER_SUCCESS) {
                    if (null != mProgressDialog) {
                        mProgressDialog.dismiss();
                    }
                    DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
                    if (null == mDeviceInfo) {
                        mDeviceInfo = uiState.getDeviceInfo();
                    }
                    //如果deviceInfo仍为空，跳转失败
                    if (mDeviceInfo == null) {
                        gotoErrPage(deviceRegisterUistate);
                    }

                    if (!TextUtils.isEmpty(mDeviceInfo.getDeviceId())) {
                        //同步注册结果给RN
                        BaseApplication.registLiveData.postValue(new DeviceRegistStatusModel(mDeviceInfo.getDeviceId(), DeviceRegistStatusModel.REGIST_RESULT_SUCCESS));
                        upgradeUser();
                    }
                    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
                    Bundle bundle = new Bundle();
                    uiState.setUrl(url);
                    bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uiState);
                    bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
                    controller.navigate(R.id.deviceRegisterInfoFragment, bundle);
                    //设备注册成功后，上传问卷调查
                    registQuestion(uiState);

                } else if (deviceRegisterUistate.getState() == DEVICE_REGISTER_ERROR) {
                    if (null != mProgressDialog) {
                        mProgressDialog.dismiss();
                    }

                    if (!TextUtils.isEmpty(deviceRegisterUistate.getMessage())) {
                        ToastUtils.showShort(deviceRegisterUistate.getMessage());
                    }

                } else if (deviceRegisterUistate.getState() == DEVICE_REGISTER_COMMIT_QUESTION_SUCCESS || deviceRegisterUistate.getState() == DEVICE_REGISTER_COMMIT_QUESTION_FAIL) {

                    LogUtils.e(TAG, "设备提交维保问卷--->" + deviceRegisterUistate.getState());

                } else if (deviceRegisterUistate.getState() == RESPONSE_FAIL) {
                    if (null != mProgressDialog) {
                        mProgressDialog.dismiss();
                    }

                    if (null != mDeviceInfo) {
                        if (!TextUtils.isEmpty(mDeviceInfo.getDeviceId())) {
                            //同步注册结果给RN
                            BaseApplication.registLiveData.postValue(new DeviceRegistStatusModel(mDeviceInfo.getDeviceId(), DeviceRegistStatusModel.REGIST_RESULT_FAIL));
                        }
                    }

                    if (TextUtils.isEmpty(deviceRegisterUistate.getMessage())) {
                        ToastUtils.showShort(deviceRegisterUistate.getMessage());
                    }
                    gotoErrPage(deviceRegisterUistate);
                } else if (deviceRegisterUistate.getState() != 0) {
                    if (deviceRegisterUistate.isRegistMore()) {
                        mViewDataBinding.clTopContainer.setVisibility(View.GONE);
                    }
                    if (!TextUtils.isEmpty(deviceRegisterUistate.getMessage())) {
                        ToastUtils.showLong(deviceRegisterUistate.getMessage());
                    }
                    gotoErrPage(deviceRegisterUistate);

                }
                deviceRegisterUistate.setState(0);
            }
        });

        mViewModel.mDeviceInfoLiveData.observe(this, new Observer<DeviceInfo>() {
            @Override
            public void onChanged(DeviceInfo deviceInfo) {

                if (null != mProgressDialog) {
                    mProgressDialog.dismiss();
                }
                //deviceInfo缺失场景--提供pid查询问卷相关逻辑
                if (deviceInfo == null) {
                    if (null != mDeviceInfo) {
                        if (!TextUtils.isEmpty(mDeviceInfo.getProductId())) {
                            mViewModel.getProductInfo(mDeviceInfo.getProductId());
                        }
                    }
                    return;
                }


                mDeviceInfo = deviceInfo;
                deviceRegisterUistate.setDeviceInfo(mDeviceInfo);
                DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
                if (mDeviceInfo != null) {
                    Glide.with(DeviceRegisterFragment.this).load(mDeviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
                    String sn = TextUtils.isEmpty(deviceInfo.getSn()) ? "--" : deviceInfo.getSn();
                    uiState.setShowSN(LanguageStrings.appDeviceregistDevicesnText() + space_language + sn);

                    if (!TextUtils.isEmpty(mDeviceInfo.getSn())) {
                        uiState.setSn(mDeviceInfo.getSn());
                        if (uiState.isRegistMore()) {
                            uiState.setShowEnterSN(true);
                            uiState.setDeviceid(TextUtils.isEmpty(deviceInfo.getDeviceId()) ? "" : deviceInfo.getDeviceId());
                            uiState.setShowModelNo(LanguageStrings.app_deviceregisteu_modelnumber_textview_text() + space_language + deviceInfo.getCommodityModel());
                        } else {
                            uiState.setShowEnterSN(false);
                        }
                        uiState.setSn(mDeviceInfo.getSn());
                    } else {
                        uiState.setShowEnterSN(true);
                    }
                    mViewDataBinding.setUiState(uiState);
                    ProgressHelper.showProgressView(getContext(), 0, true);
                    mViewModel.getProductInfo(deviceInfo.getProductId());
                }

            }
        });

        mViewModel.mValidateSnLiveData.observe(this, new Observer<DeviceRegisterUistate>() {
            @Override
            public void onChanged(DeviceRegisterUistate uiState) {
                if (null != mProgressDialog) {
                    mProgressDialog.dismiss();
                }
                if (uiState.getState() == VALIDATE_SN_SUCCESS) {
                    if (null == deviceRegisterUistate) {
                        return;
                    }
                    mViewDataBinding.linearEditSn.setBackground(etShapeNormal);
                    deviceRegisterUistate.setCheckSnHasRegistered(false);
                    deviceRegisterUistate.setShowSNRegisteredTips(false);
                    mViewDataBinding.setUiState(deviceRegisterUistate);
                    //获取产品信息
                    mViewModel.getDeviceBySn(deviceRegisterUistate.getSn());

                } else if (uiState.getState() == APP_DEVICE_INFO_ALREADY_REGISTERED) {
                    if (null != mProgressDialog) {
                        mProgressDialog.dismiss();
                    }
                    if (!TextUtils.isEmpty(uiState.getMessage())) {
                        deviceRegisterUistate.setRegisteredWarnTips(uiState.getMessage());
                    }
                    mViewDataBinding.linearEditSn.setBackground(etShapeError);
                    deviceRegisterUistate.setCheckSnHasRegistered(true);
                    deviceRegisterUistate.setShowSNRegisteredTips(true);
                    mViewDataBinding.setUiState(deviceRegisterUistate);
                    checkItemSelect();
                } else if (uiState.getState() == VALIDATE_SN_FAIL) {
                    if (!TextUtils.isEmpty(uiState.getMessage())) {
                        deviceRegisterUistate.setRegisteredWarnTips(uiState.getMessage());
                    }
                    mViewDataBinding.linearEditSn.setBackground(etShapeError);
                    deviceRegisterUistate.setCheckSnHasRegistered(false);
                    deviceRegisterUistate.setShowSNRegisteredTips(true);
                    uiState.setState(0);
                    mViewDataBinding.setUiState(deviceRegisterUistate);
                    disableBtnClick(false);
                }

            }
        });

        mViewModel.mSnDeviceLiveData.observe(this, new Observer<DeviceRegisterUistate>() {
            @Override
            public void onChanged(DeviceRegisterUistate uiState) {

                if (null != mProgressDialog) {
                    mProgressDialog.dismiss();
                }

                if (uiState.getState() == RESPONSE_SUCCESS) {
                    if (deviceRegisterUistate.isRegistMore()) {
                        //如果是注册更多
                        mDeviceInfo = uiState.getDeviceInfo();
                        if (null != mDeviceInfo) {
                            mViewDataBinding.clTopContainer.setVisibility(View.VISIBLE);
                            Glide.with(DeviceRegisterFragment.this).load(mDeviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
                            deviceRegisterUistate.setDeviceInfo(mDeviceInfo);
                            uiState.setShowEnterSN(true);
                            uiState.setName(TextUtils.isEmpty(mDeviceInfo.getDeviceName()) ? "" : mDeviceInfo.getDeviceName());
                            uiState.setDeviceid(TextUtils.isEmpty(mDeviceInfo.getDeviceId()) ? "" : mDeviceInfo.getDeviceId());
                            uiState.setModelNo(TextUtils.isEmpty(mDeviceInfo.getCommodityModel()) ? "" : mDeviceInfo.getCommodityModel());
                            uiState.setShowModelNo(LanguageStrings.app_deviceregisteu_modelnumber_textview_text() + space_language + mDeviceInfo.getCommodityModel());
                            if (!TextUtils.isEmpty(mDeviceInfo.getInfoStatus())) {
                                //设备已经被注册，并且是注册更多。把设备顶部隐藏
                                if (mDeviceInfo.getInfoStatus().equals(DEVICE_HAS_REGISTED) && deviceRegisterUistate.isShowEnterSN()) {
                                    mViewDataBinding.clTopContainer.setVisibility(View.GONE);
                                } else {
                                    mViewDataBinding.clTopContainer.setVisibility(View.VISIBLE);
                                }

                            }

                            if (!TextUtils.isEmpty(mDeviceInfo.getProductId())) {
                                mViewModel.getProductInfo(mDeviceInfo.getProductId());
                            }
                        }
                    } else {
                        //正常注册
                        //1、校验产品一致性
                        if (mDeviceInfo != null) {
                            if (null != uiState.getDeviceInfo()) {
                                if (!TextUtils.isEmpty(uiState.getDeviceInfo().getProductId()) && !TextUtils.isEmpty(mDeviceInfo.getProductId())) {
                                    if (uiState.getDeviceInfo().getProductId().equals(mDeviceInfo.getProductId())) {
                                        if (!TextUtils.isEmpty(mDeviceInfo.getProductId())) {
                                            mViewModel.getProductInfo(mDeviceInfo.getProductId());
                                        }
                                    }
                                }
                            }

                        }
                    }

                } else if (uiState.getState() == RESPONSE_FAIL) {
                    if (!TextUtils.isEmpty(uiState.getMessage())) {
                        ToastUtils.showShort(uiState.getMessage());
                        uiState.setMessage("");
                    }
                } else if (uiState.getState() == RESPONSE_SN_FAIL) {
                    if (deviceRegisterUistate.isRegistMore()) {
                        deviceRegisterUistate.setCheckSnHasRegistered(false);
                        deviceRegisterUistate.setShowSNRegisteredTips(false);
                        mViewDataBinding.setUiState(deviceRegisterUistate);
                        if (!TextUtils.isEmpty(uiState.getMessage())) {
                            ToastUtils.showShort(uiState.getMessage());
                            uiState.setMessage("");
                        }
                    }
                    ProgressHelper.hideProgressView(getContext());
                }
                uiState.setState(0);

            }
        });

        mViewModel.mHasQuestionLiveData.observe(this, new Observer<DeviceRegisterUistate>() {
            @Override
            public void onChanged(DeviceRegisterUistate deviceRegisterUistate) {

                ProgressHelper.hideProgressView(DeviceRegisterFragment.this.getActivity());
                DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
                uiState.setModelNo(deviceRegisterUistate.getModelNo());
                uiState.setShowModelNo(LanguageStrings.app_deviceregist_devicemodelno_textview_text() + space_language + deviceRegisterUistate.getModelNo());
                uiState.setName(TextUtils.isEmpty(deviceRegisterUistate.getName()) ? "" : deviceRegisterUistate.getName());
                if (mViewModel.mHasQuestionLiveData.getValue() != null && mViewModel.mHasQuestionLiveData.getValue().isHasQuestionPage()) {
                    uiState.setHasQuestionPage(true);
                } else {
                    uiState.setHasQuestionPage(false);
                    uiState.setCbAnswer(false);
                }
                mViewDataBinding.setUiState(uiState);
                checkItemSelect();
            }
        });


        mViewModel.mDictLiveData.observe(this, new Observer<DeviceDictEntry>() {
            @Override
            public void onChanged(DeviceDictEntry deviceDictEntry) {
                if (deviceDictEntry != null) {
                    DictItem[] dictItems = deviceDictEntry.getEntry();
                    for (int i = 0; dictItems != null && i < dictItems.length; i++) {
                        if ("appDeviceRegisterPurchasePlace".equalsIgnoreCase(dictItems[i].getDictName())) {
                            mPlaceItem = dictItems[i].getNodes();
                        } else if ("appDeviceRegisterReceiptInformation".equalsIgnoreCase(dictItems[i].getDictName())) {
                            mReceiptItem = dictItems[i].getNodes();
                        } else if ("appDeviceRegisterApplyWith".equalsIgnoreCase(dictItems[i].getDictName())) {
                            mPurposeItem = dictItems[i].getNodes();
                        }
                    }
                }
            }
        });

        DatePicker();

        mViewDataBinding.cbAnswer.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    isQuestionPageShow = true;
                    deviceRegisterUistate.setCbAnswer(true);
                } else {
                    isQuestionPageShow = false;
                    deviceRegisterUistate.setCbAnswer(false);

                }
                checkItemSelect();
            }
        });

        mViewDataBinding.etSn.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                editSn = mViewDataBinding.etSn.getText().toString();
                mViewDataBinding.getUiState().setSn(editSn);
                checkItemSelect();
                if (deviceRegisterUistate.isRegistMore()) {
                    mDeviceInfo = null;
                    deviceRegisterUistate.setShowlostReceipt(false);
                    deviceRegisterUistate.setHasQuestionPage(false);
                    deviceRegisterUistate.setShowQuestionPage(false);
                    deviceRegisterUistate.setCbAnswer(false);
                    deviceRegisterUistate.setDeviceInfo(null);
                    mViewDataBinding.setUiState(deviceRegisterUistate);
                }
            }
        });


        checkItemSelect();
        initPhotoSelectAdapter();

    }

    private void initUserInfo(QuestionPageData questionPageData) {
        User user = UserInfo.getDataOnly();
        questionPageData.setEmail(user.getEmail());
        questionPageData.setFirstName(user.getFirstName());
        questionPageData.setLastName(user.getLastName());
    }

    private void initPhotoSelectAdapter() {
        // 添加空值检查
        if (mViewDataBinding == null || !isAdded()) {
            return;
        }

            GridLayoutManager gridLayoutManager = new GridLayoutManager(this.getActivity(), 3, RecyclerView.VERTICAL, false);
            mViewDataBinding.rvReceip.setLayoutManager(gridLayoutManager);

            mReceiptAdapter = new ReceiptAdapter(this.getActivity(), this, url);
            mReceiptAdapter.setHasStableIds(true);
            mViewDataBinding.rvReceip.setAdapter(mReceiptAdapter);

            //已选过的图片进行填充
            if (null != deviceRegisterUistate) {
                if (null != deviceRegisterUistate.getUrl()) {
                    url = deviceRegisterUistate.getUrl();
                    if (!url.isEmpty()) {
                        mViewDataBinding.tvUpLoadReceip.setVisibility(View.VISIBLE);
                        mViewDataBinding.rvReceip.setVisibility(View.VISIBLE);
                        mReceiptAdapter.setDatas(url);
                    }
                }
            }

    }

    private void gotoErrPage(DeviceRegisterUistate uiState) {
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        uiState.setUrl(url);
        bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uiState);
        bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
        controller.navigate(R.id.deviceRegisterErrFragment, bundle);
    }


    @Override
    protected int getLayoutId() {
        return R.layout.moudle_devicemanage_fragment_device_register;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding = (MoudleDevicemanageFragmentDeviceRegisterBinding) viewDataBinding;
        mViewDataBinding.etDate.setOnFocusChangeListener(this);
        mViewDataBinding.etSn.setOnFocusChangeListener(this);

        mViewDataBinding.clContainer.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus && null != getActivity()) {
                KeyboardUtils.hideSoftInput(getActivity());
            }
        });
        mViewDataBinding.etSn.setOnKeyListener(new View.OnKeyListener() {
            @Override
            public boolean onKey(View v, int keyCode, KeyEvent event) {
                // 判断是否是回车键
                if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN) {
                    // 在这里处理回车键事件
                    String inputSN = mViewDataBinding.etSn.getText().toString();
                    if (deviceRegisterUistate.isShowEnterSN() && !TextUtils.isEmpty(inputSN)) {
                        mViewModel.validateSn(inputSN);
                    }
                    return true;
                }
                return false;
            }
        });
        initDataInView();
    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class<? extends DeviceRegisterViewModel> getViewModelClass() {
        return DeviceRegisterViewModel.class;
    }

    public void clearFocus() {
        // 添加空值检查
        if (mViewDataBinding == null || !isAdded()) {
            return;
        }

            if (null != deviceRegisterUistate) {
                if (deviceRegisterUistate.isShowEnterSN()) {
                    mViewDataBinding.etSn.clearFocus();
                    mViewDataBinding.linearEditSn.setBackground(etShapeNormal);
                }
            }


    }

    /**
     * Recepit
     *
     * @param registerUistate
     */
    public void choiceReceiptInfo(DeviceRegisterUistate registerUistate) {
        clearFocus();
        if (mReceiptDialog == null) {
            mReceiptDialog = new DialogUtil();
        }
        mReceiptDialog.showBottomListDialog2(this.getContext(), LanguageStrings.app_deviceregistactionsheet_receipt_textview_text(), new DialogBottomListAdapter2.OnItemClickListener() {
            @Override
            public void onClick(View view, String itemName) {
                // 添加空值检查
                if (mViewDataBinding == null || !isAdded()) {
                    return;
                }

                    int position = (int) view.getTag();
                    if (position != 0) {
                        mViewDataBinding.tvUpLoadReceip.setVisibility(View.GONE);
                        mViewDataBinding.rvReceip.setVisibility(View.GONE);
                        url.clear();
                        mReceiptAdapter.setDatas(url);
                        mReceiptAdapter.notifyDataSetChanged();
                    } else {
                        mViewDataBinding.tvUpLoadReceip.setVisibility(View.VISIBLE);
                        mViewDataBinding.rvReceip.setVisibility(View.VISIBLE);
                    }
                    registerUistate.setReceiptType(position + 1);
                    registerUistate.setReceipt(itemName);
                    mViewDataBinding.setUiState(registerUistate);
                    //如果设备有维保相关 选择 I lost my receipt 那么显示文案
                    if (null == mViewModel.mHasQuestionLiveData.getValue()) {
                        return;
                    }
                    if (mViewModel.mHasQuestionLiveData.getValue().isHasQuestionPage()) {
                        if (position == 1) {
                            mViewDataBinding.tvLostReceipt.setVisibility(View.VISIBLE);
                        } else {
                            mViewDataBinding.tvLostReceipt.setVisibility(View.GONE);
                        }
                    }
                    checkItemSelect();

            }
        }, mReceiptItem);

    }

    public void choiceDate(DeviceRegisterUistate registerUistate) {
        clearFocus();
        String selectTime = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA).format(selectedTimeStamp);
        customDatePicker.show(0 != selectedTimeStamp ? selectTime : now);
        checkItemSelect();
    }

    private CustomDatePicker customDatePicker;
    private String now;
    private String endTime;
    private long selectedTimeStamp = 0;

    private void DatePicker() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
        //获取当前时间
        Calendar cal = Calendar.getInstance();
        //设置当前时间
        cal.setTime(new Date());
        endTime = sdf.format(cal.getTime());
        now = sdf.format(new Date());
        customDatePicker = new CustomDatePicker(this.getContext(), new CustomDatePicker.ResultHandler() {
            @Override
            public void handle(String time, long timeStamp) {
                // 添加空值检查
                if (mViewDataBinding == null || !isAdded()) {
                    return;
                }
                    mViewDataBinding.etDate.setText(time);
                    selectedTimeStamp = timeStamp;
                    checkItemSelect();

            }
        }, "2000-01-01 00:00", endTime, now);
        customDatePicker.showSpecificTime(false);
        customDatePicker.setIsLoop(false);
    }

    public void choicePurchanceAddr(DeviceRegisterUistate registerUistate) {
        clearFocus();
        if (mPurchanceAddrDialog == null) {
            mPurchanceAddrDialog = new DialogUtil();
        }


        mPurchanceAddrDialog.showBottomListDialog2(this.getContext(), LanguageStrings.app_deviceregistactionsheet_location_textview_text(), new DialogBottomListAdapter2.OnItemClickListener() {
            @Override
            public void onClick(View view, String itemName) {
                if (null != mViewDataBinding) {


                    int position = (int) view.getTag();
                    DictNode selectedDictNode = mPlaceItem[position];

                    if (selectedDictNode.getLabel().equals(PURPOSE_OTHER)) {
                        //设置 Place of purchase为 Other 时，展开输入框
                        registerUistate.setPurchasePlaceOtherShow(true);
                        mViewDataBinding.etPlaceOther.requestFocus();
                        mViewDataBinding.etPlaceOther.addTextChangedListener(new TextWatcher() {
                            @Override
                            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                            }

                            @Override
                            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                            }

                            @Override
                            public void afterTextChanged(Editable editable) {

                                checkItemSelect();
                            }
                        });
                    } else {
                        //设置 Place of purchase Other 时，展开输入框
                        registerUistate.setPurchasePlaceOtherShow(false);
                        registerUistate.setPurchasePlaceOther("");
                        mViewDataBinding.llAddrOther.setVisibility(View.GONE);
                    }


                    registerUistate.setAddressType(selectedDictNode.getLabel());
                    registerUistate.setAddress(itemName);
                    mViewDataBinding.setUiState(registerUistate);
                    checkItemSelect();
                }
            }
        }, mPlaceItem);
    }

    public void choicePurpose(DeviceRegisterUistate registerUistate) {
        clearFocus();
        if (mPurposeDialog == null) {
            mPurposeDialog = new DialogUtil();
        }
        mPurposeDialog.showBottomListDialog2(this.getContext(), LanguageStrings.appDeviceregistactionsheetUseTextview(), new DialogBottomListAdapter2.OnItemClickListener() {
            @Override
            public void onClick(View view, String itemName) {
                int position = (int) view.getTag();
                registerUistate.setPurposeType(position + 1);
                registerUistate.setPurpose(itemName);
                mViewDataBinding.setUiState(registerUistate);


                checkItemSelect();
            }
        }, mPurposeItem);
    }

    public void clickRegisterDevice(DeviceRegisterUistate registerUistate) {
        if (isRegistedMorePage) {
            sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, COMPLETE_BUTTON_CLICK, "1");
        } else {
            sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, COMPLETE_BUTTON_CLICK, "1", "receipt_information", registerUistate.getReceipt(), "place_of_purchase", registerUistate.getAddress(), "purchase_date", registerUistate.getDate(), "purpose", registerUistate.getPurpose());

        }

        if (null == mDeviceInfo) {
            editSn = mViewDataBinding.etSn.getText().toString();
            ProgressHelper.showProgressView(getContext(), 0, false);
            if (!deviceRegisterUistate.checkSnHasRegistered()) {
                mViewModel.getDeviceBySn(editSn);
            }
        } else {
            registerDevice(registerUistate);
        }
    }

    public void realregisterDevice(DeviceRegisterUistate registerUistate) {


        List<String> receiptKey = mViewDataBinding.getUiState().getRecipKey();
        if (receiptKey != null) {
            mViewDataBinding.getUiState().getRecipKey().clear();
        }
        if (null == mProgressDialog) {
            mProgressDialog = DialogUtil.showRnLoadingDialog(getActivity());
        } else {
            mProgressDialog.show();
        }


        if (!url.isEmpty() && registerUistate.getReceiptType() == HAVE_RECEIPT) {
            //如果用户选择图片，先上传图片
            if (url.get(0) != null) {
                File receipFile = new File(url.get(0));
                mViewModel.upLoadReceipImage(mDeviceInfo.getDeviceId(), receipFile);
            }
        } else {
            //如果用户未选择图片那么就执行注册
            registerUistate.setDeviceid(mDeviceInfo.getDeviceId());
            mViewModel.registerDevice(registerUistate);
        }
    }


    private void registQuestion(DeviceRegisterUistate registerUistate) {
        if (isQuestionPageShow) {
            registerUistate.setIfCheckedWarranty(1);
            mViewModel.commitQuestionPage(registerUistate);
        }
    }

    QuestionPageDialog bottomAlertDialog;

    public void registerDevice(DeviceRegisterUistate registerUistate) {
        //手动输入框失去焦点
        clearFocus();

        if (isQuestionPageShow) {
            if (fragmentList == null) {
                fragmentList = new ArrayList<>();
                bottomAlertDialog = new QuestionPageDialog(registerUistate, fragmentList);
                fragmentList.add(new AFragment(bottomAlertDialog, registerUistate));
                fragmentList.add(new BFragment(bottomAlertDialog, registerUistate));
                fragmentList.add(new FragmentThree(bottomAlertDialog, registerUistate));
                fragmentList.add(new FragmentFour(bottomAlertDialog, registerUistate));
                fragmentList.add(new FragmentFive(bottomAlertDialog, registerUistate));
                fragmentList.add(new FragmentSix(bottomAlertDialog, registerUistate));
            }

            mViewDataBinding.getUiState().setDeviceInfo(mDeviceInfo);
            QuestionPageDialog.initTrace("3", "109", "12", pageResouce + "_109", "", "");
            QuestionPageDialog.show(getParentFragmentManager(), null, "", "", "", null, mViewDataBinding.getUiState(), this, fragmentList, bottomAlertDialog);

        } else {
            realregisterDevice(registerUistate);
        }


    }


    public void uploadReceipt() {
        ((HomeActivity) getActivity()).setResultListenner(new ResultListenner() {

            @Override
            public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data, String imgUrl) {
                if (requestCode == PICK_PDF_AND_WORD_REQUEST) {


                    try {
                        //File文件 过滤PDF和Word文件--
                        //Defensive Programming: Encapsulate potentially risky operations in try-catch blocks to handle exceptions gracefully.
                        if (TextUtils.isEmpty(imgUrl)) {
                            LogUtils.e("imgUrl is null");
                        }
                        if (imgUrl.contains(DeviceManagerConstants.MEDIA_PDF) ||
                                imgUrl.contains(DeviceManagerConstants.MEDIA_WORD_DOC) ||
                                imgUrl.contains(DeviceManagerConstants.MEDIA_WORD_DOCX) || imgUrl.contains(DeviceManagerConstants.MEDIA_WORD_PAGES)) {
                            url.add(imgUrl);
                            mReceiptAdapter.setDatas(url);
                            mReceiptAdapter.notifyDataSetChanged();
                        } else {
                            ToastUtils.showShort(LanguageStrings.app_deviceregist_illegalfile_textview_text());
                        }
                    } catch (Exception e) {
                        LogUtils.e(TAG, "DeviceRegisterFragment is onActivityResult error --" + e.getMessage());
                    }

                } else {
                    //拍照和相册
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
                        if (tempFile != null && imgUrl == null) {
                            url.clear();
                            url.add(0, tempFile.getAbsolutePath());
                            mReceiptAdapter.setDatas(url);
                            mReceiptAdapter.notifyDataSetChanged();
                        } else {
                            url.clear();
                            url.add(0, imgUrl);
                            checkItemSelect();
                            mReceiptAdapter.setDatas(url);
                            mReceiptAdapter.notifyDataSetChanged();
                        }
                    }
                }


            }
        });

        DialogUtil.showDeviceRegisterBottomCardDialog(this.getContext(), new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (view.getId() == R.id.tvOpenCamera) {
                    goToTakePhotoPage();
                } else if (view.getId() == R.id.tvOpenAlbum) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {

                        String[] permissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES};
                        if (null == getActivity()) {
                            return;
                        }
                        RxPermissions rxPermissions = new RxPermissions(getActivity());
                        rxPermissions.requestEach(permissions).subscribe(new Consumer<Permission>() {
                            @Override
                            public void accept(Permission permission) throws Exception {
                                if (permission.granted) {
                                    getToOpenGalleyWith13();
                                } else {
                                    showPermissionError(permission.name);
                                }
                            }
                        });
                    } else {
                        getToOpenGalley();
                    }
                } else if (view.getId() == R.id.tvOpenFiler) {
                    openFile();
                }
            }
        });
    }

    /**
     * 打开文件
     */
    private void openFile() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("*/*");
        String[] mimeTypes = {AppConstants.MEDIA_TYPE_PDF,
                AppConstants.MEDIA_TYPE_DOC,
                AppConstants.MEDIA_TYPE_DOCX};
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);
        try {
            // 过滤出PDF和Word文件
            getActivity().startActivityForResult(intent, PICK_PDF_AND_WORD_REQUEST);
        } catch (ActivityNotFoundException e) {
            e.printStackTrace();
            LogUtils.e("无法打开文件选择器");
        }

    }


    private void getToOpenGalleyWith13() {
        if (getActivity().isDestroyed() || getActivity().isFinishing()) {
            return;
        }

        PictureSelector.create(this).openSystemGallery(SelectMimeType.ofImage()).setSelectionMode(SelectModeConfig.SINGLE).setSkipCropMimeType().setSandboxFileEngine(new UriToFileTransformEngine() {
            @Override
            public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                //Android 10 沙盒资源访问
                if (call != null) {
                    String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                    call.onCallback(srcPath, sandboxPath);
                }
            }
        }).setSandboxFileEngine(new UriToFileTransformEngine() {
            @Override
            public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                if (call != null) {
                    String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                    call.onCallback(srcPath, sandboxPath);
                }
            }
        }).forSystemResultActivity(new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(ArrayList<LocalMedia> result) {
                String imgPath = result.get(0).getSandboxPath();
                url.clear();
                url.add(0, imgPath);
                checkItemSelect();
                mReceiptAdapter.setDatas(url);
                mReceiptAdapter.notifyDataSetChanged();
            }

            @Override
            public void onCancel() {

            }
        });
    }


    private void getToOpenGalley() {

        final String[] permissionsGroup = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
        if (Build.VERSION.SDK_INT >= 23) {

            RxPermissions rxPermissions = new RxPermissions(this);
            rxPermissions.setLogging(true);
            rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                    if (permission.granted) {
                        //处理允许权限后的操作
                        Intent i = new Intent();
                        i.setAction(Intent.ACTION_PICK);
                        i.setType("image/*");
                        startActivityForResult(i, CODE_GALLERY);
                        return;
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                        //处理用户点击禁止后的操作
                        showPermissionError(permission.name);
                    } else {
                        showPermissionError(permission.name);
                    }
                }
            });

        } else {
            Intent i = new Intent();
            i.setAction(Intent.ACTION_PICK);
            i.setType("image/*");
            startActivityForResult(i, CODE_GALLERY);
        }
    }


    private void goToTakePhotoPage() {

        final String[] permissionsGroup = new String[]{Manifest.permission.CAMERA};


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {

            RxPermissions rxPermissions = new RxPermissions(this);
            rxPermissions.setLogging(true);
            rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                    if (permission.granted) {
                        //处理允许权限后的操作
                        selectPicFromCamera();
                        return;
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                        //处理用户点击禁止后的操作
                        showPermissionError(permission.name);
                    } else {
                        showPermissionError(permission.name);
                    }

                }
            });


        } else {
            Intent takeIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            Uri photoUri = getMediaFileUri(TYPE_TAKE_PHOTO);
            takeIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
            startActivityForResult(takeIntent, CODE_TAKE_PHOTO);
        }
    }

    private void showPermissionError(String permissionText) {
        if (null == getActivity()) {
            return;
        }
        if (TextUtils.isEmpty(permissionText)) {
            return;
        }

        if (!getActivity().isFinishing()) {
            int errorCode = permissionText.equalsIgnoreCase(Manifest.permission.CAMERA) ? PERMISSION_CAMERA_UNAUTHORIZED : PERMISSION_ALBUM_UNAUTHORIZED;
            DialogUtil.cameraAndalbumAlertDialog(getContext(), errorCode, LanguageStrings.app_modifyavator_gosetting_textview_text(), LanguageStrings.app_setting_clearcachecancle_button_text(), new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // onConfirmed 跳转到系统设置
                    goIntentSetting();
                }
            }, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //onCanceled
                    // DialogUtil.closeSimpleDialog();
                }
            });
        }
    }

    /**
     * 跳转到三星系统设置。其他机型暂无适配计划
     */
    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getActivity().getPackageName(), null);
        intent.setData(uri);
        try {
            startActivity(intent);
        } catch (Exception e) {

        }
    }


    File tempFile;

    protected void selectPicFromCamera() {

        if (null == getActivity()) {
            return;
        }

        if (getActivity().isDestroyed() || getActivity().isFinishing()) {
            return;
        }
        try {
            PictureSelector.create(this).openCamera(SelectMimeType.ofImage()).setSandboxFileEngine(new UriToFileTransformEngine() {
                @Override
                public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                    //Android 10 沙盒资源访问
                    if (call != null) {
                        String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                        call.onCallback(srcPath, sandboxPath);
                    }
                }
            }).setSandboxFileEngine(new UriToFileTransformEngine() {
                @Override
                public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                    if (call != null) {
                        String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                        call.onCallback(srcPath, sandboxPath);
                    }
                }
            }).forResultActivity(new OnResultCallbackListener<LocalMedia>() {
                @Override
                public void onResult(ArrayList<LocalMedia> result) {
                    String imgPath = result.get(0).getSandboxPath();
                    url.clear();
                    url.add(0, imgPath);
                    checkItemSelect();
                    mReceiptAdapter.setDatas(url);
                    mReceiptAdapter.notifyDataSetChanged();
                }

                @Override
                public void onCancel() {
                    //取消
                }
            });
        } catch (Exception e) {

        }


    }


    public Uri getMediaFileUri(int type) {
        File mediaStorageDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "DCIM");
        if (!mediaStorageDir.exists()) {
            if (!mediaStorageDir.mkdirs()) {
                return null;
            }
        }
        //建立Media File
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

        File mediaFile;

        if (type == TYPE_TAKE_PHOTO) {
            mediaFile = new File(mediaStorageDir.getPath() + File.separator + "IMG_" + timeStamp + ".jpg");
        } else {

            return null;

        }

        return Uri.fromFile(mediaFile);

    }

    public void previewReceipt(String url) {
        DialogUtil.showPhotoDialog(this.getContext(), url, null);
    }


    public boolean checkItemSelect() {
        boolean isSn = true;
        if (!TextUtils.isEmpty(editSn)) {
            isSn = true;
        } else {
            isSn = false;
        }
        boolean isPurchasePlaceOther = true;

        try {

            DeviceRegisterUistate deviceRegisterUistate = mViewDataBinding.getUiState();

            if (deviceRegisterUistate.isPurchasePlaceOtherShow()) {
                String purchasePlaceOther = deviceRegisterUistate.getPurchasePlaceOther();
                if (TextUtils.isEmpty(purchasePlaceOther)) {
                    isPurchasePlaceOther = false;
                }
            }

            boolean isReceipt = false;
            if (null == deviceRegisterUistate) {
                return false;
            }
            String receipt = deviceRegisterUistate.getReceipt();
            if (!TextUtils.isEmpty(receipt)) {
                if (url.isEmpty() && deviceRegisterUistate.getReceiptType() == HAVE_RECEIPT) {
                    isReceipt = false;
                } else {
                    isReceipt = true;
                }

            }

            boolean isPlace = false;
            String place = deviceRegisterUistate.getAddress();
            if (!TextUtils.isEmpty(place)) {
                isPlace = true;
            }
            boolean isDate = false;
            String date = deviceRegisterUistate.getDate();
            if (!TextUtils.isEmpty(date)) {
                isDate = true;
            }
            boolean isPurpose = false;
            String purpose = deviceRegisterUistate.getPurpose();
            if (!TextUtils.isEmpty(purpose)) {
                isPurpose = true;
            }

            checkQuestionResult();


            if (deviceRegisterUistate.isShowEnterSN()) {
                //1、校验DeviceInfo存在 2、设备状态是未注册
                boolean deviceInfoExist = (null != deviceRegisterUistate.getDeviceInfo());
                boolean hasRegistered = deviceRegisterUistate.checkSnHasRegistered();
                boolean deviceInfoCheck = deviceInfoExist && !hasRegistered;
                //有输入框进行以下校验
                if (isPurpose && isDate && isPlace && isReceipt && isSn && isPurchasePlaceOther && checkMainItemSelect() && deviceInfoCheck) {
                    disableBtnClick(true);
                    return true;
                } else {
                    disableBtnClick(false);
                    return false;
                }
            } else {
                //无输入框进行以下校验
                if (isPurpose && isDate && isPlace && isReceipt && isSn && isPurchasePlaceOther && checkMainItemSelect()) {
                    disableBtnClick(true);
                    return true;
                } else {
                    disableBtnClick(false);
                    return false;
                }
            }
        } catch (Exception e) {
            LogUtils.i(TAG, "checkItemSelect is error-->" + e.getMessage());
            return false;
        }
    }

    /**
     * 检查问卷调查是否显示
     */
    public void checkQuestionResult() {
        //问卷显示条件--满足条件为：1、i have my receipt 2、已选择图片  3、目的为:家用 4、拥有调查问卷

        if (deviceRegisterUistate.getReceiptType() == HAVE_RECEIPT && deviceRegisterUistate.getPurposeType() == PURPOSE_RESIDENTIAL_POSITION && deviceRegisterUistate.isHasQuestionPage() && !deviceRegisterUistate.getUrl().isEmpty()) {

            deviceRegisterUistate.setShowlostReceipt(false);
            deviceRegisterUistate.setShowQuestionPage(true);
            deviceRegisterUistate.setShowGiftResidential(false);
        } else if (deviceRegisterUistate.getReceiptType() == PRODUCT_WAS_A_GIFT && deviceRegisterUistate.getPurposeType() == PURPOSE_RESIDENTIAL_POSITION) {
            //用户选择product was a gift && 选择非工业用途
            deviceRegisterUistate.setShowlostReceipt(false);
            deviceRegisterUistate.setShowQuestionPage(false);
            if (deviceRegisterUistate.isHasQuestionPage()) {
                //如果是有问卷调查就显示文案
                deviceRegisterUistate.setShowGiftResidential(true);
            } else {
                //没有问卷调查就显示文案
                deviceRegisterUistate.setShowGiftResidential(false);
            }

        } else {
            //如果用户按选择展示底部文案提示有问卷丢失发票选项满足条件：  1、i have my receipt 2、有问卷调查 3、并且选择非工业用途
            if (deviceRegisterUistate.isHasQuestionPage() && deviceRegisterUistate.getReceiptType() == LOST_RECEIPT && deviceRegisterUistate.getPurposeType() == PURPOSE_RESIDENTIAL_POSITION) {
                deviceRegisterUistate.setShowlostReceipt(true);
            } else if (deviceRegisterUistate.isHasQuestionPage() && deviceRegisterUistate.getPurposeType() != PURPOSE_RESIDENTIAL_POSITION) {
                deviceRegisterUistate.setShowlostReceipt(false);
            }
            deviceRegisterUistate.setShowGiftResidential(false);
            deviceRegisterUistate.setShowQuestionPage(false);
            deviceRegisterUistate.setCbAnswer(false);
        }

        mViewDataBinding.setUiState(deviceRegisterUistate);
    }


    public boolean checkMainItemSelect() {
        boolean isPurchasePlaceOther = true;
        DeviceRegisterUistate deviceRegisterUistate = mViewDataBinding.getUiState();
        if (deviceRegisterUistate.isPurchasePlaceOtherShow()) {
            deviceRegisterUistate.setPurchasePlaceOtherShow(false);
            String purchasePlaceOther = deviceRegisterUistate.getPurchasePlaceOther();
            if (TextUtils.isEmpty(purchasePlaceOther)) {
                isPurchasePlaceOther = false;
            }
        }

        boolean isReceipt = true;
        if (deviceRegisterUistate == null) {
            return false;
        }
        String receipt = deviceRegisterUistate.getReceipt();
        if (!TextUtils.isEmpty(receipt)) {
            if (deviceRegisterUistate.getReceiptType() == HAVE_RECEIPT) {
                isReceipt = !deviceRegisterUistate.getUrl().isEmpty();
            }

        }

        boolean isPlace = false;
        String place = deviceRegisterUistate.getAddress();
        if (!TextUtils.isEmpty(place)) {
            isPlace = true;
        }
        boolean isDate = false;
        String date = deviceRegisterUistate.getDate();
        if (!TextUtils.isEmpty(date)) {
            isDate = true;
        }


        return isDate && isPlace && isReceipt && isPurchasePlaceOther;

    }


    private void disableBtnClick(boolean b) {
        mViewDataBinding.btDone.setEnabled(b);
        mViewDataBinding.btDone.setClickable(b);
    }

    @Override
    public void onItemClick(Object v) {
        int id = ((View) v).getId();
        if (id == R.id.ivfooter) {
            uploadReceipt();
            sendBaseTraceClick(FILE_UPLOAD_BUTTON_CLICK);
        } else if (id == R.id.ivDeviceIcon) {
            int index = (int) ((View) v).getTag();
            previewReceipt(url.get(index));
        }

    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {
        checkItemSelect();
    }

    @Override
    public void onFocusChange(View view, boolean focus) {
        int id = view.getId();
        if (id == mViewDataBinding.etDate.getId()) {
            checkItemSelect();
        } else if (id == mViewDataBinding.etSn.getId()) {
            if (focus) {
                mViewDataBinding.linearEditSn.setBackground(etShapeFocus);
            } else {
                mViewDataBinding.linearEditSn.setBackground(etShapeNormal);
                String inputSN = mViewDataBinding.etSn.getText().toString();
                if (deviceRegisterUistate.isShowEnterSN() && !TextUtils.isEmpty(inputSN)) {
                    mViewModel.validateSn(inputSN);
                } else if (deviceRegisterUistate.isShowEnterSN() && TextUtils.isEmpty(inputSN)) {
                    deviceRegisterUistate.setShowSNRegisteredTips(false);
                    deviceRegisterUistate.setRegisteredWarnTips("");
                    mViewDataBinding.setUiState(deviceRegisterUistate);
                }
            }

        }

    }

    @Override
    public boolean OnkeyBack() {
        sendBackTrace();
        try {
            DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
            if (null == uiState) {
                if (isAdded()) {
                    this.getActivity().finish();
                }
            }
            if (1234 == uiState.getFrom()) {
                if (isAdded()) {
                    this.getActivity().finish();
                }
                return false;
            } else {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
                        .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .withInt(KEY_FROM, R.layout.moudle_devicemanage_fragment_device_register)
                        .navigation();
                return true;
            }
        } catch (Exception e) {
            if (isAdded()) {
                this.getActivity().finish();
            }
        }
        return true;
    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }

    @Override
    public void onCommitData(DeviceRegisterUistate uiState) {
        sendClickTrace(DeviceRegisterFragment.this.getContext(), "12", "109", pageResouce + "_109", "2", "");

        if (!isAdded()) {
            return;
        }

        if (uiState == null) {
            return;
        }

        if (null != mProgressDialog) {
            mProgressDialog.dismiss();
        }


        if (uiState.getState() == DEVICE_REGISTER_SUCCESS) {
            //注册成功跳转
            if (null == getActivity()) {
                return;
            }
            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
            Bundle bundle = new Bundle();
            bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uiState);
            bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
            controller.navigate(R.id.deviceRegisterInfoFragment, bundle);
        } else {
            uiState.setState(DEVICE_REGISTER_INIT);
            gotoErrPage(uiState);
        }


    }


    @Override
    protected void initDatas(Bundle savedInstanceState) {
        if (isRegistedMorePage) {
            initRegistMoreTrace();
        } else {
            initTrace();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        checkItemSelect();
    }

    public void scanQrcode(DeviceRegisterUistate registerUistate) {
        final String[] permissionsGroup = new String[]{Manifest.permission.CAMERA};
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            RxPermissions rxPermissions = new RxPermissions(this);
            rxPermissions.setLogging(true);
            rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                    if (permission.granted) {
                        //处理允许权限后的操作
                        registerUistate.setUrl(url);
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                                .withInt(KEY_FROM, deviceRegisterUistate.isRegistMore() ? R.id.deviceRegisterMoreFragment : R.id.deviceRegisterFragment)
                                .withInt(KEY_TO, R.id.scanCodeFragment)
                                .withSerializable(KEY_PREV_DATA, registerUistate)
                                .withSerializable(PARAMETER_DEVICD, mDeviceInfo)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                        //处理用户点击禁止后的操作
                        showPermissionError(permission.name);
                    } else {
                        showPermissionError(permission.name);
                    }

                }
            });


        } else {
            registerUistate.setUrl(url);
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET).withInt(KEY_FROM, R.id.deviceRegisterMoreFragment).withInt(KEY_TO, R.id.scanCodeFragment).withSerializable(KEY_PREV_DATA, registerUistate).withSerializable(PARAMETER_DEVICD, mDeviceInfo).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

        }

    }

    private void initTrace() {
        stayEleId = "4";
        pageId = "105";
        mouduleId = "12";
        nextButtoneleid = "2";
    }

    private void initRegistMoreTrace() {
        stayEleId = "4";
        pageId = "107";
        mouduleId = "12";
        nextButtoneleid = "2";
    }

    /**
     * 更新评分系统
     */
    private void upgradeUser() {
        String isRegistedSuccess = "isRegistedSuccess";
        //更新注册系统
        User user = UserInfo.get();
        user.setIsRegistedSuccess(isRegistedSuccess);
        UserInfo.set(user);
    }
}
