package com.chervon.moudleDeviceManage.ui.viewmodel;

import static com.chervon.libNetwork.iot.AwsMqttService.subscribeTopic;
import static com.chervon.libNetwork.iot.AwsMqttService.unsubscribeTopic;
import static com.chervon.moudleDeviceManage.ui.DeviceListFragment.QUICK_CONTROL_BUTTON_STATUS;

import android.app.Application;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.arch.core.util.Function;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.lifecycle.Transformations;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.moudleDeviceManage.data.model.LastMessageRespone;
import com.chervon.moudleDeviceManage.data.repository.HomeRepo;
import com.chervon.moudleDeviceManage.ui.state.DeviceListUistate;

import org.jetbrains.annotations.NotNull;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.viewmodel
 * @ClassName: DeviceListViewModel
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:13
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:13
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceListViewModel extends BaseViewModel<DeviceListUistate> {
    //控制无设备缺省页展示
    public MutableLiveData<Boolean> showDefaultList = new MutableLiveData<>(true);
    public MutableLiveData<DeviceInfo> deviceSateLiveData = new MutableLiveData<DeviceInfo>();

    private final HomeRepo mHomeRepo;
    public MutableLiveData<Boolean> mEditState = new MutableLiveData<>(true);
    public MutableLiveData<Boolean> lastMessageliveData=new MutableLiveData<Boolean>();
    public MutableLiveData<Boolean> isSmartScan = new MutableLiveData<>();



    public DeviceListViewModel(@NonNull Application application) {
        super(application);
        mHomeRepo = new HomeRepo(application);

        User input = UserInfo.getDataOnly();
        ((MutableLiveData) mLiveData).setValue(new DeviceListUistate());

        if (TextUtils.isEmpty(input.getFirstName())) {
            Date today = new Date(System.currentTimeMillis());
            SimpleDateFormat sf = new SimpleDateFormat("HH");
            int hour = Integer.valueOf(sf.format(today));
            if (hour >= 6 && hour < 12) {
                mLiveData.getValue().setSayHi(LanguageStrings.app_devicelist_morning_textview_text());
            } else if (hour >= 12 && hour < 18) {
                mLiveData.getValue().setSayHi(LanguageStrings.app_devicelist_afternoon_textview_text());
            }else if (hour >= 18 && hour < 22) {
                mLiveData.getValue().setSayHi(LanguageStrings.app_devicelist_evening_textview_text());
            } else {
                mLiveData.getValue().setSayHi(LanguageStrings.app_devicelist_night_textview_text());
            }
        } else {
            mLiveData.getValue().setSayHi(LanguageStrings.getHiString()+" " + captureName(input.getFirstName()+","));
        }



    }
    public static String captureName(String name) {
        name = name.substring(0, 1).toUpperCase() + name.substring(1);
        return  name;
    }
    @Override
    public void onCreate(@NotNull LifecycleOwner lifecycleOwner) {

    }


    @Override
    public void onStart(@NotNull LifecycleOwner lifecycleOwner) {
      mHomeRepo.getDeviceListFromDbFirst((MutableLiveData<DeviceListUistate>) mLiveData);
      mHomeRepo.getProducts();
    }

    @Override
    public void onResume(@NotNull LifecycleOwner lifecycleOwner) {
     //   getAllDevices();
    }

    @Override
    public void onPause(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onStop(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onDestroy(@NotNull LifecycleOwner lifecycleOwner) {

    }

    public void upDateDevice() {
    }

    public void controlDevice(DeviceInfo deviceInfo) {

     String status= deviceInfo.getShortCutVos().get(0).getShadowStatus()+"";
     boolean isSwitch=false;
     if(status.contains("false")){
       isSwitch=true;
     }else{
       isSwitch=false;
     }
     String shadowId=QUICK_CONTROL_BUTTON_STATUS;
     if(deviceInfo.getShortCutVos()!=null&&deviceInfo.getShortCutVos().size()>0){
       shadowId=deviceInfo.getShortCutVos().get(0).getShadowId();
     }
       ;
     String  topic= "$aws/things/"+deviceInfo.getDeviceId()+"/shadow/update";
     String  paramsStr= "{\"state\":{\"desired\":{\""+shadowId+"\":{\"1\":"+isSwitch+"},\"23005\":{\"1\":false}}}}";
     editProperty(topic,paramsStr,  deviceInfo);
    }
  public void editProperty(String topic, String dpState,DeviceInfo deviceInfo) {
    LogUtils.iTag("quikecontrol", "RN editProperty", "topic: " + topic, "dpState: " + dpState);
    AwsMqttService.publishTopic(topic, dpState,deviceInfo);
  }

    public void resetDevicePassword(DeviceInfo deviceInfo) {

        mHomeRepo.resetDevicePassword((MutableLiveData<DeviceListUistate>) mLiveData, deviceInfo.getDeviceId(),deviceInfo.getIsOnline() == 1);
    }

    public void deleteDevice(DeviceInfo deviceInfo) {
        mHomeRepo.deleteDevice((MutableLiveData<DeviceListUistate>) mLiveData, deviceInfo);
    }

    public void reFreshDeviceList() {
          mHomeRepo.getDeviceList((MutableLiveData<DeviceListUistate>) mLiveData);
    }

    public void reOrderDevice(List<DeviceInfo> data) {
        mHomeRepo.reOrderDevice((MutableLiveData<DeviceListUistate>) mLiveData,data);
    }

    public void getDeviceStatus( DeviceInfo data) {
        mHomeRepo.getDeviceStatus(  deviceSateLiveData,data);
    }

    public void getLastMessage() {
        mHomeRepo.getLastMessage(lastMessageliveData);
    }

  public void updateQuickControlStatus(String deviceId, int finalQuickControlStatus) {
    mHomeRepo.updateQuickControlStatus((MutableLiveData<DeviceListUistate>) mLiveData,  deviceId,   finalQuickControlStatus);
  }


//  public void getDebugList() {
//    mHomeRepo.getDebugList(debugDevicesLiveData);
//  }

  public void getSmartScan(MutableLiveData<Boolean> isSmartScan){
      mHomeRepo.getSmartStatus(isSmartScan);
  }


  public void subscribeDeviceTopic(List<DeviceInfo> infoList) {
    for(int i=0;infoList!=null&&i<infoList.size();i++) {
      DeviceInfo deviceInfo=infoList.get(i);
      if (deviceInfo.getShortCutVos()!=null){
        String deviceId = deviceInfo.getDeviceId();
        String  deviceTopic = String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceId);
        subscribeTopic(deviceTopic);
      }
    }
  }

  public void unsubscribeDeviceTopic(List<DeviceInfo> infoList) {
    for(int i=0;infoList!=null&&i<infoList.size();i++) {
      DeviceInfo deviceInfo=infoList.get(i);
      if (deviceInfo.getShortCutVos()!=null){
        String deviceId = deviceInfo.getDeviceId();
        String  deviceTopic = String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceId);
        unsubscribeTopic(deviceTopic);
      }
    }
  }

  public void getQuickControlStatusFromDb() {
    mHomeRepo.getQuickControlStatusFromDb((MutableLiveData<DeviceListUistate>) mLiveData);
  }

  /**
   * 移除所有观察者
   */
  public void removeAllLiveData(){
    deviceSateLiveData.removeObservers(this);
    mEditState.removeObservers(this);
    lastMessageliveData.removeObservers(this);
    isSmartScan.removeObservers(this);
    mLiveData.removeObservers(this);
  }
}
