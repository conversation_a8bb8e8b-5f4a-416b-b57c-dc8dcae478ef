package com.chervon.moudleDeviceManage.ui.adapter;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_USER;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.media.MediaFormat;
import android.media.MediaPlayer;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.os.Environment;
import android.provider.MediaStore;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.MediaController;
import android.widget.SimpleExpandableListAdapter;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.amazonaws.logging.Log;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.adapter.BaseDataBindingAdapter;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.data.model.DeviceAgreeMentRespone;
import com.chervon.moudleDeviceManage.data.model.DeviceVideoEntry;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageRecyclePrivacyItemBinding;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageRecycleProductVideoItemBinding;
import com.google.android.exoplayer2.Format;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.analytics.AnalyticsListener;
import com.google.android.exoplayer2.extractor.DefaultExtractorsFactory;
import com.google.android.exoplayer2.extractor.ExtractorsFactory;
import com.google.android.exoplayer2.source.LoadEventInfo;
import com.google.android.exoplayer2.source.MediaLoadData;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.android.exoplayer2.util.Util;
import com.google.android.exoplayer2.video.VideoFrameMetadataListener;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.zip.Inflater;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: DevicesCardAdapter
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/11 下午2:53
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/11 下午2:53
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class ProductVideoAdapter extends BaseDataBindingAdapter {
    private DeviceVideoEntry[] datas;
    private ItemClick viewModel;
    private Context context;
    private boolean showDel = false;
    //true-收起视频 只展示一个视频  false-显示所有视频
    private boolean isPart = true;
    public long enterTime = System.currentTimeMillis();
    public String mouduleId;
    public String pageId;
    public String pageResouce;
    public String stayEleId;
    public String nextButtoneleid;

    public ProductVideoAdapter(Context context) {
        super(context);
    }

    public List<SimpleExoPlayer> playerList = new ArrayList<>();
    public HashMap<Integer, SimpleExoPlayer> playerHashMap = new HashMap<Integer, SimpleExoPlayer>();
    public HashMap<Integer, PlayerView> playerViewHashMap = new HashMap<Integer, PlayerView>();

    public ProductVideoAdapter(Context context, ItemClick viewModel, DeviceVideoEntry[] datas) {
        super(context);
        this.context = context;
        this.viewModel = viewModel;
        this.datas = datas;
        initTrace();
    }

    private void initTrace() {
        stayEleId = "2";
        pageId = "75";
        mouduleId = "9";
        nextButtoneleid = "2";
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_devicemanage_recycle_product_video_item;
    }

    @SuppressLint("ResourceAsColor")
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        MoudleDevicemanageRecycleProductVideoItemBinding binding = (MoudleDevicemanageRecycleProductVideoItemBinding) ((MyViewHolder) holder).viewDataBinding;
        if (playerViewHashMap.get(position) == null) {
            PlayerView playerView = (PlayerView) LayoutInflater.from(context).inflate(R.layout.base_play_view, null);
            playerViewHashMap.put(position, playerView);
        }
        PlayerView playerView = playerViewHashMap.get(position);
        if (null != playerView.getParent()) {
            FrameLayout videoFragment = (FrameLayout) playerView.getParent();
            videoFragment.removeView(playerView);
        }
        binding.flVideoView.addView(playerViewHashMap.get(position));

        View viewControl = playerViewHashMap.get(position).findViewById(com.chervon.libBase.R.id.myExoPlayer);
        viewControl.setVisibility(View.GONE);
        LogUtils.i("lesly length = " + datas.length + " position = " + position);
        if (datas.length > 0) {
            binding.setUiState(datas[position]);
            binding.setPresenter(viewModel);
            ImageButton imageButtonExt = binding.getRoot().findViewById(com.chervon.libBase.R.id.exo_fullscreen_ext);
            imageButtonExt.setVisibility(View.GONE);
            ImageButton imageButton = binding.getRoot().findViewById(R.id.exo_fullscreen_button);
            imageButton.setVisibility(View.VISIBLE);
            LogUtils.i("leslybug data = " + datas[position] + " url = " + datas[position].getUrl());
            if (datas[position].getUrl() == null) {
                datas[position].setUrl("");
            }
            LogUtils.i("leslybug data1 = " + datas[position] + " url = " + datas[position].getUrl());
            Uri uri = Uri.parse(datas[position].getUrl());
            SimpleExoPlayer mPlayer = playerHashMap.get(position);
            if (mPlayer == null) {
                mPlayer = new SimpleExoPlayer.Builder(context).build();
                playerList.add(mPlayer);
                playerHashMap.put(position, mPlayer);
                initializePlayer(playerViewHashMap.get(position), mContext, uri, mPlayer);
            } else {
                if (mPlayer.isPlaying()) {
                    mPlayer.pause();
                }

                if (binding.clLoading.getVisibility() == View.GONE) {
                    binding.ibPlay.setVisibility(View.VISIBLE);
                }
                playerViewHashMap.get(position).setPlayer(mPlayer);
                LogUtils.d("exoPlayergetCurrentPosition_viewHolder" + mPlayer.getCurrentPosition());
            }

            binding.ibPlay.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    LogUtils.d("exoPlayer is clicked");

                    view.setVisibility(View.GONE);
                    viewControl.setVisibility(View.VISIBLE);
                    //   stopAllPlayer(position);
                    try {
                        if (playerHashMap.get(position) != null && !playerHashMap.get(position).isPlaying()) {
                            playerHashMap.get(position).play();
                        }
                    } catch (Exception e) {

                    }
                    viewModel.onItemClick(null);

                }
            });

            imageButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    if (playerHashMap.get(position) != null) {
                        playerHashMap.get(position).pause();
                    }

                    //点击视频全屏
                    LogUtils.d("exoPlayer is showFullScreenVideo");
                    initPageResouce();
                    ((Activity) context).setRequestedOrientation(SCREEN_ORIENTATION_USER);
                    enterTime = System.currentTimeMillis();
                    sendExposure(mContext, mouduleId, pageId, pageResouce);
                    binding.flVideoView.removeView(playerViewHashMap.get(position));

                    DialogUtil.showFullScreenVideoDialog(context, uri, null, new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {

                            notifyItemChanged(position);
                            sendBackTrace();
                            sendStayDuration();
                            delPageResouce();
                        }
                    }, playerHashMap.get(position).getCurrentPosition(), playerHashMap.get(position), playerViewHashMap.get(position));

                }
            });


            SimpleExoPlayer finalMPlayer = mPlayer;
            mPlayer.addAnalyticsListener(new AnalyticsListener() {

                @Override
                public void onPlaybackStateChanged(EventTime eventTime, int state) {
                    AnalyticsListener.super.onPlaybackStateChanged(eventTime, state);

                    LogUtils.d("onPlaybackStateChanged" + "state" + state + "EventTime" + eventTime.realtimeMs + "currentTimeline" + eventTime.currentPlaybackPositionMs);
                    if (state == Player.STATE_IDLE) {
                        showVideoLoading(binding, View.GONE, View.VISIBLE);
                    } else if (state == Player.STATE_BUFFERING) {
                        showVideoLoading(binding, View.VISIBLE, View.GONE);
                    } else if (state == Player.STATE_READY) {
                        if (eventTime.currentPlaybackPositionMs == 300) {
                            binding.clLoading.setVisibility(View.GONE);
                            binding.ibPlay.setVisibility(View.VISIBLE);
                        } else {
                            binding.clLoading.setVisibility(View.GONE);
                        }


                    } else if (state == Player.STATE_ENDED) {
                        finalMPlayer.seekTo(300);
                        finalMPlayer.pause();
                        binding.ibPlay.setVisibility(View.VISIBLE);
                    }


                }

                @Override
                public void onIsPlayingChanged(EventTime eventTime, boolean isPlaying) {
                    AnalyticsListener.super.onIsPlayingChanged(eventTime, isPlaying);
                    if (isPlaying) {
                        binding.clLoading.setVisibility(View.GONE);
                    }

                }

                @Override
                public void onVideoFrameProcessingOffset(EventTime eventTime, long totalProcessingOffsetUs, int frameCount) {
                    AnalyticsListener.super.onVideoFrameProcessingOffset(eventTime, totalProcessingOffsetUs, frameCount);
                    LogUtils.d("onVideoFrameProcessingOffset" + "EventTime" + eventTime.realtimeMs + "frameCount" + frameCount + "totalProcessingOffsetUs" + totalProcessingOffsetUs);
                }

                @Override
                public void onPlayerStateChanged(EventTime eventTime, boolean playWhenReady, int playbackState) {
                    if (playWhenReady) {

                    } else {

                        showVideoLoading(binding, View.GONE, View.VISIBLE);
                        LogUtils.d("AnalyticsListener" + "pause");

                    }
                }

                @Override
                public void onLoadStarted(EventTime eventTime, LoadEventInfo loadEventInfo, MediaLoadData mediaLoadData) {
                    AnalyticsListener.super.onLoadStarted(eventTime, loadEventInfo, mediaLoadData);
                    showVideoLoading(binding, View.VISIBLE, View.GONE);
                    LogUtils.d("onLoadStarted" + "EventTime" + eventTime + "LoadEventInfo" + loadEventInfo.toString());
                }

                @Override
                public void onLoadCompleted(EventTime eventTime, LoadEventInfo loadEventInfo, MediaLoadData mediaLoadData) {
                    AnalyticsListener.super.onLoadCompleted(eventTime, loadEventInfo, mediaLoadData);
                }

                @Override
                public void onLoadCanceled(EventTime eventTime, LoadEventInfo loadEventInfo, MediaLoadData mediaLoadData) {
                    AnalyticsListener.super.onLoadCanceled(eventTime, loadEventInfo, mediaLoadData);
                }

                @Override
                public void onLoadError(EventTime eventTime, LoadEventInfo loadEventInfo, MediaLoadData mediaLoadData, IOException error, boolean wasCanceled) {
                    AnalyticsListener.super.onLoadError(eventTime, loadEventInfo, mediaLoadData, error, wasCanceled);

                }
            });


        }
    }

    public void notifyDataSetChangedWithRestart() {
        if (null != datas) {
            return;
        }
        if (datas.length == 0) {
            return;
        }

        // 重新初始化播放器资源
        for (int i = 0; i < datas.length; i++) {
            if (playerHashMap.get(i) == null) {
                SimpleExoPlayer player = new SimpleExoPlayer.Builder(context).build();
                playerList.add(player);
                playerHashMap.put(i, player);

                // 重新初始化PlayerView
                PlayerView playerView = (PlayerView) LayoutInflater.from(context)
                        .inflate(R.layout.base_play_view, null);
                playerViewHashMap.put(i, playerView);

                // 初始化播放器和绑定事件
                initializePlayer(playerView, context, Uri.parse(datas[i].getUrl()), player);
            }
        }
        super.notifyDataSetChanged();
    }

    private void showVideoLoading(MoudleDevicemanageRecycleProductVideoItemBinding binding, int visible, int gone) {
        binding.clLoading.setVisibility(visible);
        binding.ibPlay.setVisibility(gone);
    }

    private void stopAllPlayer(int position) {
        Collection<SimpleExoPlayer> collections = playerHashMap.values();
        if (collections != null && collections.size() > 0) {
            for (SimpleExoPlayer simplePlayer : collections) {
                if (simplePlayer.isPlaying()) {

                    simplePlayer.stop();
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        if (isPart) {
            if (datas.length > 0) {
                return 1;
            } else {
                return datas.length;
            }
        } else {
            return datas.length;
        }

    }

    public boolean isPart() {
        return isPart;
    }

    public void setPart(boolean part) {
        isPart = part;
        notifyDataSetChanged();
    }

    public boolean isShowDel() {
        return showDel;
    }

    public void setShowDel(boolean showDel) {
        this.showDel = showDel;
    }


    @Override
    public long getItemId(int position) {
        return position;
    }


    private SimpleExoPlayer initializePlayer(PlayerView playerView, Context context, Uri uri, SimpleExoPlayer mPlayer) {

        playerView.setPlayer(mPlayer);
        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(context,
                Util.getUserAgent(context, "myExoPlayer"));
        MediaSource mediaSource = new ProgressiveMediaSource.Factory(dataSourceFactory)
                .createMediaSource(MediaItem.fromUri(uri));
        mPlayer.prepare(mediaSource);
        long duration = mPlayer.getDuration();

        mPlayer.seekTo(300);

        return mPlayer;
    }

    public DeviceVideoEntry[] getDatas() {
        return datas;
    }

    public void setDatas(DeviceVideoEntry[] datas) {
        this.datas = datas;
    }

    private void sendBackTrace() {
        sendClickTrace(mContext, mouduleId, pageId, pageResouce, "1", "1");
    }

    private void sendStayDuration() {
        sendDurationTrace(mContext, mouduleId, pageId, pageResouce, "", stayEleId, System.currentTimeMillis() - enterTime);
    }

    private void initPageResouce() {
        pageResouce = ((BaseApplication) ((Activity) mContext).getApplication()).getCurrentPageResouce();
        pageResouce = pageResouce + "_" + pageId;
    }

    private void delPageResouce() {
        String pageResourceDel = pageResouce.replace("_" + pageId, "");
        ((BaseApplication) ((Activity) mContext).getApplication()).setCurrentPageResouce(pageResourceDel);
    }


    public void onPause() {
        for (int i = 0; i < playerList.size(); i++) {
            SimpleExoPlayer exoPlayer = playerList.get(i);
            if (exoPlayer != null) {

                exoPlayer.pause();
                exoPlayer.seekToPreviousWindow();
                LogUtils.d("exoPlayergetCurrentPosition" + exoPlayer.getCurrentPosition());
            }

        }
    }

    public void onStop() {
        for (int i = 0; i < playerList.size(); i++) {
            playerList.get(i).stop();
            playerList.get(i).release();

        }

        if (playerList != null) {
            playerList.clear();

        }
        if (playerHashMap != null) {
            playerHashMap.clear();
        }
        if (playerViewHashMap != null) {
            playerViewHashMap.clear();
        }
    }
}
