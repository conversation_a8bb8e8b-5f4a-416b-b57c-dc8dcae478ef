package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.content.Context;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.BuildConfig;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityDeviceRegistedInfoBinding;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityHomeBinding;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceRegisterBinding;
import com.chervon.moudleDeviceManage.ui.state.DeviceRegistedDetailUistate;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterInfoViewModel;

import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: HomeActivity
 * @Description: the home page of app
 * @Author: wangheng
 * @CreateDate: 2022/4/28 下午4:20
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/20
 * @UpdateRemark:  欧洲注册界面显示修改
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_REGISTED_DETAIL)
public class DeviceInfoRegistedActivity extends BaseActivity<DeviceRegisterInfoViewModel> {

    private MoudleDevicemanageActivityDeviceRegistedInfoBinding mBinding;
    private ToolbarData mToolbarData;
    @Autowired(name = KEY_PREV_DATA)
    public int prevFragment;
    @Autowired(name = KEY_PREV_DATA)
    public DeviceInfo deviceInfo;
    private Uri photoUri;
    private ResultListenner resultListenner;
    private DeviceInfo mDeviceInfo;
    private String space_language = " ";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_devicemanage_activity_device_registed_info;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {

        mBinding = (MoudleDevicemanageActivityDeviceRegistedInfoBinding) viewDataBinding;
        mBinding.title1.getPaint().setTypeface(Typeface.DEFAULT_BOLD);
        findViewById(R.id.toolbarDividie).setVisibility(View.VISIBLE);
        mToolbarData = new ToolbarData(LanguageStrings.appDeviceregistTitletext(), new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goBack();
            }
        });
        mBinding.setToolbarData(mToolbarData);
        mBinding.setUiState(new DeviceRegistedDetailUistate());
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            int fragmentId = getIntent().getExtras().getInt(KEY_PREV_FRAGMENT);
            int from = bundle.getInt(KEY_FROM);
            int pageTo = bundle.getInt(KEY_TO);
            mDeviceInfo = (DeviceInfo) bundle.getSerializable(KEY_PREV_DATA);
            if (!this.isFinishing()) {
                Glide.with(this).load(mDeviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mBinding.ivDeviceIcon);
            }
            mViewModel.getDeviceRegistedDetail(mDeviceInfo.getDeviceId());
            mViewModel.getDeviceInfo(mDeviceInfo.getDeviceId());
            mViewModel.getProductDetail(mDeviceInfo.getProductId());

            if (fragmentId == 0) {
                initPanelTrace();
            } else {
                initTrace();
            }


        } else {
        }
        //欧洲页面有点差别
        if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
            mBinding.tvTitlePurpose.setVisibility(View.GONE);
            mBinding.tvPurpose.setVisibility(View.GONE);
            mBinding.divid2.setVisibility(View.GONE);
            mBinding.divide4.setVisibility(View.GONE);
            mBinding.tvTitlePlace.setVisibility(View.GONE);
            mBinding.tvPlace.setVisibility(View.GONE);
            mBinding.btnConfirm.setVisibility(View.GONE);
        }

    }

    @SuppressLint("RestrictedApi")
    private void goBack() {
        finish();
        sendBackTrace();

    }


    @Override
    protected void initData(Bundle savedInstanceState) {

        mViewModel.mLiveData.observe(this, new Observer<DeviceRegistedDetailUistate>() {
            @Override
            public void onChanged(DeviceRegistedDetailUistate data) {
                DeviceRegistedDetailUistate uiState = mBinding.getUiState();
                if (data.getState() == RESPONSE_SUCCESS) {
                    if (!TextUtils.isEmpty(mDeviceInfo.getSn())) {
                        uiState.setSn(mDeviceInfo.getSn());
                    }

                    uiState.setApplyWith(data.getApplyWith());
                    uiState.setPurchasePlace(data.getPurchasePlace());
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat(Utils.getBuildEvn().equals(Utils.EVN_NA) ? "MM/dd/yyyy" : "dd/MM/yyyy", Locale.CHINA);
                        mBinding.tvDate.setText(sdf.format(data.getPurchaseTime()));
                    } catch (Exception e) {
                        mBinding.tvDate.setText("");
                        LogUtils.e("sdf.format is error--"+e.getMessage());
                    }

                    mBinding.setUiState(uiState);
                } else if (data.getState() != RESPONSE_FAIL) {
                    if (!TextUtils.isEmpty(data.getMessage())) {
                        ToastUtils.showLong(data.getMessage());
                    }
                }
                data.setState(0);
            }
        });

        mViewModel.mProductInfoLiveData.observe(this, new Observer<DeviceRegisterUistate>() {
            @Override
            public void onChanged(DeviceRegisterUistate data) {
                DeviceRegistedDetailUistate uiState = mBinding.getUiState();
                uiState.setName(data.getName());
                uiState.setModelNo(LanguageStrings.app_deviceregist_devicemodelno_textview_text() + space_language + data.getModelNo());
                mBinding.setUiState(uiState);

            }
        });

        mViewModel.deviceInfoMutableLiveData.observe(this, new Observer<DeviceInfo>() {
            @Override
            public void onChanged(DeviceInfo deviceInfo) {
                DeviceRegistedDetailUistate uiState = mBinding.getUiState();

                if (null != deviceInfo) {
                    if (!TextUtils.isEmpty(deviceInfo.getSn())) {
                        uiState.setSn(TextUtils.isEmpty(deviceInfo.getSn()) ? "--" : deviceInfo.getSn());
                        mBinding.setUiState(uiState);
                    }
                }
            }
        });
    }

    @Override
    protected Class getViewModelClass() {
        return DeviceRegisterInfoViewModel.class;
    }


    private void initTrace() {
        stayEleId = "2";
        pageId = "86";
        mouduleId = "10";
        nextButtoneleid = "2";
    }

    private void initPanelTrace() {
        stayEleId = "2";
        pageId = "161";
        mouduleId = "12";
        nextButtoneleid = "2";
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        goBack();
    }


}
