package com.chervon.moudleDeviceManage.data.repository;

import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_SN_FAIL;
import static com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter.TURN_OFF;
import static com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter.TURN_OFF_HEATING;
import static com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter.TURN_ON;
import static com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter.TURN_ON_HEATING;

import android.app.Application;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.RequiresApi;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.chervon.libBase.model.DeviceSnStatus;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.libBase.model.ProductEntry;
import com.chervon.libBase.model.ProductsParams;
import com.chervon.libBase.model.SnScanEntry;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.TimestampToUTCConverter;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.dao.ProductDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.PartDetail;
import com.chervon.libDB.entities.PartsInfo;
import com.chervon.libDB.entities.ProductEncyclopediasInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.result.SmartScanBean;
import com.chervon.moudleDeviceManage.data.api.DeviceApi;
import com.chervon.moudleDeviceManage.data.model.DeviceAgreeMentRespone;
import com.chervon.libBase.model.DeviceDictEntry;
import com.chervon.moudleDeviceManage.data.model.DeviceRegisterParam;
import com.chervon.libBase.model.DeviceRegisterRes;
import com.chervon.moudleDeviceManage.data.model.DeviceStatus;
import com.chervon.libBase.model.DictItem;
import com.chervon.moudleDeviceManage.data.model.LastMessageRespone;
import com.chervon.moudleDeviceManage.data.model.UploadInfoEntry;
import com.chervon.moudleDeviceManage.ui.state.DeviceListUistate;
import com.chervon.moudleDeviceManage.ui.state.DevicePricyUistate;
import com.chervon.moudleDeviceManage.ui.state.DeviceRegistedDetailUistate;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.google.gson.Gson;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Date;
import java.util.List;
import java.util.Map;

import io.reactivex.observers.DefaultObserver;
import io.reactivex.schedulers.Schedulers;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.data.repository
 * @ClassName: HomeRepo
 * @Description: Data processing warehouse of Home moudule
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:02
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/20
 * @UpdateRemark: 修改设备注册接口接入欧洲crm
 * 添加设备SN校验接口
 * @Version: 1.1
 */
public class HomeRepo implements IHomeRepo {

    public static final int DEL_DEVIVE_SUCCESS = 1;

    public static final String NET_WORK_EXCEPTION = LanguageStrings.appBaseHaveNotNetWork();
    public static final int RESPONSE_SUCCESS_CAN_SCAN = 5;

    public static final int RESPONSE_SUCCESS = 3;
    public static final int ORDER_DEVICE_SUCCESS = 4;
    public static final int RECEIP_SUCCESS = 15;
    public static final int RECEIP_UPLOAD_ERROR = 17;

    public static final int DEVICE_REGISTER_SUCCESS = 16;

    public static final int DEVICE_REGISTER_COMMIT_QUESTION_SUCCESS = 18;
    public static final int DEVICE_REGISTER_COMMIT_QUESTION_FAIL = 19;
    public static final int DEVICE_REGISTER_INIT = -1;
    public static final int DEVICE_REGISTER_ERROR = 22;
    public static final int DEVICE_RESET_PASSWORD_SUCCESS = 23;

    public static final int DEVICE_RESET_PASSWORD_FAIL = 24;
    //设备已经被注册
    public static final int APP_DEVICE_INFO_ALREADY_REGISTERED = 1020032005;
    /**
     * 添加校验sn 便于单独判断
     */
    public static final int VALIDATE_SN_SUCCESS = 20;
    public static final int VALIDATE_SN_FAIL = 21;

    private DeviceDao mDeviceDao;
    private ProductDao mProductDao;
    private LiveData<List<DeviceInfo>> mAllDevices;

    public HomeRepo(Application application) {
        SoftRoomDatabase db = SoftRoomDatabase.getDatabase(application.getApplicationContext());
        mDeviceDao = db.deviceDao();
        mProductDao = db.productDao();
        mAllDevices = mDeviceDao.getDevicesLiveData();
    }


    /**
     * @param liveData
     * @param registerUistate getPurposeType 默认0 getAddressType 默认0
     */
    @Override
    public void registerDevice(LiveData<DeviceRegisterUistate> liveData, DeviceRegisterUistate registerUistate) {
        DeviceRegisterParam param = null;
        String defaultValue = "0";
        int posDiff = 1;
        try {
            Long timeStamp = TimestampToUTCConverter.convertToUTC(registerUistate.getDate());
            param = new DeviceRegisterParam(registerUistate.getIfCheckedWarranty(), registerUistate.getDeviceid(), registerUistate.getPurposeType() > 0 ? (registerUistate.getPurposeType() - posDiff) + "" : defaultValue, registerUistate.getAddressType() != null ? registerUistate.getAddressType() : defaultValue, registerUistate.getPurchasePlaceOther(), timeStamp, (registerUistate.getReceiptType() - posDiff) + "", registerUistate.getRecipKey(), registerUistate.getSn().toUpperCase(), registerUistate.isHasKit() ? registerUistate.getKitSnAsList() : new ArrayList<>());
        } catch (Exception e) {
            LogUtils.e("注册设备数据解析异常");
        }
        if (null == param) {
            registerUistate.setState(RESPONSE_FAIL);
            ((MutableLiveData) liveData).postValue(registerUistate);
            return;
        }

        new DeviceApi().registerDevice2(param).subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new DefaultObserver<HttpResponse<DeviceRegisterRes>>() {
            @Override
            public void onNext(HttpResponse<DeviceRegisterRes> httpResponse) {

                if (httpResponse.status) {
                    //手动为空
                    registerUistate.setDeviceRegisterRes(null);
                    registerUistate.setState(DEVICE_REGISTER_SUCCESS);
                    registerUistate.setRegistedSuccessed(true);
                    registerUistate.setDeviceRegisterRes(httpResponse.response);
                } else {
                    if (httpResponse.responseCode.equals(APP_DEVICE_INFO_ALREADY_REGISTERED + "")) {
                        registerUistate.setMessage(httpResponse.message);
                        registerUistate.setState(APP_DEVICE_INFO_ALREADY_REGISTERED);
                        registerUistate.setRegistedSuccessed(true);
                    } else {
                        registerUistate.setState(RESPONSE_FAIL);
                        registerUistate.setRegistedSuccessed(false);
                        registerUistate.setMessage(httpResponse.message);
                    }

                }

                registerUistate.setMessage(httpResponse.message);
                ((MutableLiveData) liveData).postValue(registerUistate);
            }

            @Override
            public void onError(Throwable e) {
                registerUistate.setState(DEVICE_REGISTER_ERROR);
                registerUistate.setMessage(NET_WORK_EXCEPTION);
                ((MutableLiveData) liveData).postValue(registerUistate);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void getDeviceListFromDbFirst(MutableLiveData<DeviceListUistate> liveData) {
        DeviceListUistate oldData = liveData.getValue();
        if (oldData == null) {
            oldData = new DeviceListUistate();
        }
        oldData.setState(RESPONSE_SUCCESS);
        List<DeviceInfo> localDeviceInfoList = mDeviceDao.getDevices();
        oldData.setInfoList(localDeviceInfoList);
        liveData.postValue(oldData);
        if (NetworkUtils.isConnected()) {
            getDeviceList(liveData);
        }
    }

    public DeviceInfo getDeviceFromDb(String id) {

        DeviceInfo deviceInfo = mDeviceDao.getDeviceById(id);
        return deviceInfo;
    }

    @Override
    public void getDeviceList(MutableLiveData<DeviceListUistate> liveData) {

        new DeviceApi().getDeviceList().subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<List<DeviceInfo>>>() {
            @Override
            public void onNext(HttpResponse<List<DeviceInfo>> httpResponse) {
                DeviceListUistate deviceListUistate = liveData.getValue();
                if (deviceListUistate == null) {
                    deviceListUistate = new DeviceListUistate();
                }

                if (httpResponse.status) {
                    deviceListUistate.setState(RESPONSE_SUCCESS);
                    deviceListUistate.setScanState(RESPONSE_SUCCESS_CAN_SCAN);
                    deviceListUistate.setInfoList(httpResponse.response);
                    List<DeviceInfo> infoList = httpResponse.response;
                    DeviceListUistate finalDeviceListUistate = deviceListUistate;
                    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                        if (infoList != null && infoList.size() > 0) {
                            mDeviceDao.deleteAll();
                            for (DeviceInfo device : infoList) {
                                DeviceInfo oldDeviceInfo = mDeviceDao.getDeviceById(device.getDeviceId());
                                if (device.getShortCutVos() != null) {
                                    Object obj = device.getShortCutVos().get(0).getShadowStatus();
                                    if (obj != null && !TextUtils.isEmpty(obj.toString())) {
                                        if ("true".equalsIgnoreCase(obj.toString())) {
                                            if (oldDeviceInfo != null) {

                                                if (oldDeviceInfo.getQuickControlStatus() == TURN_ON_HEATING) {
                                                    device.setQuickControlStatus(TURN_ON);
                                                } else {
                                                    device.setQuickControlStatus(oldDeviceInfo.getQuickControlStatus());
                                                }
                                            } else {
                                                device.setQuickControlStatus(TURN_ON);

                                            }


                                        } else {
                                            if (oldDeviceInfo != null) {
                                                if (oldDeviceInfo.getQuickControlStatus() == TURN_OFF_HEATING) {
                                                    device.setQuickControlStatus(TURN_OFF);
                                                } else {
                                                    device.setQuickControlStatus(oldDeviceInfo.getQuickControlStatus());
                                                }
                                            } else {
                                                device.setQuickControlStatus(TURN_OFF);
                                            }
                                        }
                                    }
                                }

                                mDeviceDao.insert(device);
                            }
                        } else {
                            mDeviceDao.deleteAll();
                        }
                        finalDeviceListUistate.setInfoList(infoList);
                        finalDeviceListUistate.setMessage(httpResponse.message);
                        liveData.postValue(finalDeviceListUistate);
                    });

                } else {
                    List<DeviceInfo> localInfoList = mDeviceDao.getDevices();
                    deviceListUistate.setInfoList(localInfoList);
                    deviceListUistate.setState(RESPONSE_FAIL);
                }
                deviceListUistate.setMessage(httpResponse.message);
                liveData.postValue(deviceListUistate);
            }

            @Override
            public void onError(Throwable e) {
                DeviceListUistate oldData = liveData.getValue();
                if (oldData == null) {
                    oldData = new DeviceListUistate();
                }
                oldData.setState(RESPONSE_FAIL);
                oldData.setMessage(NET_WORK_EXCEPTION);
                List<DeviceInfo> localDeviceInfoList = mDeviceDao.getDevices();
                oldData.setInfoList(localDeviceInfoList);

                liveData.postValue(oldData);
            }

            @Override
            public void onComplete() {
            }
        });


    }

    @Override
    public void upLoadReceipImage(LiveData<DeviceRegisterUistate> mLiveData, String deviceid, File receipFile) {


        new DeviceApi().getUploadInfo(deviceid, receipFile.getName()).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<UploadInfoEntry>>() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            public void onNext(HttpResponse<UploadInfoEntry> stringHttpResponse) {

                if (stringHttpResponse.status) {
                    UploadInfoEntry strArray = stringHttpResponse.response;
                    LogUtils.d("upload_imag_S3", strArray.getPreSignedUrl());
                    ApiService.instance().uploadImageAndFile(strArray.getPreSignedUrl(), receipFile.getAbsolutePath(), receipFile).enqueue(new Callback() {
                        @Override
                        public void onFailure(Call call, IOException e) {
                            LogUtils.dTag("upload imag S3", e.getMessage());
                        }

                        @Override
                        public void onResponse(Call call, Response response) throws IOException {
                            DeviceRegisterUistate uiState = mLiveData.getValue();
                            if (uiState == null) {
                                uiState = new DeviceRegisterUistate();
                            }
                            if (response.isSuccessful()) {
                                List list = uiState.getRecipKey();
                                if (list == null) {
                                    list = new ArrayList<String>();
                                }
                                list.clear();
                                list.add(strArray.getKey());
                                uiState.setRecipKey(list);
                                uiState.setState(RECEIP_SUCCESS);

                            } else {
                                uiState.setState(RESPONSE_FAIL);
                                uiState.setMessage(response.message());

                            }
                            ((MutableLiveData) mLiveData).postValue(uiState);
                        }
                    });


                }


            }

            @Override
            public void onError(Throwable throwable) {
                LogUtils.e(throwable.getCause());
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void commitQuestion(MutableLiveData<DeviceRegisterUistate> liveData, DeviceRegisterUistate registerUistate) {
        new DeviceApi().commitQuestion(registerUistate.getQuestionPageData()).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
            @Override
            public void onNext(HttpResponse value) {
                if (value.status) {
                    registerUistate.setState(DEVICE_REGISTER_COMMIT_QUESTION_SUCCESS);
                } else {
                    registerUistate.setState(DEVICE_REGISTER_COMMIT_QUESTION_FAIL);
                    registerUistate.setMessage(value.message);

                }
                liveData.postValue(registerUistate);
            }

            @Override
            public void onError(Throwable e) {
                LogUtils.e(e);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    @Override
    public void getDeviceInfo(MutableLiveData<DeviceInfo> liveData, String deviceId) {
        new DeviceApi().getDeviceInfo(deviceId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DeviceInfo>>() {
            @Override
            public void onNext(HttpResponse<DeviceInfo> deviceInfoHttpResponse) {
                if (deviceInfoHttpResponse.status) {

                    liveData.postValue(deviceInfoHttpResponse.response);
                    insertDevice(deviceInfoHttpResponse.response);
                } else {
                    liveData.postValue(null);
                }
            }

            @Override
            public void onError(Throwable e) {

            }

            @Override
            public void onComplete() {

            }
        });
    }


    LiveData<List<DeviceInfo>> getAllWords() {
        return mAllDevices;
    }


    void insertDevice(DeviceInfo deviceInfo) {
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            mDeviceDao.update(deviceInfo);
        });
    }

    public void deleteDevice(MutableLiveData<DeviceListUistate> liveData, DeviceInfo deviceInfo) {
        new DeviceApi().deleteDevice(deviceInfo.getDeviceId()).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
            @Override
            public void onNext(HttpResponse httpResponse) {
                DeviceListUistate oldData = liveData.getValue();
                if (oldData == null) {
                    oldData = new DeviceListUistate();
                }
                if (httpResponse.status) {
                    oldData.setState(DEL_DEVIVE_SUCCESS);
                    if (oldData.getInfoList() != null) {
                        oldData.getInfoList().remove(deviceInfo);
                    }
                    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                        mDeviceDao.deleteDevice(deviceInfo.getDeviceId());
                    });
                } else {
                    oldData.setState(RESPONSE_FAIL);
                }
                oldData.setMessage(httpResponse.message);
                liveData.postValue(oldData);


            }

            @Override
            public void onError(Throwable e) {
                DeviceListUistate oldData = liveData.getValue();
                if (oldData == null) {
                    oldData = new DeviceListUistate();
                }
                oldData.setState(RESPONSE_FAIL);
                oldData.setMessage(NET_WORK_EXCEPTION);
                liveData.postValue(oldData);
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void reOrderDevice(MutableLiveData<DeviceListUistate> liveData, List<DeviceInfo> data) {
        if (data != null && data.size() > 0) {

        } else {
            return;
        }
        String[] ids = new String[data.size()];
        for (int i = 0; i < data.size(); i++) {
            ids[i] = data.get(i).getDeviceId();
        }
        if (!NetworkUtils.getMobileDataEnabled() && !NetworkUtils.getWifiEnabled()) {
            getDeviceListFromDbFirst(liveData);
            DeviceListUistate oldData = liveData.getValue();
            if (oldData == null) {
                oldData = new DeviceListUistate();
            }
            oldData.setState(RESPONSE_FAIL);
            oldData.setMessage(NET_WORK_EXCEPTION);
            liveData.postValue(oldData);
            return;
        }


        new DeviceApi().reOrderDevice(ids).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
            @Override
            public void onNext(HttpResponse httpResponse) {
                DeviceListUistate oldData = liveData.getValue();
                if (oldData == null) {
                    oldData = new DeviceListUistate();
                }
                if (httpResponse.status) {
                    oldData.setState(ORDER_DEVICE_SUCCESS);
                } else {
                    oldData.setState(RESPONSE_FAIL);
                    getDeviceList(liveData);
                }
                oldData.setMessage(httpResponse.message);
                liveData.postValue(oldData);
            }

            @Override
            public void onError(Throwable e) {
                getDeviceListFromDbFirst(liveData);
                DeviceListUistate oldData = liveData.getValue();
                if (oldData == null) {
                    oldData = new DeviceListUistate();
                }
                oldData.setState(RESPONSE_FAIL);
                oldData.setMessage(NET_WORK_EXCEPTION);
                liveData.postValue(oldData);
            }

            @Override
            public void onComplete() {

            }
        });
    }


    public void getProducts() {
        ProductsParams params = new ProductsParams(1, 200);
        new DeviceApi().getProducts(params).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductEntry>>() {
            @Override
            public void onNext(HttpResponse<ProductEntry> listHttpResponse) {
                if (listHttpResponse.status) {
                    ProductInfo[] productInfos = listHttpResponse.response.getList();
                    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                        for (int i = 0; productInfos != null && i < productInfos.length; i++) {
                            ProductInfo productInfo = mProductDao.getProductInfo(productInfos[i].getProductSnCode());
                            if (productInfo != null) {
                                productInfos[i].setHistory(productInfo.getHistory());
                                productInfos[i].setHistoryTime(productInfo.getHistoryTime());
                            }
                            if (productInfos[i] != null && productInfos[i].getId() != null) {
                                mProductDao.insert(productInfos[i]);
                            }

                        }
                    });
                }
            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onComplete() {

            }
        });
    }


    public void getDeviceStatus(MutableLiveData<DeviceInfo> liveData, DeviceInfo deviceInfo) {
        new DeviceApi().getDeviceStatus(deviceInfo.getDeviceId()).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DeviceStatus>>() {
            @Override
            public void onNext(HttpResponse<DeviceStatus> httpResponse) {


                if (httpResponse.status) {

                    DeviceStatus deviceStatus = httpResponse.response;
                    if (deviceStatus.getDeviceCodeStatus() == 0 || deviceStatus.getDeviceStatus() == 0) {
                        deviceInfo.setIsOnline(2);
                    }

                } else {
                    //    httpResponse.setState(RESPONSE_FAIL);

                }
                //  httpResponse.setMessage(httpResponse.message);
                liveData.postValue(deviceInfo);
            }

            @Override
            public void onError(Throwable e) {
                liveData.getValue();
            }

            @Override
            public void onComplete() {

            }
        });

    }

    public void getDictNames(MutableLiveData<DeviceDictEntry> liveData, String info) {
        new DeviceApi().getDictNames(info).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DictItem[]>>() {
            @Override
            public void onNext(HttpResponse<DictItem[]> httpResponse) {
                if (httpResponse.status) {
                    DictItem[] strArray = httpResponse.response;
                    DeviceDictEntry dictEntry = new DeviceDictEntry();
                    dictEntry.setEntry(strArray);
                    liveData.postValue(dictEntry);
                } else {
                    //    httpResponse.setState(RESPONSE_FAIL);
                }
                //  httpResponse.setMessage(httpResponse.message);
                //   liveData.postValue(null);
            }

            @Override
            public void onError(Throwable e) {
                liveData.getValue();
            }

            @Override
            public void onComplete() {

            }
        });
    }

    public void getProductDetail(MutableLiveData<DeviceRegisterUistate> liveData, String productId) {
        new DeviceApi().getProductDetail(productId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductInfo>>() {
            @Override
            public void onNext(HttpResponse<ProductInfo> httpResponse) {
                DeviceRegisterUistate deviceRegisterUistate = liveData.getValue();
                if (deviceRegisterUistate == null) {
                    deviceRegisterUistate = new DeviceRegisterUistate();
                }
                if (httpResponse.status) {
                    ProductInfo productInfo = httpResponse.response;
                    boolean hasQuestionPage = false;
                    if (!TextUtils.isEmpty(productInfo.getQuestionTemplate())) {
                        hasQuestionPage = productInfo.getQuestionTemplate().contains("extendedWarrantyTemplate");
                    }
                    deviceRegisterUistate.setHasQuestionPage(hasQuestionPage);
                    deviceRegisterUistate.setModelNo(productInfo.getCommodityModel());
                    deviceRegisterUistate.setName(productInfo.getProductName());
                } else {
                    //    httpResponse.setState(RESPONSE_FAIL);
                    boolean hasQuestionPage = false;
                    deviceRegisterUistate.setHasQuestionPage(hasQuestionPage);
                }
                //  httpResponse.setMessage(httpResponse.message);
                liveData.postValue(deviceRegisterUistate);
            }

            @Override
            public void onError(Throwable e) {
                liveData.getValue();
            }

            @Override
            public void onComplete() {

            }
        });
    }


    //产品百科接口
    public void getProductEncyclopedias(MutableLiveData<ProductEncyclopediasInfo> liveData, String productId) {
        try {
            new DeviceApi().getProductEncyclopedias(productId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductEncyclopediasInfo>>() {
                @Override
                public void onNext(HttpResponse<ProductEncyclopediasInfo> response) {
                    LogUtils.i("lesly111 homeRepo: onNext response = " + response);
                    if (response.response == null) {
                        response.response = new ProductEncyclopediasInfo();
                    }
                    liveData.postValue(response.response);
                }

                @Override
                public void onError(Throwable e) {
                    LogUtils.i("lesly111 homeRepo: onNext response = " + e.getMessage());
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //获取设备附件 RN面板
    public void getEquimentAccessory(MutableLiveData<PartsInfo[]> liveData, String productId, String deviceId) {
        try {
            new DeviceApi().getEquipmentAccessor(productId, deviceId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<PartsInfo[]>>() {
                @Override
                public void onNext(HttpResponse<PartsInfo[]> httpResponse) {
                    if (httpResponse.status) {
                        PartsInfo[] partsInfo = httpResponse.response;
                        if (partsInfo != null) {
                            liveData.postValue(partsInfo);
                        }
                    } else {
                        liveData.postValue(null);
                    }
                }

                @Override
                public void onError(Throwable e) {
                    liveData.postValue(null);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void getPartDetail(MutableLiveData<PartDetail> mPartDetailLiveData, String partId, String deviceId) {
        try {
            new DeviceApi().getPartDetail(partId, deviceId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<PartDetail>>() {
                @Override
                public void onNext(HttpResponse<PartDetail> partDetailHttpResponse) {
                    if (partDetailHttpResponse.status) {
                        mPartDetailLiveData.postValue(partDetailHttpResponse.response);
                    } else {
                        mPartDetailLiveData.postValue(null);
                    }
                }

                @Override
                public void onError(Throwable e) {
                    mPartDetailLiveData.postValue(null);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void partReset(MutableLiveData<BaseUistate> liveData, String deviceId, String partId) {
        try {
            new DeviceApi().partReset(deviceId, partId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
                @Override
                public void onNext(HttpResponse partDetailHttpResponse) {
                    BaseUistate baseUistate = new BaseUistate();
                    if (partDetailHttpResponse.status) {
                        baseUistate.setState(RESPONSE_SUCCESS);

                    } else {

                        baseUistate.setState(RESPONSE_FAIL);

                    }

                    baseUistate.setMessage(partDetailHttpResponse.message);
                    liveData.postValue(baseUistate);
                }

                @Override
                public void onError(Throwable e) {
                    BaseUistate baseUistate = new BaseUistate();
                    baseUistate.setState(RESPONSE_FAIL);
                    baseUistate.setMessage(NET_WORK_EXCEPTION);
                    liveData.postValue(baseUistate);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void getAgreeent(MutableLiveData<DevicePricyUistate> liveData, String productID) {
        try {
            new DeviceApi().getNewAgreement(productID).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DeviceAgreeMentRespone[]>>() {
                @Override
                public void onNext(HttpResponse<DeviceAgreeMentRespone[]> value) {
                    DevicePricyUistate oldData = liveData.getValue();
                    if (oldData == null) {
                        oldData = new DevicePricyUistate();
                    }
                    if (value.status) {
                        oldData.setState(RESPONSE_SUCCESS);
                        oldData.setList(value.response);
                    } else {
                        oldData.setState(RESPONSE_FAIL);
                    }
                    oldData.setMessage(value.message);
                    liveData.postValue(oldData);
                }

                @Override
                public void onError(Throwable e) {
                    DevicePricyUistate oldData = liveData.getValue();
                    if (oldData == null) {
                        oldData = new DevicePricyUistate();
                    }
                    oldData.setState(RESPONSE_FAIL);
                    oldData.setMessage(NET_WORK_EXCEPTION);
                    liveData.postValue(oldData);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void getDeviceRegistedDetail(MutableLiveData<DeviceRegistedDetailUistate> liveData, String deviceId) {
        try {
            new DeviceApi().getDeviceRegistedDetail(deviceId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DeviceRegistedDetailUistate>>() {
                @Override
                public void onNext(HttpResponse<DeviceRegistedDetailUistate> value) {
                    if (value.response == null) {
                        value.response = new DeviceRegistedDetailUistate();
                    }

                    if (value.status) {
                        value.response.setState(RESPONSE_SUCCESS);
                    } else {
                        value.response.setState(RESPONSE_FAIL);
                    }
                    value.response.setMessage(value.message);
                    liveData.postValue(value.response);
                }

                @Override
                public void onError(Throwable e) {
                    DeviceRegistedDetailUistate oldData = liveData.getValue();
                    if (oldData == null) {
                        oldData = new DeviceRegistedDetailUistate();
                    }
                    oldData.setState(RESPONSE_FAIL);
                    oldData.setMessage(NET_WORK_EXCEPTION);
                    liveData.postValue(oldData);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void getProductBySn(MutableLiveData<ManualSNUIState> liveData, String sn) {

        new DeviceApi().getProductBySn(sn).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<SnScanEntry>>() {
            @Override
            public void onNext(HttpResponse<SnScanEntry> deviceInfoHttpResponse) {
                ManualSNUIState value = liveData.getValue();
                if (value == null) {
                    value = new ManualSNUIState();
                }
                if (deviceInfoHttpResponse.status) {
                    DeviceInfo deviceInfo = new DeviceInfo();
                    deviceInfo.setSn(sn);
                    deviceInfo.setProductId(deviceInfoHttpResponse.response.getInfo());
                    value.setDeviceInfo(deviceInfo);
                    value.setState(RESPONSE_SUCCESS);
                } else {
                    value.setState(RESPONSE_FAIL);
                    value.setMessage(deviceInfoHttpResponse.message);
                }

                liveData.postValue(value);
            }

            @Override
            public void onError(Throwable e) {
                liveData.getValue();
            }

            @Override
            public void onComplete() {
                liveData.getValue();
            }
        });
    }

    public void getDeviceBySn(MutableLiveData<DeviceRegisterUistate> liveData, String sn) {
        try {
            new DeviceApi().getDeviceDetailBysn(sn.toUpperCase()).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DeviceInfo>>() {
                @Override
                public void onNext(HttpResponse<DeviceInfo> value) {
                    DeviceRegisterUistate oldData = liveData.getValue();
                    if (oldData == null) {
                        oldData = new DeviceRegisterUistate();
                    }
                    if (value.status) {
                        oldData.setState(RESPONSE_SUCCESS);
                        oldData.setDeviceInfo(value.response);
                    } else {
                        oldData.setState(RESPONSE_SN_FAIL);
                    }
                    oldData.setMessage(value.message);
                    liveData.postValue(oldData);
                }

                @Override
                public void onError(Throwable e) {

                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public void getLastMessage(MutableLiveData<Boolean> liveData) {
        try {
            new DeviceApi().getLastMessage().subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<LastMessageRespone>>() {
                @Override
                public void onNext(HttpResponse<LastMessageRespone> value) {

                    if (value.status) {
                        LastMessageRespone lastMessageRespone = value.response;
                        boolean isDeviceRead = true;
                        if ((lastMessageRespone.getDevicesMessage() != null && lastMessageRespone.getDevicesMessage().size() > 0)) {

                            for (int i = 0; i < lastMessageRespone.getDevicesMessage().size(); i++) {
                                if (lastMessageRespone.getDevicesMessage().get(i).getIfRead() == 0) {
                                    isDeviceRead = false;
                                    break;
                                }

                            }

                        }


                        if (!isDeviceRead || (lastMessageRespone.getSystemMessage() != null) || (lastMessageRespone.getMarketingMessage() != null)) {
                            liveData.postValue(true);
                            return;
                        }
                    } else {
                        //  oldData.setState(RESPONSE_FAIL);
                    }
                    //   oldData.setMessage(value.message);
                    liveData.postValue(false);
                }

                @Override
                public void onError(Throwable e) {
                    liveData.postValue(false);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    public void reNameDevice(MutableLiveData<Boolean> liveData, String name, String deviceId) {
        try {
            new DeviceApi().reNameDevice(name, deviceId).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
                @Override
                public void onNext(HttpResponse value) {

                    if (value.status) {

                        liveData.postValue(true);


                    } else {
                        liveData.postValue(false);
                    }
                    //   oldData.setMessage(value.message);

                }

                @Override
                public void onError(Throwable e) {
                    liveData.postValue(false);
                }

                @Override
                public void onComplete() {

                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public void saveDeviceName(DeviceInfo deviceInfo) {
        mDeviceDao.update(deviceInfo);
    }

    public ProductInfo getProductByDb(String productId) {
        return mProductDao.getProductInfoFromId(productId);
    }

    public void getDebugList(MutableLiveData<List<DeviceInfo>> liveData) {
        new DeviceApi().getDebugList().subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<List<DeviceInfo>>>() {
            @Override
            public void onNext(HttpResponse<List<DeviceInfo>> httpResponse) {
                if (httpResponse.status) {
                    List<DeviceInfo> infoList = httpResponse.response;
                    liveData.postValue(infoList);
                } else {
                    liveData.postValue(null);
                }


            }

            @Override
            public void onError(Throwable e) {
                liveData.postValue(null);
            }

            @Override
            public void onComplete() {
            }
        });


    }

    public void getQuickControlStatusFromDb(MutableLiveData<DeviceListUistate> liveData) {
        List<DeviceInfo> deviceInfoList = mDeviceDao.getDevices();
        DeviceListUistate deviceListUistate = liveData.getValue();
        if (deviceListUistate == null) {
            deviceListUistate = new DeviceListUistate();
        }
        deviceListUistate.setState(RESPONSE_SUCCESS);
        deviceListUistate.setScanState(RESPONSE_SUCCESS_CAN_SCAN);
        deviceListUistate.setInfoList(deviceInfoList);
        liveData.postValue(deviceListUistate);
    }


    public void updateQuickControlStatus(MutableLiveData<DeviceListUistate> liveData, String deviceId, int quickControlStatus) {
        DeviceInfo deviceInfo = mDeviceDao.getDeviceById(deviceId);
        deviceInfo.setQuickControlStatus(quickControlStatus);
        Map paramMap = new HashMap();
        if (quickControlStatus == 1) {
            // deviceInfo.getShortCutVos().get(0).setShadowStatus("{\"1\":true}");
            paramMap.put("1", true);

        } else {
            paramMap.put("1", false);
            //  deviceInfo.getShortCutVos().get(0).setShadowStatus("{\"1\":false}");
        }
        if (deviceInfo.getShortCutVos() != null) {
            deviceInfo.getShortCutVos().get(0).setShadowStatus(paramMap);
        }
        mDeviceDao.update(deviceInfo);
        List<DeviceInfo> deviceInfoList = mDeviceDao.getDevices();
        DeviceListUistate deviceListUistate = liveData.getValue();
        if (deviceListUistate == null) {
            deviceListUistate = new DeviceListUistate();
        }
        deviceListUistate.setState(RESPONSE_SUCCESS);
        deviceListUistate.setScanState(RESPONSE_SUCCESS_CAN_SCAN);
        deviceListUistate.setInfoList(deviceInfoList);
        liveData.postValue(deviceListUistate);
    }

    public void getSmartStatus(MutableLiveData<Boolean> liveData) {

        ApiService.instance().getSmartScan().subscribe(new HttpObserver<SmartScanBean>() {
            @Override
            protected void Next(SmartScanBean httpResponse) {

                if (httpResponse.isStatus()) {
                    SmartScanBean.EntryDTO entry = httpResponse.getEntry();
                    if (null != entry) {
                        if (entry.isInfo()) {
                            liveData.postValue(true);
                        } else {
                            liveData.postValue(false);

                        }
                    }

                } else {
                    liveData.postValue(false);
                }


            }
        });
    }

    /**
     * 校验sn
     *
     * @param liveData
     * @param sn
     * @param mark     sn 标志位 欧洲 0 主 1 2 3
     */
    public void validateSn(MutableLiveData<DeviceRegisterUistate> liveData, String sn, int mark) {
        new DeviceApi().validateSn(sn).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
            @Override
            public void onNext(HttpResponse httpResponse) {
                switch (mark) {
                    case 0:
                        DeviceSnStatus status = new DeviceSnStatus();
                        status.setSn(sn);
                        status.setCanRegister(httpResponse.status);
                        if (!httpResponse.status) {
                            status.setMessage(httpResponse.message);
                        } else {
                            status.setMessage("");
                        }
                        if (liveData.getValue() != null) {
                            liveData.getValue().setState(VALIDATE_SN_SUCCESS);
                            liveData.getValue().setSnValidateStatus(status);
                            liveData.postValue(liveData.getValue());
                        }
                        break;
                    default:
                        if (liveData.getValue() != null) {
                            liveData.getValue().setState(VALIDATE_SN_SUCCESS);
                            liveData.getValue().getKitSN()[mark - 1].setCanRegister(httpResponse.status);
                            if (!httpResponse.status) {
                                liveData.getValue().getKitSN()[mark - 1].setMessage(httpResponse.message);
                            } else {
                                liveData.getValue().getKitSN()[mark - 1].setMessage("");
                            }
                            liveData.postValue(liveData.getValue());
                        }
                        break;
                }
            }

            @Override
            public void onError(Throwable e) {
                if (liveData.getValue() != null) {
                    liveData.getValue().setState(VALIDATE_SN_FAIL);
                    liveData.getValue().setMessage(NET_WORK_EXCEPTION);
                }
            }

            @Override
            public void onComplete() {
            }
        });
    }


    /**
     * 校验sn
     *
     * @param liveData
     * @param sn
     */
    public void validateSnWithNa(MutableLiveData<DeviceRegisterUistate> liveData, String sn) {

        new DeviceApi().validateSn(sn).subscribeOn(Schedulers.io()).observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
            @Override
            public void onNext(HttpResponse httpResponse) {
                DeviceRegisterUistate registerUiState = liveData.getValue();
                if (null==registerUiState){
                    registerUiState = new DeviceRegisterUistate();
                }
                if (httpResponse.status) {
                    registerUiState.setState(VALIDATE_SN_SUCCESS);
                    liveData.postValue(registerUiState);
                }else {
                    if (httpResponse.responseCode.equals(APP_DEVICE_INFO_ALREADY_REGISTERED+"")){
                        registerUiState.setState(APP_DEVICE_INFO_ALREADY_REGISTERED);
                        registerUiState.setMessage(httpResponse.message);
                        liveData.postValue(registerUiState);
                    }else {
                        registerUiState.setState(VALIDATE_SN_FAIL);
                        registerUiState.setMessage(httpResponse.message);
                        liveData.postValue(registerUiState);
                    }
                }

            }

            @Override
            public void onError(Throwable e) {
            }

            @Override
            public void onComplete() {
            }
        });
    }

    public void resetDevicePassword(MutableLiveData<DeviceListUistate> liveData,String deviceId,boolean online) {
        new DeviceApi().resetDevicePassword(deviceId,online)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new DefaultObserver<HttpResponse>() {
                    public void onNext(HttpResponse httpResponse) {
                        DeviceListUistate oldData = liveData.getValue();
                        if (oldData == null) {
                            oldData = new DeviceListUistate();
                        }
                        if (httpResponse.status) {
                            oldData.setState(DEVICE_RESET_PASSWORD_SUCCESS);
                        } else {
                            oldData.setState(DEVICE_RESET_PASSWORD_FAIL);
                        }
                        oldData.setMessage(httpResponse.message);
                        liveData.postValue(oldData);


                    }

                    @Override
                    public void onError(Throwable e) {
                        DeviceListUistate oldData = liveData.getValue();
                        if (oldData == null) {
                            oldData = new DeviceListUistate();
                        }
                        oldData.setState(DEVICE_RESET_PASSWORD_FAIL);
                        oldData.setMessage(NET_WORK_EXCEPTION);
                        liveData.postValue(oldData);
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }
}
