package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE_REGISTED_DETAIL;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_MANUAL_INFO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PART_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEL_DEVIVE_SUCCESS;
import static com.chervon.moudleDeviceManage.utils.Constants.DEVICE_SHARE_TYPE_MAIN;
import static com.chervon.moudleDeviceManage.utils.Constants.DEVICE_SHARE_TYPE_SUB;
import static com.chervon.moudleDeviceManage.utils.Constants.EMPTY;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageDeviceDetailsBinding;
import com.chervon.moudleDeviceManage.ui.state.DeviceListUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceDetailsModel;
import com.chervon.moudleDeviceManage.ui.viewmodel.NonIotPanelModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: DeviceDetailsACtivity
 * @Description:
 * @Author: LiDeLi
 * @CreateDate: 2022/12/3 下午3:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/19
 * @UpdateRemark: 开放欧洲设备注册
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_DETAILS)
public class DeviceDetailsActivity extends BaseActivity<DeviceDetailsModel> implements ItemClick {
  public static final String Equipment_Name_Button_Click = "2";
  public static final String Device_Registration_Button_Click = "3";
  public static final String Product_Encyclopedia_Button_Click = "4";
  public static final String Equipent_Information_Button_Click = "5";
  public static final String Accessories_Button_Click= "6";
  public static final String  Delete_Device_Button_Click= "7";
  private MoudleDevicemanageDeviceDetailsBinding mBinding;
  private ToolbarData mToolbar;
  private DeviceInfo mDeviceInfo;

  private static final String DEVICE_HAS_REGISTED = "1";
  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_devicemanage_device_details;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mBinding = (MoudleDevicemanageDeviceDetailsBinding)viewDataBinding;
    mToolbar = new ToolbarData(LanguageStrings.appDevicemoreinfoTitleText(), new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        goBack();
      }
    });
    mBinding.setToolbarData(mToolbar);
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      mDeviceInfo = (DeviceInfo) bundle.getSerializable(KEY_PREV_DATA);
    }else{
      mDeviceInfo = new DeviceInfo();
    }
    if("1".equals(mDeviceInfo.getInfoStatus())){
      mBinding.deviceDetailsDeviceRegistTv.setText(LanguageStrings.app_devicemoreinfo_registed_textview_text());
    }else{
      mBinding.deviceDetailsDeviceRegistTv.setText(LanguageStrings.app_devicemoreinfo_unregisted_textview_text());
    }


    if(TextUtils.isEmpty(mDeviceInfo.getNickName())){
      mBinding.tvDeviceName.setText(mDeviceInfo.getCommodityModel());
    }else{
      mBinding.tvDeviceName.setText(mDeviceInfo.getNickName());
    }

    //判断欧洲注册逻辑
    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)){
      //开放欧洲注册
//      mBinding.deviceDetailsDeviceRegistLayout.setVisibility(View.GONE);
//      mBinding.lineDeviceDetailsDeviceRegistLayout.setVisibility(View.GONE);
      mBinding.viewLast.setVisibility(View.GONE);
      mBinding.deviceDetailsAccessLayout.setVisibility(View.GONE);
      mBinding.deviceDetailsProEncyLayout.setVisibility(View.GONE);
      mBinding.linePruductDetail.setVisibility(View.GONE);
    }
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.delDeviceInfoLiveData.observe(this, new Observer<DeviceListUistate>() {
      @Override
      public void onChanged(DeviceListUistate deviceListUistate) {
        if(deviceListUistate.getState()==DEL_DEVIVE_SUCCESS){
          finish();
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        }
      }
    });
  }

  @Override
  protected Class<? extends DeviceDetailsModel> getViewModelClass() {
    return DeviceDetailsModel.class;
  }

  @Override
  public void onItemClick(Object uiState) {

  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

  }
  @SuppressLint("RestrictedApi")
  private void goBack() {
    finish();

  }
  public void viewClick(View view){
    //equipment name
    if(view.getId() == R.id.deviceDetailsEquipmentNameLayout){
      sendBaseTraceClick(Equipment_Name_Button_Click);
      ARouter.getInstance()
        .build(RouterConstants.ACTIVITY_URL_EQUIPMENT_NAME)
        .withSerializable(KEY_PREV_DATA, mDeviceInfo)
        .navigation(  this,1);
    }else if(view.getId() == R.id.deviceDetailsDeviceRegistLayout){
      //DeviceRegist
      sendBaseTraceClick(Device_Registration_Button_Click);
      if(DEVICE_HAS_REGISTED.equals(mDeviceInfo.getInfoStatus())){

        ARouter.getInstance().build(ACTIVITY_URL_DEVICE_REGISTED_DETAIL)
          .withSerializable(KEY_PREV_DATA, mDeviceInfo).withInt(KEY_PREV_FRAGMENT, 1234)
          .navigation();

      }else{

        //进入注册流程
        ARouter.getInstance().build(ACTIVITY_URL_DEVICE)
          .withSerializable(KEY_PREV_DATA, mDeviceInfo)
          .withInt(KEY_PREV_FRAGMENT, 1234)
          .navigation();
      }
    }else if(view.getId() == R.id.deviceDetailsProEncyLayout){
      //产品百科
      sendBaseTraceClick(Product_Encyclopedia_Button_Click);
      //进入产品百科
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL)
        .withSerializable(KEY_PREV_DATA, mDeviceInfo)
        .navigation();
    }else if(view.getId() == R.id.deviceDetailsEquipInfoLayout){
      //EquipInfo
      LogUtils.i("lesly device details click equiment info");
      //展示设备详情页
      sendBaseTraceClick(Equipent_Information_Button_Click);
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_EQUIPMENT_INFO)
        .withSerializable(KEY_PREV_DATA, mDeviceInfo)
        .navigation();


    }else if(view.getId() == R.id.deviceDetailsAccessLayout){
      //Accessories
      //进入配件列表
      sendBaseTraceClick(Accessories_Button_Click);
      //TODO 72002
      String deviceId =mDeviceInfo.getDeviceId();
      String productId =mDeviceInfo.getProductId();

      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS)
        .withString(PARAMETER_DEVICD_ID, deviceId)
        .withString(PARAMETER_PRODUCT_ID,productId)
        .navigation();

    }else if(view.getId() == R.id.llDelDevice){
      //delete device
      sendBaseTraceClick(Delete_Device_Button_Click);
      String title = EMPTY;
      String message = EMPTY;
      if(DEVICE_SHARE_TYPE_MAIN == mDeviceInfo.getShareType()) {
        title = LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text();
        message = LanguageStrings.app_sharedevice_dialog3_textview_text();
      } else if(DEVICE_SHARE_TYPE_SUB == mDeviceInfo.getShareType()) {
        title = LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text();
        message = LanguageStrings.app_sharedevice_dialog2_textview_text();
      } else {
        title = LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text();
        message = LanguageStrings.app_devicemoreinfo_deletedevicemessage_textview_text();
      }
      DialogUtil.simpleConfirmDialog(this,
        title,
        message, new View.OnClickListener() {
          @Override
          public void onClick(View view) {
     mViewModel.deleteDevice(mDeviceInfo);
          }
        }, new View.OnClickListener() {
          @Override
          public void onClick(View view) {

          }
        });
    }

  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if(data!=null){
      DeviceInfo  deviceInfo= (DeviceInfo) data.getSerializableExtra(PARAMETER_DEVICD);
      if(deviceInfo!=null){
        mDeviceInfo=deviceInfo;
        if(TextUtils.isEmpty(deviceInfo.getNickName())){
          mBinding.tvDeviceName.setText(deviceInfo.getCommodityModel());
        }else{
          mBinding.tvDeviceName.setText(deviceInfo.getNickName());
        }

      }
    }
  }


  private void initTrace() {
    stayEleId = "8";
    pageId = "85";
    mouduleId = "10";
    nextButtoneleid = "2";
  }



  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

}
