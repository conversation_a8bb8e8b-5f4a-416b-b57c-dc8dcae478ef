package com.chervon.moudleDeviceManage.pdf;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.PARAMETER_MANUAL_INFO;
import android.annotation.SuppressLint;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ProgressBar;

import androidx.annotation.RequiresApi;
import androidx.databinding.ViewDataBinding;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.ProductEncyManual;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityProductEncyManualBinding;
import com.chervon.moudleDeviceManage.ui.viewmodel.PDFViewModel;
import com.github.barteksc.pdfviewer.PDFView;
import com.github.barteksc.pdfviewer.listener.OnLoadCompleteListener;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import javax.net.ssl.HttpsURLConnection;

/**
 * Date: 2021/1/26
 * Author: Yang
 * Describe:
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_MANUAL)
@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class PdfRendererActivity extends BaseActivity<PDFViewModel> {
  private MoudleDevicemanageActivityProductEncyManualBinding mBinding;
  private ToolbarData toolbarData;
  private static final String PDF_URL_KEY = "PDF_URL_KEY";
  private static final String TAG = "PdfRendererActivity";
  private ProgressBar pb2;

  @Override
  public boolean onTouchEvent(MotionEvent event) {
    return mBinding.pdfView.onTouchEvent(event);
  }

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);


  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @SuppressLint("RestrictedApi")
  private void goBack() {

    sendBackTrace();
    finish();

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_devicemanage_activity_product_ency_manual;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mBinding = (MoudleDevicemanageActivityProductEncyManualBinding) viewDataBinding;
    pb2 = findViewById(R.id.pb2);
    toolbarData = new ToolbarData(LanguageStrings.getUserManualStri(), new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });
    mBinding.setToolbarData(toolbarData);
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      ProductEncyManual manual = (ProductEncyManual) bundle.get(PARAMETER_MANUAL_INFO);
      String pageFrom = bundle.getString(KEY_FROM);
      if (RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL.equalsIgnoreCase(pageFrom)) {
        initNotIotTrace();
      } else if (RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL.equalsIgnoreCase(pageFrom)) {
        initTrace();
      } else if (RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS_DETAILS.equalsIgnoreCase(pageFrom)) {
        initTrace();
      }

      if (manual != null) {
        if (TextUtils.isEmpty(manual.getUrl())) {
          mBinding.pdfView.setVisibility(View.GONE);
          mBinding.llDefault.setVisibility(View.VISIBLE);
          return;
        } else {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

            new ShowPdfFromUrl().execute(manual.getUrl());
          }
          mBinding.pdfView.setVisibility(View.VISIBLE);
          mBinding.llDefault.setVisibility(View.GONE);
        }
      } else {
        return;
      }
    }
  }

  @SuppressLint("StaticFieldLeak")
  class ShowPdfFromUrl extends AsyncTask<String, Void, InputStream> {
    @Override
    protected void onPreExecute() {
      pb2.setVisibility(View.VISIBLE);

    }

    @Override
    protected InputStream doInBackground(String... success) {
      InputStream inputStream = null;
      try {
        URL url = new URL(success[0]);
        HttpURLConnection urlConnection = (HttpsURLConnection) url.openConnection();
        if (urlConnection.getResponseCode() == 200) {
          inputStream = new BufferedInputStream(urlConnection.getInputStream());
        }
      } catch (IOException e) {
        e.printStackTrace();
        return null;
      }
      return inputStream;
    }

    @Override
    protected void onPostExecute(InputStream inputStream) {
      try {
        PDFView.Configurator configurator=mBinding.pdfView.fromStream(inputStream);
        configurator.onLoad(new OnLoadCompleteListener() {
          @Override
          public void loadComplete(int nbPages) {
            if (inputStream != null) {
              pb2.setVisibility(View.GONE);
            }
          }
        });
        configurator.load();
      }catch (Exception e){
        LogUtils.e(TAG,"loadPdf error -->"+e.getMessage());
      }

    }
  }

  @Override
  protected void initData(Bundle savedInstanceState) {

  }

  @Override
  protected Class<? extends PDFViewModel> getViewModelClass() {
    return PDFViewModel.class;
  }


  private void initTrace() {
    stayEleId = "2";
    pageId = "76";
    mouduleId = "9";
    nextButtoneleid = "2";
  }


  private void initNotIotTrace() {
    stayEleId = "2";
    pageId = "87";
    mouduleId = "10";
    nextButtoneleid = "2";
  }

  public void sendUserManualButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
    finish();
  }
}