package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.TAG;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_COMMIT_QUESTION_FAIL;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_COMMIT_QUESTION_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RECEIP_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;
import static com.chervon.moudleDeviceManage.ui.DeviceRegisterInfoFragment.DEVICE_REGISTER_UI_DATA;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.DeviceRegistStatusModel;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.libBase.model.QuestionPageData;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.adapter.ProductItemSelectAdapter;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentChildAnswerSixBinding;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterViewModel;

import java.io.File;


public class FragmentSix extends BaseEnhanceFragment<DeviceRegisterViewModel, MoudleDevicemanageFragmentChildAnswerSixBinding> {
  private QuestionPageDialog mPageDialog;
  ProductItemSelectAdapter adapterQuestionFour;

  protected Dialog mProgressDialog;
  private final int ifCheckedWarranty = 1;
  public FragmentSix() {
  }

  public FragmentSix(QuestionPageDialog pageDialog, DeviceRegisterUistate uistate) {
    mPageDialog = pageDialog;
  }


  @Override
  protected void initDatas(Bundle savedInstanceState) {
    checkQuestionPage();

  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_devicemanage_fragment_child_answer_six;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (MoudleDevicemanageFragmentChildAnswerSixBinding) viewDataBinding;
    mViewDataBinding.setUiState(mPageDialog.getmUistate());
    mViewDataBinding.setPresenter(this);
    String[] dataList2 = new String[]{
      MyLang.getString("app_deviceregist_surveyotherbandoption1_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption1_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption2_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption2_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption3_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption3_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption4_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption4_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption5_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption5_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption6_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption6_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption7_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption7_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption8_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption8_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption9_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption9_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption10_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption10_textview_text),
      MyLang.getString("app_deviceregist_surveyotherbandoption11_textview_text", com.chervon.libBase.R.string.app_deviceregist_surveyotherbandoption11_textview_text)

    };
    mViewDataBinding.rvWhatbrand.setLayoutManager(new LinearLayoutManager(this.getContext()));
    adapterQuestionFour = new ProductItemSelectAdapter(dataList2, false);

    adapterQuestionFour.setOtherPosition(2);

    setDefaultItem();

    adapterQuestionFour.setOnItemClickListener(new ProductItemSelectAdapter.OnItemClickListener() {
      @Override
      public void onClick(View var1, String itemName) {
        checkQuestionPage();
      }

      @Override
      public void onItemCheckChange(View var1, boolean checked) {
        checkQuestionPage();
      }
    });
    mViewDataBinding.rvWhatbrand.setAdapter(adapterQuestionFour);


    mViewModel.mLiveData.observe(this, new Observer<DeviceRegisterUistate>() {
      @Override
      public void onChanged(DeviceRegisterUistate deviceRegisterUistate) {
        DeviceRegisterUistate uiState = mViewDataBinding.getUiState();

        if (deviceRegisterUistate.getState() == RECEIP_SUCCESS) {
          //发票上传成功后，执行设备注册

          if (deviceRegisterUistate.getRecipKey() != null) {
            uiState.setRecipKey(deviceRegisterUistate.getRecipKey());
            mViewModel.registerDevice(uiState);
            LogUtils.e(TAG, "------发票提交成功");
          }


        } else if (deviceRegisterUistate.getState() == DEVICE_REGISTER_SUCCESS) {
          LogUtils.e(TAG, "------设备注册成功");
          if (null != mProgressDialog) {
            mProgressDialog.dismiss();
          }
          mPageDialog.commitAnswerData(uiState);
          //设备注册成功后，上传问卷调查
          uiState.setIfCheckedWarranty(ifCheckedWarranty);
          mViewModel.commitQuestionPage(uiState);
          if (!TextUtils.isEmpty(uiState.getDeviceid())) {
            //同步注册结果给RN
            BaseApplication.registLiveData.postValue(new DeviceRegistStatusModel(uiState.getDeviceid(),
              DeviceRegistStatusModel.REGIST_RESULT_SUCCESS));
          }
        } else if (deviceRegisterUistate.getState() == DEVICE_REGISTER_COMMIT_QUESTION_SUCCESS) {

          LogUtils.e(TAG, "------维保问卷提交成功");

        } else if (deviceRegisterUistate.getState() == DEVICE_REGISTER_COMMIT_QUESTION_FAIL) {

          LogUtils.e(TAG, "------维保问卷提交失败");

        } else if (deviceRegisterUistate.getState() == RESPONSE_FAIL) {
          if (null != mProgressDialog) {
            mProgressDialog.dismiss();
          }
          if (!TextUtils.isEmpty(uiState.getDeviceid())) {
            //同步注册结果给RN
            BaseApplication.registLiveData.postValue(new DeviceRegistStatusModel(uiState.getDeviceid(), DeviceRegistStatusModel.REGIST_RESULT_FAIL));
          }
          if (!TextUtils.isEmpty(uiState.getMessage())) {
            ToastUtils.showShort(uiState.getMessage());
          }
          mPageDialog.commitAnswerData(uiState);

        }
      }
    });
  }


  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends DeviceRegisterViewModel> getViewModelClass() {
    return DeviceRegisterViewModel.class;
  }

  /**
   * 如果被选中，那么就展示以前选中
   * 如果选中是Other 那么就展示内容
   */
  private void setDefaultItem() {

    if (null == adapterQuestionFour) {
      return;
    }

    DeviceRegisterUistate uiState = mPageDialog.getmUistate();
    if (null == uiState) {
      return;
    }
    if (null == uiState.getQuestionPageData()) {
      return;
    }
    //设置选中项
    Integer[] questionFourAnswers = uiState.getQuestionPageData().getQuestionFourAnswers();
    if (null != questionFourAnswers && questionFourAnswers.length > 0) {
      //设置Other内容填充
      String questionFourOther = uiState.getQuestionPageData().getQuestionFourOther();

      adapterQuestionFour.setDefaultCheckItem(questionFourAnswers,questionFourOther);
    }

  }


  public void gotoNextPage() {
    DeviceRegisterUistate registerUistate = mViewDataBinding.getUiState();

    QuestionPageData questionPageData = registerUistate.getQuestionPageData();
    if (questionPageData == null) {
      questionPageData = new QuestionPageData();
    }
    ProductItemSelectAdapter adapter4 = (ProductItemSelectAdapter) mViewDataBinding.rvWhatbrand.getAdapter();
    questionPageData.setQuestionFourOther(adapter4.getOtherEdit());
    questionPageData.setQuestionFourAnswers(adapter4.getSelectedData());
    registerUistate.setQuestionPageData(questionPageData);
    mPageDialog.setmUistate(registerUistate);
//    mPageDialog.commitAnswerData(registerUistate);

    mViewModel.registerDeviceWithQuestion(registerUistate);
    if (null == mProgressDialog) {
      mProgressDialog = DialogUtil.showRnLoadingDialog(getActivity());
    } else {
      mProgressDialog.show();
    }

  }

  public void dissMiss() {
    mPageDialog.dismiss();
  }


  public void checkQuestionPage() {


    QuestionPageData questionPageData = mViewDataBinding.getUiState().getQuestionPageData();
    questionPageData.setQuestionFourAnswers(adapterQuestionFour.getSelectedData());
    if (adapterQuestionFour.isOtherSelected()){
      questionPageData.setQuestionFourOther(adapterQuestionFour.getOtherEdit());
    }else {
      questionPageData.setQuestionFourOther("");
    }
    if (adapterQuestionFour.isOtherSelected() && TextUtils.isEmpty(adapterQuestionFour.getOtherEdit())) {
      disableBtnClick(false);
      return;
    }
    if (
      questionPageData.getQuestionFourAnswers() != null) {
      disableBtnClick(true);
    } else {
      disableBtnClick(false);
    }

  }

  private void disableBtnClick(boolean b) {
    mViewDataBinding.btnNext.setEnabled(b);
    mViewDataBinding.btnNext.setClickable(b);
  }

  public void goBack() {
    mPageDialog.getChildFragmentManager().popBackStack();
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }
}

