package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.CommonUtils.goToWebPage;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_MANUAL_INFO;
import static com.chervon.libRouter.RouterConstants.TAG;

import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.graphics.Typeface;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PersistableBundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductEncyFaq;
import com.chervon.libDB.entities.ProductEncyGuidance;
import com.chervon.libDB.entities.ProductEncyManual;
import com.chervon.libDB.entities.ProductEncyProduct;
import com.chervon.libDB.entities.ProductEncyclopediasInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.data.model.DeviceFaqEntry;
import com.chervon.moudleDeviceManage.data.model.DeviceVideoEntry;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityProductEncyclopediasBinding;
import com.chervon.moudleDeviceManage.ui.adapter.FaqAdapter;
import com.chervon.moudleDeviceManage.ui.adapter.ProductVideoAdapter;
import com.chervon.moudleDeviceManage.ui.state.DeviceRegistedDetailUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.ProductEncyclopediasViewModel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: HomeActivity
 * @Description: the home page of app
 * @Author: wangheng
 * @CreateDate: 2022/4/28 下午4:20
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/28 下午4:20
 * @UpdateRemark:
 * @Version: 1.0
 */

@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL)
public class ProductEncyclopediasActivity extends BaseActivity<ProductEncyclopediasViewModel> implements ItemClick {

  private MoudleDevicemanageActivityProductEncyclopediasBinding mBinding;
  private ToolbarData mToolbarData;
  @Autowired(name = KEY_PREV_DATA)
  public int prevFragment;
  @Autowired(name = KEY_PREV_DATA)
  public DeviceInfo deviceInfo;
  private DeviceInfo mDeviceInfo;
  private ProductVideoAdapter mProductVideoAdapter;
  private FaqAdapter mFaqAdapter;
  private ProductEncyclopediasInfo mProductEncyInfo;

  private static final int LINE_FOUR = 4;
  private static final String SUFFIX_PDF = ".pdf";

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);


  }

  @Override
  protected void onUnRegister() {
  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_devicemanage_activity_product_encyclopedias;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mBinding = (MoudleDevicemanageActivityProductEncyclopediasBinding) viewDataBinding;

    mBinding.setViewModel(mViewModel);
    //video
    mProductVideoAdapter = new ProductVideoAdapter(this, this, new DeviceVideoEntry[]{});
    mBinding.rvVideo.setLayoutManager(new LinearLayoutManager(this));
    mBinding.rvVideo.setAdapter(mProductVideoAdapter);

    //FAQ details
    mFaqAdapter = new FaqAdapter(this, this, "productEncy");
    mBinding.rvFaq.setLayoutManager(new LinearLayoutManager(this));
    mFaqAdapter.setHasStableIds(true);
    mBinding.rvFaq.setAdapter(mFaqAdapter);
    mFaqAdapter.setOnFaqItemClick(new FaqAdapter.OnFaqItemClick() {
      @Override
      public void OnFaqItemClick(DeviceFaqEntry deviceFaqEntry, int position, View view) {
        //点击每个问题图标时
        if (deviceFaqEntry.isAnswerState()) {
          //不显示答案
          deviceFaqEntry.setAnswerState(false);
          mBinding.rvFaq.post(new Runnable() {
            @Override
            public void run() {
              (view).setRotation(270);
            }
          });

        } else {
          //显示答案
          deviceFaqEntry.setAnswerState(true);
          mBinding.rvFaq.post(new Runnable() {
            @Override
            public void run() {
              (view).setRotation(90);
            }
          });

        }
        mFaqAdapter.notifyDataSetChanged();
      }
    });


    mBinding.title1.getPaint().setTypeface(Typeface.DEFAULT_BOLD);
    mToolbarData = new ToolbarData(LanguageStrings.app_producthelp_textview_text(), new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });
    mBinding.setToolbarData(mToolbarData);
    mBinding.setUiState(new DeviceRegistedDetailUistate());
    //bundle is data attached to the privious intent.
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      mDeviceInfo = (DeviceInfo) bundle.getSerializable(KEY_PREV_DATA);
      Log.i("lesly ", "lesly product ency device info = " + mDeviceInfo);
      if (!this.isFinishing()) {
        Glide.with(this).load(mDeviceInfo.getDeviceIcon()).into(mBinding.produceEncyDeviceIcon);
      }
      mViewModel.getProductEncyclopedias(mDeviceInfo.getProductId());
    }
  }

  @SuppressLint("RestrictedApi")
  private void goBack() {
    sendBackTrace();
    finish();
  }


  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.mProductEncyclopediasInfo.observe(this, new Observer<ProductEncyclopediasInfo>() {
      @RequiresApi(api = Build.VERSION_CODES.N)
      @Override
      public void onChanged(ProductEncyclopediasInfo productEncyclopediasInfo) {
        try {
          if (productEncyclopediasInfo != null) {
            mProductEncyInfo = productEncyclopediasInfo;
            //产品信息
            ProductEncyProduct productInfo = productEncyclopediasInfo.getProduct();
            mBinding.setProductEncyInfo(productInfo);
            Glide.with(ProductEncyclopediasActivity.this).load(productInfo.getProductPicUrl()).into(mBinding.produceEncyDeviceIcon);
            mBinding.productEncyDetailTv.setText(productInfo.getShortDescription());
            //处理View THE Munual
            ProductEncyManual productEncyManual = mProductEncyInfo.getManual();
            if (null == productEncyManual) {
              mViewModel.manualStatus.set(false);
            } else {
              String url = productEncyManual.getUrl();
              if (TextUtils.isEmpty(url)) {
                mViewModel.manualStatus.set(false);
              } else {
                mViewModel.manualStatus.set(true);
              }
            }

            //TECH SPECS 部分展示(50个字符)
            if (productInfo.getTechnicalSpecification() != null) {
              String techSpecs = productInfo.getTechnicalSpecification();
              mBinding.productEncyTectSpecTv.setText(techSpecs);
              mBinding.productEncyTectSpecTv.setMaxLines(3);
              mBinding.productEncyTectSpecTv.setEllipsize(TextUtils.TruncateAt.END);
            }

            //how-to-video数据显示
            ProductEncyGuidance[] guidances = productEncyclopediasInfo.getGuidance();
            if (null == guidances) {
              mViewModel.howtoVideoStatus.set(false);
            } else {
              if (guidances.length > 0) {
                mViewModel.howtoVideoStatus.set(true);
              } else {
                mViewModel.howtoVideoStatus.set(false);
              }
            }
            //设置detail
            if (mProductEncyInfo != null && mProductEncyInfo.getProduct() != null &&!TextUtils.isEmpty(mProductEncyInfo.getProduct().getShortDescription())) {
              mViewModel.details.set(true);
            } else {
              mViewModel.details.set(false);
            }

            //设置tech specs
            if (mProductEncyInfo != null && mProductEncyInfo.getProduct() != null &&  !TextUtils.isEmpty(mProductEncyInfo.getProduct().getTechnicalSpecification())) {
              mViewModel.techSpecs.set(true);
            } else {
              mViewModel.techSpecs.set(false);
            }

            DeviceVideoEntry[] datas = new DeviceVideoEntry[guidances.length];
            for (int i = 0; i < datas.length; i++) {
              datas[i] = new DeviceVideoEntry(guidances[i].getUrl(), guidances[i].getName());
            }
            if (datas.length != 0) {
              mProductVideoAdapter.setDatas(datas);
              if (mProductVideoAdapter != null) {
                mProductVideoAdapter.notifyDataSetChanged();
              }
            }
            //FAQ数据显示
            ProductEncyFaq[] faqs = productEncyclopediasInfo.getFaq();
            if (null == faqs) {
              //如果整个FAQ模块未配置就隐藏
              mViewModel.faq.set(false);
            } else {
              if (null == faqs) {
                mViewModel.faq.set(false);
              } else {
                if (faqs.length > 0) {
                  mViewModel.faq.set(true);
                  //移除未配置多语言的Item
                  List<ProductEncyFaq> faqEntryList = Arrays.asList(faqs);
                  List<DeviceFaqEntry> faqEntryResult = new ArrayList<>();
                  for (int i = 0; i < faqEntryList.size(); i++) {
                    ProductEncyFaq  faqItem = faqEntryList.get(i);
                    if (null!=faqItem){
                      if (!TextUtils.isEmpty(faqItem.getTitle())){
                        faqEntryResult.add(new DeviceFaqEntry(faqItem.getTitle(),faqItem.getAnswer()));
                      }
                    }
                  }
                  if (faqEntryResult.size()!=0){
                    mFaqAdapter.setDatas(faqEntryResult);
                    mFaqAdapter.notifyDataSetChanged();
                  }
                } else {
                  mViewModel.faq.set(false);
                }
              }
            }
          } else {
            mViewModel.manualStatus.set(true);
            mViewModel.howtoVideoStatus.set(true);
            mViewModel.details.set(true);
            mViewModel.techSpecs.set(true);
            mViewModel.faq.set(true);
          }
        }catch (Exception e){
          LogUtils.e(TAG,"产品百科存在异常数据");
        }

      }
    });

  }


  @Override
  protected Class getViewModelClass() {
    return ProductEncyclopediasViewModel.class;
  }

  @Override
  public void onItemClick(Object uiState) {
    sendToVideosButtonClick();
  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

  }


  public void switchExtendVideo(View view) {
    Object isExtend = view.getTag();
    ImageView arrowButton = null;
    if (view.getId() == R.id.llExpendVideo) {
      //video 下拉框
      if (mProductVideoAdapter == null) {
        return;
      }
      mProductVideoAdapter.onPause();
      sendToVideosMoreButtonClick();

      if (isExtend != null) {
        //收起视频
        if (mProductVideoAdapter.getDatas().length > 1) {
          ViewGroup.LayoutParams params = mBinding.cvVideo.getLayoutParams();
          params.height = -2;
          mBinding.cvVideo.setLayoutParams(params);
        }

        mProductVideoAdapter.setPart(true);
        //  mProductVideoAdapter.notifyDataSetChanged();
      } else {
        if (mProductVideoAdapter.getDatas().length > 1) {
          ViewGroup.LayoutParams params = mBinding.cvVideo.getLayoutParams();
          if (mProductVideoAdapter.getDatas().length < LINE_FOUR) {
            params.height = AutoSizeUtils.mm2px(ProductEncyclopediasActivity.this, mProductVideoAdapter.getDatas().length * 560);
          } else {
            params.height = AutoSizeUtils.mm2px(ProductEncyclopediasActivity.this, 3 * 560);
          }
        }
        mProductVideoAdapter.setPart(false);
      }
      arrowButton = mBinding.ibExpendVideo;
    } else if (view.getId() == R.id.productEncyTectSpecll) {
      //技术规格 下拉框
      String techSpecs = "";
      if (mProductEncyInfo != null && mProductEncyInfo.getProduct() != null && mProductEncyInfo.getProduct().getTechnicalSpecification() != null) {
        techSpecs = mProductEncyInfo.getProduct().getTechnicalSpecification();
      }
      sendTechSpecsButtonClick();
      if (isExtend != null) {
        // 展示部分字符
        //TECH SPECS 部分展示(50个字符)
        /*if(techSpecs.length() > 50){
          techSpecs = techSpecs.substring(0,50);
        }*/
        mBinding.productEncyTectSpecTv.setMaxLines(3);
        mBinding.productEncyTectSpecTv.setEllipsize(TextUtils.TruncateAt.END);
        mBinding.productEncyTectSpecTv.setText(techSpecs);
      } else {
        //全部字符展示
        mBinding.productEncyTectSpecTv.setMaxLines(Integer.MAX_VALUE);
        mBinding.productEncyTectSpecTv.setText(techSpecs);
      }
      arrowButton = mBinding.productEncyTectSpecIb;
    } else if (view.getId() == R.id.faqll) {
      //Faq 下拉框
      arrowButton = mBinding.ibfaq;
      if (isExtend != null) {
        mFaqAdapter.setState(true);
        mFaqAdapter.setEntryAnswerState(false);
        mFaqAdapter.notifyDataSetChanged();
      } else {
        mFaqAdapter.setState(false);
        mFaqAdapter.notifyDataSetChanged();
      }
    } else if (view.getId() == R.id.productEncyDetaill) {
      //DETAILS下拉框
      arrowButton = mBinding.productEncyDetailIb;
      sendLongDescriptionButtonClick();
      if (mProductEncyInfo != null && mProductEncyInfo.getProduct() != null && mProductEncyInfo.getProduct().getShortDescription() != null) {
        if (isExtend != null) {
          //显示短描述
          mBinding.productEncyDetailTv.setText(mProductEncyInfo.getProduct().getShortDescription());
        } else {
          //显示长描述
          mBinding.productEncyDetailTv.setText(mProductEncyInfo.getProduct().getLongDescription());
        }
      }
    }

    //箭头方向调整
    if (isExtend == null) {
      arrowButton.setRotation(90);
      view.setTag(true);
    } else {
      arrowButton.setRotation(270);
      view.setTag(null);
    }
  }


  public void viewProductEncyManual(View view) {
    LogUtils.i("lesly product ency click ");
    sendUserManualButtonClick();
    if (view.getId() == R.id.produceEncyManualView) {
      if (mProductEncyInfo == null) {
        mProductEncyInfo = new ProductEncyclopediasInfo();
      }
      ProductEncyManual productEncyManual = mProductEncyInfo.getManual();
      try {
        String url = productEncyManual.getUrl();
        if (!TextUtils.isEmpty(url) && url.endsWith(SUFFIX_PDF)) {
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_MANUAL)
            .withSerializable(PARAMETER_MANUAL_INFO, productEncyManual)
            .withString(KEY_FROM, RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL)
            .navigation();
        } else {
          goToWebPage(this, url);
        }
      } catch (Exception e) {

      }
    }
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    DialogUtil.dismiss();
  }

  @Override
  public void onCreate(@Nullable Bundle savedInstanceState, @Nullable PersistableBundle persistentState) {
    super.onCreate(savedInstanceState, persistentState);
    LogUtils.i("lesly newConfig.orientatio222 ");
  }

  @Override
  public void onConfigurationChanged(Configuration newConfig) {
    super.onConfigurationChanged(newConfig);
    LogUtils.i("lesly newConfig.orientation = " + newConfig.orientation);
    if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
      // 横屏
    } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
      // 竖屏
    }
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  private void initTrace() {
    stayEleId = "7";
    pageId = "74";
    mouduleId = "9";
    nextButtoneleid = "2";
  }

  public void sendUserManualButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendLongDescriptionButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
  }

  public void sendTechSpecsButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 4 + "", "1");
  }

  public void sendToVideosButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 5 + "", "1");
  }

  public void sendToVideosMoreButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 6 + "", "1");
  }


  @Override
  protected void onPause() {
    super.onPause();
    if (mProductVideoAdapter != null) {
      mProductVideoAdapter.onPause();
    }

  }

  @Override
  public void onStop() {
    super.onStop();
    if (mProductVideoAdapter != null) {
      mProductVideoAdapter.onStop();
      // mProductVideoAdapter = null;
    }
  }

  @Override
  protected void onRestart() {
    super.onRestart();
    if (mProductVideoAdapter != null) {
      mProductVideoAdapter.notifyDataSetChanged();
    }

  }

  @Override
  protected void onResume() {
    super.onResume();
    if (mProductVideoAdapter != null) {
      // 保持当前的isPart状态
      boolean currentIsPart = mProductVideoAdapter.isPart();

      // 重新初始化adapter
      mProductVideoAdapter = new ProductVideoAdapter(this, this, mProductVideoAdapter.getDatas());
      // 设置保存的isPart状态
      mProductVideoAdapter.setPart(currentIsPart);

      mBinding.rvVideo.setAdapter(mProductVideoAdapter);
    }

  }
}
