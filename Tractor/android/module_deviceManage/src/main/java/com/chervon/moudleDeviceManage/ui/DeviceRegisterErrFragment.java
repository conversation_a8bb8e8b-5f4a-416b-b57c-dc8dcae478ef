package com.chervon.moudleDeviceManage.ui;


import static com.chervon.moudleDeviceManage.ui.DeviceRegisterInfoFragment.DEVICE_REGISTER_UI_DATA;
import android.os.Bundle;
import android.text.TextUtils;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceRegistedErrBinding;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterErrViewModel;


public class DeviceRegisterErrFragment extends BaseEnhanceFragment<DeviceRegisterErrViewModel,MoudleDevicemanageFragmentDeviceRegistedErrBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  private static final String RETRY_BUTTON_CLICK = "2";
  private MoudleDevicemanageFragmentDeviceRegistedErrBinding mViewDataBinding;
  private DeviceRegisterUistate uiState;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    Bundle bundle = getArguments();
    DeviceRegisterUistate deviceRegisterUistate = new DeviceRegisterUistate();
    if (bundle != null) {
      deviceRegisterUistate = (DeviceRegisterUistate) bundle.getSerializable(DEVICE_REGISTER_UI_DATA);
      uiState = (DeviceRegisterUistate) bundle.getSerializable(DEVICE_REGISTER_UI_DATA);
    }
    mViewDataBinding.setUiState(deviceRegisterUistate);
    mViewDataBinding.setController(this);

    if (null != uiState){
      if (!TextUtils.isEmpty(uiState.getMessage())){
        ToastUtils.showShort(uiState.getMessage());
      }
    }

  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_devicemanage_fragment_device_registed_err;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (MoudleDevicemanageFragmentDeviceRegistedErrBinding) viewDataBinding;

  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends DeviceRegisterErrViewModel> getViewModelClass() {
    return DeviceRegisterErrViewModel.class;
  }

  public void retry(DeviceRegisterUistate uiState) {
    sendBaseTraceClick(RETRY_BUTTON_CLICK);
    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
//    DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
//    uistate.setUrl(url);
//    bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uistate);
//    bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
//    controller.navigate(R.id.deviceRegisterErrFragment, bundle);
    controller.popBackStack();
  }

  public void goBack(DeviceRegisterUistate uiState) {
      sendBackTrace();
    if(isAdded()){
      this.getActivity().finish();
    }

  }


  @Override
  public boolean OnkeyBack() {
    DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
    if (null==uiState){
      uiState = new DeviceRegisterUistate();
    }
    goBack(uiState);
    return true;
  }

  private void initTrace() {
    stayEleId="3";
    pageId="108";
    mouduleId="12";
    nextButtoneleid ="2";
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }
}
