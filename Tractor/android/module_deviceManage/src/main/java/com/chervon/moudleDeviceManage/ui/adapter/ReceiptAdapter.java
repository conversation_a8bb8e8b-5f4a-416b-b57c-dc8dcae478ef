package com.chervon.moudleDeviceManage.ui.adapter;


import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.pdf.PdfRenderer;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.utils.DeviceManagerConstants;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.adapter
 * @ClassName: ReceiptAdapter
 * @Description: device register with receipt
 * @Author: wangheng
 * @CreateDate: 2022/4/28 下午4:20
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/18
 * @UpdateRemark: 适配欧洲设备注册流程
 * @Version: 1.1
 */
public class ReceiptAdapter extends RecyclerView.Adapter {
    public List<String> datas = new ArrayList<>();
    public static final int TYPE_HEADER = 0;
    public static final int TYPE_FOOTER = 1;
    public static final int TYPE_ITEM = 2;
    private Context context;
    private ItemClick itemClick;

    public void setDatas(List<String> datas) {
        this.datas = datas;
    }

    public ReceiptAdapter(Context context, ItemClick itemClick, List<String> datas) {
        this.context = context;
        this.datas = datas;
        this.itemClick = itemClick;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_FOOTER) {
            View itemView = LayoutInflater.from(context).inflate(R.layout.moudle_devicemanage_recycle_receipt_item_footer, parent, false);
            return new FooterViewHolder(itemView);
        } else {
            View itemView = LayoutInflater.from(context).inflate(R.layout.moudle_devicemanage_recycle_receipt_item, parent, false);
            return new ItemViewHolder(itemView);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, @SuppressLint("RecyclerView") int position) {
        int viewType = getItemViewType(position);
        if (viewType == TYPE_FOOTER) {
            ((FooterViewHolder) holder).tvFooter.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    itemClick.onItemClick(v);
                }
            });
            if (datas.size() == 0) {
                ((FooterViewHolder) holder).tvFooter.setVisibility(View.VISIBLE);
            } else {
                ((FooterViewHolder) holder).tvFooter.setVisibility(View.GONE);
            }

        } else {

            ((ItemViewHolder) holder).btDel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    datas.remove(position);
                    notifyDataSetChanged();
                    itemClick.onItemClick(v, null);
                }
            });

            ((ItemViewHolder) holder).clContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    v.setTag(position);
                    itemClick.onItemClick(v);
                }
            });
            ((ItemViewHolder) holder).ivDeviceIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                   String url =  datas.get(position);
                   if (TextUtils.isEmpty(url)){
                       return;
                   }
                   //PDF和WORD不支持预览
                   if (url.contains(DeviceManagerConstants.MEDIA_WORD_DOCX)||url.contains(DeviceManagerConstants.MEDIA_WORD_DOC)){
                       return;
                   }

                    v.setTag(position);
                    itemClick.onItemClick(v);
                }
            });
            //针对pdf与word显示适配
            checkResource(datas.get(position), ((ItemViewHolder) holder).ivDeviceIcon);
        }
    }

    /**
     * 处理选择的资源
     *
     * @param url
     * @param ivDeviceIcon
     */
    private void checkResource(String url, ImageView ivDeviceIcon) {
        if (TextUtils.isEmpty(url)) {
            return;
        }
        if (url.contains(DeviceManagerConstants.MEDIA_PDF)) {
            File urlFile = new File(url);
            try {
                ParcelFileDescriptor fileDescriptor = ParcelFileDescriptor.open(urlFile, ParcelFileDescriptor.MODE_READ_ONLY);
                PdfRenderer renderer = new PdfRenderer(fileDescriptor);
                
                // 检查PDF是否有页面
                if (renderer.getPageCount() <= 0) {
                    LogUtils.e("PDF文件页面数为0");
                    renderer.close();
                    fileDescriptor.close();
                    return;
                }

                // 安全打开第一页
                try {
                    PdfRenderer.Page page = renderer.openPage(0);
                    Bitmap bitmap = Bitmap.createBitmap(
                        Math.max(1, page.getWidth()),
                        Math.max(1, page.getHeight()),
                        Bitmap.Config.ARGB_8888);
                    page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY);
                    page.close();
                    
                    Glide.with(context).load(bitmap).into(ivDeviceIcon);
                } finally {
                    renderer.close();
                    fileDescriptor.close();
                }
            } catch (IOException e) {
                LogUtils.e("PDF渲染失败: " + e.getMessage());
            } catch (IllegalArgumentException e) {
                LogUtils.e("PDF页面索引无效: " + e.getMessage());
            }
        } else if (url.contains(DeviceManagerConstants.MEDIA_WORD_DOC)||url.contains(DeviceManagerConstants.MEDIA_WORD_DOCX)) {
            Glide.with(context).load(R.drawable.icon_file_word).into(ivDeviceIcon);
        }else if (url.contains(DeviceManagerConstants.MEDIA_WORD_PAGES)){
            Glide.with(context).load(R.drawable.icon_file_pages).into(ivDeviceIcon);
        }else {
            Glide.with(context).load(url).into(ivDeviceIcon);
        }

    }

    @Override
    public int getItemCount() {
        return datas.size() + 1;
    }

    @Override
    public int getItemViewType(int position) {
        if (position == getItemCount() - 1) {
            return TYPE_FOOTER;
        } else {
            return TYPE_ITEM;
        }
    }


    class FooterViewHolder extends RecyclerView.ViewHolder {

        View tvFooter;

        public FooterViewHolder(@NonNull View itemView) {
            super(itemView);
            tvFooter = itemView;
        }
    }

    class ItemViewHolder extends RecyclerView.ViewHolder {
        ImageButton btDel;
        ImageView ivDeviceIcon;
        View clContainer;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            btDel = itemView.findViewById(R.id.ibDeviceDel);
            ivDeviceIcon = itemView.findViewById(R.id.ivDeviceIcon);
            clContainer = itemView.findViewById(R.id.clContainer);
        }
    }
}
