package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_ROUTER_PATH;
import static com.chervon.libRouter.RouterConstants.KEY_TO;

import android.annotation.SuppressLint;
import android.content.ContentUris;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.provider.OpenableColumns;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.navigation.NavBackStackEntry;
import androidx.navigation.NavController;
import androidx.navigation.NavDestination;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.BuildConfig;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityHomeBinding;
import com.chervon.moudleDeviceManage.ui.state.HomeDeviceUiState;
import com.chervon.moudleDeviceManage.ui.viewmodel.HomeViewModel;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: HomeActivity
 * @Description: the home page of app
 * @Author: wangheng
 * @CreateDate: 2022/4/28 下午4:20
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/18
 * @UpdateRemark: 适配欧洲设备注册流程
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE)
public class HomeActivity extends BaseActivity<HomeViewModel> {

    private MoudleDevicemanageActivityHomeBinding mBinding;
    private ToolbarData mToolbarData;
    @Autowired(name = KEY_PREV_DATA)
    public int prevFragment;
    @Autowired(name = KEY_PREV_DATA)
    public DeviceInfo deviceInfo;
    private Uri photoUri;
    private ResultListenner resultListenner;
    public static final int PICK_PDF_AND_WORD_REQUEST = 101101;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_devicemanage_activity_home;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mBinding = (MoudleDevicemanageActivityHomeBinding) viewDataBinding;
        mToolbarData = new ToolbarData("", new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goBack();
            }
        });
        mBinding.setToolbarData(mToolbarData);

        Bundle bundle = getIntent().getExtras();
        NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        if (bundle != null) {

            NavBackStackEntry navBackStackEntry;
            if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)){
                //欧洲进入
                controller.setGraph(R.navigation.nav_graph_device_eu);
                navBackStackEntry  = controller.getBackStackEntry(R.id.deviceRegisterEuFragment);
            } else {
                //其他进入
                controller.setGraph(R.navigation.nav_graph_device);
                navBackStackEntry = controller.getBackStackEntry(R.id.deviceRegisterFragment);
            }
            controller.getBackStack().clear();
            controller.getBackStack().add(navBackStackEntry);
            int fragmentId = getIntent().getExtras().getInt(KEY_PREV_FRAGMENT);
            if (fragmentId == R.id.deviceDetailPage) {
                //    controller.navigate(R.id.deviceRegisterListFragment, bundle);
            }else{
                String routerPath=  getIntent().getExtras().getString(KEY_ROUTER_PATH);
                if ((!TextUtils.isEmpty(routerPath)&& ACTIVITY_URL_DEVICE.equals(routerPath))) {

                }else{
                    int from = bundle.getInt(KEY_FROM);
                    int pageTo = bundle.getInt(KEY_TO);
                    if (pageTo != 0) {
                        controller.navigate(pageTo, bundle);
                    }
                }

            }

        } else {
        }
        controller.addOnDestinationChangedListener(new NavController.OnDestinationChangedListener() {
            @Override
            public void onDestinationChanged(@NonNull NavController controller, @NonNull NavDestination destination, @Nullable Bundle arguments) {
                if (R.id.deviceRegisterInfoFragment == destination.getId()) {
                    mToolbarData.setTitle(LanguageStrings.appDeviceregistsuccessTitleTextviewText());
                    mBinding.setToolbarData(mToolbarData);
                }
                if (R.id.deviceRegisterFragment == destination.getId() || R.id.deviceRegisterEuFragment == destination.getId() || R.id.deviceRegisterMoreFragment == destination.getId()) {
                    mToolbarData.setTitle(LanguageStrings.appDeviceregistTitletext());
                    mBinding.setToolbarData(mToolbarData);
                }

            }
        });
    }

    @SuppressLint("RestrictedApi")
    private void goBack() {
        if (mkeyBackListener != null) {
            if (mkeyBackListener.OnkeyBack()) {
                mkeyBackListener = null;
            }
        }
    }


    @Override
    protected void initData(Bundle savedInstanceState) {
        mViewModel.mLiveData.observe(this, new Observer<HomeDeviceUiState>() {
            @Override
            public void onChanged(HomeDeviceUiState homeDeviceUiState) {

            }
        });
    }

    @Override
    protected Class getViewModelClass() {
        return HomeViewModel.class;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK && data != null && data.getData() != null) {
            Uri uri = data.getData();
            if (requestCode == PICK_PDF_AND_WORD_REQUEST) {
                getFilePathFromUriAsync(uri, path -> {
                    if (resultListenner != null) {
                        resultListenner.onActivityResult(requestCode, resultCode, data, path);
                    }
                });
            } else {
                String imgUrl = getRealPathFromUri(this, uri);
                if (imgUrl != null && resultListenner != null) {
                    resultListenner.onActivityResult(requestCode, resultCode, data, imgUrl);
                }
            }
        }
    }


    public static String getRealPathFromUri(Context context, Uri uri) {
        int sdkVersion = Build.VERSION.SDK_INT;
        if (sdkVersion >= 19) {
            return getRealPathFromUriAboveApi19(context, uri);
        } else {
            return getRealPathFromUriBelowAPI19(context, uri);
        }
    }


    private static String getRealPathFromUriBelowAPI19(Context context, Uri uri) {
        return getDataColumn(context, uri, null, null);
    }


    private static String getRealPathFromUriAboveApi19(Context context, Uri uri) {
        String filePath = null;
        if (DocumentsContract.isDocumentUri(context, uri)) {
            String documentId = DocumentsContract.getDocumentId(uri);
            String[] split = documentId.split(":");
            String type = split[0];
            if ("primary".equalsIgnoreCase(type)) {
                filePath = Environment.getExternalStorageDirectory() + "/" + split[1];
            } else if ("raw".equalsIgnoreCase(type)) {
                filePath = split[1];
            }else if (isMediaDocument(uri)) {
                // MediaProvider
                String id = documentId.split(":")[1];

                String selection = MediaStore.Images.Media._ID + "=?";
                String[] selectionArgs = {id};
                filePath = getDataColumn(context, MediaStore.Images.Media.EXTERNAL_CONTENT_URI, selection, selectionArgs);
            } else if (isDownloadsDocument(uri)) {
                Uri contentUri = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), Long.valueOf(documentId));
                filePath = getDataColumn(context, contentUri, null, null);
            }
        } else if ("content".equalsIgnoreCase(uri.getScheme())) {
            filePath = getDataColumn(context, uri, null, null);
        } else if ("file".equals(uri.getScheme())) {
            filePath = uri.getPath();
        }
        return filePath;
    }

    private static String getDataColumn(Context context, Uri uri, String selection, String[] selectionArgs) {
        String path = null;

        String[] projection = new String[]{MediaStore.Images.Media.DATA};
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver().query(uri, projection, selection, selectionArgs, null);
            if (cursor != null && cursor.moveToFirst()) {
                int columnIndex = cursor.getColumnIndexOrThrow(projection[0]);
                path = cursor.getString(columnIndex);
            }
        } catch (Exception e) {
            if (cursor != null) {
                cursor.close();
            }
        }
        return path;
    }

    /**
     * @param uri the Uri to check
     * @return Whether the Uri authority is MediaProvider
     */
    private static boolean isMediaDocument(Uri uri) {
        return "com.android.providers.media.documents".equals(uri.getAuthority());
    }

    /**
     * @param uri the Uri to check
     * @return Whether the Uri authority is DownloadsProvider
     */
    private static boolean isDownloadsDocument(Uri uri) {
        return "com.android.providers.downloads.documents".equals(uri.getAuthority());
    }


    public int getPrevFragment() {
        return prevFragment;
    }

    public void setPrevFragment(int prevFragment) {
        this.prevFragment = prevFragment;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public void setUri(Uri photoUri) {
        this.photoUri = photoUri;
    }

    public void setResultListenner( ResultListenner resultListenner) {
        this.resultListenner = resultListenner;

    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            goBack();
        }
        return false;
    }


    private void getFilePathFromUriAsync(Uri uri, OnFilePathCallback callback) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    InputStream inputStream = getContentResolver().openInputStream(uri);
                    String fileName = getFileName(uri);
                    File tempFile = new File(getCacheDir(), fileName);
                    FileOutputStream outputStream = new FileOutputStream(tempFile);
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = inputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, length);
                    }
                    outputStream.close();
                    inputStream.close();
                    runOnUiThread(() -> callback.onFilePath(tempFile.getAbsolutePath()));
                } catch (IOException e) {
                    runOnUiThread(() -> callback.onFilePath(null));
                }
            }
        }).start();
    }

    private interface OnFilePathCallback {
        void onFilePath(String path);
    }

    private String getFileName(Uri uri) {
        String result = null;
        if (uri.getScheme().equals("content")) {
            try (Cursor cursor = getContentResolver().query(uri, null, null, null, null)) {
                if (cursor != null && cursor.moveToFirst()) {
                    result = cursor.getString(cursor.getColumnIndexOrThrow(OpenableColumns.DISPLAY_NAME));
                }
            }
        }
        if (result == null) {
            result = uri.getPath();
            int cut = result.lastIndexOf('/');
            if (cut != -1) {
                result = result.substring(cut + 1);
            }
        }
        return result;
    }

}

