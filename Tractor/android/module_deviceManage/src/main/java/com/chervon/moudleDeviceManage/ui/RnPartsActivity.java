package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_INSTANCE_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PART_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.PartsInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityRnPartsBinding;
import com.chervon.moudleDeviceManage.ui.adapter.RnPartsAdapter;
import com.chervon.moudleDeviceManage.ui.viewmodel.RnPartsModel;

import java.util.Arrays;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: RnPartsActivity
 * @Description: RN面板 配件Activity
 * @Author: LiDeLi
 * @CreateDate: 2022/12/2 上午10:23
 * @UpdateUser: LiDeLi
 * @UpdateDate: 2022/12/2 上午10:23
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS)
public class RnPartsActivity extends BaseActivity<RnPartsModel> implements ItemClick {
    private MoudleDevicemanageActivityRnPartsBinding mBinding;
    private ToolbarData mToolbarData;
    private RnPartsAdapter rnPartsAdapter;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_devicemanage_activity_rn_parts;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mBinding = (MoudleDevicemanageActivityRnPartsBinding) viewDataBinding;
        mToolbarData = new ToolbarData(LanguageStrings.appfittinglistTitle(), new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goBack();
            }
        });
        mBinding.setToolbarData(mToolbarData);
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            mViewModel.deviceId = bundle.getString(PARAMETER_DEVICD_ID);
            mViewModel.productId = bundle.getString(PARAMETER_PRODUCT_ID);
            LogUtils.i("lesly  productId =  " + mViewModel.deviceId);
        }
        String mac = mViewModel.getDeviceMac(mViewModel.deviceId);

        rnPartsAdapter = new RnPartsAdapter(this, this, mac);
        rnPartsAdapter.setOnPartClick(new RnPartsAdapter.OnPartClick() {
            @Override
            public void onPartClick(String partsId, int position) { //点击单个配件
                sendItemTraceClick();
                String partsInstanceId = "";
                if (rnPartsAdapter != null && rnPartsAdapter.getAccessorieInfos() != null && rnPartsAdapter.getAccessorieInfos().length > position) {
                    partsInstanceId = rnPartsAdapter.getAccessorieInfos()[position].getProductPartsInstanceId() + "";
                }

                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS_DETAILS)
                        .withString(PARAMETER_PART_ID, partsId)
                        .withString(PARAMETER_PRODUCT_ID, mViewModel.productId)
                        .withString(PARAMETER_DEVICD_ID, mViewModel.deviceId)
                        .withString(PARAMETER_INSTANCE_ID, partsInstanceId)
                        .navigation();
            }
        });
        mBinding.rnPartsRecy.setLayoutManager(new LinearLayoutManager(this));
        rnPartsAdapter.setHasStableIds(true);
        mBinding.rnPartsRecy.setAdapter(rnPartsAdapter);

    }

    private void getAccessDatas() {
        if (TextUtils.isEmpty(mViewModel.deviceId) && TextUtils.isEmpty(mViewModel.productId)) {
            mBinding.rnPartsRecy.setVisibility(View.GONE);
            mBinding.defaultPartLayout.setVisibility(View.VISIBLE);
        } else {

            ProgressHelper.showProgressView(this, 0, true);
            mViewModel.getEquipmentAccess(mViewModel.productId, mViewModel.deviceId);
        }
    }

    @SuppressLint("RestrictedApi")
    private void goBack() {
        sendBackTrace();
        finish();

    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        loadingData();
    }

    private void loadingData() {
        mViewModel.mAccessorLiveData.observe(this, new Observer<PartsInfo[]>() {
            @Override
            public void onChanged(PartsInfo[] partsInfos) {

                ProgressHelper.hideProgressView(RnPartsActivity.this);
                Log.i("lesly", "lesly AccessorieInfo initData accessorieInfos = " + Arrays.toString(partsInfos));
                if (partsInfos == null || partsInfos.length == 0) {
                    mBinding.rnPartsRecy.setVisibility(View.GONE);
                    mBinding.defaultPartLayout.setVisibility(View.VISIBLE);
                } else {
                    mBinding.rnPartsRecy.setVisibility(View.VISIBLE);
                    mBinding.defaultPartLayout.setVisibility(View.GONE);
                    rnPartsAdapter.setAccessorieInfos(partsInfos);
                    rnPartsAdapter.notifyDataSetChanged();
                }

            }
        });
    }

    @Override
    protected Class<? extends RnPartsModel> getViewModelClass() {
        return RnPartsModel.class;
    }

    @Override
    public void onItemClick(Object uiState) {

    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

    }


    private void initTrace() {
        stayEleId = "3";
        pageId = "78";
        mouduleId = "9";
        nextButtoneleid = "2";
    }

    public void sendItemTraceClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        sendBackTrace();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadingData();
        getAccessDatas();
    }
}
