package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.CommonUtils.goToWebPage;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_INSTANCE_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_MANUAL_INFO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PART_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.dialog.ConfirmAlertDialog;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.DialogUtil;

import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.PartDetail;
import com.chervon.libDB.entities.ProductEncyFaq;
import com.chervon.libDB.entities.ProductEncyGuidance;
import com.chervon.libDB.entities.ProductEncyManual;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.result.BaseResult;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.data.model.DeviceFaqEntry;
import com.chervon.moudleDeviceManage.data.model.DeviceVideoEntry;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityPartDetailBinding;
import com.chervon.moudleDeviceManage.ui.adapter.FaqAdapter;
import com.chervon.moudleDeviceManage.ui.adapter.ProductVideoAdapter;
import com.chervon.moudleDeviceManage.ui.viewmodel.RnPartDetailModel;
import com.google.firebase.iid.FirebaseInstanceId;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;
import io.reactivex.schedulers.Schedulers;
import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: RnPartDetailActivity
 * @Description: 配件详情
 * @Author: LiDeLi
 * @CreateDate: 2022/12/2 下午2:27
 * @UpdateUser: LiDeLi
 * @UpdateDate: 2022/12/2 下午2:27
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS_DETAILS)
public class RnPartDetailActivity extends BaseActivity<RnPartDetailModel> implements ItemClick {
    public static final int EXPIRE = 2;
    public static final String PDF = ".pdf";
    private MoudleDevicemanageActivityPartDetailBinding mBinding;
    private Context mContext;
    private ToolbarData mToolbarData;
    private PartDetail mPartDetail;
    protected Dialog mProgressDialog;
    @Autowired(name = PARAMETER_DEVICD_ID)
    public String deviceId;
    public String pid;
    private String partId;
    private String partInstanceId;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_devicemanage_activity_part_detail;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mContext = this;
        mBinding = (MoudleDevicemanageActivityPartDetailBinding) viewDataBinding;
        mToolbarData = new ToolbarData(LanguageStrings.appfittinglistTitle(), new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goBack();
            }
        });
        mBinding.setToolbarData(mToolbarData);
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            partId = bundle.getString(PARAMETER_PART_ID);
            partInstanceId = bundle.getString(PARAMETER_INSTANCE_ID);
            deviceId = bundle.getString(PARAMETER_DEVICD_ID);
            pid = bundle.getString(PARAMETER_PRODUCT_ID);
            LogUtils.i("lesly  partId =  " + partId);

        }


    }

    private void getPartDetails() {
        if (!"".equals(partId) && partId != null) {
            if (mProgressDialog != null && mProgressDialog.isShowing()) {
            } else {
                mProgressDialog = DialogUtil.showRnLoadingDialog(this);
            }
            mViewModel.getPartDetail(partId, deviceId);
        }
    }

    @SuppressLint("RestrictedApi")
    private void goBack() {
        sendBackTrace();
        finish();

    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);
        initTrace();
        loadingData();
    }

    private void loadingData() {
        mViewModel.mPartResetLiveData.observe(this, new Observer<BaseUistate>() {
            @Override
            public void onChanged(BaseUistate uistate) {
                if (uistate.getState() == RESPONSE_SUCCESS) {
                    getPartDetails();

                }
            }
        });
        mViewModel.mPartDetailLiveData.observe(this, new Observer<PartDetail>() {
            @Override
            public void onChanged(PartDetail partDetails) {
                partDetailOnChanged(partDetails);
            }
        });
    }

    private void partDetailOnChanged(PartDetail partDetails) {
        dissmissDialog();
        if (partDetails != null) {
            mBinding.scrollView.setVisibility(View.VISIBLE);
            mBinding.defaultPartLayout.setVisibility(View.GONE);
            if (partDetails.getMaintenanceType() != null) {
                showResetBtn();
                if (partDetails.getMaintenanceStatus() == EXPIRE) {
                    showTvNeedImmediately();
                } else {
                    hideTvNeedImmediately();
                    if (partDetails.getMaintenanceType() == 1) {
                        partDetails.setMaintenanceResidueStr(partDetails.getMaintenanceResidue() + " " + LanguageStrings.app_devicemanageaccessorie_days_textview_text());
                    } else if (partDetails.getMaintenanceType() == EXPIRE) {
                        partDetails.setMaintenanceResidueStr(partDetails.getMaintenanceResidue() + " " + LanguageStrings.app_devicemanageaccessorie_workinghours_textview_text());
                    }
                }
                changeTvColor(partDetails);
            } else {
                showTvDescription();
            }
            mPartDetail = partDetails;
            if (mPartDetail != null && !TextUtils.isEmpty(mPartDetail.getBuyUrl())) {
                mBinding.buyNowView.setVisibility(View.VISIBLE);
            }
            mBinding.setPartDetail(partDetails);
            Glide.with(mContext).load(partDetails.getIconUrl()).placeholder(R.drawable.ic_device_default).into(mBinding.produceEncyDeviceIcon);

        } else {
            mBinding.scrollView.setVisibility(View.GONE);
            mBinding.defaultPartLayout.setVisibility(View.VISIBLE);
        }
    }

    private void showTvDescription() {
        mBinding.tvNextMaintenance.setVisibility(View.GONE);
        mBinding.tvWorkingHour.setVisibility(View.GONE);
        mBinding.tvAccessoriesTip.setVisibility(View.GONE);
        mBinding.tvNeedImmediately.setVisibility(View.GONE);
        mBinding.tvDescription.setVisibility(View.GONE);
        if (mBinding.getPartDetail() != null && !TextUtils.isEmpty(mBinding.getPartDetail().getLongDescription())) {
            mBinding.divid1.setVisibility(View.VISIBLE);
            mBinding.tvDetail.setVisibility(View.VISIBLE);
            mBinding.tvDetailContent.setVisibility(View.VISIBLE);
        } else {
            mBinding.divid1.setVisibility(View.GONE);
            mBinding.tvDetail.setVisibility(View.GONE);
            mBinding.tvDetailContent.setVisibility(View.GONE);
        }


        mBinding.btReset.setVisibility(View.GONE);
    }

    private void dissmissDialog() {
        if (mProgressDialog != null) {
            mProgressDialog.dismiss();
        }
    }

    private void changeTvColor(PartDetail partDetails) {
        if (partDetails.getIsRed() == 1) {
            mBinding.tvNextMaintenance.setTextColor(ContextCompat.getColor(this, com.chervon.libBase.R.color.colorRedDA322B));
            mBinding.tvWorkingHour.setTextColor(ContextCompat.getColor(this, com.chervon.libBase.R.color.colorRedDA322B));
        } else {
            mBinding.tvNextMaintenance.setTextColor(ContextCompat.getColor(this, com.chervon.libBase.R.color.colorButtonNormal));
            mBinding.tvWorkingHour.setTextColor(ContextCompat.getColor(this, com.chervon.libBase.R.color.colorButtonNormal));
        }
    }

    private void hideTvNeedImmediately() {
        mBinding.tvNeedImmediately.setVisibility(View.GONE);
        mBinding.tvNextMaintenance.setVisibility(View.VISIBLE);
        mBinding.tvWorkingHour.setVisibility(View.VISIBLE);
        mBinding.divid1.setVisibility(View.GONE);
        mBinding.tvDetail.setVisibility(View.GONE);
        mBinding.tvDetailContent.setVisibility(View.GONE);
    }

    private void showTvNeedImmediately() {
        mBinding.tvNeedImmediately.setVisibility(View.VISIBLE);
        mBinding.tvNextMaintenance.setVisibility(View.GONE);
        mBinding.tvWorkingHour.setVisibility(View.GONE);
        mBinding.divid1.setVisibility(View.GONE);
        mBinding.tvDetail.setVisibility(View.GONE);
        mBinding.tvDetailContent.setVisibility(View.GONE);
    }

    private void showResetBtn() {
        mBinding.btReset.setVisibility(View.VISIBLE);
        mBinding.tvAccessoriesTip.setVisibility(View.VISIBLE);
        mBinding.tvNeedImmediately.setVisibility(View.GONE);
        mBinding.tvDescription.setVisibility(View.GONE);
    }


    public void llPartSwitchExtend(View view) {

    }


    public void viewPartManual(View view) {
        sendUserManualButtonClick();
        if (mPartDetail == null) {
            mPartDetail = new PartDetail();
        }

        ProductEncyManual productEncyManual = mPartDetail.getManual();
        try {
            String url = productEncyManual.getUrl();
            if (!TextUtils.isEmpty(url) && url.endsWith(PDF)) {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_MANUAL).withSerializable(PARAMETER_MANUAL_INFO, productEncyManual).withString(KEY_FROM, RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL).navigation();
            } else {
                goToWebPage(this, url);
            }
        } catch (Exception e) {

        }


    }

    @Override
    protected Class<? extends RnPartDetailModel> getViewModelClass() {
        return RnPartDetailModel.class;
    }

    public void partReset(View v) {
        ConfirmAlertDialog.show(getSupportFragmentManager(), LanguageStrings.app_devicemanagepartdetail_dialog_textview_text(), LanguageStrings.app_devicemanagepartdetail_cancel_button_text(), LanguageStrings.app_devicemanagepartdetail_confirm_button_text(), new Consumer<Boolean>() {
            @Override
            public void accept(Boolean aBoolean) throws Exception {
                if (aBoolean) {
                    mViewModel.partReset(deviceId, partInstanceId);
                } else {
                    //close
                }
            }
        });
    }

    public void partDetailClicl(View v) {
        LogUtils.i("lesly buy click");
        sendToBuyButtonClick();
        if (v.getId() == R.id.buyNowView) {
            if (mPartDetail != null && mPartDetail.getBuyUrl() != null) {
                Uri uri = Uri.parse(mPartDetail.getBuyUrl());
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                startActivity(intent);
            }/*else{
        Toast.makeText(this,"无购买链接！",Toast.LENGTH_SHORT).show();
      }*/
        }
    }

    @Override
    public void onItemClick(Object uiState) {
        sendHowToVideosButtonClick();
    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }


    private void initTrace() {
        stayEleId = "8";
        pageId = "79";
        mouduleId = "9";
        nextButtoneleid = "2";
    }

    public void sendUserManualButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
    }

    public void sendLongDescriptionButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
    }

    public void sendHowToVideosButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, 5 + "", "1");
    }


    public void sendHowToVideosMoreButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, 6 + "", "1");
    }

    public void sendToBuyButtonClick() {

         String eleId = "7";
        String expandDeviceId = "deviceid";
        String expandPid = "pid";
        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(expandDeviceId, TextUtils.isEmpty(deviceId)?"":deviceId);
        expandMap.put(expandPid, TextUtils.isEmpty(pid)?"":pid);
        Utils.sendClickTraceNew(this, mouduleId, pageId, pageResouce, eleId, expandMap);

    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        sendBackTrace();
    }


    @Override
    protected void onPause() {
        super.onPause();


    }

    @Override
    public void onStop() {
        super.onStop();

    }

    @Override
    protected void onResume() {
        super.onResume();
        loadingData();

        getPartDetails();
    }

    @Override
    protected void onRestart() {
        super.onRestart();
    }
}
