package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FragmentUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.BaseDialogQuestionContainerBinding;
import com.chervon.libBase.databinding.DialogBottomAlertBinding;
import com.chervon.libBase.model.DeviceRegisterUistate;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: BottomAlertDialog
 * @Description: 类描述
 * @Author: langmeng
 * @CreateDate: 2022/7/14 17:03
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/14 17:03
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class QuestionPageDialog extends DialogFragment {

  public static String mouduleId;
  public static String pageId;
  public static String pageResouce;
  public static String stayEleId;
  public static String doButtoneleid;
  public static String cancelButtoneleid;
  public  long enterTime;


  public   DeviceRegisterUistate mUistate;
  private static DialogListenner mDialogListenner;
  public List<Fragment>  fragmentList;

  //content text
  public SpannableString content;
  //image url
  public String imgUrl;
  //left btn text
  public String lText;
  //right btn text
  public String rText;
  //data observer
  private Consumer<Boolean> consumer;

  public QuestionPageDialog(DeviceRegisterUistate uistate,  List<Fragment>  list) {
    mUistate=uistate;
    fragmentList=list;

  }

  /**
   * @param fragmentManager 用于显示dialog,conten 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口
   * @return void
   * @method show
   * @description show dialog and get update info.
   * @date: 2022/6/22 11:36
   * @author: LangMeng
   */
  public static void show(@NonNull FragmentManager fragmentManager, @NonNull SpannableString content, String imgUrl, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback, DeviceRegisterUistate uistate,DialogListenner dialogListenner,List<Fragment>  fragmentList, QuestionPageDialog bottomAlertDialog ) {
    Fragment fragment = FragmentUtils.findFragment(fragmentManager, QuestionPageDialog.class.getName());
    mDialogListenner=dialogListenner;

    if (fragment == null) {

      bottomAlertDialog.setData(content, imgUrl, lText, rText, callback);
      // 实例化

      // 将aFragment 放入到 Activity 中
      bottomAlertDialog.show(fragmentManager, QuestionPageDialog.class.getName());


    }
  }

  @Override
  public void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
  }

  @SuppressLint("NotifyDataSetChanged")
  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

    //  getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
    BaseDialogQuestionContainerBinding binding = DataBindingUtil.inflate(getLayoutInflater(), R.layout.base_dialog_question_container, null, false);

    getChildFragmentManager().beginTransaction().add(R.id.content_fragment, fragmentList.get(0), "a").commitAllowingStateLoss();


//    binding.btnChangeFragment.setOnClickListener(v -> {
//      // 切换
//      BFragment bFragment = new BFragment();
//
//      // 防止实例
//      Fragment fragment = getChildFragmentManager().findFragmentByTag("a");
//      if (fragment != null) {
//        getChildFragmentManager().beginTransaction().hide(fragment).add(R.id.content_fragment, bFragment).addToBackStack(null).commitAllowingStateLoss();
//      } else {
//        getChildFragmentManager().beginTransaction().replace(R.id.content_fragment, bFragment).addToBackStack(null).commitAllowingStateLoss();
//      }
//    });

    return binding.getRoot();
  }


  public void gotoNextPage(String tag) {
//
//    if (tag.equalsIgnoreCase("two")) {
//      Fragment bFragment = getChildFragmentManager().findFragmentByTag(tag);
//      if (bFragment == null) {
//        bFragment = new BFragment(this);
//      }
//
//      Fragment fragment = getChildFragmentManager().findFragmentByTag("a");
//      if (fragment == null) {
//        getChildFragmentManager().beginTransaction().hide(fragment).add(R.id.content_fragment, bFragment).addToBackStack(null).commitAllowingStateLoss();
//      } else {
//        getChildFragmentManager().beginTransaction().replace(R.id.content_fragment, bFragment).addToBackStack(null).commitAllowingStateLoss();
//      }
//    }

    // 切换
    // 防止实例


  }

  @Override
  public void onStart() {
    super.onStart();
    //set dialog window
 /*   Dialog dialog = getDialog();
    assert dialog != null;
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
        attributes.height = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().setAttributes(attributes);
//      dialog.getWindow().setWindowAnimations(R.style.dialog_bottom);*/
     getDialog().getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
    getDialog().getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

    getDialog().setCancelable(false);
    enterTime=System.currentTimeMillis();
    sendExposure(this.getContext(),mouduleId,pageId,pageResouce);

  }


  /**
   * @param content 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口
   * @return void
   * @method setData
   * @description 设置弹窗数据
   * @date: 2022/6/22 11:35
   * @author: LangMeng
   */
  public void setData(@NonNull SpannableString content, @NonNull String imgUrl, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback) {
    this.content = content;
    this.imgUrl = imgUrl;
    this.lText = lText;
    this.rText = rText;
    this.consumer = callback;
  }

  /**
   * @return void
   * @method lCLick
   * @description 左按钮点击事件
   * @date: 2022/6/22 11:39
   * @author: LangMeng
   */
  public void lCLick() {
    if (consumer != null) {
      try {
        consumer.accept(false);
        sendNextBottonClick(cancelButtoneleid);
        dismiss();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  /**
   * @return void
   * @method rCLick
   * @description 右按钮点击事件
   * @date: 2022/6/22 11:39
   * @author: LangMeng
   */
  public void rCLick() {
    if (consumer != null) {
      try {
        consumer.accept(true);
        sendNextBottonClick(doButtoneleid);
        dismiss();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }
public void commitAnswerData(DeviceRegisterUistate uistate){
    if(mDialogListenner!=null){
      this.dismiss();
      mDialogListenner.onCommitData(uistate);

    }
}
  public interface DialogListenner{
    public void onCommitData(DeviceRegisterUistate uistate);
  }

  public DeviceRegisterUistate getmUistate() {
    return mUistate;
  }

  public void setmUistate(DeviceRegisterUistate mUistate) {
    this.mUistate = mUistate;
  }

  public List<Fragment> getFragmentList() {
    return fragmentList;
  }

  public void setFragmentList(List<Fragment> fragmentList) {
    this.fragmentList = fragmentList;
  }

  public static void initTrace(String stayEleIda,String pageIda,String mouduleIda,String pageResoucea,String cancelButtoneleida,String doButtoneleida) {
    stayEleId=stayEleIda;
    pageId=pageIda;
    mouduleId=mouduleIda;
    pageResouce=pageResoucea;
    cancelButtoneleid =cancelButtoneleida;
    doButtoneleid =doButtoneleida;
  }

  @Override
  public void dismiss() {
    super.dismiss();
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, "1","1");
    sendDurationTrace(this.getContext(),mouduleId,pageId,pageResouce,"",stayEleId,System.currentTimeMillis()-enterTime);

  }


  public void sendNextBottonClick(String eleid) {
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, eleid,"1");
  }
}

