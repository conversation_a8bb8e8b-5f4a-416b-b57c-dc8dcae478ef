package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRIVACY_ID;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.data.model.DeviceAgreeMentRespone;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityPrivacyBinding;
import com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter;
import com.chervon.moudleDeviceManage.ui.adapter.PrivacyAdapter;
import com.chervon.moudleDeviceManage.ui.state.DevicePricyUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.DevicePrivacyViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: LegalInfoActivity
 * @Description: legal info activity
 * @Author: LangMeng
 * @CreateDate: 2022/6/28 14:47
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/28 14:47
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_PRIVACY)
public class DevicePrivacyActivity extends BaseActivity<DevicePrivacyViewModel> implements ItemClick {
  private String mProductId;
  private MoudleDevicemanageActivityPrivacyBinding mViewDataBinding;
  private PrivacyAdapter mAdapter;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_devicemanage_activity_privacy;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (MoudleDevicemanageActivityPrivacyBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      int from = bundle.getInt(KEY_FROM);
      if (from == R.id.controlPanel) {
        mProductId = bundle.getString(KEY_PREV_DATA);
        mViewModel.getDeviceAgreeMentList(mProductId);
      }
    }

    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_privacy_title_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));

    mViewModel.mLiveData.observe(this, new Observer<DevicePricyUistate>() {
      @Override
      public void onChanged(DevicePricyUistate devicePricyUistate) {
        if (devicePricyUistate.getList() != null) {
          mAdapter.setDatas(devicePricyUistate.getList());
          mAdapter.notifyDataSetChanged();
        }

      }
    });


    LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
    mViewDataBinding.rvList.setLayoutManager(linearLayoutManager);
    //    mViewDataBinding.rvList.addItemDecoration(new DividerItemDecoration(this, LinearLayoutManager.VERTICAL));
    DeviceAgreeMentRespone[] pricys = new DeviceAgreeMentRespone[0];
    mAdapter = new PrivacyAdapter(this, this, pricys);
    mAdapter.setHasStableIds(true);
    mViewDataBinding.rvList.setAdapter(mAdapter);
    mViewDataBinding.setController(this);
    mViewDataBinding.setPresenter(mViewModel);
  }

  @Override
  protected Class<? extends DevicePrivacyViewModel> getViewModelClass() {
    return DevicePrivacyViewModel.class;
  }

  @Override
  public void onItemClick(Object uiState) {
    DeviceAgreeMentRespone deviceAgreeMentData = (DeviceAgreeMentRespone) uiState;
    sendDeviceUserAgreementButtonClick();
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN_USER_AGREEMENT).withInt(KEY_FROM, R.id.controlPanel)
      .withString(KEY_PREV_DATA, mProductId)
      .withString(PARAMETER_PRIVACY_ID, deviceAgreeMentData.getId())
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

  }

  public void withdrawAuthorize() {
    sendWithdrawalOfConsentButtonClick();
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_REVOKING_AUTHORIZATION).withInt(KEY_FROM, R.id.controlPanel)
      .withString(KEY_PREV_DATA, mProductId)
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
  }


  private void initTrace() {
    stayEleId = "5";
    pageId = "80";
    mouduleId = "9";
    nextButtoneleid = "2";
  }

  public void sendDevicePrivacyAgreementButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendDeviceUserAgreementButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
  }

  public void sendWithdrawalOfConsentButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 4 + "", "1");
  }



  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }
}
