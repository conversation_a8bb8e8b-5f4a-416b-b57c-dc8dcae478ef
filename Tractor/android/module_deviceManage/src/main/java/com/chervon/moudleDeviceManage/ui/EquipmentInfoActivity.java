package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageEquipmentInfoBinding;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceDetailsModel;
import com.chervon.moudleDeviceManage.ui.viewmodel.EquipmentInfoModel;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: EquipmentInfoActivity
 * @Description:
 * @Author: LiDeLi
 * @CreateDate: 2022/12/3 下午5:37
 * @UpdateUser: LiDeLi
 * @UpdateDate: 2022/12/3 下午5:37
 * @UpdateRemark: new class
 * @Version: 1.0
 */

@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_EQUIPMENT_INFO)
public class EquipmentInfoActivity extends BaseActivity<EquipmentInfoModel> {
    private MoudleDevicemanageEquipmentInfoBinding mBinding;
    private ToolbarData toolbarData;
    private DeviceInfo mDeviceInfo;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.moudle_devicemanage_equipment_info;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mBinding = (MoudleDevicemanageEquipmentInfoBinding) viewDataBinding;
        toolbarData = new ToolbarData(LanguageStrings.app_devicemoreinfo_codes_textview_text(), new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                goBack();
            }
        });
        mBinding.setToolbarData(toolbarData);

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            mDeviceInfo = (DeviceInfo) bundle.getSerializable(KEY_PREV_DATA);
            ProductInfo productInfo = mViewModel.getProductInfo(mDeviceInfo.getProductId());
            if (productInfo != null && !TextUtils.isEmpty(productInfo.getCommodityModel())) {
                mDeviceInfo.setCommodityModel(productInfo.getCommodityModel());
            }
        } else {
            mDeviceInfo = new DeviceInfo();
        }
        mBinding.setDeviceInfo(mDeviceInfo);
        mBinding.setViewModel(mViewModel);
    }

    @SuppressLint("RestrictedApi")
    private void goBack() {
        sendBackTrace();

        finish();
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
    }

    @Override
    protected Class<? extends EquipmentInfoModel> getViewModelClass() {
        return EquipmentInfoModel.class;
    }

    private void initTrace() {
        stayEleId = "8";
        pageId = "85";
        mouduleId = "10";
        nextButtoneleid = "2";
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        sendBackTrace();
    }


}
