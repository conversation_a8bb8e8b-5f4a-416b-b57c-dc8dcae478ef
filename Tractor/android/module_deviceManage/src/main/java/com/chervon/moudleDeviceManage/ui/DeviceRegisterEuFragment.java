package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.model.AppConstants.PERMISSION_ALBUM_UNAUTHORIZED;
import static com.chervon.libBase.model.AppConstants.PERMISSION_CAMERA_UNAUTHORIZED;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.CameraUtil.CODE_GALLERY;
import static com.chervon.libBase.utils.CameraUtil.CODE_TAKE_PHOTO;
import static com.chervon.libBase.utils.CameraUtil.TYPE_TAKE_PHOTO;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_INPUT_MARK;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_REGISTER_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RECEIP_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.VALIDATE_SN_SUCCESS;
import static com.chervon.moudleDeviceManage.ui.DeviceRegisterInfoFragment.DEVICE_REGISTER_UI_DATA;
import static com.chervon.moudleDeviceManage.ui.HomeActivity.PICK_PDF_AND_WORD_REQUEST;

import android.Manifest;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.AppConstants;
import com.chervon.libBase.model.DeviceRegistStatusModel;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.libBase.model.DictItem;
import com.chervon.libBase.model.DictNode;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.ui.widget.CustomDatePicker;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceRegisterEuBinding;
import com.chervon.moudleDeviceManage.ui.adapter.ReceiptAdapter;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceRegisterEuViewModel;
import com.chervon.moudleDeviceManage.utils.DeviceManagerConstants;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.SandboxTransformUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.reactivex.functions.Consumer;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: DeviceRegisterEuFragment
 * @Description: 欧洲设备注册
 * @Author: wuxd
 * @CreateDate: 2024/6/18
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @UpdateRemark
 * @Version: 1.0
 */
public class DeviceRegisterEuFragment extends BaseEnhanceFragment<DeviceRegisterEuViewModel, MoudleDevicemanageFragmentDeviceRegisterEuBinding> implements ItemClick, View.OnFocusChangeListener, BaseEnhanceFragment.OnkeyBackListener{

  public static final String FILE_UPLOAD_BUTTON_CLICK = "2";
  private static final String COMPLETE_BUTTON_CLICK = "3";
  private static final String KIT_BUTTON_CLICK = "5";
  private static final String KIT_KEY = "switch";
  private static final String KIT_YES= "1";
  private static final String KIT_NO = "0";
  private static final String EVENT_ID_1 = "1";
  private static final String RECEIPT_INFORMATION_KEY ="receipt_information";
  private static final String PLACE_OF_PURCHASE_KEY = "place_of_purchase";
  private static final String PURCHASE_DATE_KEY = "purchase_date";
  private static final String PURPOSE_KEY = "purpose";

  private List<String> url = new ArrayList<>();
  private ReceiptAdapter mReceiptAdapter;
  private DictNode[] mReceiptItem = new DictNode[]{};
  private DialogUtil mRcipDialog;
  int prevFragment = 0;
  protected Dialog mProgressDialog;
  /**
   * 用户选择了 i have my receipt
   */
  public static final int HAVE_RECEIPT = 1;
  /**
   * 用户选择了  i lost my receipt
   */
  public  static final int LOST_RECEIPT = 2;
  /**
   * 用户选择了 product was gift
   */
  public static final int PRODUCT_WAS_A_GIFT = 3;
  /**
   * 防止跳转时失焦重复请求
   */
  private boolean toJump;
  private static final int PRIV_FRAGMENT_1234 = 1234;
  private static final int PRIV_FRAGMENT_12345 = 12345;
  private static final String APP_DEVICE_REGISTER_RECEIPT_INFORMATION = "appDeviceRegisterReceiptInformation";
  private static final String SPACE = " ";
  public static final int INIT_STATE = 0;
  private static final int MARK_SN = 0;
  private static final int MARK_KIT_ONE = 1;
  private static final int MARK_KIT_TWO = 2;
  private static final int MARK_KIT_THREE = 3;
  private static final int INDEX_KIT_ONE = 0;
  private static final int INDEX_KIT_TWO = 1;
  private static final int INDEX_KIT_THREE = 2;

  protected void initDatasInView(Bundle savedInstanceState) {

    // 添加空值检查
    if (mViewDataBinding == null || !isAdded()) {
      return;
    }

    HomeActivity homeActivity = (HomeActivity) getActivity();
    if (null == mViewModel.mSnDeviceLiveData.getValue()) {
      mViewModel.mSnDeviceLiveData.setValue(new DeviceRegisterUistate());
      mViewModel.mSnDeviceLiveData.getValue().setModelNo("");
      mViewModel.mSnDeviceLiveData.getValue().setName("");
      mViewModel.mSnDeviceLiveData.getValue().setSn("");
    }


    if (homeActivity != null) {
      DeviceInfo deviceInfo = null;
      Bundle bundle = getArguments();
      if (bundle == null) {
        bundle = homeActivity.getIntent().getExtras();
      }
      if (bundle != null) {
        prevFragment = (int) bundle.getInt(KEY_PREV_FRAGMENT);

        if (prevFragment == R.id.scanNearbyDevicesFragment || prevFragment == PRIV_FRAGMENT_1234 || prevFragment == PRIV_FRAGMENT_12345) {
          //1234页面是非IOT设备立即注册或者非iot面板跳转
          // 12345是WIFI设备立即注册跳转
          mViewModel.mSnDeviceLiveData.getValue().setFrom(prevFragment);
          Object object = bundle.get(KEY_PREV_DATA);
          if (object != null) {
            deviceInfo = (DeviceInfo) object;
          }
        } else if (prevFragment == R.id.controlPanel) {
          mViewModel.mSnDeviceLiveData.getValue().setFrom(prevFragment);
          String deviceId = bundle.getString(KEY_PREV_DATA);
          if (deviceId != null) {
            mViewModel.getDeviceInfo(deviceId);
          }

        } else if (prevFragment == R.id.scanCodeFragment) {
          //来自扫描页面 或 扫描的二级页面
          toJump = false;
          Object object = bundle.get(PARAMETER_DEVICD);
          mViewModel.mSnDeviceLiveData.setValue((DeviceRegisterUistate) bundle.get(KEY_PREV_DATA));

          if (object != null) {
            deviceInfo = (DeviceInfo) object;
            mViewModel.mSnDeviceLiveData.getValue().setShowQuestionPage(false);
            mViewModel.getProductInfo(deviceInfo.getProductId());
          }
          switch (bundle.getInt(KEY_INPUT_MARK)) {
            case MARK_SN:
              String sn = mViewModel.mSnDeviceLiveData.getValue().getSnValidateStatus().getSn();
              if(!sn.isEmpty()) {
                mViewModel.validateSn(sn,MARK_SN);
              }
              break;
            default:
              String serialNumber = mViewModel.mSnDeviceLiveData.getValue().getKitSN(bundle.getInt(KEY_INPUT_MARK)-1);
              if(!serialNumber.isEmpty()) {
                mViewModel.validateSn(serialNumber,bundle.getInt(KEY_INPUT_MARK));
              }
              break;
          }
        } else if (prevFragment == R.id.deviceRegisterInfoFragment) {
          //来自注册结果页面
          Object object = bundle.get(PARAMETER_DEVICD);
          if (object != null) {
            deviceInfo = (DeviceInfo) object;
            mViewDataBinding.ietEnterIncludedCodeOne.setText(deviceInfo.getSn());
            mViewDataBinding.clTopContainer.setVisibility(View.VISIBLE);
            mViewModel.getProductInfo(deviceInfo.getProductId());
          } else {
            mViewDataBinding.clTopContainer.setVisibility(View.GONE);

          }
          mViewModel.mSnDeviceLiveData.setValue((DeviceRegisterUistate) bundle.getSerializable(DEVICE_REGISTER_UI_DATA));
          url = mViewModel.mSnDeviceLiveData.getValue().getUrl();
          if (mViewModel.mSnDeviceLiveData.getValue().getReceiptType() == 1) {
            mViewDataBinding.tvUpLoadReceip.setVisibility(View.VISIBLE);
            mViewDataBinding.rvReceip.setVisibility(View.VISIBLE);
          }
          mViewModel.mSnDeviceLiveData.getValue().setRegistMore(false);
        }
      }

      if (deviceInfo != null) {
        if (TextUtils.isEmpty(deviceInfo.getSn())) {
          String sn = TextUtils.isEmpty(deviceInfo.getSn()) ? "--" : deviceInfo.getSn();
          mViewModel.mSnDeviceLiveData.getValue().setShowSN(LanguageStrings.app_deviceregisteu_devicesn_textview_text() +SPACE + sn);
          mViewModel.mSnDeviceLiveData.getValue().setShowEnterSN(true);
        } else {
          mViewModel.mSnDeviceLiveData.getValue().setSn(deviceInfo.getSn());
          mViewModel.mSnDeviceLiveData.getValue().setShowSN(LanguageStrings.app_deviceregisteu_devicesn_textview_text() +SPACE + deviceInfo.getSn());
          mViewModel.mSnDeviceLiveData.getValue().setShowEnterSN(false);
        }
        mViewModel.mSnDeviceLiveData.getValue().setDeviceInfo(deviceInfo);
        mViewModel.mSnDeviceLiveData.getValue().setName(deviceInfo.getDeviceName());
        mViewModel.mSnDeviceLiveData.getValue().setDeviceid(deviceInfo.getDeviceId());
        mViewModel.mSnDeviceLiveData.getValue().setModelNo(deviceInfo.getCommodityModel());
        mViewModel.mSnDeviceLiveData.getValue().setShowModelNo(LanguageStrings.app_deviceregisteu_modelnumber_textview_text() +SPACE + deviceInfo.getCommodityModel());
        mViewModel.getProductInfo(deviceInfo.getProductId());
        mViewModel.getDeviceInfo(deviceInfo.getDeviceId());
        if (TextUtils.isEmpty(deviceInfo.getDeviceIcon()) && TextUtils.isEmpty(deviceInfo.getIconUrl())) {
          mViewModel.mSnDeviceLiveData.getValue().setModelNo("");
          mViewModel.mSnDeviceLiveData.getValue().setName("");
        } else {
          if (!TextUtils.isEmpty(deviceInfo.getDeviceIcon())) {
            Glide.with(this).load(deviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
          } else {
            Glide.with(this).load(deviceInfo.getIconUrl()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
          }
        }
      }
    }
    mViewDataBinding.setUiState(mViewModel.mSnDeviceLiveData.getValue());
    mViewDataBinding.setPresenter(mViewModel);
    mViewDataBinding.setController(this);
    initObserver();

    datePicker();
    disableBtnClick(mViewModel.checkItemSelect());
    initPhotoSelectAdapter();

  }

  private void initObserver() {
    mViewModel.mSnDeviceLiveData.observe(this, uistate -> {
      if (uistate.getState() == RESPONSE_SUCCESS) {
        if (uistate.getDeviceInfo() != null) {
          Glide.with(DeviceRegisterEuFragment.this).load(uistate.getDeviceInfo().getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
          if (mViewModel.mSnDeviceLiveData.getValue() != null) {
            mViewModel.mSnDeviceLiveData.getValue().setDeviceInfo(uistate.getDeviceInfo());
          }
          if (!TextUtils.isEmpty(uistate.getDeviceInfo().getProductId())) {
            mViewModel.getProductInfo(uistate.getDeviceInfo().getProductId());
          }
        }
      } else if (uistate.getState() == VALIDATE_SN_SUCCESS) {
        // 添加空值检查
        if (mViewDataBinding == null || !isAdded()) {
          return;
        }

        mViewDataBinding.ietEnterSerialNumberCode.showInlineTip(uistate.getSnValidateStatus().getMessage());
        mViewDataBinding.ietEnterIncludedCodeOne.showInlineTip(uistate.getKitSNMessage(INDEX_KIT_ONE));
        mViewDataBinding.ietEnterIncludedCodeTwo.showInlineTip(uistate.getKitSNMessage(INDEX_KIT_TWO));
        mViewDataBinding.ietEnterIncludedCodeThree.showInlineTip(uistate.getKitSNMessage(INDEX_KIT_THREE));
        disableBtnClick(mViewModel.checkItemSelect());
      } else if (uistate.getState() != INIT_STATE) {
        if (!TextUtils.isEmpty(uistate.getMessage())) {
          ToastUtils.showShort(uistate.getMessage());
        }
      }
      uistate.setState(INIT_STATE);
    });
    mViewModel.mLiveData.observe(this, deviceRegisterUiState -> {
      if (deviceRegisterUiState.getState() == RECEIP_SUCCESS) {
        //发票上传成功后，执行设备注册
        if (deviceRegisterUiState.getRecipKey() != null && deviceRegisterUiState.getRecipKey().size() == url.size()) {
          DeviceRegisterUistate registerUistate = mViewDataBinding.getUiState();
          registerUistate.setDeviceid(mViewModel.mSnDeviceLiveData.getValue() != null ? mViewModel.mSnDeviceLiveData.getValue().getDeviceid():"");
          registerUistate.setRecipKey(deviceRegisterUiState.getRecipKey());
          mViewModel.registerDevice(registerUistate);
        }
      } else if (deviceRegisterUiState.getState() == DEVICE_REGISTER_SUCCESS) {
        if (null != mProgressDialog) {
          mProgressDialog.dismiss();
        }
        NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
        uiState.setUrl(url);
        bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uiState);
        bundle.putSerializable(PARAMETER_DEVICD, uiState.getDeviceInfo());
        controller.navigate(R.id.deviceRegisterInfoFragment, bundle);
        if (!TextUtils.isEmpty(uiState.getDeviceInfo().getDeviceId())) {
          //同步注册结果给RN
          BaseApplication.registLiveData.postValue(new DeviceRegistStatusModel(uiState.getDeviceInfo().getDeviceId(),
            DeviceRegistStatusModel.REGIST_RESULT_SUCCESS));
        }
      } else if (deviceRegisterUiState.getState() == RESPONSE_FAIL) {
        if (null != mProgressDialog) {
          mProgressDialog.dismiss();
        }
        if (!TextUtils.isEmpty(deviceRegisterUiState.getDeviceInfo().getDeviceId())) {
          //同步注册结果给RN
          BaseApplication.registLiveData.postValue(new DeviceRegistStatusModel(deviceRegisterUiState.getDeviceInfo().getDeviceId(),
            DeviceRegistStatusModel.REGIST_RESULT_FAIL));
        }
        if (TextUtils.isEmpty(deviceRegisterUiState.getMessage())) {
          ToastUtils.showShort(deviceRegisterUiState.getMessage());
        }
        gotoErrPage();
      } else if (deviceRegisterUiState.getState() != INIT_STATE) {
        if (null != mProgressDialog) {
          mProgressDialog.dismiss();
        }
        if (deviceRegisterUiState.isRegistMore()) {
          mViewDataBinding.clTopContainer.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(deviceRegisterUiState.getMessage())) {
          ToastUtils.showLong(deviceRegisterUiState.getMessage());
        }
        gotoErrPage();

      }
      ProgressHelper.hideProgressView(DeviceRegisterEuFragment.this.getActivity());
      deviceRegisterUiState.setState(INIT_STATE);
    });
    mViewModel.mDeviceInfoLiveData.observe(this, deviceInfo -> {
      // 添加空值检查
      if (mViewDataBinding == null || !isAdded()) {
        return;
      }

      if (deviceInfo == null) {
        return;
      }
      DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
      Glide.with(DeviceRegisterEuFragment.this).load(deviceInfo.getDeviceIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivDeviceIcon);
      String sn = TextUtils.isEmpty(deviceInfo.getSn()) ? "--" : deviceInfo.getSn();
      uiState.setShowSN(LanguageStrings.app_deviceregisteu_devicesn_textview_text() + SPACE + sn);
      uiState.setShowModelNo(LanguageStrings.app_deviceregisteu_modelnumber_textview_text() + SPACE + deviceInfo.getCommodityModel());
      uiState.setShowEnterSN(TextUtils.isEmpty(deviceInfo.getSn()));
      if(!TextUtils.isEmpty(deviceInfo.getSn())){
        uiState.setSn(deviceInfo.getSn());
      }
      mViewDataBinding.setUiState(uiState);
      mViewModel.getProductInfo(deviceInfo.getProductId());

    });

    mViewModel.mDictLiveData.observe(this, deviceDictEntry -> {
      if (deviceDictEntry != null) {
        DictItem[] dictItems = deviceDictEntry.getEntry();
        for (int i = 0; dictItems != null && i < dictItems.length; i++) {
          if (APP_DEVICE_REGISTER_RECEIPT_INFORMATION.equalsIgnoreCase(dictItems[i].getDictName())) {
            mReceiptItem = dictItems[i].getNodes();
          }
        }
      }
    });
  }

  private void initPhotoSelectAdapter() {

    // 添加空值检查
    if (mViewDataBinding == null || !isAdded()) {
      return;
    }

    GridLayoutManager gridLayoutManager = new GridLayoutManager(this.getActivity(), 3, RecyclerView.VERTICAL, false);
    mViewDataBinding.rvReceip.setLayoutManager(gridLayoutManager);

    mReceiptAdapter = new ReceiptAdapter(this.getActivity(), this, url);
    mReceiptAdapter.setHasStableIds(true);
    mViewDataBinding.rvReceip.setAdapter(mReceiptAdapter);

    //已选过的图片进行填充
    if (null != mViewModel.mSnDeviceLiveData.getValue()) {
      if (null != mViewModel.mSnDeviceLiveData.getValue().getUrl()) {
        url = mViewModel.mSnDeviceLiveData.getValue().getUrl();
        if (url.size() > 0) {
          mViewDataBinding.tvUpLoadReceip.setVisibility(View.VISIBLE);
          mViewDataBinding.rvReceip.setVisibility(View.VISIBLE);
          mReceiptAdapter.setDatas(url);
        }
      }
    }

  }

  private void gotoErrPage() {
    NavController controller = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
    uiState.setUrl(url);
    bundle.putSerializable(DEVICE_REGISTER_UI_DATA, uiState);
    bundle.putSerializable(PARAMETER_DEVICD, uiState.getDeviceInfo());
    controller.navigate(R.id.deviceRegisterErrFragment, bundle);
  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_devicemanage_fragment_device_register_eu;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (MoudleDevicemanageFragmentDeviceRegisterEuBinding) viewDataBinding;
    mViewDataBinding.etDate.setOnFocusChangeListener(this);
    mViewDataBinding.clContainer.setOnFocusChangeListener((v, hasFocus) -> {
      if(hasFocus && null != getActivity()) {
        KeyboardUtils.hideSoftInput(getActivity());
      }
    });
    mViewDataBinding.cbIncludedY.setOnCheckedChangeListener((buttonView, isChecked) -> {
      if(!isChecked) {
        disableBtnClick(mViewModel.checkItemSelect());
      }
    });
    mViewDataBinding.cbIncludedN.setOnCheckedChangeListener((buttonView, isChecked) -> {
      if(!isChecked) {
        disableBtnClick(mViewModel.checkItemSelect());
      }
    });
    mViewDataBinding.cbIncludedY.setOnClickListener(v -> {
      if(mViewModel.mSnDeviceLiveData.getValue() != null) {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, KIT_BUTTON_CLICK, EVENT_ID_1,KIT_KEY,KIT_YES);
        mViewModel.mSnDeviceLiveData.getValue().setHasKit(true);
      }
    });
    mViewDataBinding.cbIncludedN.setOnClickListener(v -> {
      if(mViewModel.mSnDeviceLiveData.getValue() != null) {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, KIT_BUTTON_CLICK, EVENT_ID_1,KIT_KEY,KIT_NO);
        mViewModel.mSnDeviceLiveData.getValue().setHasKit(false);
      }
    });
    mViewDataBinding.ietEnterSerialNumberCode.setIconOnClickListener(v -> {
      toJump = true;
      scanQrcode(mViewDataBinding.getUiState(),MARK_SN);
    });
    mViewDataBinding.ietEnterSerialNumberCode.setTextChangedListener(str -> {
      if(!str.equals(mViewDataBinding.getUiState().getSnValidateStatus().getSn())) {
        mViewDataBinding.getUiState().getSnValidateStatus().setCanRegister(false);
        mViewDataBinding.getUiState().getSnValidateStatus().setMessage("");
      }
      mViewDataBinding.getUiState().setSn(str);
      mViewDataBinding.getUiState().getSnValidateStatus().setSn(str);
      disableBtnClick(mViewModel.checkItemSelect());
    });
    mViewDataBinding.ietEnterSerialNumberCode.setEditTextOnFocusChangeListener((v, hasFocus) -> {
        if(!hasFocus && !toJump) {
          if(!mViewDataBinding.ietEnterSerialNumberCode.getInlineTipEditTextString().isEmpty()) {
            mViewModel.validateSn(mViewDataBinding.ietEnterSerialNumberCode.getInlineTipEditTextString(),MARK_SN);
          } else {
            mViewDataBinding.ietEnterSerialNumberCode.showInlineTip("");
          }
        }
    });
    mViewDataBinding.ietEnterSerialNumberCode.setOnEditorActionListener((v, actionId, event) -> {
      if(actionId == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietEnterSerialNumberCode.clearFocus();
      }
      return true;
    });
    mViewDataBinding.ietEnterIncludedCodeOne.setIconOnClickListener(v -> {
      toJump = true;
      scanQrcode(mViewDataBinding.getUiState(),MARK_KIT_ONE);
    });
    mViewDataBinding.ietEnterIncludedCodeOne.setTextChangedListener(str -> {
      if(!str.isEmpty()&&LanguageStrings.app_deviceregisteu_sninputerror_textview_text().equals(mViewDataBinding.getUiState().getKitSN()[0].getMessage())) {
        mViewDataBinding.getUiState().setKitMessage("",INDEX_KIT_ONE);
        mViewDataBinding.ietEnterIncludedCodeOne.showInlineTip("");
      }
      if(!str.equals(mViewDataBinding.getUiState().getKitSN(INDEX_KIT_ONE))) {
        mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_ONE].setCanRegister(false);
        mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_ONE].setMessage("");
      }
      mViewDataBinding.getUiState().setKitSN(str,INDEX_KIT_ONE);
      disableBtnClick(mViewModel.checkItemSelect());
    });
    mViewDataBinding.ietEnterIncludedCodeOne.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if(!hasFocus && !toJump) {
        if(!mViewDataBinding.ietEnterIncludedCodeOne.getInlineTipEditTextString().isEmpty()) {
          mViewModel.validateSn(mViewDataBinding.ietEnterIncludedCodeOne.getInlineTipEditTextString(),MARK_KIT_ONE);
        } else {
          mViewDataBinding.ietEnterIncludedCodeOne.showInlineTip("");
          mViewDataBinding.getUiState().setKitMessage("",INDEX_KIT_ONE);
          checkKit(mViewDataBinding.getUiState());
        }
      }
    });
    mViewDataBinding.ietEnterIncludedCodeOne.setOnEditorActionListener((v, actionId, event) -> {
      if(actionId == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietEnterIncludedCodeOne.clearFocus();
      }
      return true;
    });
    mViewDataBinding.ietEnterIncludedCodeTwo.setIconOnClickListener(v -> {
      toJump = true;
      scanQrcode(mViewDataBinding.getUiState(),MARK_KIT_TWO);
    });
    mViewDataBinding.ietEnterIncludedCodeTwo.setTextChangedListener(str -> {
      if(!str.isEmpty()&&LanguageStrings.app_deviceregisteu_sninputerror_textview_text().equals(mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_ONE].getMessage())) {
        mViewDataBinding.getUiState().setKitMessage("",INDEX_KIT_ONE);
        mViewDataBinding.ietEnterIncludedCodeOne.showInlineTip("");
      }
      if(!str.equals(mViewDataBinding.getUiState().getKitSN(INDEX_KIT_TWO))) {
        mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_TWO].setCanRegister(false);
        mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_TWO].setMessage("");
      }
      mViewDataBinding.getUiState().setKitSN(str,INDEX_KIT_TWO);
      disableBtnClick(mViewModel.checkItemSelect());
    });
    mViewDataBinding.ietEnterIncludedCodeTwo.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if(!hasFocus && !toJump) {
        if(!mViewDataBinding.ietEnterIncludedCodeTwo.getInlineTipEditTextString().isEmpty()) {
          mViewModel.validateSn(mViewDataBinding.ietEnterIncludedCodeTwo.getInlineTipEditTextString(),MARK_KIT_TWO);
        } else {
          mViewDataBinding.ietEnterIncludedCodeTwo.showInlineTip("");
          mViewDataBinding.getUiState().setKitMessage("",INDEX_KIT_TWO);
          checkKit(mViewDataBinding.getUiState());
        }
      }
    });
    mViewDataBinding.ietEnterIncludedCodeTwo.setOnEditorActionListener((v, actionId, event) -> {
      if(actionId == EditorInfo.IME_ACTION_DONE) {
        mViewDataBinding.ietEnterIncludedCodeTwo.clearFocus();
      }
      return true;
    });
    mViewDataBinding.ietEnterIncludedCodeThree.setIconOnClickListener(v -> {
      toJump = true;
      scanQrcode(mViewDataBinding.getUiState(),MARK_KIT_THREE);
    });
    mViewDataBinding.ietEnterIncludedCodeThree.setTextChangedListener(str -> {
      if(!str.isEmpty()&&LanguageStrings.app_deviceregisteu_sninputerror_textview_text().equals(mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_ONE].getMessage())) {
        mViewDataBinding.getUiState().setKitMessage("",INDEX_KIT_ONE);
        mViewDataBinding.ietEnterIncludedCodeOne.showInlineTip("");
      }
      if(!str.equals(mViewDataBinding.getUiState().getKitSN(INDEX_KIT_THREE))) {
        mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_THREE].setCanRegister(false);
        mViewDataBinding.getUiState().getKitSN()[INDEX_KIT_THREE].setMessage("");
      }
      mViewDataBinding.getUiState().setKitSN(str,INDEX_KIT_THREE);
      disableBtnClick(mViewModel.checkItemSelect());
    });
    mViewDataBinding.ietEnterIncludedCodeThree.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if(!hasFocus && !toJump) {
        if(!mViewDataBinding.ietEnterIncludedCodeThree.getInlineTipEditTextString().isEmpty()) {
          mViewModel.validateSn(mViewDataBinding.ietEnterIncludedCodeThree.getInlineTipEditTextString(),MARK_KIT_THREE);
        } else {
          mViewDataBinding.ietEnterIncludedCodeThree.showInlineTip("");
          mViewDataBinding.getUiState().setKitMessage("",INDEX_KIT_THREE);
          checkKit(mViewDataBinding.getUiState());
        }
      }
    });
    mViewDataBinding.ietEnterIncludedCodeThree.setOnEditorActionListener((v, actionId, event) -> {
      if(actionId == EditorInfo.IME_ACTION_DONE) {

        mViewDataBinding.ietEnterIncludedCodeThree.clearFocus();
      }
      return true;
    });
    initDatasInView(null);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends DeviceRegisterEuViewModel> getViewModelClass() {
    return DeviceRegisterEuViewModel.class;
  }

  public void clearFocus() {
    // 添加空值检查
    if (mViewDataBinding == null || !isAdded()) {
      return;
    }

    mViewDataBinding.ietEnterSerialNumberCode.clearFocus();
    mViewDataBinding.ietEnterIncludedCodeOne.clearFocus();
    mViewDataBinding.ietEnterIncludedCodeTwo.clearFocus();
    mViewDataBinding.ietEnterIncludedCodeThree.clearFocus();
  }

  /**
   * Recepit
   *
   * @param registerUistate
   */
  private int selectPosition = -1;
  public void choiceRcipInfo(DeviceRegisterUistate registerUistate) {
    clearFocus();
    if (mRcipDialog == null) {
      mRcipDialog = new DialogUtil();
    }
    mRcipDialog.showBottomListDialog2WithSelectPosition(this.getContext(),
      LanguageStrings.app_deviceregistactionsheet_receipt_textview_text(),
      (view, itemName) -> {
        if (mViewDataBinding == null || !isAdded()) {
          return;
        }
        int position = (int) view.getTag();
        selectPosition = position;
        if (position != 0) {
          mViewDataBinding.tvUpLoadReceip.setVisibility(View.GONE);
          mViewDataBinding.rvReceip.setVisibility(View.GONE);
          url.clear();
          mReceiptAdapter.setDatas(url);
          mReceiptAdapter.notifyDataSetChanged();
        } else {
          mViewDataBinding.tvUpLoadReceip.setVisibility(View.VISIBLE);
          mViewDataBinding.rvReceip.setVisibility(View.VISIBLE);
        }
        registerUistate.setReceiptType(position + 1);
        registerUistate.setReceipt(itemName);
        mViewDataBinding.setUiState(registerUistate);
        disableBtnClick(mViewModel.checkItemSelect());
      }, mReceiptItem,selectPosition);

  }

  public void choiceDate(DeviceRegisterUistate registerUistate) {
    clearFocus();
    String selectTime = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA).format(selectedTimeStamp);

    customDatePicker.show(0 != selectedTimeStamp ? selectTime : now);
  }

  private CustomDatePicker customDatePicker;
  private String now;
  private String endTime;
  private long selectedTimeStamp = 0;

  private void datePicker() {

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
    //获取当前时间
    Calendar cal = Calendar.getInstance();
    cal.setTime(new Date());
    endTime = sdf.format(cal.getTime());
    now = sdf.format(new Date());
    customDatePicker = new CustomDatePicker(this.getContext(),
      (time, timeStamp) -> {
        mViewDataBinding.etDate.setText(time);
        selectedTimeStamp = timeStamp;
        disableBtnClick(mViewModel.checkItemSelect());
      }, "2000-01-01 00:00", endTime, now);
    customDatePicker.showSpecificTime(false);
    customDatePicker.setIsLoop(false);
  }

  public void registerDevice(DeviceRegisterUistate registerUiState) {
    // 添加空值检查
    if (mViewDataBinding == null || !isAdded()) {
      return;
    }

    List<String> recipKey = mViewDataBinding.getUiState().getRecipKey();
    if (recipKey != null) {
      mViewDataBinding.getUiState().getRecipKey().clear();
    }
    if (null == mProgressDialog) {
      mProgressDialog = DialogUtil.showRnLoadingDialog(getActivity());
    }
    if (url.size() != 0 && registerUiState.getReceiptType() == HAVE_RECEIPT) {
      //如果用户选择图片，先上传图片
      if (url.get(0) != null) {
        File receipFile = new File(url.get(0));
        mViewModel.upLoadReceipImage(registerUiState.getDeviceInfo().getDeviceId(), receipFile);
      }
    } else {
      //如果用户未选择图片那么就执行注册
      registerUiState.setDeviceid(registerUiState.getDeviceInfo().getDeviceId());
      mViewModel.registerDevice(registerUiState);
    }
  }

  public void clickRegisterDevice(DeviceRegisterUistate registerUistate) {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, COMPLETE_BUTTON_CLICK, EVENT_ID_1, RECEIPT_INFORMATION_KEY, registerUistate.getReceipt()
      , PLACE_OF_PURCHASE_KEY, ""
      , PURCHASE_DATE_KEY, registerUistate.getDate()
      , PURPOSE_KEY, "");
    registerDevice(registerUistate);
  }

  public void checkKit(DeviceRegisterUistate registerUiState) {
    if(registerUiState.isHasKit() && registerUiState.getKitSN(INDEX_KIT_ONE).isEmpty() &&
      registerUiState.getKitSN(INDEX_KIT_TWO).isEmpty() && registerUiState.getKitSN(INDEX_KIT_THREE).isEmpty()) {
      registerUiState.setKitMessage(LanguageStrings.app_deviceregisteu_sninputerror_textview_text(),INDEX_KIT_ONE);
      mViewDataBinding.ietEnterIncludedCodeOne.showInlineTip(LanguageStrings.app_deviceregisteu_sninputerror_textview_text());
    }
  }


  public void uploadReceipt() {
    ((HomeActivity) getActivity()).setResultListenner(new ResultListenner() {

      @Override
      public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data, String imgUrl) {
        if (requestCode == PICK_PDF_AND_WORD_REQUEST) {

          //File文件 过滤PDF和Word文件
          if (TextUtils.isEmpty(imgUrl)) {
            LogUtils.e("imgUrl is null");
          }
          if (imgUrl.contains(DeviceManagerConstants.MEDIA_PDF) ||
                  imgUrl.contains(DeviceManagerConstants.MEDIA_WORD_DOC) ||
                  imgUrl.contains(DeviceManagerConstants.MEDIA_WORD_DOCX) || imgUrl.contains(DeviceManagerConstants.MEDIA_WORD_PAGES)) {
            url.add(imgUrl);
            mReceiptAdapter.setDatas(url);
            mReceiptAdapter.notifyDataSetChanged();
          } else {
            ToastUtils.showShort(LanguageStrings.app_deviceregist_illegalfile_textview_text());
          }
        } else {
          //拍照和相册
          if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            if (tempFile != null && imgUrl == null) {
              url.clear();
              url.add(0, tempFile.getAbsolutePath());
              mReceiptAdapter.setDatas(url);
              mReceiptAdapter.notifyDataSetChanged();
            } else {
              url.clear();
              url.add(0, imgUrl);
              mReceiptAdapter.setDatas(url);
              mReceiptAdapter.notifyDataSetChanged();
            }
          }
        }


      }
    });

    DialogUtil.showDeviceRegisterBottomCardDialog(this.getContext(), new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (view.getId() == R.id.tvOpenCamera) {
          goToTakePhotoPage();
        } else if (view.getId() == R.id.tvOpenAlbum) {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {

            String[] permissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES};
            if (null == getActivity()) {
              return;
            }
            RxPermissions rxPermissions = new RxPermissions(getActivity());
            rxPermissions.requestEach(permissions).subscribe(new Consumer<Permission>() {
              @Override
              public void accept(Permission permission) throws Exception {
                if (permission.granted) {
                  getToOpenGalleyWith13();
                } else {
                  showPermissionError(permission.name);
                }
              }
            });
          } else {
            getToOpenGalley();
          }
        } else if (view.getId() == R.id.tvOpenFiler) {
          openFile();
        }
      }
    });
  }

  /**
   * 打开文件
   */
  private void openFile() {
    Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
    intent.setType("*/*");
    String[] mimeTypes = {AppConstants.MEDIA_TYPE_PDF,
            AppConstants.MEDIA_TYPE_DOC,
            AppConstants.MEDIA_TYPE_DOCX};
    intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);
    try {
      // 过滤出PDF和Word文件
      getActivity().startActivityForResult(intent, PICK_PDF_AND_WORD_REQUEST);
    } catch (ActivityNotFoundException e) {
      e.printStackTrace();
      LogUtils.e("无法打开文件选择器");
    }

  }

  private void getToOpenGalleyWith13() {
    if (requireActivity().isDestroyed() || requireActivity().isFinishing()) {
      return;
    }
    PictureSelector.create(this)
      .openSystemGallery(SelectMimeType.ofImage())
      .setSelectionMode(SelectModeConfig.SINGLE)
      .setSkipCropMimeType()
      .setSandboxFileEngine((context, srcPath, mineType, call) -> {
        //Android 10 沙盒资源访问
        if (call != null) {
          String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
          call.onCallback(srcPath, sandboxPath);
        }
      })
      .setSandboxFileEngine((context, srcPath, mineType, call) -> {
        if (call != null) {
          String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
          call.onCallback(srcPath, sandboxPath);
        }
      }).forSystemResultActivity(new OnResultCallbackListener<LocalMedia>() {
      @Override
      public void onResult(ArrayList<LocalMedia> result) {
        String imgPath = result.get(0).getSandboxPath();
        url.clear();
        url.add(0, imgPath);
//        url.add(0, imgPath);
        disableBtnClick(mViewModel.checkItemSelect());
        mReceiptAdapter.setDatas(url);
        mReceiptAdapter.notifyDataSetChanged();
      }

      @Override
      public void onCancel() {}
    });
  }


  private void getToOpenGalley() {
    final String[] permissionsGroup = new String[]{
      Manifest.permission.WRITE_EXTERNAL_STORAGE,
      Manifest.permission.READ_EXTERNAL_STORAGE
    };
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      RxPermissions rxPermissions = new RxPermissions(this);
      rxPermissions.setLogging(true);
      rxPermissions.requestEachCombined(permissionsGroup)
        .subscribe(permission -> {
          if (permission.granted) {
            //处理允许权限后的操作
            Intent i = new Intent();
            i.setAction(Intent.ACTION_PICK);
            i.setType("image/*");
            startActivityForResult(i, CODE_GALLERY);
            return;
          } else if (permission.shouldShowRequestPermissionRationale) {
            //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
            //处理用户点击禁止后的操作
            showPermissionError(permission.name);
          } else {
            showPermissionError(permission.name);
          }
        });
    } else {
      Intent i = new Intent();
      i.setAction(Intent.ACTION_PICK);
      i.setType("image/*");
      startActivityForResult(i, CODE_GALLERY);
    }
  }


  private void goToTakePhotoPage() {
    final String[] permissionsGroup = new String[]{
      Manifest.permission.CAMERA
    };
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
      RxPermissions rxPermissions = new RxPermissions(this);
      rxPermissions.setLogging(true);
      rxPermissions.requestEachCombined(permissionsGroup)
        .subscribe(permission -> {
          if (permission.granted) {
            //处理允许权限后的操作
            selectPicFromCamera();
            return;
          } else if (permission.shouldShowRequestPermissionRationale) {
            //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
            //处理用户点击禁止后的操作
            showPermissionError(permission.name);
          } else {
            showPermissionError(permission.name);
          }

        });
    } else {
      Intent takeIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
      Uri photoUri = getMediaFileUri(TYPE_TAKE_PHOTO);
      takeIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
      startActivityForResult(takeIntent, CODE_TAKE_PHOTO);
    }
  }

  private void showPermissionError(String permissionText) {
    if (null == getActivity()) {
      return;
    }
    if (TextUtils.isEmpty(permissionText)) {
      return;
    }

    if (!getActivity().isFinishing()) {
      int errorCode = permissionText.equalsIgnoreCase(Manifest.permission.CAMERA) ? PERMISSION_CAMERA_UNAUTHORIZED : PERMISSION_ALBUM_UNAUTHORIZED;
      DialogUtil.cameraAndalbumAlertDialog(getContext(), errorCode, LanguageStrings.app_modifyavator_gosetting_textview_text(), LanguageStrings.app_setting_clearcachecancle_button_text(), new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          // onConfirmed 跳转到系统设置
          goIntentSetting();
        }
      }, new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          //onCanceled
          // DialogUtil.closeSimpleDialog();
        }
      });
    }
  }

  /**
   * 跳转到三星系统设置。其他机型暂无适配计划
   */
  private void goIntentSetting() {
    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
    Uri uri = Uri.fromParts("package", requireActivity().getPackageName(), null);
    intent.setData(uri);
    try {
      startActivity(intent);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }
  File tempFile;
  protected void selectPicFromCamera() {
    if (requireActivity().isDestroyed() || requireActivity().isFinishing()) {
      return;
    }
    try {
      PictureSelector.create(this)
        .openCamera(SelectMimeType.ofImage())
        .setSandboxFileEngine((context, srcPath, mineType, call) -> {
          //Android 10 沙盒资源访问
          if (call != null) {
            String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
            call.onCallback(srcPath, sandboxPath);
          }
        }).setSandboxFileEngine((context, srcPath, mineType, call) -> {
          if (call != null) {
            String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
            call.onCallback(srcPath, sandboxPath);
          }
        }).forResultActivity(new OnResultCallbackListener<LocalMedia>() {
        @Override
        public void onResult(ArrayList<LocalMedia> result) {
          String imgPath = result.get(0).getSandboxPath();
          url.clear();
          url.add(0, imgPath);
          disableBtnClick(mViewModel.checkItemSelect());
          mReceiptAdapter.setDatas(url);
          mReceiptAdapter.notifyDataSetChanged();
        }
        @Override
        public void onCancel() {
          //取消
        }
      });
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public Uri getMediaFileUri(int type) {
    File mediaStorageDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "DCIM");
    if (!mediaStorageDir.exists()) {
      if (!mediaStorageDir.mkdirs()) {
        return null;
      }
    }
    //建立Media File
    String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
    File mediaFile;
    if (type == TYPE_TAKE_PHOTO) {
      mediaFile = new File(mediaStorageDir.getPath() + File.separator + "IMG_" + timeStamp + ".jpg");
    } else {
      return null;
    }
    return Uri.fromFile(mediaFile);
  }

  public void previewReceip(String url) {
    DialogUtil.showPhotoDialog(this.getContext(), url, null);
  }

  private void disableBtnClick(boolean b) {
    if (mViewDataBinding == null) {
      return;
    }
    mViewDataBinding.btDone.setEnabled(b);
    mViewDataBinding.btDone.setClickable(b);
    if(b) {
      mViewDataBinding.tvCheckKit.setVisibility(View.GONE);
    } else {
      mViewDataBinding.tvCheckKit.setVisibility(View.VISIBLE);
    }
  }

  @Override
  public void onItemClick(Object v) {
    int id = ((View) v).getId();
    if (id == R.id.ivfooter) {
      uploadReceipt();
      sendBaseTraceClick(FILE_UPLOAD_BUTTON_CLICK);
    } else if (id == R.id.ivDeviceIcon) {
      int index = (int) ((View) v).getTag();
      previewReceip(url.get(index));
    }
  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {
    disableBtnClick(mViewModel.checkItemSelect());
  }

  @Override
  public void onFocusChange(View view, boolean b) {
    disableBtnClick(mViewModel.checkItemSelect());
  }

  @Override
  public boolean OnkeyBack() {
    toJump = true;
    sendBackTrace();
    try {
      DeviceRegisterUistate uiState = mViewDataBinding.getUiState();
      if (null == uiState) {
        if (isAdded()) {
          this.requireActivity().finish();
        }
      }
      if (uiState != null && PRIV_FRAGMENT_1234 == uiState.getFrom()) {
        if (isAdded()) {
          this.requireActivity().finish();
        }
        return false;
      } else {
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        return true;
      }
    } catch (Exception e) {
      if (isAdded()) {
        this.requireActivity().finish();
      }
    }
    return true;
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {}

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  protected void initDatas(Bundle savedInstanceState) {
      initTrace();
  }

  @Override
  public void onResume() {
    super.onResume();
    disableBtnClick(mViewModel.checkItemSelect());
  }

  public void scanQrcode(DeviceRegisterUistate registerUiState,int mark) {

    final String[] permissionsGroup = new String[]{Manifest.permission.CAMERA};
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
      RxPermissions rxPermissions = new RxPermissions(this);
      rxPermissions.setLogging(true);
      rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
        @Override
        public void accept(Permission permission) throws Exception {
          if (permission.granted) {
            //处理允许权限后的操作
            registerUiState.setUrl(url);
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                    .withInt(KEY_FROM, R.id.deviceRegisterEuFragment)
                    .withInt(KEY_TO, R.id.scanCodeFragment)
                    .withInt(KEY_INPUT_MARK, mark)
                    .withSerializable(KEY_PREV_DATA, registerUiState)
                    .withSerializable(PARAMETER_DEVICD, registerUiState.getDeviceInfo())
                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation(); } else if (permission.shouldShowRequestPermissionRationale) {
            //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
            //处理用户点击禁止后的操作
            showPermissionError(permission.name);
          } else {
            showPermissionError(permission.name);
          }

        }
      });

    } else {
      registerUiState.setUrl(url);
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
              .withInt(KEY_FROM, R.id.deviceRegisterEuFragment)
              .withInt(KEY_TO, R.id.scanCodeFragment)
              .withInt(KEY_INPUT_MARK, mark)
              .withSerializable(KEY_PREV_DATA, registerUiState)
              .withSerializable(PARAMETER_DEVICD, registerUiState.getDeviceInfo())
              .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
    }
  }

  private void initTrace() {
    stayEleId = "4";
    pageId = "105";
    mouduleId = "12";
    nextButtoneleid = "2";
  }
}
