package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.utils.CommonUtils.goToWebPage;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE_REGISTED_DETAIL;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_MANUAL_INFO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.FireBaseUtils;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.PartsInfo;
import com.chervon.libDB.entities.ProductEncyFaq;
import com.chervon.libDB.entities.ProductEncyGuidance;
import com.chervon.libDB.entities.ProductEncyManual;
import com.chervon.libDB.entities.ProductEncyProduct;
import com.chervon.libDB.entities.ProductEncyclopediasInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.data.model.DeviceFaqEntry;
import com.chervon.moudleDeviceManage.data.model.DeviceVideoEntry;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageActivityNonIotPanelBinding;
import com.chervon.moudleDeviceManage.ui.adapter.NotIotFaqAdapter;
import com.chervon.moudleDeviceManage.ui.adapter.ProductVideoAdapter;
import com.chervon.moudleDeviceManage.ui.state.DeviceRegistedDetailUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.NonIotPanelModel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: NonIotPanelActivity
 * @Description: 非IOT面板 Activity
 * @Author: LiDeLi
 * @CreateDate: 2022/11/30 下午4:46
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/19
 * @UpdateRemark: 开放欧洲设备注册
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL)
public class NonIotPanelActivity extends BaseActivity<NonIotPanelModel> implements ItemClick, View.OnClickListener {
  private MoudleDevicemanageActivityNonIotPanelBinding mBinding;
  private ToolbarData mToolbarData;
  private NotIotFaqAdapter nonIotPanelFaqAdapter;
  private Context mContext;
  private DeviceInfo mDeviceInfo;
  private ProductEncyclopediasInfo mProEncyInfo;
  private ProductVideoAdapter mNonIotVideoAdapter;
  /**
   * 是否注册 1-是 0-否
   */
  private final String  IS_REGISTER = "1";
  private LoadingDialog loadingDialog;
  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
    loadingDialog = LoadingDialog.showDialog(getSupportFragmentManager());
    upgradeUser();
    mViewModel.getDeviceDetail(mDeviceInfo.getDeviceId());
    setToolBar();
  }


  /**
   * 更新评分系统
   */
  private void upgradeUser(){
    String openRnContainer = "openRnContainer";
    //更新注册系统
    User user = UserInfo.get();
    user.setHasOpenRnContainer(openRnContainer);
    UserInfo.set(user);
  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_devicemanage_activity_non_iot_panel;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mContext = this;
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      mDeviceInfo = (DeviceInfo) bundle.getSerializable(KEY_PREV_DATA);
    } else {
      mDeviceInfo = new DeviceInfo();
    }
    if(null != mDeviceInfo) {
      //设置当前操作设备
      BaseApplication.getInstance().setCurrentDeviceId(null != mDeviceInfo.getDeviceId() ?mDeviceInfo.getDeviceId():"");
    }
    mBinding = (MoudleDevicemanageActivityNonIotPanelBinding) viewDataBinding;

    //Faq初始化
    nonIotPanelFaqAdapter = new NotIotFaqAdapter(this, this);
    nonIotPanelFaqAdapter.setOnFaqItemClick(new NotIotFaqAdapter.OnFaqItemClick() {
      @Override
      public void OnFaqItemClick(DeviceFaqEntry deviceFaqEntry, int position, View view) {

        if (deviceFaqEntry.isAnswerState()) {
          //不显示答案
          deviceFaqEntry.setAnswerState(false);
          (view).setRotation(270);
        } else {
          //显示答案
          deviceFaqEntry.setAnswerState(true);
          (view).setRotation(90);
        }
        nonIotPanelFaqAdapter.notifyItemChanged(position);
        //nonIotPanelFaqAdapter.notifyDataSetChanged();
      }
    });
    mBinding.nonIotPanelFaqRecy.setLayoutManager(new LinearLayoutManager(this));
    mBinding.nonIotPanelFaqRecy.setAdapter(nonIotPanelFaqAdapter);

    //how to video
    mNonIotVideoAdapter = new ProductVideoAdapter(this, this, new DeviceVideoEntry[]{});
    mBinding.nonIotPanelVideoREcy.setLayoutManager(new LinearLayoutManager(this));
    mBinding.nonIotPanelVideoREcy.setAdapter(mNonIotVideoAdapter);


    mViewModel.getProductEncyInfo(mDeviceInfo.getProductId());
    mViewModel.getPartsInfo(mDeviceInfo.getProductId(),mDeviceInfo.getDeviceId());

    //如果是欧洲隐藏 产品百科、相关附件、维保注册
    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)){
      mBinding.llmenuFirst.setVisibility(View.VISIBLE);
      mBinding.nonIotPanelAccessoriesLayout.setVisibility(View.GONE);
      mBinding.nonIotPanelManualLayout.setVisibility(View.GONE);
      mBinding.nonIotPanelDeviceInfoLayout2.setVisibility(View.VISIBLE);
      mBinding.nonIotPanelDeviceInfoLayout.setVisibility(View.GONE);
      if(null != mDeviceInfo) {
        mViewModel.getDeviceRegistedDetail(!mDeviceInfo.getDeviceId().isEmpty()?mDeviceInfo.getDeviceId():"");
      }
    }else {
      mBinding.llmenuFirst.setVisibility(View.VISIBLE);
      mBinding.nonIotPanelManualLayout.setVisibility(View.VISIBLE);
      mBinding.nonIotPanelDeviceInfoLayout.setVisibility(View.VISIBLE);
      mBinding.nonIotPanelDeviceInfoLayout2.setVisibility(View.GONE);
      mViewModel.getDeviceRegistedDetail(mDeviceInfo.getDeviceId());
    }

    mBinding.setViewModel(mViewModel);
  }

  private void setToolBar(){
    String title = "";
    if (TextUtils.isEmpty(mDeviceInfo.getNickName())) {
      title = TextUtils.isEmpty(mDeviceInfo.getCommodityModel())?"":mDeviceInfo.getCommodityModel();
    } else {
      title = TextUtils.isEmpty(mDeviceInfo.getNickName())?"":mDeviceInfo.getNickName();
    }

    mToolbarData = new ToolbarData(title, new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });

    mToolbarData.setTitleClick(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        ARouter.getInstance()
                .build(RouterConstants.ACTIVITY_URL_EQUIPMENT_NAME)
                .withSerializable(KEY_PREV_DATA, mDeviceInfo)
                .navigation(NonIotPanelActivity.this  ,1);
      }
    });


    mToolbarData.setJumpNextClick(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        LogUtils.i("lesly 点击右上角 ");
        //进入Device Details页面
        sendDeviceDetailsButtonClick();
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_DETAILS)
                .withSerializable(KEY_PREV_DATA, mDeviceInfo)
                .navigation();

      }
    });

    mBinding.setToolbarData(mToolbarData);
  }



  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.deviceDetail.observe(this, new Observer<DeviceInfo>() {
      @Override
      public void onChanged(DeviceInfo deviceInfo) {
        if (null!=loadingDialog){
          loadingDialog.dismiss();
        }
        if (deviceInfo != null) {
          mDeviceInfo = deviceInfo;
          setFirebaseTag();
          mViewModel.checkCh7000OrPowerStation(deviceInfo);
          if (IS_REGISTER.equals(mDeviceInfo.getInfoStatus())) {
            mBinding.nonIotPanelRegisterTv.setText(LanguageStrings.app_deviceboard_registinfo_button_text());
          } else {
            mBinding.nonIotPanelRegisterTv.setText(LanguageStrings.app_notiotbindresult_register_button_text());
          }

        }
      }
    });
    //产品百科
    mViewModel.mProductEncyInfoLiveData.observe(this, new Observer<ProductEncyclopediasInfo>() {
      @Override
      public void onChanged(ProductEncyclopediasInfo productEncyclopediasInfo) {
        if (productEncyclopediasInfo == null) {
          productEncyclopediasInfo = new ProductEncyclopediasInfo();
        }
        mProEncyInfo = productEncyclopediasInfo;
        Log.i("lesly", "leslyiot pEncy initData encyInfo = " + productEncyclopediasInfo);
        if (productEncyclopediasInfo.getProduct() != null) {
          ProductEncyProduct product = productEncyclopediasInfo.getProduct();

          Glide.with(mContext).load(product.getProductPicUrl())
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .placeholder(R.drawable.ic_device_default)
            .into(mBinding.nonIotPanelImg);
        }

        //video数据显示
        if (productEncyclopediasInfo.getGuidance() != null) {
          mBinding.nonIotPanelVideoLayout.setVisibility(View.VISIBLE);
          ProductEncyGuidance[] guidances = productEncyclopediasInfo.getGuidance();
          DeviceVideoEntry[] datas = new DeviceVideoEntry[guidances.length];
          for (int i = 0; i < datas.length; i++) {
            datas[i] = new DeviceVideoEntry(guidances[i].getUrl(), guidances[i].getName());
          }
          LogUtils.i("leslyIot datas = " + Arrays.toString(datas));

          if (null != datas) {
            if (datas.length > 0) {

              mBinding.nonIotPanelVideoLayout.setVisibility(View.VISIBLE);
              mNonIotVideoAdapter.setDatas(datas);
              mNonIotVideoAdapter.notifyDataSetChanged();
            } else {
              mBinding.nonIotPanelVideoLayout.setVisibility(View.GONE);

            }
          } else {
            mBinding.nonIotPanelVideoLayout.setVisibility(View.GONE);
          }


        }

        //FAQ数据显示
        if (productEncyclopediasInfo.getFaq() != null) {
          ProductEncyFaq[] faqs = productEncyclopediasInfo.getFaq();
          List<DeviceFaqEntry> faqDatas = new ArrayList<>();
          for (int i = 0; i < faqs.length; i++) {
            if (!TextUtils.isEmpty(faqs[i].getTitle())){
              faqDatas.add(new DeviceFaqEntry(faqs[i].getTitle(), faqs[i].getAnswer()));
            }
          }
          if (null != faqDatas) {
            if (faqDatas.size() > 0) {

              mBinding.nonIotPanelFaqLayout.setVisibility(View.VISIBLE);
              nonIotPanelFaqAdapter.setDatas(faqDatas);
              nonIotPanelFaqAdapter.notifyDataSetChanged();
            } else {
              mBinding.nonIotPanelFaqLayout.setVisibility(View.GONE);

            }
          } else {
            mBinding.nonIotPanelVideoLayout.setVisibility(View.GONE);
          }


        }
      }
    });
    //配件相关
    mViewModel.mPartsInfoLiveData.observe(this, new Observer<PartsInfo[]>() {
      @Override
      public void onChanged(PartsInfo[] partsInfos) {
        Log.i("lesly", "leslyiot part initData partInfo = " + Arrays.toString(partsInfos));

        if (partsInfos != null) {
          String str = "";
          if (partsInfos.length == 0) {
            str = "coming soon";
          } else {
            str = partsInfos.length + " available";
          }
          mBinding.nonIotPanelAccessoriesTv2.setText(str);
        }

      }
    });
    //设备注册相关
    mViewModel.mDeviceRegistedDetailLiveData.observe(this, new Observer<DeviceRegistedDetailUistate>() {
      @Override
      public void onChanged(DeviceRegistedDetailUistate deviceRegistedDetailUistate) {
        Log.i("lesly", "leslyiot deviceRegister initData registerInfo = " + deviceRegistedDetailUistate);

      }
    });

  }

  public void llPartSwitchExtend(View view) {

    Object isExtend = view.getTag();
    ImageView arrowButton = new ImageButton(this);
    LogUtils.i("lesly isExtend = " + isExtend);
    if (view.getId() == R.id.nonIotPanelFaqll) {
      //Faq  展开/收起图标
      arrowButton = mBinding.nonIotPanelFaqIb;
      if (isExtend != null) {
        nonIotPanelFaqAdapter.setState(true);
        nonIotPanelFaqAdapter.setEntryAnswerState(false);
        nonIotPanelFaqAdapter.notifyDataSetChanged();
      } else {
        nonIotPanelFaqAdapter.setState(false);
        nonIotPanelFaqAdapter.notifyDataSetChanged();
      }
    } else if (view.getId() == R.id.nonIotPanelFaqItemIb) {

    } else if (view.getId() == R.id.nonIotPanelVideoll) {
      if (mProEncyInfo != null) {
        if (mNonIotVideoAdapter == null) {
          return;
        }
        mNonIotVideoAdapter.onPause();
        //video
        arrowButton = mBinding.nonIotPanelVideoIb;
        //收起视频
        if (isExtend != null) {
          if (mProEncyInfo.getGuidance().length > 1) {
            ViewGroup.LayoutParams params = mBinding.nonIotPanelVideoLayout.getLayoutParams();
            params.height = -2;
            mBinding.nonIotPanelVideoLayout.setLayoutParams(params);
          }
          mNonIotVideoAdapter.setPart(true);
        } else {
          if (mProEncyInfo.getGuidance().length > 1) {
            ViewGroup.LayoutParams params = mBinding.nonIotPanelVideoLayout.getLayoutParams();

            if (mProEncyInfo.getGuidance().length < 4) {
              params.height = AutoSizeUtils.mm2px(NonIotPanelActivity.this, mProEncyInfo.getGuidance().length * 560);
            } else {
              params.height = AutoSizeUtils.mm2px(NonIotPanelActivity.this, 3 * 560);
            }

            mBinding.nonIotPanelVideoLayout.setLayoutParams(params);
          }
          mNonIotVideoAdapter.setPart(false);
        }
        //  mNonIotVideoAdapter.notifyDataSetChanged();
      }
    }

    //箭头方向调整
    if (isExtend == null) {
      arrowButton.setRotation(90);
      view.setTag(true);
    } else {
      arrowButton.setRotation(270);
      view.setTag(null);
    }
  }

  @Override
  protected Class<? extends NonIotPanelModel> getViewModelClass() {
    return NonIotPanelModel.class;
  }

  @Override
  public void onItemClick(Object uiState) {
    sendHowToVideosButtonClick();
  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

  }

  @Override
  public void onClick(View v) {
    if (v.getId() == R.id.nonIotPanelRegisterLayout) {
      //Register
      if (IS_REGISTER.equals(mDeviceInfo.getInfoStatus())) {
        //注册信息展示
        ARouter.getInstance().build(ACTIVITY_URL_DEVICE_REGISTED_DETAIL)
          .withSerializable(KEY_PREV_DATA, mDeviceInfo).withInt(KEY_PREV_FRAGMENT, 1234)
          .navigation();
      } else {
        //进入注册流程
        sendRegisterNowButtonClick();
        ARouter.getInstance().build(ACTIVITY_URL_DEVICE)
          .withSerializable(KEY_PREV_DATA, mDeviceInfo)
          .withInt(KEY_PREV_FRAGMENT, 1234)
          .navigation();
      }
    } else if (v.getId() == R.id.nonIotPanelAccessoriesLayout) {
      //配件
      sendAccessoriesButtonClick();
      //展示配件列表
      // TODO 72002
      String deviceId =mDeviceInfo.getDeviceId();
      String productId =mDeviceInfo.getProductId();

      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_PARTS)
        .withString(PARAMETER_DEVICD_ID, deviceId)
        .withString(PARAMETER_PRODUCT_ID,productId)
        .navigation();
    } else if (v.getId() == R.id.nonIotPanelManualLayout) {
      //产品手册
      sendManualButtonClick();
      try {
        ProductEncyManual manual = mProEncyInfo.getManual();
        String url = manual.getUrl();

        //未配置用户手册跳转PDF页面。显示空页面
        if (TextUtils.isEmpty(url)) {
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_MANUAL)
            .withSerializable(PARAMETER_MANUAL_INFO, manual)
            .withString(KEY_FROM, RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL)
            .navigation();
          return;
        }


        if (url.endsWith(".pdf")) {
          //显示产品手册
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_MANUAL)
            .withSerializable(PARAMETER_MANUAL_INFO, manual)
            .withString(KEY_FROM, RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL)
            .navigation();
        } else {
          goToWebPage(this, url);
        }

      } catch (Exception e) {
        LogUtils.d("ProductEncyManual" + e.getMessage());
      }
    } else if (v.getId() == R.id.nonIotPanelDeviceInfoLayout) {
      //设备信息
      sendEquipmentInfoButtonClick();
      //展示设备详情页
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_EQUIPMENT_INFO)
        .withSerializable(KEY_PREV_DATA, mDeviceInfo)
        .navigation();
    } else if (v.getId() == R.id.nonIotPanelDeviceInfoLayout2) {
      //设备信息
      sendEquipmentInfoButtonClick();
      //展示设备详情页
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_EQUIPMENT_INFO)
              .withSerializable(KEY_PREV_DATA, mDeviceInfo)
              .navigation();
    }
  }


  @Override
  public void onConfigurationChanged(@NonNull Configuration newConfig) {
    super.onConfigurationChanged(newConfig);
  }


  @Override
  public boolean onKeyDown(int keyCode, KeyEvent event) {
    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
      goBack();
      return false;
    }
    return super.onKeyDown(keyCode, event);

  }


  private void initTrace() {
    stayEleId = "8";
    pageId = "84";
    mouduleId = "10";
    nextButtoneleid = "2";
    String pidKey = "pid";
    String deviceIdKey = "deviceid";
    if (null!=mDeviceInfo){
      if (!TextUtils.isEmpty(mDeviceInfo.getDeviceId())){
        expandMap.put(deviceIdKey,mDeviceInfo.getDeviceId());
      }
      if (!TextUtils.isEmpty(mDeviceInfo.getProductId())){
        expandMap.put(pidKey,mDeviceInfo.getProductId());
      }
    }
  }


  public void sendDeviceDetailsButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendRegisterNowButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
  }

  public void sendAccessoriesButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 4 + "", "1");
  }

  public void sendManualButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 5 + "", "1");
  }

  public void sendEquipmentInfoButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 6 + "", "1");
  }

  public void sendHowToVideosButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 7 + "", "1");
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if(data!=null){
      DeviceInfo  deviceInfo= (DeviceInfo) data.getSerializableExtra(PARAMETER_DEVICD);
      if(deviceInfo!=null){
        mDeviceInfo=deviceInfo;
        if (null==mToolbarData){
          return;
        }
        if(TextUtils.isEmpty(deviceInfo.getNickName())){
          mToolbarData.setTitle(deviceInfo.getCommodityModel());
        }else{
          mToolbarData.setTitle(deviceInfo.getNickName());
        }

      }
    }
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  @Override
  protected void onPause() {
    super.onPause();
    if (mNonIotVideoAdapter != null) {
      mNonIotVideoAdapter.onPause();
    }

  }

  @Override
  public void onStop() {
    super.onStop();
    if (mNonIotVideoAdapter != null) {
      mNonIotVideoAdapter.onStop();
    }
  }

  @Override
  protected void onRestart() {
    super.onRestart();
    mNonIotVideoAdapter.notifyDataSetChanged();
  }

  @SuppressLint("RestrictedApi")
  private void goBack() {
    sendBackTrace();
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME)
            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            .withInt(KEY_FROM,R.layout.moudle_devicemanage_activity_non_iot_panel)
            .navigation();
    finish();
  }




  private void setFirebaseTag() {
    final String RN_Bundle_Name = "RN_Bundle_Name_Not_IOT";
    final String evn_key = "APP_EVN";
    Bundle bundle = new Bundle();
    if (null!=mDeviceInfo){
      bundle.putString(RN_Bundle_Name, mDeviceInfo.getSn());
    }
    bundle.putString(evn_key, BuildConfig.EVN + BuildConfig.FLAVOR);
    FireBaseUtils.addPanelCrashLogEvent(bundle);
  }
}
