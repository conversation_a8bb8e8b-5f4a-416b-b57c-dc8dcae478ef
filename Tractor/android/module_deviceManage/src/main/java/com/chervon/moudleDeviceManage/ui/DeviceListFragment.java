package com.chervon.moudleDeviceManage.ui;

import static com.chervon.libBase.model.AppConstants.ADD_0;
import static com.chervon.libBase.model.AppConstants.Z6_BLENAME_LENGTH;
import static com.chervon.libBase.model.AppConstants.Z6_HEAD;
import static com.chervon.libBase.model.AppConstants.ZT_01;
import static com.chervon.libBase.model.AppConstants.ZT_02;
import static com.chervon.libBase.model.AppConstants.ZT_03;
import static com.chervon.libBase.utils.APPConstants.PUBLISH_ERROR;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.APPConstants.SHARE_OPERATE_TYPE_MAIN_REMOVE;
import static com.chervon.libBase.utils.CommonUtils.isZ6Device;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libBase.utils.Utils.z6DeviceIdHex2digit;
import static com.chervon.libNetwork.iot.AwsMqttService.APP_KICK_NOTIFICATION;
import static com.chervon.libNetwork.iot.AwsMqttService.APP_MESSAGE_NOTIFICATION_PATH;
import static com.chervon.libNetwork.iot.AwsMqttService.APP_SHORT_TOKEN_NOTIFICATION;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.SHOW_ALERT_PERMISSION;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEL_DEVIVE_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_RESET_PASSWORD_FAIL;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEVICE_RESET_PASSWORD_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.ORDER_DEVICE_SUCCESS;
import static com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter.TURN_OFF;
import static com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter.TURN_ON;
import static com.chervon.moudleDeviceManage.utils.Constants.DEVICE_SHARE_TYPE_MAIN;
import static com.chervon.moudleDeviceManage.utils.Constants.DEVICE_SHARE_TYPE_SUB;
import static com.chervon.moudleDeviceManage.utils.Constants.EMPTY;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.MotionEvent;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.databinding.ObservableField;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.alibaba.android.arouter.launcher.ARouter;
import com.amazonaws.mobileconnectors.iot.AWSIotMqttClientStatusCallback;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.CBleDevice;
import com.chervon.libBase.model.ShortTokenEntry;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.dialog.AppVersionService;
import com.chervon.libBase.ui.dialog.BottomAlertDialog;
import com.chervon.libBase.ui.dialog.ConfirmAlertDialog;
import com.chervon.libBase.ui.widget.LiveDataBus;
import com.chervon.libBase.ui.widget.refresh.PullRefreshLayout;
import com.chervon.libBase.utils.AESUtil;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.ReviewManagerService;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.ProductDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.event.ShareUpdateEvent;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.result.CertPemBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libNetwork.iot.data.AwsUpdateBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleDeviceManage.R;
import com.chervon.moudleDeviceManage.data.repository.HomeRepo;
import com.chervon.moudleDeviceManage.databinding.MoudleDevicemanageFragmentDeviceListBinding;
import com.chervon.moudleDeviceManage.ui.adapter.DevicesListAdapter;
import com.chervon.moudleDeviceManage.ui.adapter.DevicesListDefaultAdapter;
import com.chervon.moudleDeviceManage.ui.adapter.MyItemTouchHelperCallBack;
import com.chervon.moudleDeviceManage.ui.state.DeviceListUistate;
import com.chervon.moudleDeviceManage.ui.viewmodel.DeviceListViewModel;
import com.chervon.moudleDeviceManage.ui.viewmodel.HomeViewModel;
import com.chervon.moudleDeviceManage.utils.EditDeviceListHelper;
import com.clj.fastble.data.BleDevice;
import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import io.reactivex.functions.Consumer;
import me.jessyan.autosize.AutoSizeCompat;
import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui
 * @ClassName: DeviceListFragment
 * @Description:
 * @Author:
 * @CreateDate:
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/8/8
 * @UpdateRemark: 设备分享需求
 * @Version: 1.2
 */
public class DeviceListFragment extends BaseEnhanceFragment<DeviceListViewModel, MoudleDevicemanageFragmentDeviceListBinding> implements ItemClick {

    private static final String TAG = "DeviceListFragment";
    public static final String QUICK_CONTROL_BUTTON_STATUS = "23001";
    private DevicesListAdapter mAdapter;

    private DevicesListDefaultAdapter mDevicesListDefaultAdapter;
    public ObservableField<Boolean> showDefaultState = new ObservableField<>(false);
    private EditDeviceListHelper mEditDeviceListHelper;
    private HomeViewModel homeViewModel;

    private float mFloatSlot = 200;

    protected static final String NOT_IOT_DEVICE = "notIotDevice";
    protected static final String GAT_WAY_SUB_DEVICE = "gatewaySubDevice";
    private static final String GATEWAY_SUB_DEVICE = "gatewaySubDevice";
    private static final String COMMUNICATE_MODE_DT = "DT";
    /**
     * 2024/9/20 需求crm弹框弹出时，拦截掉所有下方智能添加的弹窗。
     */
    private boolean isCrmDialogShow = false;
    private final String space_language = " ";

  private DeviceInfo resetPasswordDevice;


    private String quickControlTopic;

    public DeviceListFragment() {
    }

    @Override
    protected void initDatas(Bundle savedInstanceState) {
        if (null != getContext()) {
            mFloatSlot = AutoSizeUtils.mm2px(getContext(), 180);
        }
        initTrace();
        if (null != getActivity()) {
            homeViewModel = new ViewModelProvider(getActivity()).get(HomeViewModel.class);
        }
        mViewDataBinding.setUiState(mViewModel.mLiveData.getValue());
        mViewDataBinding.gpDefault.setVisibility(View.INVISIBLE);
        mViewModel.deviceSateLiveData.observe(this, new Observer<DeviceInfo>() {
            @Override
            public void onChanged(DeviceInfo deviceInfo) {
                if (deviceInfo != null) {
                    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL).withSerializable(PARAMETER_DEVICD, deviceInfo).navigation();
                }
            }
        });

        mViewModel.isSmartScan.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean result) {
                if (result) {
                    scanDevices();
                }
            }
        });


        mViewModel.lastMessageliveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    mViewDataBinding.ivRedDot.setVisibility(View.VISIBLE);
                } else {
                    mViewDataBinding.ivRedDot.setVisibility(View.GONE);
                }
            }
        });
    }


    @Override
    protected int getLayoutId() {
        return R.layout.moudle_devicemanage_fragment_device_list;
    }

    @SuppressLint("ClickableViewAccessibility")
    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {

        AutoSizeCompat.autoConvertDensityOfGlobal(this.getResources());

        GridLayoutManager gridLayoutManager = new GridLayoutManager(this.getActivity(), 2, RecyclerView.VERTICAL, false);
        mViewDataBinding.rvDeviceList.setLayoutManager(gridLayoutManager);

        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new MyItemTouchHelperCallBack(mViewModel));
        itemTouchHelper.attachToRecyclerView(mViewDataBinding.rvDeviceList);
        mViewDataBinding.scroll.setOnTouchListener((View v, MotionEvent event) -> {
            if (mAdapter.isShowDel()) {
                mAdapter.setShowDel(false);
                mViewModel.mEditState.postValue(true);
                mAdapter.notifyDataSetChanged();
            }
            return true;
        });
        List<DeviceInfo> list = new ArrayList<DeviceInfo>();
        mAdapter = new DevicesListAdapter(this.getContext(), this, list);
        mAdapter.setHasStableIds(true);
        mViewDataBinding.rvDeviceList.setAdapter(mAdapter);


        //第一步：找到 swipeRefresh 控件

        mViewDataBinding.srlCotainer.setOnLoadListener(new PullRefreshLayout.OnLoadListener() {
            @Override
            public void onLoad() {
                mViewDataBinding.srlCotainer.setLoading(false);
            }
        });
        mViewDataBinding.srlCotainer.setOnRefreshListener(new PullRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                mViewModel.reFreshDeviceList();
            }
        });

        mViewDataBinding.scroll.setOnScrollChangeListener(new View.OnScrollChangeListener() {

            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                if (scrollY > mFloatSlot) {
                    mViewDataBinding.floating.setVisibility(View.VISIBLE);
                } else {
                    mViewDataBinding.floating.setVisibility(View.GONE);
                }
            }
        });
        mEditDeviceListHelper = new EditDeviceListHelper();
        mEditDeviceListHelper.attatchRecyclerView(mViewDataBinding.rvDeviceList, gridLayoutManager, mAdapter, mViewModel);
    }

    private void changeToDefaultAdapter() {

        if (!(mViewDataBinding.rvDeviceList.getAdapter() instanceof DevicesListDefaultAdapter) &&
                mViewDataBinding.rvDeviceList.getScrollState() == RecyclerView.SCROLL_STATE_IDLE &&
                (!mViewDataBinding.rvDeviceList.isComputingLayout())) {
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this.getActivity(), null, RecyclerView.VERTICAL, 0);
            mViewDataBinding.rvDeviceList.setLayoutManager(linearLayoutManager);
            List<DeviceInfo> defaultList = new ArrayList<DeviceInfo>();
            defaultList.add(new DeviceInfo());
            mDevicesListDefaultAdapter = new DevicesListDefaultAdapter(this.getContext(), this, defaultList);
            mViewDataBinding.rvDeviceList.setAdapter(mDevicesListDefaultAdapter);
            mDevicesListDefaultAdapter.notifyDataSetChanged();
        }
        mViewDataBinding.rvDeviceList.setVisibility(View.VISIBLE);

    }


    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {
        ReviewManagerService.getInstance().intentEvaluateView(getActivity(), pageResouce);

        BluetoothConection.getInstance().disconnectDeviceByNative(getContext());
        BaseApplication.getInstance().setCurrentDeviceId("");

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class<? extends DeviceListViewModel> getViewModelClass() {
        return DeviceListViewModel.class;
    }

    @Override
    public void onStart() {
        super.onStart();
        if (isAdded()) {
            ((BaseActivity) getActivity()).setPageSource(pageResouce);
            //手动断开设备
            try {
                BluetoothConection.getInstance().disConnectDevice();
            } catch (Exception e) {
                LogUtils.e(TAG, "设备二次断开失败");
            }

        }
        AutoSizeCompat.autoConvertDensityOfGlobal(this.getResources());
        mViewModel.getLastMessage();
        subscribeTopic();
        initAws();
        EventBus.getDefault().register(this);
//        AwsMqttService.subscribeTopic(String.format(APP_KICK_NOTIFICATION, UserInfo.get().getId()));

    }


    private void initAws() {

        if (UserInfo.get().getCertificatePem() != null && !UserInfo.get().getCertificatePem().isEmpty()) {
            //启动AWS MQTT服务
            startMqttService();
        } else {
            ApiService.instance().getCertPem().subscribe(new HttpObserver<CertPemBean>() {
                @Override
                protected void Next(CertPemBean entity) {
                    User user = UserInfo.get();
                    user.setDeviceArn(entity.getEntry().getDeviceArn());
                    user.setCertificatePem(entity.getEntry().getCertificatePem());
                    user.setPrivateKey(entity.getEntry().getPrivateKey());
                    UserInfo.set(user);
                    try {
                        String aesStr = AESUtil.decryptQf(entity.getEntry().getCertJSONStr(), BuildConfig.DECRYPT_PWD);
                        if (!TextUtils.isEmpty(aesStr)) {
                            CertPemBean.EntryDTO entryDTO = GsonUtils.fromJson(aesStr, CertPemBean.EntryDTO.class);
                            user.setDeviceArn(entryDTO.getDeviceArn());
                            user.setCertificatePem(entryDTO.getCertificatePem());
                            user.setPrivateKey(entryDTO.getPrivateKey());
                            UserInfo.set(user);
                        }
                        LogUtils.iTag("MQTT", "aesStr_:" + aesStr);
                    } catch (Exception e) {
                        LogUtils.e("MQTT", "aesStr_:" + e.getMessage());
                    }

                    startMqttService();

                }
            });
        }
    }

    private void subscribeTopic(){
        AwsMqttService.connectAwsLiveData.observe(DeviceListFragment.this, new Observer<AWSIotMqttClientStatusCallback.AWSIotMqttClientStatus>() {
            @Override
            public void onChanged(AWSIotMqttClientStatusCallback.AWSIotMqttClientStatus status) {
                LogUtils.i(TAG,"connectAwsLiveData2----->"+status.name());

                if (status == AWSIotMqttClientStatusCallback.AWSIotMqttClientStatus.Connected){
                    AwsMqttService.publishTopic(String.format(APP_SHORT_TOKEN_NOTIFICATION, UserInfo.get().getId()),
                            GsonUtils.toJson(new ShortTokenEntry(UserInfo.get().getShortToken())));
                    AwsMqttService.subscribeTopic(String.format(APP_KICK_NOTIFICATION, UserInfo.get().getId()));
                }

            }
        });
    }

    @Override
    public void onDetach() {
        super.onDetach();
        LogUtils.i(TAG,"connectAwsLiveData3-removeObservers---->");
        if (AwsMqttService.connectAwsLiveData.hasObservers()){
            AwsMqttService.connectAwsLiveData.removeObservers(this);
        }
    }


    private void startMqttService() {
        AwsMqttService.getInstance().initIoTClient();
    }


    private void scanDevices() {
        //智能搜索被关闭
        if (!UserInfo.get().isSmartScanState()) {
            return;
        }
        if (!isVisible){
            return;
        }

        BluetoothService.getInstance().setTag(false, TAG, new BluetoothService.DeviceFindListener() {
            @Override
            public void findDevice(CBleDevice bleDevice) {
                if (isAdded()) {
                    scanResultOldZ6(bleDevice);
                    scanResult(bleDevice.getCBleDevice());
                }
            }

            @Override
            public void scanTimeOut() {
            }
        });


    }

    /**
     * Z6  判断是否Z6 并进行只能添加逻辑
     *
     * @param bleDevice
     */
    private void scanResultOldZ6(BleDevice bleDevice) {
        String Z6_TAG = "X";
        if (bleDevice != null) {
            String bleName = bleDevice.getName();

            if (!TextUtils.isEmpty(bleName) && bleName.contains(Z6_HEAD) && bleName.length() == Z6_BLENAME_LENGTH) {
                String deviceId = bleName.substring(2);
                String hexSnCodeStr = deviceId.substring(2, 6);
                String header = hexString2String(hexSnCodeStr);
                String numberStr1 = z6DeviceIdAppendWithZero(deviceId.substring(6, 8)) + z6DeviceIdAppendWithZero(deviceId.substring(8, 10)) + z6DeviceIdAppendWithZero(deviceId.substring(10, 12)) + z6DeviceIdHex2digit(deviceId.substring(12, 18));
                String end = hexString2String(deviceId.substring(18, 20));
                deviceId = Z6_TAG + header + numberStr1 + end;
                //过滤绑定设备
                DeviceInfo deviceByMac = SoftRoomDatabase.getDatabase(getContext()).deviceDao().getDeviceById(deviceId);
                if (null != deviceByMac) {
                    return;
                }
                //snCode
                String snCodeString = deviceId.substring(1, 5);
                if (!snCodeString.equalsIgnoreCase(ZT_01) && !snCodeString.equalsIgnoreCase(ZT_02) && !snCodeString.equalsIgnoreCase(ZT_03)) {
                    return;
                }


                for (String noScanMac : BluetoothService.getInstance().noScanMacs) {
                    if (bleDevice.getMac().equals(noScanMac)) {
                        return;
                    }
                }


                //show dialog
                String tip1 = LanguageStrings.app_smartadddevice_descriptionfirst_textview_text();
                String tip2 = space_language + LanguageStrings.app_smartadddevice_descriptionsecond_textview_text();
                String deviceName = bleDevice.getName();
                if (TextUtils.isEmpty(deviceName)) {
                    deviceName = "";
                }
                DeviceInfo deviceInfo = CommonUtils.createDeviceInfo(bleDevice);
                //  String stateString = getDeviceStateString(bleDevice);

                //  if ("di".equalsIgnoreCase(stateString)) {
                deviceInfo.setStatus(8);
                //    }


                ProductDao productDao = SoftRoomDatabase.getDatabase(getContext()).productDao();
                List<ProductInfo> products = productDao.getProducts();
                //获取当前设备产品
                String productName = "";
                String imagUrl = "";
                String productId = "";
                ProductInfo currentProduct = new ProductInfo();
                currentProduct.setProductSnCode(snCodeString);
                for (ProductInfo product : products) {
                    if (product != null && product.getProductSnCode() != null) {
                        if (product.getProductSnCode().equals(snCodeString)) {
                            currentProduct = product;
                            imagUrl = product.getProductIcon();
                            productName = product.getCommodityModel();
                            productId = product.getId();
                            if (product.getProductIcon() != null) {
                                deviceInfo.setIconUrl(product.getProductIcon());
                            }
                            deviceInfo.setProductId(product.getId());
                            deviceInfo.setProductType(product.getProductType());
                            deviceInfo.setCommunicateMode(product.getNetworkModes());
                            deviceInfo.setDeviceName(product.getCommodityModel());
                            deviceInfo.setCommodityModel(product.getCommodityModel());
                        }
                    }
                }

                if (TextUtils.isEmpty(productName)) {
                    productName = "";
                } else {
                    productName = space_language + productName;
                }
                String spanString = tip1 + productName + tip2;
                ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(getContext().getResources().getColor(R.color.colorButtonNormal));
                SpannableString spannableString = new SpannableString(spanString);
                spannableString.setSpan(foregroundColorSpan, tip1.length(), tip1.length() + productName.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

                String finalProductId = productId;

                if (!TextUtils.isEmpty(finalProductId)) {
                    //停止扫描
                    ProductInfo finalCurrentProduct = currentProduct;

                    homeViewModel.isScan = false;
                    //当App升级弹窗显示--拦截当前设备搜索弹窗
                    if (AppVersionService.getInstance().getAppVersionShowStatus()){
                        return;
                    }

                    BottomAlertDialog.show(getActivity().getSupportFragmentManager(), spannableString, imagUrl, LanguageStrings.app_smartadddevice_cancel_button_text(), LanguageStrings.app_smartadddevice_connect_button_text(), new Consumer<Boolean>() {
                        @Override
                        public void accept(Boolean aBoolean) throws Exception {
                            if (aBoolean) {
                                //connect
                                BluetoothService.getInstance().stopPostDevice();
                                ArrayList<ProductInfo> productInfoArrayList = new ArrayList<>();
                                productInfoArrayList.add(finalCurrentProduct);
                                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET).withSerializable(PARAMETER_DEVICD, productInfoArrayList).withInt(KEY_FROM, R.id.deviceListFragment).withInt(KEY_TO, R.id.deviceGuidePageOneFragment).withInt(KEY_PREV_FRAGMENT, R.id.deviceListFragment).navigation();

                            } else {
                                //cancle
                                BluetoothService.getInstance().noScanMacs.add(bleDevice.getMac());
                            }
                        }
                    });
                }
            }


        }
    }

    /**
     * Z6  16 进制转换 为string
     *
     * @param hexSnCodeStr
     * @return
     */
    @NonNull
    private String hexString2String(String hexSnCodeStr) {
        String str = "0123456789ABCDEF";
        char[] hexs = hexSnCodeStr.toCharArray();
        byte[] bytes = new byte[hexSnCodeStr.length() / 2];
        int n;
        for (int i = 0; i < bytes.length; i++) {
            n = str.indexOf(hexs[2 * i]) * 16;
            n += str.indexOf(hexs[2 * i + 1]);
            bytes[i] = (byte) (n & 0xff);
        }
        return new String(bytes);
    }

    /**
     * Z6  设备id 转换 补0
     *
     * @param numberStr1
     * @return
     */
    private String z6DeviceIdAppendWithZero(String numberStr1) {
        Integer number1 = ConvertUtils.hexString2Int(numberStr1);
        if (number1.toString().length() == 1) {
            return ADD_0 + number1;
        }
        return number1 + "";
    }


    private void scanResult(BleDevice bleDevice) {

        String EGO_TAG_BROADCASTING = "di";

        if (!homeViewModel.isScan) {
            LogUtils.e(TAG, "DeviceList设备搜索被禁用");
            return;
        } else {
            LogUtils.e(TAG, "DeviceList设备搜索被启用");
        }


        //过滤绑定设备
        DeviceInfo deviceByMac = SoftRoomDatabase.getDatabase(getContext()).deviceDao().getDeviceByMac(bleDevice.getMac());
        if (null != deviceByMac) {
            return;
        }

        String snCode = BluetoothConection.getSNCode(bleDevice.getScanRecord());
        //过滤不识别设备
        if (BluetoothConection.isEGO(bleDevice.getScanRecord())) {


            if (snCode == null || TextUtils.isEmpty(snCode)) {
                snCode = "0";
            }
            //转bytes
            byte[] snCodeBytes = ConvertUtils.hexString2Bytes(snCode);
            //deviceId string
            String snCodeString = ConvertUtils.bytes2String(snCodeBytes).replaceAll("\\n", "").replaceAll("\\u0000", "");


            //show dialog
            String tip1 = LanguageStrings.app_smartadddevice_descriptionfirst_textview_text();
            String tip2 = space_language + LanguageStrings.app_smartadddevice_descriptionsecond_textview_text();
            String deviceName = bleDevice.getName();
            if (TextUtils.isEmpty(deviceName)) {
                deviceName = "";
            }
            DeviceInfo deviceInfo = CommonUtils.createDeviceInfo(bleDevice);
            String stateString = getDeviceStateString(bleDevice);

            if (EGO_TAG_BROADCASTING.equalsIgnoreCase(stateString)) {
                deviceInfo.setStatus(8);
            }
            ProductDao productDao = SoftRoomDatabase.getDatabase(getContext()).productDao();
            List<ProductInfo> products = productDao.getProducts();
            //获取当前设备产品
            String productName = "";
            String imagUrl = "";
            String productId = "";
            ProductInfo currentProduct = new ProductInfo();
            currentProduct.setProductSnCode(snCodeString);
            for (ProductInfo product : products) {
                if (product != null && product.getProductSnCode() != null) {
                    if (product.getProductSnCode().equals(snCodeString)) {
                        currentProduct = product;
                        imagUrl = product.getProductIcon();
                        productName = product.getCommodityModel();
                        productId = product.getId();
                        if (product.getProductIcon() != null) {
                            deviceInfo.setIconUrl(product.getProductIcon());
                        }
                        deviceInfo.setProductId(product.getId());
                        deviceInfo.setProductType(product.getProductType());
                        deviceInfo.setCommunicateMode(product.getNetworkModes());
                        deviceInfo.setDeviceName(product.getCommodityModel());
                        deviceInfo.setCommodityModel(product.getCommodityModel());
                    }
                }
            }

            if (TextUtils.isEmpty(productName)) {
                productName = "";
            } else {
                productName = space_language + productName;
            }
            String spanString = tip1 + productName + tip2;
            ForegroundColorSpan foregroundColorSpan = new ForegroundColorSpan(getContext().getResources().getColor(R.color.colorButtonNormal));
            SpannableString spannableString = new SpannableString(spanString);
            spannableString.setSpan(foregroundColorSpan, tip1.length(), tip1.length() + productName.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

            String finalProductId = productId;

            if (!TextUtils.isEmpty(finalProductId)) {
                //停止扫描
                ProductInfo finalCurrentProduct = currentProduct;

                //当App升级弹窗显示--拦截当前设备搜索弹窗
                if (AppVersionService.getInstance().getAppVersionShowStatus()){
                    LogUtils.e(TAG,"AppVersionService is showing");
                    return;
                }

                homeViewModel.isScan = false;
                //crm弹出时，不弹出
                if (!isCrmDialogShow) {

                    BottomAlertDialog.show(getActivity().getSupportFragmentManager(), spannableString, imagUrl,
                            LanguageStrings.app_smartadddevice_cancel_button_text(),
                            LanguageStrings.app_smartadddevice_connect_button_text(),
                            new Consumer<Boolean>() {
                                @Override
                                public void accept(Boolean aBoolean) throws Exception {
                                    if (aBoolean) {
                                        //connect
                                        BluetoothService.getInstance().stopPostDevice();
                                        ArrayList<ProductInfo> productInfoArrayList = new ArrayList<>();
                                        productInfoArrayList.add(finalCurrentProduct);
                                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                                                .withSerializable(PARAMETER_DEVICD, productInfoArrayList)
                                                .withInt(KEY_FROM, R.id.deviceListFragment)
                                                .withInt(KEY_TO, R.id.deviceGuidePageOneFragment)
                                                .withInt(KEY_PREV_FRAGMENT, R.id.deviceListFragment)
                                                .navigation();

                                        TracePoints.addDeviceWithSmartScan(getContext(), UserInfo.get().getEmail(), finalCurrentProduct.getProductSnCode());
                                        traceSmartDialogConfirm();
                                    } else {
                                        //cancle
                                        BluetoothService.getInstance().noScanMacs.add(bleDevice.getMac());
                                        traceSmartDialogCancel();
                                    }
                                }
                            });
                }
            }
        }
    }


    public void addDevice(DeviceListUistate uistate) {
        BluetoothService.getInstance().stopPostDevice();
        sendAddButtonClick();

        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET).withBoolean(SHOW_ALERT_PERMISSION, true).navigation();
    }


    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

        BluetoothService.getInstance().stopPostDevice();
        if (getString(R.string.action_update).equals(String.valueOf(uiState))) {
            mViewModel.upDateDevice();
        } else if ((getString(R.string.action_control)).equals(String.valueOf(uiState))) {
            //欧洲crm需求 8/13 北美也需要弹窗配网
            if (isIotDevice(deviceInfo) && TextUtils.isEmpty(deviceInfo.getMac())) {
                ArrayList<DeviceInfo> arrayList = new ArrayList<>();
                arrayList.add(deviceInfo);
                if (!getCrmShowState()) {
                    isCrmDialogShow = true;
                    DialogUtil.simpleTipCheckDialog(requireActivity(), "", LanguageStrings.app_devicelist_alertmessage_textview_text(),
                            LanguageStrings.app_devicelist_alerttip_textview_text(), LanguageStrings.app_devicelist_alerttip_button_text(), new DialogUtil.OnConfirmedWithCheckbox() {
                                @Override
                                public void onClick(View v, boolean b) {
                                    setCrmShowState(b);
                                    goToGuidePage(arrayList);
                                }

                                @Override
                                public void onDismiss() {
                                    isCrmDialogShow = false;
                                }
                            });
                } else {
                    goToGuidePage(arrayList);
                }
                return;
            }
            LiveDataBus.get().with(PUBLISH_ERROR, DeviceInfo.class)
                    .observe(this, new Observer<DeviceInfo>() {

                        @Override
                        public void onChanged(@Nullable DeviceInfo deviceInfo1) {
                            if (!TextUtils.isEmpty(deviceInfo.getDeviceId()) && deviceInfo.getDeviceId().equalsIgnoreCase(deviceInfo1.getDeviceId())) {
                                mViewModel.getQuickControlStatusFromDb();
                            }
                        }
                    });

            quickControlTopic = String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceInfo.getDeviceId());
            AwsMqttService.subscribeTopic(quickControlTopic);
            mViewModel.controlDevice(deviceInfo);

        } else if ((getString(R.string.action_del)).equals(String.valueOf(uiState))) {
            ConfirmAlertDialog.initTrace("3", "10", "5", pageResouce + "_10", "1", "2");
            String title = EMPTY;
            String message = EMPTY;
            if(DEVICE_SHARE_TYPE_MAIN == deviceInfo.getShareType()) {
              title = LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text();
              message = LanguageStrings.app_sharedevice_dialog3_textview_text();
            } else if(DEVICE_SHARE_TYPE_SUB == deviceInfo.getShareType()) {
              title = LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text();
              message = LanguageStrings.app_sharedevice_dialog2_textview_text();
            } else {
              title = LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text();
              message = LanguageStrings.app_devicemoreinfo_deletedevicemessage_textview_text();
            }
            if (CommonUtils.is61001Device(deviceInfo.getCommodityModel()) && DEVICE_SHARE_TYPE_MAIN == deviceInfo.getShareType()) {
                DialogUtil.simpleConfirmDialog3(getContext(),
                        LanguageStrings.app_devicelist_deletedevicealerttitle_textview_text(),
                        LanguageStrings.app_deivcelist_resetpassword_textview_text(),
                        LanguageStrings.app_devicelist_deletedevicealertok_button_text(),
                        LanguageStrings.app_setting_clearcachecancle_button_text(), view -> {
                            //删除键置位normal
                            if (null == mAdapter) {
                                return;
                            }
                            resetPasswordDevice = deviceInfo;
                            mViewModel.resetDevicePassword(deviceInfo);
                        }, view -> {
                            LogUtils.e("");
                        },pageResouce);

            } else {
                DialogUtil.simpleConfirmDialog3(getContext(),
                        title,
                        message,
                        LanguageStrings.app_devicelist_deletedevicealertok_button_text(),
                        LanguageStrings.app_setting_clearcachecancle_button_text(),
                        view -> {
                            //删除键置位normal
                            if (null==mAdapter){
                                return;
                            }
                            mAdapter.setShowDel(false);
                            mViewModel.mEditState.postValue(true);

                            mViewModel.deleteDevice(deviceInfo);
                        }, view -> {
                            LogUtils.e("");
                        },pageResouce);

            }




        } else {
            //欧洲crm需求 8/13 北美也需要弹窗配网
            if (isIotDevice(deviceInfo) && TextUtils.isEmpty(deviceInfo.getMac())) {
                ArrayList<DeviceInfo> arrayList = new ArrayList<>();
                arrayList.add(deviceInfo);
                if (!getCrmShowState()) {
                    isCrmDialogShow = true;
                    DialogUtil.simpleTipCheckDialog(requireActivity(), "", LanguageStrings.app_devicelist_alertmessage_textview_text(),
                            LanguageStrings.app_devicelist_alerttip_textview_text(), LanguageStrings.app_devicelist_alerttip_button_text(), new DialogUtil.OnConfirmedWithCheckbox() {
                                @Override
                                public void onClick(View v, boolean b) {
                                    setCrmShowState(b);
                                    goToGuidePage(arrayList);
                                }

                                @Override
                                public void onDismiss() {
                                    isCrmDialogShow = false;
                                }
                            });
                } else {
                    goToGuidePage(arrayList);
                }
                return;
            }
            if (NOT_IOT_DEVICE.equals(deviceInfo.getProductType())) {
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL)
                        .withSerializable(KEY_PREV_DATA, deviceInfo)
                        .navigation();

            } else {
                if (mAdapter.isShowDel()) {
                    return;
                }

                //如果是网关子设备&通过DT脚连接
                if (GAT_WAY_SUB_DEVICE.equals(deviceInfo.getProductType())) {
                    if (COMMUNICATE_MODE_DT.equals(deviceInfo.getCommunicateMode())) {
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL).withSerializable(KEY_PREV_DATA, deviceInfo).navigation();
                        return;
                    }
                }

                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL).withSerializable(PARAMETER_DEVICD, deviceInfo).withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP).navigation();
            }
        }
    }

    /**
     * @return void
     * @method inMessageCenter
     * @description 进入消息中心页面
     * @date: 2022/8/30 11:32
     * @author: langmeng
     */
    public void inMessageCenter() {

        BluetoothService.getInstance().stopPostDevice();

        sendMessageButtonClick();
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_MESSAGE_CENTER).navigation();
    }

    @Override
    public void onItemClick(Object uiState) {

    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate baseUistate) {
        if (null == getActivity()) {
            return;
        }
        if (getActivity().isFinishing()) {
            return;
        }
        DeviceListUistate uiState = (DeviceListUistate) baseUistate;
        List<DeviceInfo> infoList = uiState.getInfoList();
        mViewDataBinding.srlCotainer.setRefreshing(false);

        if (uiState.getScanState() == HomeRepo.RESPONSE_SUCCESS_CAN_SCAN) {
            homeViewModel.isScan = true;
            mViewModel.getSmartScan(mViewModel.isSmartScan);
            mAdapter.setData(infoList);
        }

        if (uiState.getInfoList() != null && uiState.getInfoList().size() > 0) {
            if (!(mViewDataBinding.rvDeviceList.getAdapter() instanceof DevicesListAdapter)) {
                GridLayoutManager gridLayoutManager = new GridLayoutManager(this.getActivity(), 2, RecyclerView.VERTICAL, false);
                mViewDataBinding.rvDeviceList.setLayoutManager(gridLayoutManager);
                mAdapter = new DevicesListAdapter(this.getActivity(), this, uiState.getInfoList());
                mViewDataBinding.rvDeviceList.post(new Runnable() {
                    @Override
                    public void run() {
                        mViewDataBinding.rvDeviceList.setAdapter(mAdapter);
                    }
                });

                if (mEditDeviceListHelper != null) {
                    mEditDeviceListHelper.attatchRecyclerView(mViewDataBinding.rvDeviceList, gridLayoutManager, mAdapter, mViewModel);
                }

            }


            mViewDataBinding.setUiState(uiState);
            showDefaultState.set(false);

            mViewDataBinding.setController(DeviceListFragment.this);
            mViewModel.getLastMessage();
            mAdapter.setData(uiState.getInfoList());
            mViewDataBinding.rvDeviceList.post(new Runnable() {
                @Override
                public void run() {
                    if (mAdapter != null && mViewDataBinding.rvDeviceList.getScrollState() == RecyclerView.SCROLL_STATE_IDLE && (!mViewDataBinding.rvDeviceList.isComputingLayout())) {
                        mAdapter.notifyDataSetChanged();
                        mViewDataBinding.rvDeviceList.setVisibility(View.VISIBLE);
                    }

                }
            });


            mViewModel.subscribeDeviceTopic(uiState.getInfoList());
        } else if (uiState.getInfoList() != null && uiState.getInfoList().size() <= 0) {

            changeToDefaultAdapter();
            mViewModel.getLastMessage();

        }
        if (baseUistate.getState() == RESPONSE_FAIL) {
            if (!TextUtils.isEmpty(baseUistate.getMessage())) {
                ToastUtils.showLong(baseUistate.getMessage());
            }

        } else if (baseUistate.getState() == DEL_DEVIVE_SUCCESS) {
            mViewModel.reFreshDeviceList();

        } else if (baseUistate.getState() == ORDER_DEVICE_SUCCESS) {
            mViewModel.reFreshDeviceList();
        } else if (baseUistate.getState() == DEVICE_RESET_PASSWORD_SUCCESS) {
            mAdapter.setShowDel(false);
            mViewModel.mEditState.postValue(true);

            mViewModel.deleteDevice(resetPasswordDevice);
        } else if (baseUistate.getState() == DEVICE_RESET_PASSWORD_FAIL) {
            if (!TextUtils.isEmpty(baseUistate.getMessage())) {
                ToastUtils.showLong(baseUistate.getMessage());
            }

        }
    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return this;
    }


    private void initTrace() {
        stayEleId = "4";
        pageId = "9";
        mouduleId = "5";
        pageResouce = "1_9";
        nextButtoneleid = "3";
    }

    private void sendMessageButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    }

    private void sendAddButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "2", "1");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        homeViewModel = null;
        try {
            if (null != mAdapter) {
                mAdapter.onDestroy();
            }
            if (null != mDevicesListDefaultAdapter) {
                mDevicesListDefaultAdapter.onDestroy();
            }
            mViewModel.removeAllLiveData();
        } catch (Exception e) {
            LogUtils.e(TAG, "onDestroy is error:" + e.getMessage());
        }

    }

    @Override
    public void onDestroyView() {
        mViewDataBinding.srlCotainer.setOnLoadListener(null);
        mEditDeviceListHelper = null;
        if (null != mAdapter) {
            mAdapter.onDestroy();
        }
        mAdapter = null;
        mDevicesListDefaultAdapter = null;
        super.onDestroyView();
    }

    @Override
    public void onStop() {
        //EventBus 反注册
        if(EventBus.getDefault().isRegistered(this)) {
          EventBus.getDefault().unregister(this);
        }
        AwsMqttService.unsubscribeTopic(String.format(APP_KICK_NOTIFICATION, UserInfo.get().getId()));
        mViewModel.unsubscribeDeviceTopic(mAdapter.getData());
        LiveDataBus.get().with(PUBLISH_ERROR, DeviceInfo.class).removeObservers(this);
        super.onStop();
    }

    private static String getDeviceStateString(BleDevice bledevice) {
        String stateStr = BluetoothConection.getDIStringCode(bledevice.getScanRecord());

        if (TextUtils.isEmpty(stateStr)) {
            stateStr = "0";
        }
        //转bytes
        byte[] snCodeBytes = ConvertUtils.hexString2Bytes(stateStr);
        //deviceId string
        String state = ConvertUtils.bytes2String(snCodeBytes).replaceAll("\\n", "").replaceAll("\\u0000", "");
        return state;

    }

    private final String QUICK_SWITCH_KEY = "1";

    @Subscribe(threadMode = ThreadMode.BACKGROUND, sticky = true)
    public void onDeviceListDataEvent(IotModelEvent iotModelEvent) {
        String checkTopic = String.format(APP_KICK_NOTIFICATION, UserInfo.get().getId());
        if (iotModelEvent.topic.equals(checkTopic)) {
            mViewModel.reFreshDeviceList();
        } else {
            if (iotModelEvent.topic.contains(APP_MESSAGE_NOTIFICATION_PATH)) {
                mViewModel.getLastMessage();
            }
            List<DeviceInfo> deviceInfos = mAdapter.getData();
            for (int i = 0; deviceInfos != null && i < deviceInfos.size(); i++) {
                String deviceId = deviceInfos.get(i).getDeviceId();
                String shadowId = QUICK_CONTROL_BUTTON_STATUS;
                if (deviceInfos.get(i).getShortCutVos() != null && deviceInfos.get(i).getShortCutVos().size() > 0) {
                    shadowId = deviceInfos.get(i).getShortCutVos().get(0).getShadowId();
                }
                quickControlTopic = String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceId);
                if (iotModelEvent.topic.equals(quickControlTopic)) {
                    AwsUpdateBean awsUpdateBean = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), AwsUpdateBean.class);
                    HashMap<Object, Object> reported = awsUpdateBean.getState().getReported();
                    if (reported != null) {
                        Map<Object, Boolean> controlStatus = (Map<Object, Boolean>) reported.get(shadowId);
                        int quickControlStatus = TURN_OFF;
                        if (controlStatus != null && controlStatus.get(QUICK_SWITCH_KEY)) {
                            quickControlStatus = TURN_ON;
                        }
                        mViewModel.updateQuickControlStatus(deviceId, quickControlStatus);
                    }
                }
            }
        }
    }

    /**
     * 获取欧洲CRM弹框状态
     *
     * @return
     */
    private static final String CRM_EU = "crm_eu";
    private static final String NOT_SHOW_TO_BE_CONNECT = "not_show_to_be_connect";

    private boolean getCrmShowState() {
        SharedPreferences preferences = requireContext().getSharedPreferences(CRM_EU, Context.MODE_PRIVATE);
        return preferences.getBoolean(NOT_SHOW_TO_BE_CONNECT, false);
    }

    private void setCrmShowState(boolean state) {
        SharedPreferences preferences = requireContext().getSharedPreferences(CRM_EU, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(NOT_SHOW_TO_BE_CONNECT, state);
        editor.apply();
    }

    /**
     * 是否需要配网
     *
     * @param deviceInfo
     * @return
     */
    private boolean isIotDevice(DeviceInfo deviceInfo) {
        return !isZ6Device(deviceInfo.getDeviceId()) && !NOT_IOT_DEVICE.equals(deviceInfo.getProductType()) && !(GATEWAY_SUB_DEVICE.equals(deviceInfo.getProductType()) && COMMUNICATE_MODE_DT.equals(deviceInfo.getCommunicateMode()));
    }

    private void goToGuidePage(ArrayList<DeviceInfo> arrayList) {
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET).withInt(KEY_PREV_FRAGMENT, R.id.deviceListFragment).withInt(KEY_FROM, R.id.deviceListFragment).withInt(KEY_TO, R.id.deviceGuidePageOneFragment).withSerializable(PARAMETER_DEVICD, arrayList).navigation();
    }

    /**
     * 智能弹窗埋点-点击连接
     */
    private void traceSmartDialogConfirm() {

        String module_id = "9";
        String page_id = "9";
        String ele_id = "5";
        String mod_id = "0";
        String pageRes = pageResouce;
        sendClickTraceNew(this.getContext(), module_id, page_id, pageRes, ele_id, mod_id);
    }

    /**
     * 智能弹窗埋点-点击取消
     */
    private void traceSmartDialogCancel() {
        String module_id = "9";
        String page_id = "9";
        String ele_id = "6";
        String mod_id = "0";
        String pageRes = pageResouce;
        sendClickTraceNew(this.getContext(), module_id, page_id, pageRes, ele_id, mod_id);
    }

  @Subscribe(threadMode = ThreadMode.MAIN)
  public void onUpdate(ShareUpdateEvent event) {
    if(SHARE_OPERATE_TYPE_MAIN_REMOVE.equals(event.shareOperateType)) {
      mViewModel.reFreshDeviceList();
    }
    mViewModel.getLastMessage();
  }

}
