rootProject.name = "app"

apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':hermes-engine'
project(':hermes-engine').projectDir = new File(rootProject.projectDir, '../node_modules/hermes-engine/android/')
includeBuild('../node_modules/@react-native/gradle-plugin')

include ':app'
include ':lib_base'
include ':module_container'
include ':lib_network'
include ':lib_db'
include ':lib_router'
include ':lib_ota'
include ':lib_bluetooth'
include ':module_oobe'
include ':module_userCenter'
include ':module_messageCenter'
include ':module_deviceManage'
include ':module_configNet'
//include ':selector'
//include ':ucrop'
//include ':compress'
include ':FastBleLib'
include ':module_explore'
include ':android-pdf-viewer'
