package com.chervon.libOTA.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libOTA.ui.viewmodel.DeviceUpgradeResultViewModel.TRACE_DONE_CLICK;
import static com.chervon.libOTA.ui.viewmodel.DeviceUpgradeResultViewModel.TRACE_RETRY_CLICK;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.request.UpgradeConfirmReq;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libNetwork.http.model.result.UpgradeConfirmRes;
import com.chervon.libOTA.R;
import com.chervon.libOTA.databinding.ActivityDeviceUpgradeResultBinding;
import com.chervon.libOTA.ui.viewmodel.DeviceUpgradeResultViewModel;
import com.chervon.libRouter.RouterConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.libOTA.ui
 * @ClassName: DeviceUpgradeResultActivity
 * @Description: 设备固件升级结果界面
 * @Author: langmeng
 * @CreateDate: 2022/7/19 10:38
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/19 10:38
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE_RESULT)
public class DeviceUpgradeResultActivity extends BaseActivity<DeviceUpgradeResultViewModel> {

  private static final String TAG = "DeviceUpgradeResultActivity";
  public static String STATE_KEY = "state";
  public static int STATE_SUCCESS = 0;
  public static int STATE_FAIL = 1;
  @Autowired
  public int state = 0;
  @Autowired
  public String deviceId;
  @Autowired
  public String jobId;
  @Autowired
  public String customVersion;
  private ActivityDeviceUpgradeResultBinding binding;

  public DeviceInfo deviceInfo;
  @Autowired
  public int upgradeModel = 0;
  //是否是单MCU升级
  @Autowired
  public boolean singleMcu = false;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {

    ARouter.getInstance().inject(this);

    super.onCreate(savedInstanceState);
    if (!TextUtils.isEmpty(deviceId)){
      deviceInfo = SoftRoomDatabase.getDatabase(this).deviceDao().getDeviceById(deviceId);
    }
  }

  @Override
  protected void onUnRegister() {
  }

  @Override
  protected void onRegister() {
    ARouter.getInstance().inject(this);
  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.activity_device_upgrade_result;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (ActivityDeviceUpgradeResultBinding) viewDataBinding;
  }

  @SuppressLint({"StringFormatInvalid", "LocalSuppress"})
  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_OTA_equipmentupgrade_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          mViewModel.back();
          finish();

        }
      }));
    mViewModel.traceClick.observe(this, new Observer<Integer>() {
      @Override
      public void onChanged(Integer value) {
        if (value.equals(TRACE_DONE_CLICK)) {
          sendCompleteBottonClick();
        } else if (value.equals(TRACE_RETRY_CLICK)) {
          sendRetryBottonClick();

          ArrayList<DeviceInfo> list = new ArrayList<DeviceInfo>();
          if (deviceInfo != null) {
            list.add(deviceInfo);
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
                    .withSerializable(PARAMETER_DEVICD, list)
                    .withInt(KEY_FROM, R.id.activity_device_upgrade)
                    .withInt(KEY_TO, R.id.deviceGuidePageOneFragment)
                    .withString("deviceId", deviceId)
                    .withString("jobId", jobId)
                    .withInt("upgradeModel", upgradeModel)
                    .withBoolean("singleMcu", singleMcu)
                    .navigation();
          }

          ActivityUtils.finishActivity(ActivityUtils.getTopActivity());
        }
      }
    });

    mViewModel.setFragmentManager(getSupportFragmentManager());

    mViewModel.stateValue.set(state);

    binding.setDeviceUpgradeResultViewModel(mViewModel);

    //subtitle
    if (state == STATE_SUCCESS) {
      binding.activityDeviceUpgradeResultSubtip.setText(LanguageStrings.app_OTA_newversion_textview_text() + " " + customVersion);
    } else {
      binding.activityDeviceUpgradeResultSubtip.setText(LanguageStrings.app_OTA_failedinfo_textview_text());
    }

    deviceUpgradeConfirm();

  }

  /**
   * App端确认设备升级
   */
  private void deviceUpgradeConfirm() {
    List<Long> list = new ArrayList<>();
    list.add(Long.parseLong(jobId));
    UpgradeConfirmReq upgradeConfirmReq = new UpgradeConfirmReq(list, deviceId, UserInfo.get().getId());
    ApiService.instance().upgradeConfirm(upgradeConfirmReq).subscribe(new HttpObserver<HttpResponse<UpgradeConfirmRes>>() {
      @Override
      protected void Next(HttpResponse<UpgradeConfirmRes> entity) {
        if (entity.status) {
          LogUtils.e(TAG, "deviceUpgradeConfirm success");
        }
      }

      @Override
      public void Error(ErrorBean errorBean) {
        super.Error(errorBean);
      }
    });
  }
  @Override
  protected void sendBackTrace() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, "1", "1");
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
  }

  @Override
  protected Class<? extends DeviceUpgradeResultViewModel> getViewModelClass() {
    return DeviceUpgradeResultViewModel.class;
  }

  @Override
  public void onBackPressed() {
    sendBackTrace();
    mViewModel.back();
    return;
  }


  private void initTrace() {
    stayEleId = "4";
    pageId = "64";
    mouduleId = "7";
    nextButtoneleid = "2";
  }

  public void sendCompleteBottonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendRetryBottonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, "3", "1");
  }


  @Override
  public boolean onKeyDown(int keyCode, KeyEvent event) {
    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
      sendBackTrace();
      mViewModel.back();
    }
    return false;

  }
}
