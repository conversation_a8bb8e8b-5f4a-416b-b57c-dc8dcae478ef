package com.chervon.libOTA.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ConvertUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.ui.dialog.UpgradeConfirmAlertDialog;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libBase.model.OTACheckBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libOTA.R;
import com.chervon.libOTA.databinding.ActivityMountDeviceUpgradeBinding;
import com.chervon.libOTA.ui.viewmodel.DeviceMountUpgradeViewModel;
import com.chervon.libRouter.RouterConstants;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libOTA.ui
 * @ClassName: DeviceMountUpgradeActivity
 * @Description: 挂载设备升级页面
 * @Author: langmeng
 * @CreateDate: 2022/9/7 16:26
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/7 16:26
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_MOUNT_UPGRADE)
public class DeviceMountUpgradeActivity extends BaseActivity<DeviceMountUpgradeViewModel> {

    private ActivityMountDeviceUpgradeBinding binding;
    @Autowired
    public String deviceId;
    //是否是单MCU升级
    @Autowired
    public boolean singleMcu;
    //升级模式， 0 网络升级 1 BLE升级
    @Autowired
    public int upgradeModel;
    //是否挂载设备
    @Autowired
    public boolean mountDevice;
    private LoadingDialog loadingDialog;
    private OTACheckBean otaCheckBean;
  private UpgradeConfirmAlertDialog upgradeDialog;

  @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);

        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onUnRegister() {


    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onResume() {
        super.onResume();
        EventBus.getDefault().register(this);

        //订阅任务详情
        AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId));
        String json = "{\n" +
                "  \"componentMap\" : {\n" +
                "    \"component001\" : \"3.2.0.1\",\n" +
                "    \"component002\" : \"2.1.5.6\"\n" +
                "  },\n" +
                "  \"singleMcu\" : true,\n" +
                "  \"deviceId\" : \"XPS012215444269\"\n" +
                "}";
        //获取任务详情
        AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_CHECK_TOPIC, deviceId, json));
    }

    @Override
    protected void onPause() {
        super.onPause();
        EventBus.getDefault().unregister(this);
        AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId));
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.activity_mount_device_upgrade;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (ActivityMountDeviceUpgradeBinding) viewDataBinding;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        loadingDialog = LoadingDialog.showDialog(getSupportFragmentManager());

        mViewModel.toolbarData.setValue(new ToolbarData(
                LanguageStrings.app_OTA_upgrade_textview_text(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        if (otaCheckBean!=null&&otaCheckBean.getUpgradeMode() == 1) {

                          if(upgradeDialog==null){
                            upgradeDialog=  UpgradeConfirmAlertDialog.showAndReturn(getSupportFragmentManager(),
                              LanguageStrings.app_OTAInfo_cancelUpgrade_textview_text(),
                              LanguageStrings.app_OTAInfo_cancel_button_text(),
                              LanguageStrings.app_OTA_upgrade_textview_text(),
                              getResources().getColor(com.chervon.libBase.R.color.colorButtonNormal), new Consumer<Boolean>() {
                                @Override
                                public void accept(Boolean aBoolean) throws Exception {

                                  if (aBoolean) {

                                  } else {
                                    finish();
                                  }
                                }
                              });
                          }else{
                            upgradeDialog.getDialog().show();
                          }

                        } else {
                            finish();
                        }
                    }
                }));

        mViewModel.setFragmentManager(getSupportFragmentManager());

        mViewModel.setDeviceId(deviceId);
        binding.setDeviceMountUpgradeViewModel(mViewModel);

        initDeviceInfo();
    }

    @Override
    protected Class<? extends DeviceMountUpgradeViewModel> getViewModelClass() {
        return DeviceMountUpgradeViewModel.class;
    }

    /**
     * @method initDeviceInfo
     * @description 初始化设备图片/名称/版本信息
     * @date: 2022/8/12 9:59
     * @author: langmeng
     * @return void
     */
    private void initDeviceInfo() {
        SoftRoomDatabase database = SoftRoomDatabase.getDatabase(this);
        List<ProductInfo> products = database.productDao().getProducts();
        List<DeviceInfo> devices = database.deviceDao().getDevices();

        if (devices != null && products != null && deviceId != null) {

            DeviceInfo deviceInfo = null;
            ProductInfo productInfo = null;

            for (DeviceInfo device : devices) {
                if (device.getDeviceId().equals(deviceId)) {
                    deviceInfo = device;
                }
            }

            for (ProductInfo pinfo : products) {
                assert deviceInfo != null;
                if (deviceInfo.getProductId().toString().equals(pinfo.getId())) {
                    productInfo = pinfo;
                }
            }

            if (productInfo != null) {
              if(!this.isFinishing()) {
                Glide.with(this).load(productInfo.getProductIcon()).into(binding.activityMountDeviceUpgradeImage);
              }
                binding.activityMountDeviceUpgradeName.setText(productInfo.getProductName());
            }
        }
    }

    @Override
    public void onBackPressed() {
      sendClickTrace(this,mouduleId,pageId,pageResouce, "1","1");
        //强制升级
        if (otaCheckBean!=null&&otaCheckBean.getUpgradeMode() == 1) {
          if(upgradeDialog==null){
            upgradeDialog=  UpgradeConfirmAlertDialog.showAndReturn(getSupportFragmentManager(),
              LanguageStrings.app_OTAInfo_cancelUpgrade_textview_text(),
              LanguageStrings.app_OTAInfo_cancel_button_text(),
              LanguageStrings.app_OTA_upgrade_textview_text(),
              getResources().getColor(com.chervon.libBase.R.color.colorButtonNormal), new Consumer<Boolean>() {
                @Override
                public void accept(Boolean aBoolean) throws Exception {

                  if (aBoolean) {

                  } else {
                    finish();
                  }
                }
              });
          }else{
            upgradeDialog.getDialog().show();
          }

        } else {
            finish();
        }
        return;
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onOTADataEvent(IotModelEvent iotModelEvent) {
        String checkTopic = String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId);
        if (iotModelEvent.topic.equals(checkTopic)) {
            otaCheckBean = new Gson().fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), OTACheckBean.class);
            mViewModel.jobCheckData.setValue(otaCheckBean);
            loadingDialog.dismiss();
        }
    }



  private void initTrace() {
    stayEleId="3";
    pageId="40";
    mouduleId="7";
    pageResouce="1_9_40";
    nextButtoneleid ="2";
  }

  public void sendBottonClick() {
    sendClickTrace(this,mouduleId,pageId,pageResouce, nextButtoneleid,"1");
  }

}
