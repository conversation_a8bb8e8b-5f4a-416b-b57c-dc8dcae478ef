package com.chervon.libOTA.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.ui.dialog.UpgradeConfirmAlertDialog;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libBase.model.OTACheckBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libNetwork.iot.data.OTACheckRequest;
import com.chervon.libOTA.R;
import com.chervon.libOTA.databinding.ActivityDeviceUpgradeBinding;
import com.chervon.libOTA.event.OTABackEvent;
import com.chervon.libOTA.ui.viewmodel.DeviceUpgradeViewModel;
import com.chervon.libRouter.RouterConstants;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;
import java.util.List;

import io.reactivex.functions.Consumer;
import com.chervon.libBase.R.color;
/**
 * @ProjectName: app
 * @Package: com.chervon.libOTA.ui.viewmodel
 * @ClassName: DeviceUpdateViewModel
 * @Description: 设备固件升级操作界面
 * @Author: langmeng
 * @CreateDate: 2022/7/18 16:03
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/18 16:03
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE)
public class DeviceUpgradeActivity extends BaseActivity<DeviceUpgradeViewModel> {

  private static final String TAG = "DeviceUpgradeActivity";
  private ActivityDeviceUpgradeBinding binding;
  @Autowired
  public String deviceId;
  //是否是单MCU升级
  @Autowired
  public boolean singleMcu = false;
  //升级模式， 0 网络升级 1 BLE升级
  @Autowired
  public int upgradeModel;
  //是否挂载设备
  @Autowired
  public boolean mountDevice;
  private WeakReference<LoadingDialog> loadingDialogWeakReference;

  private OTACheckBean otaCheckBean;
  private UpgradeConfirmAlertDialog upgradeDialog;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    ARouter.getInstance().inject(this);
    super.onCreate(savedInstanceState);
  }

  @Override
  protected void onUnRegister() {


  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onResume() {
    super.onResume();
    requestNewDeviceInfo();
    EventBus.getDefault().register(this);
    //订阅任务详情
    AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC_V2,AwsMqttService.CLIENT_NAME, deviceId));


    OTACheckRequest otaCheckRequest = new OTACheckRequest();
    otaCheckRequest.setDeviceId(deviceId);
    //是否是单MCU升级
    otaCheckRequest.setSingleMcu(singleMcu);
    //设置语言
    otaCheckRequest.setMessageId(Utils.getMessageId());
    otaCheckRequest.setLang(MyLang.getCurrentLanguageCode());
    String json = GsonUtils.toJson(otaCheckRequest);

    //TODO ota新版本修改
    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_CHECK_TOPIC,AwsMqttService.CLIENT_NAME, deviceId), json);

  }

  @Override
  protected void onPause() {
    super.onPause();
    EventBus.getDefault().unregister(this);
    AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC,
      AwsMqttService.CLIENT_NAME, deviceId));
  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.activity_device_upgrade;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (ActivityDeviceUpgradeBinding) viewDataBinding;
    //默认隐藏
    binding.activityDeviceUpgradeButton.setVisibility(View.GONE);
    //隐藏最新版本号和relase node
    binding.activityDeviceUpgradeLatestVersionLayout.setVisibility(View.GONE);
    binding.activityDeviceUpgradeContentTitle.setVisibility(View.GONE);
    binding.activityDeviceUpgradeRelaseNode.setVisibility(View.GONE);
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));

    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_OTA_upgrade_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendClickTrace(DeviceUpgradeActivity.this, mouduleId, pageId, pageResouce, "1", "1");
          if (otaCheckBean != null && otaCheckBean.getUpgradeMode() == 1) {
            if (upgradeDialog == null) {
              upgradeDialog = UpgradeConfirmAlertDialog.showAndReturn(getSupportFragmentManager(),
                LanguageStrings.app_OTAInfo_cancelUpgrade_textview_text(),
                LanguageStrings.app_OTAInfo_cancel_button_text(),
                LanguageStrings.app_OTA_upgrade_textview_text(),
                getResources().getColor(R.color.colorButtonNormal), new Consumer<Boolean>() {
                  @Override
                  public void accept(Boolean aBoolean) throws Exception {
                    if (aBoolean) {

                    } else {
                      finish();
                    }
                  }
                });
            } else {
              if (null!=upgradeDialog.getDialog()){
                upgradeDialog.getDialog().show();
              }
            }
          } else {
            finish();
          }
        }
      }));

    mViewModel.updateButtonClick.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          sendBottonClick();
        }

      }
    });
    mViewModel.historyButtonClick.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          sendHistoryBottonClick();
        }

      }
    });

    mViewModel.setFragmentManager(getSupportFragmentManager());

    mViewModel.setDeviceId(deviceId);
    mViewModel.setUpgradeModel(upgradeModel);
    mViewModel.setSingleMcu(singleMcu);

    binding.setDeviceUpgradeViewModel(mViewModel);

    initDeviceInfo(null);

  }

  @Override
  protected Class<? extends DeviceUpgradeViewModel> getViewModelClass() {
    return DeviceUpgradeViewModel.class;
  }

  /**
   * @return void
   * @method initDeviceInfo
   * @description 初始化设备图片/名称/版本信息
   * @date: 2022/8/12 9:59
   * @author: langmeng
   */
  private void initDeviceInfo(DeviceInfo object) {
    SoftRoomDatabase database = SoftRoomDatabase.getDatabase(this);
    List<ProductInfo> products = database.productDao().getProducts();
    List<DeviceInfo> devices = database.deviceDao().getDevices();
    if (devices != null && products != null && deviceId != null) {
      DeviceInfo deviceInfo = null;
      if (object != null) {
        deviceInfo = object;
        database.deviceDao().save(deviceInfo);
      } else {
        for (DeviceInfo device : devices) {
          if (device.getDeviceId().equals(deviceId)) {
            deviceInfo = device;
          }
        }
      }

      if (deviceInfo != null) {

        if (!this.isFinishing()) {
          Glide.with(this).load(deviceInfo.getDeviceIcon()).placeholder(R.color.colorDivider).into(binding.activityDeviceUpgradeImage);
        }

        binding.activityDeviceUpgradeName.setText(deviceInfo.getNickName());
        if (TextUtils.isEmpty(deviceInfo.getNickName())) {
          binding.activityDeviceUpgradeName.setText(deviceInfo.getDeviceName());
        }
        mViewModel.currentVersion.set(TextUtils.isEmpty(deviceInfo.getVersion())?"--":deviceInfo.getVersion());
      } else {
        requestNewDeviceInfo();
      }

      if (deviceInfo != null) {
        mViewModel.setDeviceInfo(deviceInfo);
      }
    }
  }

  private void requestNewDeviceInfo() {
    ApiService.instance().getDeviceInfo(deviceId).subscribe(new HttpObserver<HttpResponse<DeviceInfo>>() {
      @Override
      protected void Next(HttpResponse<DeviceInfo> entity) {

        initDeviceInfo(entity.response);

      }

      @Override
      public void Error(ErrorBean errorBean) {
        super.Error(errorBean);

      }
    });
  }

  @Override
  public void onBackPressed() {
    sendClickTrace(DeviceUpgradeActivity.this, mouduleId, pageId, pageResouce, "1", "1");

    //强制升级
    if (otaCheckBean != null && otaCheckBean.getUpgradeMode() == 1) {
      if (upgradeDialog == null) {
        upgradeDialog = UpgradeConfirmAlertDialog.showAndReturn(getSupportFragmentManager(),
          LanguageStrings.app_OTAInfo_cancelUpgrade_textview_text(),
          LanguageStrings.app_OTAInfo_cancel_button_text(),
          LanguageStrings.app_OTA_upgrade_textview_text(),
          getResources().getColor(R.color.colorButtonNormal), new Consumer<Boolean>() {
            @Override
            public void accept(Boolean aBoolean) throws Exception {
              if (aBoolean) {

              } else {
                //通知RN
                EventBus.getDefault().postSticky(new OTABackEvent());
                finish();
              }
            }
          });
      } else {

        upgradeDialog.getDialog().show();
      }

    } else {
      finish();
    }
    return;
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
//        EventBus.getDefault().postSticky(new OTABackEvent());
    if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
      loadingDialogWeakReference.get().dismiss();
    }
    if (upgradeDialog != null) {
      upgradeDialog.dismiss();
      upgradeDialog = null;
    }


  }

  @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
  public void onOTADataEvent(IotModelEvent iotModelEvent) {
//    String checkTopic = String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId);
//    if (iotModelEvent.topic.equals(checkTopic)) {
    // String checkTopic = String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId);
    String checkTopicV2 =   String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC_V2,AwsMqttService.CLIENT_NAME, deviceId);

    if (iotModelEvent.topic.equals(checkTopicV2)) {
      otaCheckBean = new Gson().fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), OTACheckBean.class);
      if (otaCheckBean.getPackages() != null && otaCheckBean.getPackages().size() > 0) {
        ((BaseApplication) this.getApplication()).setOTACheckBean(otaCheckBean);
        mViewModel.setJobId(otaCheckBean.getJobIds().get(otaCheckBean.getJobIds().size() - 1));
        if (mViewModel.jobCheckData.getValue() == null) {
          mViewModel.jobCheckData.setValue(otaCheckBean);
          mViewModel.lastVersion.set(otaCheckBean.getCustomVersion());
          otaCheckBean.setRemark(Utils.exchangeString(otaCheckBean.getRemark()));
          mViewModel.jobCheckData.setValue(otaCheckBean);
        }
        //如果有升级任务则显示升级内容
        binding.activityDeviceUpgradeButton.setVisibility(View.VISIBLE);
        binding.activityDeviceUpgradeLatestVersionLayout.setVisibility(View.VISIBLE);
        binding.activityDeviceUpgradeContentTitle.setVisibility(View.VISIBLE);
        binding.activityDeviceUpgradeRelaseNode.setVisibility(View.VISIBLE);
      } else {
        //如果没有升级包

      }


      if (loadingDialogWeakReference.get() != null) {
        loadingDialogWeakReference.get().dismiss();
      }

    }
  }


  private void initTrace() {
    stayEleId = "4";
    pageId = "41";
    mouduleId = "7";
    pageResouce = "1_9_41";
    nextButtoneleid = "2";
  }

  public void sendBottonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendHistoryBottonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
  }
}
