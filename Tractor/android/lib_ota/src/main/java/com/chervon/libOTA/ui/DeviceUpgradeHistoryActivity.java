package com.chervon.libOTA.ui;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BR;
import com.chervon.libBase.BaseDataBindingAdapter;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.model.request.UpgradHistoryRequest;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libNetwork.http.model.result.UpgradHistoryBean;
import com.chervon.libOTA.R;
import com.chervon.libOTA.data.UpgradeHistoryData;
import com.chervon.libOTA.databinding.ActivityDeviceUpgradeHistoryBinding;
import com.chervon.libOTA.ui.viewmodel.DeviceUpgradeHistoryViewModel;
import com.chervon.libRouter.RouterConstants;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;


/**
 * @ProjectName: app
 * @Package: com.chervon.libOTA.ui
 * @ClassName: DeviceUpgradeHistoryActivity
 * @Description: 设备升级历史
 * @Author: langmeng
 * @CreateDate: 2022/7/18 19:26
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/18 19:26
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE_HISTORY)
public class DeviceUpgradeHistoryActivity extends BaseActivity<DeviceUpgradeHistoryViewModel> {

  private static final String TAG = "DeviceUpgradeHistoryAct";
  private ActivityDeviceUpgradeHistoryBinding binding;
  private ArrayList<UpgradeHistoryData> dataList = new ArrayList<>();
  @Autowired
  public String deviceId;
  @Autowired
  public String jobId;

  @Autowired
  public String commodityModel;

  private BaseDataBindingAdapter upgradHistoryListAdapter;

  private WeakReference<LoadingDialog> loadingDialogWeakReference;

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    ARouter.getInstance().inject(this);

    super.onCreate(savedInstanceState);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.activity_device_upgrade_history;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (ActivityDeviceUpgradeHistoryBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_OTAhistory_upgradehistory_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));

    mViewModel.setFragmentManager(getSupportFragmentManager());

    binding.setDeviceUpgradeHistoryViewModel(mViewModel);

    initAdapter();

    initDeviceInfo(null);

    initUpgradeHistory();
  }

  /**
   * @return void
   * @method initUpgradeHistory
   * @description 初始化升级历史列表
   * @date: 2022/8/12 9:59
   * @author: langmeng
   */
  private void initUpgradeHistory() {


    ApiService.instance().getUpgradHistory(new UpgradHistoryRequest(deviceId))
      .subscribe(new HttpObserver<UpgradHistoryBean>() {
        @Override
        protected void Next(UpgradHistoryBean entity) {

          for (UpgradHistoryBean.EntryDTO entryDTO : entity.getEntry()) {
            String yyyy = TimeUtils.millis2String(Long.parseLong(entryDTO.getUpgradeTime()), "yyyy");
            String mm = TimeUtils.millis2String(Long.parseLong(entryDTO.getUpgradeTime()), "MM");
            String dd = TimeUtils.millis2String(Long.parseLong(entryDTO.getUpgradeTime()), "dd");
            String hm = TimeUtils.millis2String(Long.parseLong(entryDTO.getUpgradeTime()), "HH:mm");

            String date = mm + "/" + dd + "/" + yyyy;

            String content = (TextUtils.isEmpty(commodityModel)?"":commodityModel)+" "+
              LanguageStrings.app_OTAhistory_versionhistoryfirst_textview_text()
              + " " + entryDTO.getOldVersion()
              + " " +LanguageStrings.app_OTAhistory_versionhistorysecond_textview_text()
              + " " + entryDTO.getNewVersion();
            dataList.add(new UpgradeHistoryData(date, hm, content, entryDTO.getContent()));
          }

          upgradHistoryListAdapter.notifyDataSetChanged();

          if (loadingDialogWeakReference.get() != null) {
            loadingDialogWeakReference.get().dismiss();
          }

        }

        @Override
        public void Error(ErrorBean errorBean) {
          super.Error(errorBean);
          if (loadingDialogWeakReference.get() != null) {
            loadingDialogWeakReference.get().dismiss();
          }
          String errorMessage = errorBean.getErrorMsg();

          if (!TextUtils.isEmpty(errorMessage)){
            errorFun(errorMessage);
          }
        }
      });
  }

  /**
   * @return void
   * @method initDeviceInfo
   * @description 初始化设备图片/名称/版本信息
   * @date: 2022/8/12 9:59
   * @author: langmeng
   */
  private void initDeviceInfo(DeviceInfo entity) {
    SoftRoomDatabase database = SoftRoomDatabase.getDatabase(this);
    List<ProductInfo> products = database.productDao().getProducts();
    List<DeviceInfo> devices = database.deviceDao().getDevices();

    if (devices != null && products != null && deviceId != null) {

      DeviceInfo deviceInfo = null;
      ProductInfo productInfo = null;
      if (entity != null) {
        deviceInfo = entity;
      } else {
        for (DeviceInfo device : devices) {
          if (device.getDeviceId().equals(deviceId)) {
            deviceInfo = device;
          }
        }
      }


      //设置icon
      if (deviceInfo != null) {
        if (!this.isFinishing()) {
          Glide.with(this).load(deviceInfo.getDeviceIcon()).placeholder(R.color.colorDivider).into(binding.activityDeviceUpgradeHistoryImage);
        }
      } else {
        ApiService.instance().getDeviceInfo(deviceId).subscribe(new HttpObserver<HttpResponse<DeviceInfo>>() {
          @Override
          protected void Next(HttpResponse<DeviceInfo> entity) {

            initDeviceInfo(entity.response);

          }

          @Override
          public void Error(ErrorBean errorBean) {
            super.Error(errorBean);
            if (loadingDialogWeakReference.get() != null) {
              loadingDialogWeakReference.get().dismiss();
            }

            String errorMessage = errorBean.getErrorMsg();

            if (!TextUtils.isEmpty(errorMessage)){
              errorFun(errorMessage);
            }
          }
        });
        return;
      }

      //设置name

      binding.activityDeviceUpgradeHistoryName.setText(deviceInfo.getNickName());
      if (TextUtils.isEmpty(deviceInfo.getNickName())) {
        binding.activityDeviceUpgradeHistoryName.setText(deviceInfo.getDeviceName());
      }

      //设置版本号
      mViewModel.currentVersion.set(TextUtils.isEmpty(deviceInfo.getVersion())?"--":deviceInfo.getVersion());

    } else {
      if (loadingDialogWeakReference.get() != null) {
        loadingDialogWeakReference.get().dismiss();
      }

    }
  }

  private void errorFun(String msg) {
    if (loadingDialogWeakReference.get() != null) {
      loadingDialogWeakReference.get().dismiss();
    }

    LogUtils.eTag(TAG, "upgrade history error :" + msg);
    ToastUtils.showShort(msg);
  }

  /**
   * @return void
   * @method initAdapter
   * @description init adapter
   * @date: 2022/7/18 19:26
   * @author: LangMeng
   */
  private void initAdapter() {
    upgradHistoryListAdapter = new BaseDataBindingAdapter(
      this,
      R.layout.device_upgrade_history_item,
      dataList,
      BR.upgradeHistoryData,
      new BaseDataBindingAdapter.itemCallBack() {
        @Override
        public void onBindViewHolder(BaseDataBindingAdapter.ViewHolder holder,
                                     int position,
                                     RecyclerView.Adapter<BaseDataBindingAdapter.ViewHolder> adapter) {
        }
      });

    binding.activityDeviceUpgradeHistoryRecycler.setAdapter(upgradHistoryListAdapter);
    binding.activityDeviceUpgradeHistoryRecycler.setLayoutManager(new LinearLayoutManager(this));

    upgradHistoryListAdapter.notifyDataSetChanged();
  }

  @Override
  protected Class<? extends DeviceUpgradeHistoryViewModel> getViewModelClass() {
    return DeviceUpgradeHistoryViewModel.class;
  }


  private void initTrace() {
    stayEleId = "2";
    pageId = "42";
    mouduleId = "7";
    nextButtoneleid = "2";
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();

    if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
      loadingDialogWeakReference.get().dismiss();
    }

  }
}
