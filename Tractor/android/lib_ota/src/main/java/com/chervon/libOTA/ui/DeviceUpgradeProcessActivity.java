package com.chervon.libOTA.ui;

import static com.chervon.libBase.model.AppConstants.DEVICE_MODULE;
import static com.chervon.libBase.ui.widget.ProgressHelper.isMainThread;
import static com.chervon.libBase.utils.Utils.getMessageId;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libOTA.data.api.OtaRepo.RESPONSE_SUCCESS;
import static com.chervon.libOTA.event.ExitDialogUiState.SHOW_NORMAL;

import android.bluetooth.BluetoothGatt;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.FileHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libBluetooth.BlueToothUtilListener;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libBluetooth.data.UpgradPackageData;
import com.chervon.libBluetooth.data.UpgradStateData;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.configs.DeviceUpgradeProcessModel;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libNetwork.iot.data.AwsUpdateBean;
import com.chervon.libBase.model.OTACheckBean;
import com.chervon.libNetwork.iot.data.OTACheckRequest;
import com.chervon.libNetwork.iot.data.OTAPackageUrlBean;
import com.chervon.libNetwork.iot.data.OTAUpdateFirmwareUpgradeStateBean;
import com.chervon.libNetwork.iot.data.OTAUpdateUpgradeStateBean;
import com.chervon.libOTA.R;
import com.chervon.libOTA.data.FirmwareVersionData;
import com.chervon.libOTA.data.RUpgradeProcessData;
import com.chervon.libBase.model.UpdateAllow;
import com.chervon.libOTA.data.model.OTAProgress;
import com.chervon.libOTA.data.model.OtaProcessInfo;
import com.chervon.libOTA.databinding.ActivityDeviceUpgradeProcessBinding;
import com.chervon.libOTA.event.ExitDialogUiState;
import com.chervon.libOTA.ui.viewmodel.DeviceUpgradeProcessViewModel;
import com.chervon.libOTA.ui.viewmodel.OtaUpdateProcessUistate;
import com.chervon.libRouter.RouterConstants;
import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

/**
 * @ProjectName: app
 * @Package: com.chervon.libOTA.ui
 * @ClassName: DeviceUpgradeProcessActivity
 * @Description: 设备固件升级过程界面
 * @Author: langmeng
 * @CreateDate: 2022/7/19 10:38
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/19 10:38
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE_PROCESS)
public class DeviceUpgradeProcessActivity extends BaseActivity<DeviceUpgradeProcessViewModel> implements BlueToothUtilListener {

    public static final String BLE_MODULE = "bleModule";
    private CountDownTimer mDownTimer;
    private static final String TAG = "DeviceUpgradeProcessActivity";
    private ActivityDeviceUpgradeProcessBinding binding;
    @Autowired
    public String deviceId;
    @Autowired
    public String jobId;
    //升级模式 0 IOT升级 1 蓝牙升级
    @Autowired
    public String deviceType;
    @Autowired
    public int upgradeModel = 0;
    //是否是单MCU升级
    @Autowired
    public boolean singleMcu = false;

    @Autowired
    public boolean isGuidePageEmpty = true;
    @Autowired
    public DeviceInfo deviceInfo;

    boolean isBleConnecting = false;

    //配置分片大小
    private static int BLOCK_SIZE = 200;
    private int blockNum = 0;
    private int fragementNum = 0;
    private OTACheckBean otaCheckBean;
    //是否升级完成 0 升级中  1 升级完成 2升级失败 3蓝牙模组升级中 4 设备开始升级
    private int UpgradeState = 0;
    private BleDevice bleDevice;
    private MutableLiveData<Integer> progressLiveData = new MutableLiveData<>();
    private ArrayList<Call> calls;

    private OTAProgress otaUpdateFirmwareUpgradeState;

    private String mCustomVersion = "";

    private boolean upgrade = false;

    //设备总成版本集
    private ArrayList<FirmwareVersionData> firmwareVersionDatas;
    String activityName = "com.chervon.libOTA.ui.DeviceUpgradeProcessActivity";

    private final String BLE_MODEL = "ble";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);
        super.onCreate(savedInstanceState);
        BaseApplication.getInstance().resetUpgradeComplete();
    }

    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {
        mViewModel.showExitDialog.observe(this, new Observer<ExitDialogUiState>() {
            @Override
            public void onChanged(ExitDialogUiState uiState) {
                if (uiState.show && uiState.status == ExitDialogUiState.CLICK_DONE) {
                    sendButtonClick();
                    BluetoothConection.getInstance().setmBleGattCallback(null);
                    finish();
                } else if (uiState.show && uiState.status == ExitDialogUiState.CLICK_CANCEL) {
                    uiState.show = false;
                    uiState.status = SHOW_NORMAL;
                    mViewModel.showExitDialog.postValue(uiState);
                }
            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_GET_PACKAGE_URL_ACCEPTED_TOPIC, deviceId));
        LogUtils.d(TAG, "subscribeTopicOTA_GET_PACKAGE_URL_ACCEPTED_TOPIC" + String.format(AwsMqttService.OTA_GET_PACKAGE_URL_ACCEPTED_TOPIC, deviceId));

        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        OTACheckRequest otaCheckRequest = new OTACheckRequest();
        otaCheckRequest.setDeviceId(deviceId);
        //是否是单MCU升级
        otaCheckRequest.setSingleMcu(singleMcu);
        //设置语言
        otaCheckRequest.setLang(MyLang.getCurrentLanguageCode());

        ArrayList<String> packageKeys = new ArrayList<>();
        otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
        if (otaCheckBean != null) {
            mCustomVersion = otaCheckBean.getCustomVersion();


            for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
                packageKeys.add(aPackage.getPackageKey());
            }

            //set last jobid
            jobId = otaCheckBean.getJobIds().get(otaCheckBean.getJobIds().size() - 1);
            //IOT

            if (upgradeModel == 0) {
                String lastjobId = otaCheckBean.getJobIds().get(otaCheckBean.getJobIds().size() - 1);
                AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC, deviceId, lastjobId));

                //订阅新版本Topic
                AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC_V2, deviceId, lastjobId));

                subscribeJobCancelTopic(lastjobId);
            }
        }
        if (upgradeModel == 0) {
            //开始升级任务
            if (!TextUtils.isEmpty(jobId)) {
                mViewModel.getOtaStateInfo(deviceId, new Long[]{Long.parseLong(jobId)});
            }

            //  AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_NET_JOB_ACTION_TOPIC, deviceId, jobId),GsonUtils.toJson(new UpdateAllow(true)));
            LogUtils.d(TAG, "subscribeTopicOTA_NET_JOB_ACTION_TOPIC" + String.format(AwsMqttService.OTA_NET_JOB_ACTION_TOPIC, deviceId, jobId));
        } else {
            AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_GET_PACKAGE_URL_TOPIC, deviceId), GsonUtils.toJson(packageKeys));
        }

        if (!TextUtils.isEmpty(jobId)) {
            mViewModel.upLoadUserid(deviceId, new Long[]{Long.parseLong(jobId)});
        }
    }

    private void subscribeJobCancelTopic(String lastjobId) {
        AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_EVENTS_CANCELED_TOPIC, lastjobId));
        AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_NOMAL_TOPIC, deviceId, lastjobId));
        AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_WRONG_TOPIC, deviceId, lastjobId));
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        //  AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId));
        //  AwsMqttService.unsubscribeTopic(String.format(AwsMqttService.OTA_GET_PACKAGE_URL_ACCEPTED_TOPIC, deviceId));
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.activity_device_upgrade_process;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (ActivityDeviceUpgradeProcessBinding) viewDataBinding;
        binding.activityDeviceUpgradeProcessProgress.setMaxValue(100);
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
        getDeviceInfoFromDb();
        initToolbar();
//    otaUpdateFirmwareUpgradeStateRequest = new ArrayList<>();

        otaUpdateFirmwareUpgradeState = new OTAProgress();

        initProgressState();
        mViewModel.liveData.observe(this, new Observer<OtaUpdateProcessUistate>() {
            @Override
            public void onChanged(OtaUpdateProcessUistate otaUpdateProcessUistate) {
                if (otaUpdateProcessUistate.getState() == RESPONSE_SUCCESS) {
                    List<OtaProcessInfo> list = otaUpdateProcessUistate.getOtaProcessInfoList();
                    OtaProcessInfo otaProcessInfo = list.get(0);
                    if (otaProcessInfo.getStatus() == 0 || otaProcessInfo.getStatus() == 1 || otaProcessInfo.getSysReportTime() == null || otaProcessInfo.getUpgradeExpectTime() == null) {
                        AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_NET_JOB_ACTION_TOPIC, deviceId, jobId), GsonUtils.toJson(new UpdateAllow(true)));
                        LogUtils.d(TAG, "publishTopic_OTA_NET_JOB_ACTION_TOPIC_mViewModel.liveData");

                    } else if (otaProcessInfo.getStatus() == 4) {
                        upgradeFail();
                    } else if (otaProcessInfo.getStatus() == 3) {
                        upgradeSuccess();
                    } else if (otaProcessInfo.getStatus() == 1) {

                        try {
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
                            int downLoadProcess = Integer.parseInt(otaProcessInfo.getDetail());
                            progressLiveData.postValue(downLoadProcess);
                        } catch (Exception e) {
                            LogUtils.d(e.getMessage());
                        }


                    } else {
                        mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                        if (mDownTimer == null) {
                            long currentTime = Long.parseLong(otaProcessInfo.getSysReportTime()) * 1000;
                            long startTime = Long.parseLong(otaProcessInfo.getUpgradeStartTime());
                            long endTime = Long.parseLong(otaProcessInfo.getUpgradeExpectTime());
                            long duration = (endTime - startTime) * 1000;

                            mDownTimer = new CountDownTimer(duration, 1000) {

                                @Override
                                public void onTick(long millisUntilFinished) {
                                    try {
                                        long progress = (duration - millisUntilFinished) * 100 / duration;
                                        progressLiveData.postValue((int) progress);
                                    } catch (Exception e) {
                                        LogUtils.d(e.getMessage());
                                    }

                                }

                                @Override
                                public void onFinish() {
                                    progressLiveData.postValue((int) 99);
                                }

                            };
                            mDownTimer.onTick(currentTime);
                            mDownTimer.start();

                        }
                    }


                }
            }
        });

    }

    private void initProgressState() {
        mViewModel.setFragmentManager(getSupportFragmentManager());
        mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
        binding.setDeviceUpgradeProcessViewModel(mViewModel);
        progressLiveData.setValue(0);
        progressLiveData.observe(this, new Observer<Integer>() {
            @Override
            public void onChanged(Integer integer) {

                if (integer != 0) {
                    if (binding.activityDeviceUpgradeProcessProgress.getmCurrentValue() < integer) {
                        //更新进度条
                        binding.activityDeviceUpgradeProcessProgress.setCurrentValue(integer);
                        //更新进度文本
                        binding.activityDeviceUpgradeProcessProgressTv.setText(integer + "%");
                    }

                } else {
                    //更新进度条
                    binding.activityDeviceUpgradeProcessProgress.setCurrentValue(integer);
                    //更新进度文本
                    binding.activityDeviceUpgradeProcessProgressTv.setText(integer + "%");
                }

            }
        });
    }

    private void initToolbar() {
        mViewModel.toolbarData.setValue(new ToolbarData(
                LanguageStrings.app_OTAInfo_upgrade_textview_text(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        sendBackTrace();
                        exit();
                    }
                }));
    }


    private void getDeviceInfoFromDb() {
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            //查找设备数据
            for (DeviceInfo device : SoftRoomDatabase.getDatabase(this).deviceDao().getDevices()) {
                if (device.getDeviceId().equals(deviceId)) {
                    deviceInfo = device;
                }
            }
        });

        if (null == deviceInfo) {
            return;
        }

        if (TextUtils.isEmpty(deviceInfo.getCommunicateMode())) {
            return;
        }

        String communicateMode = deviceInfo.getCommunicateMode();
        if (BLE_MODEL.equalsIgnoreCase(communicateMode)) {
            BaseApplication.bleResult.observe(this, new Observer<Integer>() {
                @Override
                public void onChanged(Integer integer) {
                    if (integer == BaseApplication.BLE_OFF) {
                        LogUtils.e("ScanNearbyDevicesFragment", "蓝牙开关状态---关");
                        upgradeFail();
                    }
                }
            });
        }

    }


    private void bleUpgrade2() {

        //查找设备数据
        for (DeviceInfo device : SoftRoomDatabase.getDatabase(this).deviceDao().getDevices()) {
            if (device.getDeviceId().equals(deviceId)) {
                deviceInfo = device;
            }
        }

        if (deviceInfo == null || deviceInfo.getMac() == null) {
            LogUtils.eTag(TAG, "设备信息获取失败");
            LogUtils.d("ota_upgradeFail" + "设备信息获取失败deviceInfo == null" + (deviceInfo == null));
            upgradeFail();
        }

        if (!isMainThread()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        if (deviceInfo == null) {
            return;
        }

        //如果当前蓝牙不可用，那么显示升级失败
        String communicateMode = deviceInfo.getCommunicateMode();
        //WIFI设备升级相关
        if (BLE_MODEL.equalsIgnoreCase(communicateMode)) {
            if (!BleManager.getInstance().isBlueEnable()) {
                upgradeFail();
                return;
            }
        }


        BluetoothConection.getInstance().setListener(this);

        BluetoothConection.getInstance().connectDevice2(this, deviceInfo.getMac());

    }


    /**
     * @param upgradState 升级状态
     * @return void
     * @method upgradeStateCheck
     * @description 检查升级状态并上传
     * @date: 2022/10/13 17:27
     * @author: langmeng
     */
    private void upgradeStateCheck(String upgradState) {
        upgrade = true;
        //升级任务状态
        String jobState = AwsMqttService.AWS_STATUS_QUEUED;

        switch (upgradState) {
            //升级成功
            case "00":

                UpgradeState = 1;
                //   获取总成固件版本指令报文写入
                BluetoothConection.getInstance().writeDevice2(bleDevice,
                        BluetoothConection.toCMD(BluetoothConection.COMMAND_ASSEMBLY_FIRMWARE_VERSION,
                                ""));
                jobState = AwsMqttService.AWS_STATUS_SUCCEEDED;
                otaUpdateFirmwareUpgradeState.setTimestamp(System.currentTimeMillis() + "");
                otaUpdateFirmwareUpgradeState.setJobStatus(jobState);
                AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC_V2, deviceId, jobId),
                        GsonUtils.toJson(otaUpdateFirmwareUpgradeState)
                );


                upgradeSuccess();
                break;
            case "03":
                jobState = AwsMqttService.AWS_STATUS_FAILED;
                otaUpdateFirmwareUpgradeState.setTimestamp(System.currentTimeMillis() + "");
                otaUpdateFirmwareUpgradeState.setJobStatus(jobState);
                AwsMqttService.publishTopic(
                        String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC_V2, deviceId, jobId),
                        GsonUtils.toJson(otaUpdateFirmwareUpgradeState)
                );
                break;
            //升级中
            case "04":
                jobState = AwsMqttService.AWS_STATUS_IN_PROGRESS;
                break;
            //待升级
            case "05":
                jobState = AwsMqttService.AWS_STATUS_QUEUED;
                break;
            //开始升级蓝牙模组
            case "06":

                //升级状态设置未蓝牙升级中
                UpgradeState = 3;

                LogUtils.dTag(TAG, "开始升级蓝牙模组，设备准备重启");
                return;
            //升级失败
            default:
                jobState = AwsMqttService.AWS_STATUS_FAILED;
                LogUtils.d("ota_upgradeFail" + "upgradeStateCheck_default  " + upgradState);
                upgradeFail();
                break;
        }
    }

    private long lastShowTime = 0;

    /**
     * @return void
     * @method upgradeFail
     * @description 升级失败
     * @date: 2022/8/12 17:15
     * @author: langmeng
     */
    private void upgradeFail() {
        if (lastShowTime == 0) {
            lastShowTime = System.currentTimeMillis();
        } else if (System.currentTimeMillis() - lastShowTime < 2000) {
            lastShowTime = System.currentTimeMillis();
            return;
        }
        lastShowTime = System.currentTimeMillis();
        //升级成功后不能再执行升级失败流程
        if (getLifecycle().getCurrentState() == Lifecycle.State.DESTROYED) {
            return;
        }

        UpgradeState = 2;

        clearData();

        finish();
        //跳转升级结果页
        ARouter.getInstance()
                .build(RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE_RESULT)
                .withInt(DeviceUpgradeResultActivity.STATE_KEY, DeviceUpgradeResultActivity.STATE_FAIL)
                .withSerializable("deviceInfo", deviceInfo)
                .withString("deviceId", deviceId)
                .withString("jobId", jobId)
                .withInt("upgradeModel", upgradeModel)
                .withBoolean("singleMcu", singleMcu)
                .navigation();
    }

    /**
     * @return void
     * @method upgradeSuccess
     * @description 升级成功
     * @date: 2022/8/12 17:15
     * @author: langmeng
     */
    private void upgradeSuccess() {

        if (isFinishing()) {
            return;
        }

        clearData();

        finish();

        //跳转升级结果页
        ARouter.getInstance()
                .build(RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE_RESULT)
                .withInt(DeviceUpgradeResultActivity.STATE_KEY,
                        DeviceUpgradeResultActivity.STATE_SUCCESS)
                .withString("deviceId", deviceId)
                .withString("jobId", jobId)
                .withString("customVersion", mCustomVersion)
                .navigation();
    }

    /**
     * @param bleDevice 蓝牙设备, file 升级文件, packageSn 升级包序号, componentType 升级包类型, offset 文件读取指针
     * @return void
     * @method sendUpgradeFile
     * @description 发送升级包
     * @date: 2022/8/9 19:37
     * @author: langmeng
     */
    private void sendUpgradeFile(BleDevice bleDevice, byte[] file, int packageSn, String componentType, int offset) {
        //开始发送升级包给设备

        if (file == null) {
            LogUtils.d("sendUpgradeFile_File_is_Null");
            return;
        }
//        "总成零件类型：
//        MCU:mcu
//        子设备:subDevice
//        蓝牙模组:bleModule
//        4G模组:4gModule"
        String packageType = "";
        switch (componentType) {
            case "mcu":
                packageType = "00";
                break;
            case "subDevice":
                packageType = "01";
                break;
            default:
                packageType = "02";
                break;
        }
        String upgradeData = BluetoothConection.addZeroForNum(ConvertUtils.int2HexString(packageSn), 2) + packageType;

        if ("mcu".equalsIgnoreCase(componentType)) {
            otaCheckBean.getPackages();
        }

        byte[] byteArray;
        if (offset > file.length) {
            byteArray = new byte[0];
        } else {
            if (file.length - offset < BLOCK_SIZE) {
                //如果不足一片
                byteArray = new byte[file.length - offset];
                System.arraycopy(file, offset, byteArray, 0, file.length - offset);
            } else {
                byteArray = new byte[BLOCK_SIZE];
                System.arraycopy(file, offset, byteArray, 0, BLOCK_SIZE);
            }
        }

        String blockData = ConvertUtils.bytes2HexString(byteArray);

        blockNum = ((offset / BLOCK_SIZE) + 1);
        fragementNum++;


        String blockNumHexString = BluetoothConection
                .addZeroForNum(ConvertUtils.int2HexString(fragementNum & 0XFF), 2);

        upgradeData += blockNumHexString;
        upgradeData += blockData;

        LogUtils.iTag(TAG, "发送packageType" + packageType + "componentType" + componentType + "packageSn" + packageSn + "升级包第" + ConvertUtils.hexString2Int(blockNumHexString) + "片", blockData, offset);
        //下载升级包指令报文写入
        BluetoothConection.getInstance().writeDevice2(
                bleDevice,
                BluetoothConection.toCMD(BluetoothConection.COMMAND_DOWNLOAD, upgradeData));


    }

    /**
     * @param bleDevice
     * @param firmwareVersionDatas 固件版本信息
     * @return void
     * @method checkUpgradeVersion
     * @description 校验升级版本有效性
     * @date: 2022/7/19 15:31
     * @author: langmeng
     */
    private void checkUpgradeVersion(BleDevice bleDevice, ArrayList<FirmwareVersionData> firmwareVersionDatas) {

        //避免发送间隔太小
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        ArrayList<OTACheckBean.PackagesDTO> otaPackages = new ArrayList<>();

        int sn = 0;
        OTACheckBean.PackagesDTO modulePackage = null;
        if (otaCheckBean == null) {
            otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
        }
        if (otaCheckBean == null) {
            return;
        }
        for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
            if (BLE_MODULE.equalsIgnoreCase(aPackage.getComponentType())) {
                modulePackage = aPackage;
            } else {
                sn++;
                //对升级包排序
                aPackage.setPackageSn(sn);
                aPackage.setOffset(0);
                otaPackages.add(aPackage);
            }

//      //todo 校验升级包是否符合升级规则
//            for (FirmwareVersionData firmwareVersionData : firmwareVersionDatas) {
//                if (aPackage.getComponentNo().equals(firmwareVersionData.getHardwareVersion())) {
//                    if (firmwareVersionData.getSoftwareVersion())
//                }
//            }
        }

        if (modulePackage != null) {
            sn++;
            //对升级包排序
            modulePackage.setPackageSn(sn);
            modulePackage.setOffset(0);
            otaPackages.add(modulePackage);
        }

        boolean isPackageDowned = true;
        for (OTACheckBean.PackagesDTO otaPackage : otaPackages) {
            if (otaPackage.getPackageFile() == null) {
                isPackageDowned = false;
                break;
            }
        }

        if (!isPackageDowned) {
            calls = new ArrayList<>();
            for (int i = 0; i < otaPackages.size(); i++) {
                OTACheckBean.PackagesDTO packagesDTO = otaPackages.get(i);
                LogUtils.dTag(TAG, "升级包地址请求", packagesDTO.getPackageSn());
                LogUtils.dTag(TAG, "packageOrder升级包顺序", packagesDTO.getComponentType());

                try {
                    TracePoints.deviceUpgradeStartDown(UserInfo.get().getEmail(), new DeviceUpgradeProcessModel(
                            TracePoints.FIRMWARE_DOWN_LOAD_START,
                            jobId,
                            deviceId,
                            packagesDTO.getPackageName(),
                            packagesDTO.getPackageUrl())
                    );
                } catch (Exception e) {
                    LogUtils.e(TAG, "TracePoints.deviceUpgradeDown is error");
                }

                Call call = ApiService.instance().dowloadFile(packagesDTO.getPackageUrl());
                //管理所有请求
                calls.add(call);
                LogUtils.dTag(TAG, "开始下载升级包：" + TimeUtils.getNowString());
                call.enqueue(new Callback() {
                    @Override
                    public void onFailure(Call call, IOException e) {
                        LogUtils.eTag(TAG, "升级包地址请求失败", e.getMessage());
                        LogUtils.d("ota_upgradeFail" + "升级包地址请求失败");
                        upgradeFail();
                        call.cancel();
                        try {
                            TracePoints.deviceUpgradeStartDown(UserInfo.get().getEmail(), new DeviceUpgradeProcessModel(
                                    TracePoints.FIRMWARE_DOWN_LOAD_FAIL,
                                    jobId,
                                    deviceId,
                                    packagesDTO.getPackageName(),
                                    packagesDTO.getPackageUrl())
                            );
                        } catch (Exception exception) {
                            LogUtils.e(TAG, "TracePoints.deviceUpgradeDown is error");
                        }
                    }

                    @Override
                    public void onResponse(Call call, Response response) throws IOException {
                        byte[] bytes = response.body().bytes();
                        if (otaCheckBean == null) {
                            otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
                        }
                        try {
                            LogUtils.dTag(TAG, "下载升级包完成：" + TimeUtils.getNowString(), "升级包大小：" + bytes.length);
                            LogUtils.dTag(TAG, "请求结果", packagesDTO.getPackageSn(), response.isSuccessful(), ConvertUtils.bytes2HexString(bytes));

                            packagesDTO.setPackageFile(bytes);
                            for (OTACheckBean.PackagesDTO otaPackage : otaPackages) {
                                if (otaPackage.getPackageFile() == null) {
                                    return;
                                } else {
                                    LogUtils.dTag(TAG, "otaPackage_getPackageFile_size" + otaPackage.getPackageFile().length);
                                }
                            }


                            //更新数据
                            otaCheckBean.setPackages(otaPackages);

                            ((BaseApplication) getApplication()).setOTACheckBean(otaCheckBean);
                            //升级包数据标示
                            StringBuilder upgradeData = new StringBuilder();
                            for (OTACheckBean.PackagesDTO otaPackage : otaPackages) {
                                //文件校验
                                String crc = BluetoothConection.getCRC(otaPackage.getPackageFile());
                                //文件长度
                                String fileLength = BluetoothConection
                                        .addZeroForNum(ConvertUtils.int2HexString(otaPackage.getPackageFile().length), 8);
                                //包序号
                                String packageSn = BluetoothConection.addZeroForNum(ConvertUtils.int2HexString(otaPackage.getPackageSn()), 2);
                                //每个升级包的数据表示
                                String packageData = packageSn + fileLength + crc;
                                //拼接数据
                                upgradeData.append(packageData);
                            }

                            LogUtils.iTag(TAG, "发送packageType请求升级", BluetoothConection.toCMDStr(BluetoothConection.COMMAND_REQUEST_UPGRADE,
                                    upgradeData.toString()));

                            //请求升级指令报文写入
                            BluetoothConection.getInstance().writeDevice2(bleDevice,
                                    BluetoothConection.toCMD(BluetoothConection.COMMAND_REQUEST_UPGRADE,
                                            upgradeData.toString()));
                        } catch (Exception e) {
                            if (e != null) {
                                LogUtils.d(e.getMessage());
                            }

                        }

                    }
                });
            }
        } else {
            //更新数据
            otaCheckBean.setPackages(otaPackages);
            ((BaseApplication) getApplication()).setOTACheckBean(otaCheckBean);
            //升级包数据标示
            StringBuilder upgradeData = new StringBuilder();
            for (OTACheckBean.PackagesDTO otaPackage : otaPackages) {
                //文件校验
                String crc = BluetoothConection.getCRC(otaPackage.getPackageFile());
                //文件长度
                String fileLength = BluetoothConection
                        .addZeroForNum(ConvertUtils.int2HexString(otaPackage.getPackageFile().length), 8);
                //包序号
                String packageSn = BluetoothConection.addZeroForNum(ConvertUtils.int2HexString(otaPackage.getPackageSn()), 2);
                //每个升级包的数据表示
                String packageData = packageSn + fileLength + crc;
                //拼接数据
                upgradeData.append(packageData);
            }

            LogUtils.iTag(TAG, "发送packageType请求升级", BluetoothConection.toCMDStr(BluetoothConection.COMMAND_REQUEST_UPGRADE,
                    upgradeData.toString()));

            //请求升级指令报文写入
            BluetoothConection.getInstance().writeDevice2(bleDevice,
                    BluetoothConection.toCMD(BluetoothConection.COMMAND_REQUEST_UPGRADE,
                            upgradeData.toString()));
        }

    }

    @Override
    protected Class<? extends DeviceUpgradeProcessViewModel> getViewModelClass() {
        return DeviceUpgradeProcessViewModel.class;
    }

    private void exit() {
        ExitDialogUiState exitDialogUiState = mViewModel.showExitDialog.getValue();
        if (null == exitDialogUiState) {
            exitDialogUiState = new ExitDialogUiState();
        }
        exitDialogUiState.show = true;
        mViewModel.showExitDialog.postValue(exitDialogUiState);

        BluetoothConection.getInstance().setmBleGattCallback(null);

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        clearData();
    }

    public void clearData() {
        //取消执行中请求
        if (calls != null && !calls.isEmpty()) {
            for (Call call : calls) {
                if (!call.isCanceled()) {
                    call.cancel();
                }
            }
        }


        BluetoothConection.getInstance().clearReWriteData();


        BluetoothConection.getInstance().setmBleGattCallback(null);

        //释放数据
        otaCheckBean = null;
        otaUpdateFirmwareUpgradeState = null;
        deviceInfo = null;
    }

    @Override
    public void onBackPressed() {
        sendBackTrace();
        exit();
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onOTADataEvent(IotModelEvent iotModelEvent) {

        String jobCheckTopic = String.format(AwsMqttService.OTA_JOB_CHECK_ACCEPTED_TOPIC, deviceId);
        String packageUrlTopic = String.format(AwsMqttService.OTA_GET_PACKAGE_URL_ACCEPTED_TOPIC, deviceId, jobId);
        String stateUpdateTopic = String.format(AwsMqttService.OTA_JOB_STATE_UPDATE_ACCEPTED_TOPIC, deviceId, jobId);
        String firmwareStateUpdateTopic = String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_ACCEPTED_TOPIC, deviceId, jobId);
        if (upgradeModel == 0) {
            //WIFI设备相关逻辑
            // 0 升级中  1 升级完成 2升级失败 3蓝牙模组升级中 4 设备开始升级
            String jobWrongTopic = String.format(AwsMqttService.OTA_JOB_WRONG_TOPIC, deviceId, jobId);
            String jobNormalTopic = String.format(AwsMqttService.OTA_JOB_NOMAL_TOPIC, deviceId, jobId);
            String jobCancelTopic = String.format(AwsMqttService.OTA_JOB_EVENTS_CANCELED_TOPIC, jobId);
            if (iotModelEvent.topic.equals(jobCancelTopic)) {
                AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_EVENTS_CANCELED_PUBLISH_TOPIC, deviceId, jobId));
            } else if (iotModelEvent.topic.equals(jobNormalTopic)) {
                unSubscribeJobCancelTopic(jobWrongTopic, jobNormalTopic, jobCancelTopic);
            } else if (iotModelEvent.topic.equals(jobNormalTopic)) {
                LogUtils.d("jobCancelTopic_jobWrongTopic" + jobNormalTopic);
                upgradeFail();
                return;
            }

            //老topic
            String jobRUpadateTopic = String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC, deviceId, jobId);
            String jobNewRUpadateTopic = String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC_V2, deviceId, jobId);

            if (iotModelEvent.topic.equals(jobRUpadateTopic)) {
                List<RUpgradeProcessData> rUpgradeProcessDataList = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData),
                        new TypeToken<List<RUpgradeProcessData>>() {
                        }.getType());

                deviceInfo = SoftRoomDatabase.getDatabase(this).deviceDao().getDeviceById(deviceId);

                String communicateMode = deviceInfo.getCommunicateMode();
                //wifiStatus  0 升级中  1 升级完成 2升级失败 3蓝牙模组升级中 4 设备开始升级

                if ("wifi".equalsIgnoreCase(communicateMode)) {
                    RUpgradeProcessData rUpgradeProcessData = rUpgradeProcessDataList.get(0);
                    int wifiStatus = 0;
                    int downLoadProcess = 0;
                    int updateProcess = 0;
                    boolean isUpgradeAll = true;


                    //TODO 如果设备升级成功------那么就阻断下面操作
                    for (int f = 0; rUpgradeProcessDataList != null && f < rUpgradeProcessDataList.size(); f++) {
                        if (rUpgradeProcessDataList.get(f).getStatus() != 3) {
                            isUpgradeAll = false;
                        }
                    }
                    if (isUpgradeAll) {
                        wifiStatus = 3;
                        LogUtils.e(TAG, "WIFI升级成功--->" + updateProcess);
                        progressLiveData.postValue(100);
                        upgradeSuccess();
                        TracePoints.wifiDeviceUpgradeStatus(null, UserInfo.get().getId(), "upgradeSuccess");
                        AwsMqttService.unsubscribeTopic(communicateMode);
                        return;
                    }
                    //TODO 以上是-----------新增逻辑

                    for (int f = 0; rUpgradeProcessDataList != null && f < rUpgradeProcessDataList.size(); f++) {
                        if (rUpgradeProcessDataList.get(f).getStatus() == 4) {
                            wifiStatus = 4;
                            break;
                        }
                        if (rUpgradeProcessDataList.get(f).getStatus() == 1) {
                            wifiStatus = 1;
                            break;
                        }
                    }


                    if (wifiStatus == 1) {
                        //wifiStatus  0 升级中  1 升级完成 2升级失败 3蓝牙模组升级中 4 设备开始升级

                        //升级完成
                        if (rUpgradeProcessDataList != null) {
                            for (int f = 0; f < rUpgradeProcessDataList.size(); f++) {
                                if (rUpgradeProcessDataList.get(f).getStatus() == 1) {
                                    downLoadProcess = downLoadProcess + Integer.parseInt(rUpgradeProcessDataList.get(f).getDetail());

                                } else {
                                    downLoadProcess = downLoadProcess + 100;
                                }
                            }
                            downLoadProcess = downLoadProcess / rUpgradeProcessDataList.size();
                        }

                    } else if (wifiStatus == 4) {
                        wifiStatus = 4;
                    } else {
                        for (int f = 0; rUpgradeProcessDataList != null && f < rUpgradeProcessDataList.size(); f++) {
                            if (rUpgradeProcessDataList.get(f).getStatus() == 2) {
                                wifiStatus = 2;
                                break;
                            }
                        }

                        //wifiStatus
                        //0: 等待升级 初始状态
                        //1:下载中
                        //2:升级中
                        //3:升级成功
                        //4:升级失败

                        //非升级完成
                        if (wifiStatus == 2) {
                            if (rUpgradeProcessDataList != null) {
                                for (int f = 0; f < rUpgradeProcessDataList.size(); f++) {
                                    if (rUpgradeProcessDataList.get(f).getStatus() == 2) {
                                        updateProcess = updateProcess + Integer.parseInt(rUpgradeProcessDataList.get(f).getDetail());

                                    } else {
                                        updateProcess = updateProcess + 100;
                                    }
                                }
                                updateProcess = updateProcess / rUpgradeProcessDataList.size();
                            }

                        } else {
                            wifiStatus = 3;
                        }
                    }

                    //wifiStatus  0 升级中  1 升级完成 2升级失败 3蓝牙模组升级中 4 设备开始升级
                    if (wifiStatus == 1) {
                        try {
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
                            progressLiveData.postValue(downLoadProcess);
                            TracePoints.wifiDeviceUpgradeDownLoad(null, UserInfo.get().getId(), downLoadProcess + "");
                        } catch (Exception e) {
                            LogUtils.d(e.getMessage());
                        }
                    } else if (wifiStatus == 2) {

                        mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                        progressLiveData.postValue(updateProcess);
                        TracePoints.wifiDeviceUpgradeProgress(null, UserInfo.get().getId(), downLoadProcess + "");

                    } else if (wifiStatus == 3) {
                        progressLiveData.postValue(100);
                        upgradeSuccess();
                        TracePoints.wifiDeviceUpgradeStatus(null, UserInfo.get().getId(), "upgradeSuccess");

                    } else if (wifiStatus == 4) {
                        upgradeFail();
                        TracePoints.wifiDeviceUpgradeStatus(null, UserInfo.get().getId(), "upgradeFail");

                    }
                }

                //通过DT脚去升级的设备---子设备
                else if ("dt".equalsIgnoreCase(communicateMode)) {

                    if (rUpgradeProcessDataList.get(0).getStatus() == 1) {
                        try {
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
                            int downLoadProcess = Integer.parseInt(rUpgradeProcessDataList.get(0).getDetail());
                            progressLiveData.postValue(downLoadProcess);
                        } catch (Exception e) {
                            LogUtils.d(e.getMessage());
                        }


                    } else if (rUpgradeProcessDataList.get(0).getStatus() == 2) {
                        try {
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                            int downLoadProcess = Integer.parseInt(rUpgradeProcessDataList.get(0).getDetail());
                            progressLiveData.postValue(downLoadProcess);
                        } catch (Exception e) {
                            LogUtils.d(e.getMessage());
                        }
                    }
                    if (rUpgradeProcessDataList.get(0).getStatus() == 3) {
                        progressLiveData.postValue((int) 100);
                        upgradeSuccess();
                    } else if (rUpgradeProcessDataList.get(0).getStatus() == 4) {
                        upgradeFail();

                    }


                } else {
                    //R相关升级
                    if (rUpgradeProcessDataList.get(0).getStatus() == 1) {
                        try {
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
                            int downLoadProcess = Integer.parseInt(rUpgradeProcessDataList.get(0).getDetail());
                            progressLiveData.postValue(downLoadProcess);
                        } catch (Exception e) {
                            LogUtils.d(e.getMessage());
                        }


                    } else if (mDownTimer == null && rUpgradeProcessDataList.get(0).getStatus() == 2) {
                        mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                        long startTime = rUpgradeProcessDataList.get(0).getUpgradeStartTime();
                        long endTime = rUpgradeProcessDataList.get(0).getUpgradeExpectTime();
                        long duration = (endTime - startTime) * 1000;
                        mDownTimer = new CountDownTimer(duration, 1000) {
                            @Override
                            public void onTick(long millisUntilFinished) {
                                try {
                                    long progress = (duration - millisUntilFinished) * 100 / duration;
                                    LogUtils.d("mDownTimer", "mDownTimer_millisUntilFinished" + millisUntilFinished + "progress" + progress);
                                    progressLiveData.postValue((int) progress);
                                } catch (Exception e) {
                                    LogUtils.d("Exception", "Exception" + e.getMessage());
                                }

                            }

                            @Override
                            public void onFinish() {
                                if (rUpgradeProcessDataList.get(0).getStatus() == 3) {
                                    progressLiveData.postValue((int) 100);
                                    upgradeSuccess();
                                } else {
                                    progressLiveData.postValue((int) 99);
                                }
                            }

                        };
                        mDownTimer.start();

                    }
                    if (rUpgradeProcessDataList.get(0).getStatus() == 3) {
                        if (mDownTimer != null) {
                            mDownTimer.cancel();
                            mDownTimer = null;
                        }
                        progressLiveData.postValue((int) 100);
                        upgradeSuccess();
                    } else if (rUpgradeProcessDataList.get(0).getStatus() == 4) {
                        upgradeFail();

                    }
                }


            } else if (iotModelEvent.topic.equals(jobNewRUpadateTopic)) {
                //适配新wifi的topic
                fixNewTopicWithWifiDevice(iotModelEvent);
            }


        } else {
            //蓝牙升级 任务详情
            if (iotModelEvent.topic.equals(jobCheckTopic) && otaCheckBean == null) {
                otaCheckBean = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), OTACheckBean.class);
                if (otaCheckBean != null) {
                    mCustomVersion = otaCheckBean.getCustomVersion();
                }
                ArrayList<String> packageKeys = new ArrayList<>();
                for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
                    packageKeys.add(aPackage.getPackageKey());
                }

                //set last jobid
                jobId = otaCheckBean.getJobIds().get(otaCheckBean.getJobIds().size() - 1);
                //IOT 升级
                if (upgradeModel == 0) {
                    //开始升级任务
                    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_NET_JOB_ACTION_TOPIC, deviceId, jobId), GsonUtils.toJson(new UpdateAllow(true)));
                    LogUtils.d(TAG, "publishTopic_OTA_NET_JOB_ACTION_TOPIC_onOTADataEvent");
                } else {
                    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_GET_PACKAGE_URL_TOPIC, deviceId), GsonUtils.toJson(packageKeys));
                }
            }


            //获取升级包URL
            if (iotModelEvent.topic.equals(packageUrlTopic)) {

                if (otaCheckBean == null) {
                    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_CHECK_TOPIC, AwsMqttService.CLIENT_NAME, deviceId));
                    return;
                }

                //解析数据
                List<OTAPackageUrlBean> packageUrlList = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), new TypeToken<List<OTAPackageUrlBean>>() {
                }.getType());
                List<OTACheckBean.PackagesDTO> packages = otaCheckBean.getPackages();

                //替换url数据
                for (int i = 0; i < packages.size(); i++) {
                    for (OTAPackageUrlBean otaPackageUrlBean : packageUrlList) {
                        if (packages.get(i).getPackageKey().equals(otaPackageUrlBean.getKey())) {
                            packages.get(i).setPackageUrl(otaPackageUrlBean.getPreSignedUrl());
                        }
                    }
                }

                //替换包数据
                otaCheckBean.setPackages(packages);
                //开始蓝牙升级流程
                bleUpgrade2();
            }

            //IOT 升级
            if (upgradeModel == 0) {

                //升级状态通知
                if (iotModelEvent.topic.equals(stateUpdateTopic)) {
                    OTAUpdateUpgradeStateBean otaUpdateUpgradeStateBean =
                            GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), OTAUpdateUpgradeStateBean.class);
                    switch (otaUpdateUpgradeStateBean.getStatus()) {
                        //升级中
                        case AwsMqttService.AWS_STATUS_IN_PROGRESS:
                            break;
                        //升级成功
                        case AwsMqttService.AWS_STATUS_SUCCEEDED:
                            upgradeSuccess();
                            break;
                        //升级失败
                        case AwsMqttService.AWS_STATUS_FAILED:
                            LogUtils.d("ota_upgradeFail" + "AwsMqttService.AWS_STATUS_FAILED");
                            upgradeFail();
                            break;
                        //用户主动拒绝升级
                        case AwsMqttService.AWS_STATUS_REJECTED:
                            LogUtils.d("ota_upgradeFail" + "AwsMqttService.AWS_STATUS_REJECTED");
                            upgradeFail();
                            break;
                        //升级超时
                        case AwsMqttService.AWS_STATUS_TIMED_OUT:
                            LogUtils.d("ota_upgradeFail" + "AwsMqttService.AWS_STATUS_TIMED_OUT");
                            upgradeFail();
                            break;
                        default:
                            break;
                    }
                }

                //升级进度通知
                if (iotModelEvent.topic.equals(firmwareStateUpdateTopic)) {
                    ArrayList<OTAUpdateFirmwareUpgradeStateBean> otaUpdateFirmwareUpgradeStateBeans =
                            GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData),
                                    new TypeToken<List<OTAUpdateFirmwareUpgradeStateBean>>() {
                                    }.getType());
                }
            }

        }
    }


    private void fixNewTopicWithWifiDevice(IotModelEvent iotModelEvent) {
        //TODO 新topic适配
        final String STATUS_SUCCESSED = "SUCCEEDED";
        final String STATUS_FAILED = "FAILED";
        final String STATUS_IN_PROGRESS = "IN_PROGRESS";
        final String WIFI_MODE = "wifi";
        final int WIFI_OTA_DOWNING = 1;
        final int WIFI_OTA_UPGRADING = 2;


        // 任务状态: "SUCCEEDED"：成功 "FAILED": "失败"   "IN_PROGRESS": "进行中"
        OTAProgress otaProgress = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), OTAProgress.class);

        FileHelper.appendToFile(DeviceUpgradeProcessActivity.this, ConvertUtils.bytes2String(iotModelEvent.modelData));


        deviceInfo = SoftRoomDatabase.getDatabase(this).deviceDao().getDeviceById(deviceId);

        String communicateMode = deviceInfo.getCommunicateMode();
        //非R设备升级逻辑
        if (!CommonUtils.isRDevice(deviceId)) {
            String jobStatus = otaProgress.getJobStatus();
            List<OTAProgress.OTAJobModel> packageResults = otaProgress.getPackageResults();
            //下载进度
            int downLoadProcess = 0;
            //升级进度
            int updateProcess = 0;

            switch (jobStatus) {
                case STATUS_IN_PROGRESS:
                    //TODO "IN_PROGRESS": "进行中"
                    int firmware_status = 0;
                    int packageSize = packageResults.size();
                    for (OTAProgress.OTAJobModel packageResult : packageResults) {
                        int current_status = TextUtils.isEmpty(packageResult.getStatus()) ? 0 : Integer.parseInt(packageResult.getStatus());
                        //当firmware_status为0时候进行初始化赋值
                        if (firmware_status == 0) {
                            firmware_status = current_status;
                        }
                        //如果当前进度大于最小状态，那么就重置为最小状态
                        if (current_status < firmware_status) {
                            firmware_status = current_status;
                        }

                        if (firmware_status == WIFI_OTA_DOWNING) {
                            //1:下载中
                            downLoadProcess = downLoadProcess + (TextUtils.isEmpty(packageResult.getDetail()) ? 0 : Integer.parseInt(packageResult.getDetail()));
                            TracePoints.wifiDeviceUpgradeProgress(null, UserInfo.get().getId(), downLoadProcess + "");
                        } else {
                            //2:升级中
                            updateProcess = updateProcess + (TextUtils.isEmpty(packageResult.getDetail()) ? 0 : Integer.parseInt(packageResult.getDetail()));
                            TracePoints.wifiDeviceUpgradeStatus(null, UserInfo.get().getId(), updateProcess + "");
                        }

                    }

                    if (WIFI_OTA_DOWNING == firmware_status) {
                        //下载中
                        downLoadProcess = downLoadProcess / packageSize;
                        progressLiveData.postValue(downLoadProcess);
                        mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
                    } else if (WIFI_OTA_UPGRADING == firmware_status) {
                        //OTA升级状态改变。将进度条初始化设置为0
                        if (mViewModel.processState.get() == 0) {
                            progressLiveData.postValue(0);
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                        } else {
                            //升级中
                            updateProcess = updateProcess / packageSize;
                            progressLiveData.postValue(updateProcess);
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                        }

                    }

                    break;

                case STATUS_SUCCESSED:
                    //任务状态: "SUCCEEDED"
                    upgradeSuccess();
                    TracePoints.wifiDeviceUpgradeStatus(null, UserInfo.get().getId(), "upgradeSuccess");
                    break;
                case STATUS_FAILED:
                    //"FAILED": "失败"
                    upgradeFail();
                    TracePoints.wifiDeviceUpgradeStatus(null, UserInfo.get().getId(), "upgradeFail");
                    break;

                default:
                    break;

            }
        } else {
            //R设备适配
            //1:下载中 2:升级中 3:升级成功 4:升级失败
            OTAProgress.OTAJobModel otaJobModel = otaProgress.getPackageResults().get(0);
            int status = TextUtils.isEmpty(otaJobModel.getStatus()) ? 0 : Integer.parseInt(otaJobModel.getStatus());
            if (status == 1) {
                try {
                    mViewModel.processState.set(DeviceUpgradeProcessViewModel.DOWLAOD_STATE);
                    int downLoadProcess = Integer.parseInt(otaJobModel.getDetail());
                    progressLiveData.postValue(downLoadProcess);
                } catch (Exception e) {
                    LogUtils.d(e.getMessage());
                }
            } else if (mDownTimer == null && status == 2) {
                mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                long startTime = TextUtils.isEmpty(otaJobModel.getUpgradeStartTime()) ? 0 : Long.parseLong(otaJobModel.getUpgradeStartTime());
                long endTime = TextUtils.isEmpty(otaJobModel.getUpgradeExpectTime()) ? 0 : Long.parseLong(otaJobModel.getUpgradeExpectTime());
                long duration = (endTime - startTime) * 1000;
                mDownTimer = new CountDownTimer(duration, 1000) {
                    @Override
                    public void onTick(long millisUntilFinished) {
                        try {
                            long progress = (duration - millisUntilFinished) * 100 / duration;
                            LogUtils.d("mDownTimer", "mDownTimer_millisUntilFinished" + millisUntilFinished + "progress" + progress);
                            progressLiveData.postValue((int) progress);
                        } catch (Exception e) {
                            LogUtils.d("Exception", "Exception" + e.getMessage());
                        }

                    }

                    @Override
                    public void onFinish() {
                        if (status == 3) {
                            progressLiveData.postValue((int) 100);
                            upgradeSuccess();
                        } else {
                            progressLiveData.postValue((int) 99);
                        }
                    }

                };
                mDownTimer.start();

            }
            if (status == 3) {
                if (mDownTimer != null) {
                    mDownTimer.cancel();
                    mDownTimer = null;
                }
                progressLiveData.postValue((int) 100);
                upgradeSuccess();
            } else if (status == 4) {
                upgradeFail();

            }
        }

    }

    private void unSubscribeJobCancelTopic(String jobWrongTopic, String jobNormalTopic, String jobCancelTopic) {
        AwsMqttService.unsubscribeTopic(jobCancelTopic);
        AwsMqttService.unsubscribeTopic(jobNormalTopic);
        AwsMqttService.unsubscribeTopic(jobWrongTopic);
    }

    @Override
    public void onStop() {
        super.onStop();
        BluetoothConection.getInstance().uNnotifyDevice(DEVICE_MODULE);

    }


    private void initTrace() {
        stayEleId = "3";
        pageId = "63";
        mouduleId = "7";
        pageResouce = "1_9_41_43_63";
        nextButtoneleid = "2";
    }

    public void sendButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
    }


    //TODO 蓝牙连接重构相关
    @Override
    public void isBlueEnable(boolean enable) {
        LogUtils.e(TAG, "isBlueEnable--->" + enable);
    }

    @Override
    public void onBleStartConnect() {
        LogUtils.e(TAG, "onBleStartConnect------");

    }

    @Override
    public void onBleConnectFail(BleDevice bleDevice, BleException exception, int times) {
        LogUtils.e(TAG, "onBleConnectFail------");
        LogUtils.e(TAG, "isBleConnecting------" + isBleConnecting);
        if (isBleConnecting) {
            reconnectBleOta();
        }

    }

    @Override
    public void onBleConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {

        FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
        if (!topActivity.getClass().getName().equals(activityName)) {
            return;
        }
        // 连接成功，BleDevice即为所连接的BLE设备

        DeviceUpgradeProcessActivity.this.bleDevice = bleDevice;


        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    @Override
    public void onMtuChanged(int mtu) {
        LogUtils.e(TAG, "onMtuChanged------mtu--" + mtu);

    }

    @Override
    public void onSetMTUFailure(BleException exception) {
        LogUtils.e(TAG, "onSetMTUFailure------");

    }

    @Override
    public void onGattStartConnect() {
        LogUtils.e(TAG, "onSetMTUFailure------");

    }

    @Override
    public void onGattConnectFail(BleDevice bleDevice, BleException exception) {
        LogUtils.e(TAG, "onGattConnectFail------");

    }

    @Override
    public void onGattConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
        LogUtils.e(TAG, "onGattConnectSuccess------");

    }

    @Override
    public void onGattDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {
        try {
            FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
            if (!topActivity.getClass().getName().equals(activityName)) {
                return;
            }
            if (isBleConnecting) {
                return;
            }
            if (!upgrade) {
                return;
            }

            isBleConnecting = false;
            // 连接中断，isActiveDisConnected表示是否是主动调用了断开连接方法
            LogUtils.dTag(TAG, "蓝牙连接状态---isActiveDisConnected-->" + isActiveDisConnected);
            if (!isActiveDisConnected) {
                //蓝牙模组升级导致断连，重连逻辑
                if (UpgradeState == 3) {
                    upgrade = false;
                    BluetoothConection.getInstance().stopNotify(device);
                    LogUtils.dTag(TAG, "蓝牙升级主动断开连接");
                    reconnectBleOta();
                } else {
                    LogUtils.d("ota_upgradeFail" + "UpgradeState != 3");
                    upgradeFail();
                }
            }
        } catch (Exception e) {
            LogUtils.d(TAG, "ota_exception--->" + e.getMessage());
        }


    }

    /**
     * 对于组合升级后，如果是重连中，进行多次重新直至连接成功
     */
    private void reconnectBleOta() {
        binding.activityDeviceUpgradeProcessProgressTv.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isFinishing()) {
                    LogUtils.e(TAG, "升级页面正在销毁");
                    return;
                }
                if (isDestroyed()) {
                    LogUtils.e(TAG, "升级页面已经销毁");
                    return;
                }

                if (null == deviceInfo) {
                    LogUtils.e(TAG, "deviceInfo is null");
                    upgradeFail();
                    return;
                }

                if (TextUtils.isEmpty(deviceInfo.getMac())) {
                    LogUtils.e(TAG, "deviceInfo.getMac() is null");
                    upgradeFail();
                    return;
                }

                if (BluetoothConection.isConnected(deviceInfo.getMac())) {
                    isBleConnecting = false;
                    LogUtils.e(TAG, "蓝牙升级后，进行重连，当前状态为蓝牙连接成功,过滤重新连接");
                    return;
                }
                BluetoothConection.getInstance().setEnableRnConnect(true);
                BluetoothConection.getInstance().connectDevice2(DeviceUpgradeProcessActivity.this, deviceInfo.getMac());
                isBleConnecting = true;
            }
        }, 8000);
    }

    @Override
    public void onNotifySuccess(BleDevice bleDevice) {
        FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
        if (!topActivity.getClass().getName().equals(activityName)) {
            return;
        }
        isBleConnecting = false;

        // 打开通知操作成功
        if (otaCheckBean == null) {
            otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
        }
        if (UpgradeState == 3) {
            //获取升级结果
            BluetoothConection.getInstance().writeDevice2(bleDevice,
                    BluetoothConection.toCMD(BluetoothConection.COMMAND_GET_UPGRADE_STATE,
                            BluetoothConection.COMMAND_BLE_MCU_ID));
        } else if (UpgradeState == 4 || UpgradeState == 1) {

        } else {
            LogUtils.iTag("onNotifySuccess--->", "deviceNotify_UpgradeState" + UpgradeState);
            //获取总成固件版本指令报文写入
            BluetoothConection.getInstance().writeDevice2(bleDevice,
                    BluetoothConection.toCMD(BluetoothConection.COMMAND_ASSEMBLY_FIRMWARE_VERSION,
                            ""));
            //init data
            firmwareVersionDatas = new ArrayList<>();
            checkUpgradeVersion(bleDevice, firmwareVersionDatas);
            firmwareVersionDatas.clear();
        }

    }

    @Override
    public void onNotifyFailure(BleDevice bleDevice, BleException exception) {
        FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
        if (!topActivity.getClass().getName().equals(activityName)) {
            return;
        }
        upgradeFail();
    }

    @Override
    public void onWriteSuccess(int current, int total, byte[] justWrite) {

    }

    @Override
    public void onCharacteristicChanged(byte[] data) {
        try {
            FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
            if (!topActivity.getClass().getName().equals(activityName)) {
                return;
            }
            if (this.isFinishing()) {
                return;
            }

            LogUtils.e(TAG, "onCharacteristicChanged------" + HexUtil.formatHexString(data));
            // 打开通知后，设备发过来的数据将在这里出现

            String dataHexString = HexUtil.formatHexString(data).toUpperCase();

            String command = dataHexString
                    .substring(
                            BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length(),
                            BluetoothConection.DATA_COMMAND_END_INDEX
                    );

            //截取数据内容
            String dataString = dataHexString.substring(
                    BluetoothConection.DATA_DATALENGTH_END_INDEX,
                    dataHexString.length() - BluetoothConection.DATA_CRC_LENGTH);
            LogUtils.iTag(TAG, "packageType_recv_content" + command + "dataHexString" + dataHexString);

            // TODO test 如果是D5指令且设备断开连接 2023/6/19 wj
//          if (command.equals("D5")){
//            if (!TextUtils.isEmpty(dataString)){
//              if (dataString.equals(ONLINE_STATUS_OFF)){
//                LogUtils.iTag(TAG, "device offline---dataHexString" + dataHexString);
//                upgradeFail();
//                return;
//              }
//            }
//          }

            //判断指令返回
            switch (command) {
                case BluetoothConection.COMMAND_ASSEMBLY_FIRMWARE_VERSION:

                    //获取设备版本
                    String fmSize = dataString.substring(0, 2);
                    String fmPosition = dataString.substring(2, 4);
                    String mcuId = dataString.substring(4, 6);
                    String hardwareVersion = BluetoothConection.byteToStr(ConvertUtils.hexString2Bytes(dataString.substring(6, 32 * 2 + 6)));
                    String softwareVersion = BluetoothConection.byteToStr(ConvertUtils.hexString2Bytes(dataString.substring(32 * 2 + 6)));
                    if (firmwareVersionDatas == null) {
                        firmwareVersionDatas = new ArrayList<>();
                    }
                    firmwareVersionDatas.add(new FirmwareVersionData(mcuId, hardwareVersion, softwareVersion));

                    LogUtils.iTag(TAG,
                            "fmSize :" + fmSize,
                            "fmPosition :" + fmPosition,
                            "mcuId :" + mcuId,
                            "hardwareVersion :" + hardwareVersion,
                            "softwareVersion :" + softwareVersion
                    );

                    //获取所有固件版本完成
                    if (fmSize.equals(fmPosition)) {
                        switch (UpgradeState) {
                            case 0:
//                    checkUpgradeVersion(bleDevice, firmwareVersionDatas);
//                    firmwareVersionDatas.clear();
                                break;
                            case 1:
                                AwsUpdateBean awsUpdateBean = new AwsUpdateBean();
                                AwsUpdateBean.StateDTO stateDTO = new AwsUpdateBean.StateDTO();
                                HashMap<Object, Object> dataMap = new HashMap<>();
                                for (FirmwareVersionData firmwareVersionData : firmwareVersionDatas) {
                                    dataMap.put(firmwareVersionData.getHardwareVersion(), firmwareVersionData.getSoftwareVersion());
                                    LogUtils.iTag(TAG, "listenMessage_firmwareVersionData.getHardwareVersion()" + firmwareVersionData.getHardwareVersion() + "getSoftwareVersion" + firmwareVersionData.getSoftwareVersion());
                                }
                                stateDTO.setReported(dataMap);
                                awsUpdateBean.setState(stateDTO);
                                //上报设备最新版本号至AWS

                                AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_UPDATE_COMPONENT_VERSION_TOPIC, deviceId),
                                        GsonUtils.toJson(awsUpdateBean));
                                LogUtils.iTag(TAG, "listenMessage_firmwareVersionDatas" + GsonUtils.toJson(awsUpdateBean)
                                );


                                firmwareVersionDatas.clear();

                                upgradeSuccess();
                                break;
                        }
                    } else {
                        //MCU个数及当前收到第几个
                        String nextData = fmSize + fmPosition;
                        //向设备发送确认收到消息
                        BluetoothConection.getInstance().writeDevice2(bleDevice,
                                BluetoothConection.toCMD(BluetoothConection.COMMAND_ASSEMBLY_FIRMWARE_VERSION, nextData));
                    }

                    break;

                case BluetoothConection.COMMAND_REQUEST_UPGRADE:
                    int packagePosition = 0;
                    int offset = 0;
                    //续传逻辑
                    if (dataString.isEmpty()) {
                        //新升级包
                        fragementNum = 0;
                        packagePosition = 0;
                        offset = 0;
                    } else {
                        //未完成升级
                        fragementNum = 0;
                        //上次传输的升级包的序号
                        String packageSn = dataString.substring(0, 2);
                        int packageSnInt = ConvertUtils.hexString2Int(packageSn);
                        if (packageSnInt > 0) {
                            packagePosition = packageSnInt - 1;
                        }
                        //当前分片指针
                        try {
                            offset = ConvertUtils.hexString2Int(dataString.substring(2, dataString.length()));
                            LogUtils.d("发送packageType_recv_" + "未完成升级offset" + offset);
                        } catch (NumberFormatException e) {
                            e.fillInStackTrace();
                            LogUtils.d("ota_upgradeFail" + "offset_offset");
                            upgradeFail();
                        }
                    }

                    LogUtils.dTag(TAG, "开始发送升级包给设备：" + TimeUtils.getNowString());

                    if (otaCheckBean == null) {
                        otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
                    }

                    //设备切片数据异常
                    try {
                        //开始发送升级包给设备
                        sendUpgradeFile(bleDevice,
                                otaCheckBean.getPackages().get(packagePosition).getPackageFile(),
                                otaCheckBean.getPackages().get(packagePosition).getPackageSn(),
                                otaCheckBean.getPackages().get(packagePosition).getComponentType(),
                                offset);
                    } catch (Exception e) {
                        LogUtils.i(TAG, "device sendupgradefile exception");
                    }


                    break;
                case BluetoothConection.COMMAND_DOWNLOAD:
                    if (otaCheckBean == null) {
                        otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();
                    }

                    //每次设备成功收到升级包分片会通知
                    LogUtils.iTag(TAG, "command download", dataString);

                    if (dataString.isEmpty()) {
                        mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                        progressLiveData.postValue(0);
                        LogUtils.iTag(TAG, "download done");
                        return;
                    }

                    //当前传输升级包的序号
                    String packageSn = dataString.substring(0, 2);
                    int packageSnInt = ConvertUtils.hexString2Int(packageSn);
                    //升级包已经传输过去的分片数量
//                                int blockNum = ConvertUtils.hexString2Int(dataString.substring(2, dataString.length()));
                    int allPackageLength = 0;

                    //当前传输的升级包
                    OTACheckBean.PackagesDTO otaPackage = null;
                    for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
                        LogUtils.d("ota_SendPackage" + "getComponentType" + aPackage.getComponentType() + "getPackageSn" + aPackage.getPackageSn());
                        //如果升级包丢失则升级失败
                        if (aPackage.getPackageFile() == null) {
                            LogUtils.d("ota_upgradeFail" + "aPackage.getPackageFile() == null升级包丢失则升级失败");
                            upgradeFail();
                            return;
                        }

                        //升级包长度总和
                        allPackageLength += aPackage.getPackageFile().length;
                        //识别出当前的升级包
                        if (aPackage.getPackageSn() == packageSnInt) {
                            otaPackage = aPackage;
                        }
                    }

                    if (otaPackage == null) {
                        LogUtils.eTag(TAG, "升级失败 ：文件读取错误");
                        return;
                    }

                    //如果分片长度足够向设备发送已下载完成的指令
                    if (blockNum * BLOCK_SIZE >= otaPackage.getPackageFile().length) {

                        LogUtils.iTag(TAG, packageSn + "分片包传输完成");

                        //已经下载完最后一个升级包发送下载完成指令
                        if (otaPackage.getPackageSn() == otaCheckBean.getPackages().size()) {
                            LogUtils.dTag(TAG, "发送升级包给设备完成：" + TimeUtils.getNowString());
                            progressLiveData.postValue(100);
                            mViewModel.processState.set(DeviceUpgradeProcessViewModel.UPGRADE_STATE);
                            BluetoothConection.getInstance().writeDevice2(bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_DOWNLOAD, ""));
                        } else {
                            //因为packageSn从1开始 升级包集合从0开始，所以用packageSn作为下一包索引即可
                            int nextPackagePosition = packageSnInt;
                            //开始发送升级包给设备
                            fragementNum = 0;
                            sendUpgradeFile(bleDevice,
                                    otaCheckBean.getPackages().get(nextPackagePosition).getPackageFile(),
                                    otaCheckBean.getPackages().get(nextPackagePosition).getPackageSn(),
                                    otaCheckBean.getPackages().get(nextPackagePosition).getComponentType(),
                                    0);
                        }
                    } else {


                        int donePackageLength = 0;
                        int customPackageLength = blockNum * BLOCK_SIZE;
                        int customPackageListPosition = packageSnInt - 1;

                        //如果当前下载>1包则下载完成的长度 = 当前包下载长度 + 之前包长度和
                        if (customPackageListPosition > 0) {
                            for (int i = 0; i < customPackageListPosition; i++) {
                                OTACheckBean.PackagesDTO packagesDTO = otaCheckBean.getPackages().get(i);
                                donePackageLength += packagesDTO.getPackageFile().length;
                            }
                        }

                        //小数
                        double value = new BigDecimal((float) (donePackageLength + customPackageLength) / allPackageLength)
                                .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

                        int floor = (int) Math.floor(value * 100);
                        int currentProcessData = progressLiveData.getValue();
                        if (currentProcessData < floor) {
                            //更新进度
                            progressLiveData.postValue(floor);
                        }


                        LogUtils.iTag(TAG, "下载进度", floor);

                        //发送下一分片
                        sendUpgradeFile(bleDevice,
                                otaCheckBean.getPackages().get(customPackageListPosition).getPackageFile(),
                                packageSnInt,
                                otaCheckBean.getPackages().get(customPackageListPosition).getComponentType(),
                                customPackageLength);
                    }
                    break;
                case BluetoothConection.COMMAND_UPGRADE_PROGRESS:
                    //升级进度
                    int upgradeProgress = ConvertUtils.hexString2Int(dataString);
                    int progressLiveDataValuerocessData = progressLiveData.getValue();
                    if (progressLiveDataValuerocessData < upgradeProgress) {
                        //更新进度
                        progressLiveData.postValue(upgradeProgress);
                    }

                    UpgradeState = 4;
                    LogUtils.iTag(TAG, "OTA_UPGRADE_PROGRESS", upgradeProgress);

                    break;
                case BluetoothConection.COMMAND_STATE_PROGRESS:
                    //升级状态
                    String subString = dataString;
                    String allUpgradState = subString.substring(0, 2);
                    subString = subString.substring(2);

                    UpgradStateData upgradStateData = new UpgradStateData();
                    upgradStateData.setUpgradState(allUpgradState);
                    ArrayList<UpgradPackageData> upgradPackageDataList = new ArrayList<>();
                    //解析数据
                    while (subString.length() > 0) {

                        //解析包序号
                        String pgId = subString.substring(0, 2);
                        //解析单包升级状态
                        String pgState = subString.substring(2, 4);
                        //解析mcu个数
                        int mcuSize = ConvertUtils.hexString2Int(subString.substring(4, 6));
                        //解析mcu详细数据
                        String mcuData = subString.substring(6, 6 + (mcuSize * 4));

                        subString = subString.substring(6 + (mcuSize * 4), subString.length());

                        upgradPackageDataList.add(new UpgradPackageData(pgId, pgState, mcuSize, mcuData));
                    }
                    upgradStateData.setPackageData(upgradPackageDataList);


                    //OTA重构
                    if (otaUpdateFirmwareUpgradeState != null) {
                        otaUpdateFirmwareUpgradeState = new OTAProgress();
                    }

                    List<OTAProgress.OTAJobModel> packageResults = new ArrayList<>();

                    for (UpgradPackageData upgradPackageData : upgradPackageDataList) {

                        //如果升级otaCheckBean异常 直接暴露失败
                        if (null == otaCheckBean.getPackages()) {
                            upgradeFail();
                        }


                        for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
                            //通过比对包序号获取包id
                            OTAProgress.OTAJobModel progress = new OTAProgress.OTAJobModel();

                            if (aPackage.getPackageSn() == ConvertUtils.hexString2Int(upgradPackageData.getPackageId())) {
                                progress.setPackageId(aPackage.getPackageId());
                                //  firmwareUpgradeStateMap.put("packageId", aPackage.getPackageId());
                                if ("00".equals(upgradPackageData.getPackageState())) {
                                    progress.setStatus("03");
                                    progress.setDetail("100");
                                    progress.setUpgradeStartTime(System.currentTimeMillis() + "");
                                    progress.setUpgradeExpectTime(System.currentTimeMillis() + "");
                                    //  firmwareUpgradeStateMap.put("status", "03");
                                    // firmwareUpgradeStateMap.put("detail", "100");
                                } else {
                                    progress.setStatus(upgradPackageData.getPackageState());
                                    progress.setDetail("0");
                                    progress.setUpgradeStartTime(System.currentTimeMillis() + "");
                                    progress.setUpgradeExpectTime(System.currentTimeMillis() + "");
//                firmwareUpgradeStateMap.put("status", upgradPackageData.getPackageState());
//                firmwareUpgradeStateMap.put("detail", "0");
                                }
                                packageResults.add(progress);

                            }
                            otaUpdateFirmwareUpgradeState.setPackageResults(packageResults);
                            otaUpdateFirmwareUpgradeState.setTimestamp(System.currentTimeMillis() + "");
                            otaUpdateFirmwareUpgradeState.setJobStatus(AwsMqttService.AWS_STATUS_IN_PROGRESS);
                            otaUpdateFirmwareUpgradeState.setMessageId(getMessageId());

                            //  firmwareUpgradeStateMap.put("packageResults",progress);

                            //otaUpdateFirmwareUpgradeStateRequest.add(firmwareUpgradeStateMap);
                        }
                    }

                    //上报每个总成固件升级状态和进度
                    AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC, deviceId, jobId),
                            GsonUtils.toJson(otaUpdateFirmwareUpgradeState)
                    );

                    upgradeStateCheck(upgradStateData.getUpgradState());

                    LogUtils.iTag(TAG, "OTA UPGRADE STATE", GsonUtils.toJson(upgradStateData));
                    break;
                case BluetoothConection.COMMAND_GET_UPGRADE_STATE:

                    //蓝牙升级状态
                    String bleUpgradeState = dataString.substring(2);
                    LogUtils.dTag(TAG, "查询蓝牙升级结果：" + bleUpgradeState);

                    //本次任务升级结果
                    String allUpgradeState = "00";
                    if (otaCheckBean == null) {
                        otaCheckBean = ((BaseApplication) getApplication()).getOtaCheckBean();

                    }
                    if (otaCheckBean != null) {

                        //如果是单蓝牙升级
                        if (null == otaUpdateFirmwareUpgradeState.getPackageResults()) {
                            otaUpdateFirmwareUpgradeState.setTimestamp(System.currentTimeMillis() + "");
                            otaUpdateFirmwareUpgradeState.setJobStatus(AwsMqttService.AWS_STATUS_IN_PROGRESS);
                            otaUpdateFirmwareUpgradeState.setMessageId(getMessageId());

                            for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
                                if ("bleModule".equals(aPackage.getComponentType())) {
                                    List<OTAProgress.OTAJobModel> packageResults2 = new ArrayList<>();
                                    OTAProgress.OTAJobModel progress = new OTAProgress.OTAJobModel();

                                    progress.setPackageId(aPackage.getPackageId());
                                    if ("00".equals(bleUpgradeState)) {
                                        progress.setStatus("03");
                                        progress.setDetail("100");
                                        progress.setUpgradeStartTime(System.currentTimeMillis() + "");

                                        progress.setUpgradeExpectTime(System.currentTimeMillis() + "");
                                    } else {
                                        progress.setStatus(bleUpgradeState);
                                        progress.setDetail("0");
                                        progress.setUpgradeStartTime(System.currentTimeMillis() + "");
                                        progress.setUpgradeExpectTime(System.currentTimeMillis() + "");
                                    }
                                    packageResults2.add(progress);
                                }
                            }
                        } else {

                            //如果是组合升级

                            List<OTAProgress.OTAJobModel> packageResults2 = otaUpdateFirmwareUpgradeState.getPackageResults();


                            for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
                                if ("bleModule".equals(aPackage.getComponentType())) {
                                    for (int i = 0; i < packageResults2.size(); i++) {

                                        if (packageResults2.get(i).getPackageId().equals(aPackage.getPackageId())) {
                                            packageResults2.get(i).setPackageId(aPackage.getPackageId());
                                            if ("00".equals(bleUpgradeState)) {
                                                packageResults2.get(i).setStatus("03");
                                                packageResults2.get(i).setDetail("100");
                                                packageResults2.get(i).setUpgradeStartTime(System.currentTimeMillis() + "");
                                                packageResults2.get(i).setUpgradeExpectTime(System.currentTimeMillis() + "");

                                            } else {
                                                packageResults2.get(i).setStatus(bleUpgradeState);
                                                packageResults2.get(i).setDetail("0");
                                                packageResults2.get(i).setUpgradeStartTime(System.currentTimeMillis() + "");
                                                packageResults2.get(i).setUpgradeExpectTime(System.currentTimeMillis() + "");

                                            }
                                        }
                                    }
                                }

                            }
                        }


                    }

                    //替换蓝牙MCU升级结果
//        for (OTACheckBean.PackagesDTO aPackage : otaCheckBean.getPackages()) {
//          if (aPackage.getComponentType().equals("bleModule")) {
//            for (int i = 0; otaUpdateFirmwareUpgradeStateRequest != null && i < otaUpdateFirmwareUpgradeStateRequest.size(); i++) {
//              Map<String, Object> firmwareUpgradeState = otaUpdateFirmwareUpgradeStateRequest.get(i);
//              if (firmwareUpgradeState.get("packageId").equals(aPackage.getPackageId())) {
//                if (bleUpgradeState.equals("00")) {
//                  firmwareUpgradeState.put("status", "03");
//                  firmwareUpgradeState.put("detail", "100");
//                } else {
//                  firmwareUpgradeState.put("status", bleUpgradeState);
//                  firmwareUpgradeState.put("detail", "0");
//                }
//              }
//            }
//          }
//        }

                    //校验是否全部MCU升级成功
                    for (OTAProgress.OTAJobModel packageResult : otaUpdateFirmwareUpgradeState.getPackageResults()) {
                        int status = Integer.parseInt((String) packageResult.getStatus(), 16);
                        if (status != 3) {
                            //如果有MCU升级未成功则本次升级任务失败
                            allUpgradeState = "03";
                            packageResult.setStatus(bleUpgradeState);
                        }
                    }


                    upgradeStateCheck(allUpgradeState);
                    break;
            }
        } catch (Exception e) {
            LogUtils.i(TAG, "onCharacteristicChanged is error--->" + e.getMessage());
        }

    }


}
