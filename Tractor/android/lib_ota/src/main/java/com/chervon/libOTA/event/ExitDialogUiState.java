package com.chervon.libOTA.event;

public class ExitDialogUiState {
    public static final int SHOW_NORMAL = 0;
    public static final int CLICK_DONE = 1;
    public static final int CLICK_CANCEL = 2;

    public boolean show;
    //0不处理，1:确认按钮 2:取消按钮
    public int status;

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
