plugins {
    id 'com.android.application'
    id 'com.facebook.react'

    id 'com.google.gms.google-services'

    id 'com.google.firebase.crashlytics'

    id 'com.huawei.agconnect'

}


react {
    root = file("../../")
    reactNativeDir = file("../../node_modules/react-native")
    codegenDir = file("../../node_modules/@react-native/codegen")
    cliFile = file("../../node_modules/react-native/cli.js")
}

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.*/
def enableProguardInReleaseBuilds = false

def jscFlavor = 'org.webkit:android-jsc:+'

android {
    compileSdk 34
    defaultConfig {
        applicationId "com.chervon.iot_pre"
        minSdkVersion this.rootProject.android.minSdkVersion
        targetSdkVersion 34
        versionCode 66
        versionName "1.3.3"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        multiDexEnabled true
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
        ndk {
            //abiFilters "armeabi-v7a", "arm64-v8a", "x86_64", "x86"
            abiFilters "armeabi-v7a", "arm64-v8a"

        }

        flavorDimensions "default"

        lintOptions {
            disable 'DescendantFindViewByIdPresent'
            disable 'GoogleAppIndexingWarning'
            disable 'DeprecatedProvider'
            abortOnError false
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    buildFeatures {
        dataBinding = true
    }
    signingConfigs {
        release {
            storeFile file("./keystore/release_iot.jks")
            storePassword "iot2022"
            keyAlias "release_iot"
            keyPassword "iot2022"
        }
        debug {
            storeFile file("./keystore/debug_iot.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
    }
    buildTypes {
        release {
            debuggable false
            signingConfig signingConfigs.release
        }
    }

    productFlavors {
        sit {
            //test
            dimension = 'default'
            applicationId "com.chervon.iot_pre"
            signingConfig signingConfigs.release
            buildConfigField "String", "BASE_URL", "\"https://gateway.sit.na.chervoncloud.com/app-hmac/\""
            buildConfigField "String", "MQTT_END_POINT", "\"a1zr96a51e1tc0-ats.iot.us-east-1.amazonaws.com\""
            buildConfigField "String", "EVN", "\"na\""
            buildConfigField "String", "DECRYPT_PWD",  "\"2q0ua230feng426\""
            manifestPlaceholders = [package_name    : "com.chervon.iot_pre",
                                    map_key         : "AIzaSyA4d32OsPDDGLgTtMDltBFbP6Rjupt1VC8",
                                    package_provider: "com.chervon.iot_pre.provider",
                                    app_icon        : "@mipmap/icon_logo_na",
                                    app_icon_round  : "@mipmap/icon_logo_round_na"]
        }

        pre_na {
            dimension = 'default'
            applicationId "com.chervon.iot_pre"
            signingConfig signingConfigs.release
            buildConfigField "String", "BASE_URL", "\"https://gateway.pre.na.chervoncloud.com/app-hmac/\""
            buildConfigField "String", "MQTT_END_POINT", "\"a2gxjoaszj0pb1-ats.iot.us-east-1.amazonaws.com\""
            buildConfigField "String", "EVN", "\"na\""
            buildConfigField "String", "DECRYPT_PWD",  "\"2q0ua230feng426\""
            manifestPlaceholders = [package_name    : "com.chervon.iot_pre",
                                    map_key         : "AIzaSyA4d32OsPDDGLgTtMDltBFbP6Rjupt1VC8",
                                    package_provider: "com.chervon.iot_pre.provider",
                                    app_icon        : "@mipmap/icon_logo_na",
                                    app_icon_round  : "@mipmap/icon_logo_round_na"]
        }

        pre_eu {
            dimension = 'default'
            applicationId "com.chervon.connect.eu.pre"
            signingConfig signingConfigs.release
            buildConfigField "String", "BASE_URL", "\"https://gateway.pre.eu.chervoncloud.com/app-hmac/\""
            buildConfigField "String", "MQTT_END_POINT", "\"a1ya79uhb2grlo-ats.iot.eu-central-1.amazonaws.com\""
            buildConfigField "String", "EVN", "\"eu\""
            buildConfigField "String", "DECRYPT_PWD",  "\"Vi5goFku2V6dzXA\""
            manifestPlaceholders = [package_name    : "com.chervon.connect.eu.pre",
                                    map_key         : "AIzaSyAhuBB--QEBB-oHQWjOZNS2tdNZAyN3G24",
                                    package_provider: "com.chervon.connect.eu.pre.provider",
                                    app_icon        : "@mipmap/icon_logo_eu",
                                    app_icon_round  : "@mipmap/icon_logo_round_eu"]
        }

        sit_eu {
            //test
            dimension = 'default'
            applicationId "com.chervon.iot_pre"
            signingConfig signingConfigs.release
            buildConfigField "String", "BASE_URL", "\"https://gateway.sit.na.chervoncloud.com/app-hmac/\""
            buildConfigField "String", "MQTT_END_POINT", "\"a1zr96a51e1tc0-ats.iot.us-east-1.amazonaws.com\""
            buildConfigField "String", "EVN", "\"eu\""
            buildConfigField "String", "DECRYPT_PWD",  "\"2q0ua230feng426\""
            manifestPlaceholders = [package_name    : "com.chervon.iot_pre",
                                    map_key         : "AIzaSyA4d32OsPDDGLgTtMDltBFbP6Rjupt1VC8",
                                    package_provider: "com.chervon.iot_pre.provider",
                                    app_icon        : "@mipmap/icon_logo_eu",
                                    app_icon_round  : "@mipmap/icon_logo_round_eu"]
        }

    }

    packagingOptions {
        pickFirst "lib/arm64-v8a/librealm-jni.so"
        pickFirst "lib/armeabi-v7a/libc++_shared.so"
        pickFirst "lib/arm64-v8a/libc++_shared.so"
        pickFirst "lib/x86/libc++_shared.so"
        pickFirst "lib/x86_64/libc++_shared.so"
        pickFirst "lib/armeabi-v7a/libfbjni.so"
        pickFirst "lib/arm64-v8a/libfbjni.so"
        pickFirst "lib/x86/libfbjni.so"
        pickFirst "lib/x86_64/libfbjni.so"
        pickFirst "lib/armeabi-v7a/libjsi.so"
        pickFirst "lib/armeabi-v7a/libglog.so"
        pickFirst "lib/armeabi-v7a/libfolly_runtime.so"
        pickFirst "lib/armeabi-v7a/libyoga.so"
        pickFirst "lib/armeabi-v7a/libreact_nativemodule_core.so"
        pickFirst "lib/armeabi-v7a/libturbomodulejsijni.so"
        pickFirst "lib/armeabi-v7a/librrc_view.so"
        pickFirst "lib/armeabi-v7a/libreact_render_core.so"
        pickFirst "lib/armeabi-v7a/libreact_render_graphics.so"
        pickFirst "lib/armeabi-v7a/libfabricjni.so"
        pickFirst "lib/armeabi-v7a/libreact_debug.so"
        pickFirst "lib/armeabi-v7a/libreact_render_componentregistry.so"
        pickFirst "lib/armeabi-v7a/libreact_render_debug.so"
        pickFirst "lib/armeabi-v7a/libruntimeexecutor.so"
        pickFirst "lib/armeabi-v7a/libreact_render_mapbuffer.so"
        pickFirst "lib/armeabi-v7a/libreact_render_uimanager.so"
        pickFirst "lib/armeabi-v7a/libreact_codegen_rncore.so"
        pickFirst "lib/armeabi-v7a/libreact_codegen_rngesturehandler.so"
        pickFirst "lib/arm64-v8a/libjsi.so"
        pickFirst "lib/arm64-v8a/libglog.so"
        pickFirst "lib/arm64-v8a/libfolly_runtime.so"
        pickFirst "lib/arm64-v8a/libyoga.so"
        pickFirst "lib/arm64-v8a/libreact_nativemodule_core.so"
        pickFirst "lib/arm64-v8a/libturbomodulejsijni.so"
        pickFirst "lib/arm64-v8a/librrc_view.so"
        pickFirst "lib/arm64-v8a/libreact_render_core.so"
        pickFirst "lib/arm64-v8a/libreact_render_graphics.so"
        pickFirst "lib/arm64-v8a/libfabricjni.so"
        pickFirst "lib/arm64-v8a/libreact_debug.so"
        pickFirst "lib/arm64-v8a/libreact_render_componentregistry.so"
        pickFirst "lib/arm64-v8a/libreact_render_debug.so"
        pickFirst "lib/arm64-v8a/libruntimeexecutor.so"
        pickFirst "lib/arm64-v8a/libreact_render_mapbuffer.so"
        pickFirst "lib/arm64-v8a/libreact_render_uimanager.so"
        pickFirst "lib/arm64-v8a/libreact_codegen_rncore.so"
        pickFirst "lib/arm64-v8a/libreact_codegen_rngesturehandler.so"
        pickFirst "lib/x86_64/libjsi.so"
        pickFirst "lib/x86_64/libglog.so"
        pickFirst "lib/x86_64/libfolly_runtime.so"
        pickFirst "lib/x86_64/libyoga.so"
        pickFirst "lib/x86_64/libreact_nativemodule_core.so"
        pickFirst "lib/x86_64/libturbomodulejsijni.so"
        pickFirst "lib/x86_64/librrc_view.so"
        pickFirst "lib/x86_64/libreact_render_core.so"
        pickFirst "lib/x86_64/libreact_render_graphics.so"
        pickFirst "lib/x86_64/libfabricjni.so"
        pickFirst "lib/x86_64/libreact_debug.so"
        pickFirst "lib/x86_64/libreact_render_componentregistry.so"
        pickFirst "lib/x86_64/libreact_render_debug.so"
        pickFirst "lib/x86_64/libruntimeexecutor.so"
        pickFirst "lib/x86_64/libreact_render_mapbuffer.so"
        pickFirst "lib/x86_64/libreact_render_uimanager.so"
        pickFirst "lib/x86_64/libreact_codegen_rncore.so"
        pickFirst "lib/x86_64/libreact_codegen_rngesturehandler.so"
        pickFirst "lib/x86/libjsi.so"
        pickFirst "lib/x86/libglog.so"
        pickFirst "lib/x86/libfolly_runtime.so"
        pickFirst "lib/x86/libyoga.so"
        pickFirst "lib/x86/libreact_nativemodule_core.so"
        pickFirst "lib/x86/libturbomodulejsijni.so"
        pickFirst "lib/x86/librrc_view.so"
        pickFirst "lib/x86/libreact_render_core.so"
        pickFirst "lib/x86/libreact_render_graphics.so"
        pickFirst "lib/x86/libfabricjni.so"
        pickFirst "lib/x86/libreact_debug.so"
        pickFirst "lib/x86/libreact_render_componentregistry.so"
        pickFirst "lib/x86/libreact_render_debug.so"
        pickFirst "lib/x86/libruntimeexecutor.so"
        pickFirst "lib/x86/libreact_render_mapbuffer.so"
        pickFirst "lib/x86/libreact_render_uimanager.so"
        pickFirst "lib/x86/libreact_codegen_rncore.so"
        pickFirst "lib/x86/libreact_codegen_rngesturehandler.so"
    }

    //Version output package name auto append version number and version name
    applicationVariants.all { variant ->
        variant.outputs.all {
            def createTime = new Date().format("MM-dd", TimeZone.getTimeZone("GMT+08:00"))
            // App package name
            outputFileName = "EGO_IOT_Android_V" + defaultConfig.versionName + "_" + buildType.name + "_" + createTime + ".apk"
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }


}

//pictureselector库 kotlin版本过高，使用以下配置
configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.6.0'
        force 'androidx.core:core:1.6.0'

        force 'androidx.appcompat:appcompat:1.3.1'
        force "androidx.activity:activity:1.3.1"
        force "androidx.fragment:fragment:1.3.1"
    }
}


dependencies {
    implementation this.rootProject.ext.dependencies.constraintLayout
    implementation 'com.github.tbruyelle:rxpermissions:0.10.2'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.0'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.4'
    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'org.jetbrains:annotations:15.0'
    implementation 'androidx.navigation:navigation-fragment:2.3.5'
    implementation 'androidx.navigation:navigation-ui:2.3.5'

    implementation project(path: ':lib_router')
    implementation project(path: ':lib_base')
    implementation project(path: ':lib_network')

    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }

    if (isBuildModule.toBoolean()) {

    } else {
        implementation project(path: ':module_container')
        implementation project(path: ':module_configNet')
        implementation project(path: ':module_deviceManage')
        implementation project(path: ':module_messageCenter')
        implementation project(path: ':module_oobe')
        implementation project(path: ':module_userCenter')
        implementation project(path: ':lib_ota')
        implementation project(path: ':module_explore')

    }


    implementation 'com.alibaba:arouter-api:1.5.2'
    annotationProcessor 'com.alibaba:arouter-compiler:1.5.2'
    implementation("com.facebook.react:react-android")

    implementation 'androidx.work:work-runtime:2.7.1'
    implementation 'com.android.support:multidex:2.0.1'
    // Import the BoM for the Firebase platform
    implementation platform('com.google.firebase:firebase-bom:30.5.0')
    implementation "com.google.firebase:firebase-iid"

    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'

    // 如果你需要支持GIF动图
    implementation 'com.facebook.fresco:animated-gif:2.0.0'

}

// Add the following line to the bottom of the file:
apply plugin: 'com.google.gms.google-services'  // Google Play services Gradle plugin
//RN Autolinking
apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)