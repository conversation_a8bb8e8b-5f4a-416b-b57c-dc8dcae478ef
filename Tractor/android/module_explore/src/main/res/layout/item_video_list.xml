<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="video"
            type="com.chervon.module_explore.data.VideoModel" />
        <variable
            name="isSelected"
            type="Boolean" />
        <variable
            name="viewModel"
            type="com.chervon.module_explore.viewmodel.MediaPlayerViewModel2" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black">

        <FrameLayout
            android:id="@+id/playerContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.exoplayer2.ui.PlayerView
                android:id="@+id/playerView"
                android:layout_width="match_parent"
                android:layout_gravity="center"
                android:layout_height="match_parent"
                app:resize_mode="fit"
                app:use_controller="false" />

            <ImageView
                android:id="@+id/iconControl"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:src="@drawable/icon_explore_play" />

            <ProgressBar
                android:id="@+id/progress"
                style="@android:style/Widget.ProgressBar"
                android:layout_width="50mm"
                android:layout_height="50mm"
                android:layout_gravity="center"
                android:background="@null"
                android:indeterminateDrawable="@drawable/progress"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </FrameLayout>

        <!-- 自定义进度条 -->
        <com.chervon.module_explore.widget.VideoSeekBar
            android:id="@+id/videoSeekBar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
