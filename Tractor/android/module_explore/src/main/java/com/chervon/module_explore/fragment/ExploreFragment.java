package com.chervon.module_explore.fragment;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.EXTRA_IMAGE_URL;
import static com.chervon.libRouter.RouterConstants.EXTRA_TRANSITION_NAME;
import static com.chervon.libRouter.RouterConstants.EXTRA_VIDEO_LIST_URL;
import static com.chervon.libRouter.RouterConstants.EXTRA_VIDEO_PLAY_POSITION_URL;

import android.content.ComponentCallbacks;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.content.res.Configuration;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.RequiresApi;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.viewpager2.widget.ViewPager2;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.Utils;
import com.chervon.libRouter.RouterConstants;
import com.chervon.module_explore.ExploreApplication;
import com.chervon.module_explore.R;
import com.chervon.module_explore.adapter.BannerAdapter;
import com.chervon.module_explore.adapter.ContentAdapter;
import com.chervon.module_explore.data.res.HaveFunRes;
import com.chervon.module_explore.databinding.FragmentExploreBinding;
import com.chervon.module_explore.utils.FoldableDeviceCodes;
import com.chervon.module_explore.viewmodel.ExploreViewModel;

import java.util.ArrayList;
import java.util.HashMap;

public class ExploreFragment extends BaseEnhanceFragment<ExploreViewModel, FragmentExploreBinding> {
    private static final String TAG = "ExploreFragment";
    private FragmentExploreBinding binding;
    private BannerAdapter bannerAdapter;
    private ContentAdapter contentAdapter;
    private Handler autoScrollHandler;
    private final long AUTO_SCROLL_DELAY = 3000;
    private View customStatusBar;
    private int customStatusBarHeight;

    @Override
    protected void initDatas(Bundle savedInstanceState) {
        initTrace();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_explore;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
    }

    @Override
    protected void onUnRegister() {
        stopAutoScroll();
    }

    @Override
    protected void onRegister() {
        initTrace();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setRetainInstance(true);
        requireActivity().registerComponentCallbacks(new ComponentCallbacks() {
            @Override
            public void onConfigurationChanged(@NonNull Configuration newConfig) {
                ExploreFragment.this.onConfigurationChanged(newConfig);
            }

            @Override
            public void onLowMemory() {
            }
        });
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (binding == null) {
            binding = FragmentExploreBinding.inflate(inflater, container, false);
        }
        binding.setLifecycleOwner(getViewLifecycleOwner());

        return binding.getRoot();
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
//        viewModel = new ViewModelProvider(requireActivity()).get(ExploreViewModel.class);
        binding.setViewModel(mViewModel);
        binding.setLifecycleOwner(getViewLifecycleOwner());
        setStatusBarPadding();
        setupBanner();
        setupContentList();
        observeViewModel();
        setupStatusBarScrollToTop();
    }

    private void setupStatusBarScrollToTop() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // 设置ScrollView的滚动监听
            binding.scroll.setOnScrollChangeListener((View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) -> {
                if (scrollY > customStatusBarHeight) {
                    customStatusBar.setVisibility(View.VISIBLE);
                } else {
                    customStatusBar.setVisibility(View.GONE);
                }
                // 保存滚动位置
                ExploreApplication.scrollY = scrollY;
                ExploreApplication.scrollX = scrollX;
            });
            customStatusBar.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    binding.scroll.scrollTo(0, 0);
                }
            });
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (null != contentAdapter) {
            contentAdapter.notifyDataSetChanged();
        }
    }

    private void setStatusBarPadding() {
        customStatusBar = binding.customStatusbar;
        customStatusBarHeight = binding.customStatusbar.getHeight();
    }

    private void setupBanner() {
        bannerAdapter = new BannerAdapter(new BannerAdapter.BannerClickListener() {
            @Override
            public void bannerClick(int position, String url, String modelNumber) {
                switch (position % bannerAdapter.getRealItemCount()) {
                    case 0:
                        saleBanner1Click(modelNumber);
                        break;
                    case 1:
                        saleBanner2Click(modelNumber);
                        break;
                    case 2:
                        clickNewProductBanner();
                        break;
                }
                jumpToWeb(url);
            }
        });
        binding.bannerViewpager.setAdapter(bannerAdapter);

        binding.bannerViewpager.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                updateIndicator(position % bannerAdapter.getRealItemCount());
                super.onPageSelected(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
                if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                    bannerSwipeClick();
                    stopAutoScroll();
                } else if (state == ViewPager2.SCROLL_STATE_IDLE) {
                    autoScrollHandler.postDelayed(() -> startAutoScroll(), 3000);
                }
            }
        });

        autoScrollHandler = new Handler(Looper.getMainLooper());
    }

    private void startAutoScroll() {
        stopAutoScroll();
        autoScrollHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (binding != null && binding.bannerViewpager != null && bannerAdapter != null) {
                    binding.bannerViewpager.setCurrentItem(binding.bannerViewpager.getCurrentItem() + 1, true);
                }
                autoScrollHandler.postDelayed(this, AUTO_SCROLL_DELAY);
            }
        }, AUTO_SCROLL_DELAY);
    }

    private void stopAutoScroll() {
        if (autoScrollHandler != null) {
            autoScrollHandler.removeCallbacksAndMessages(null);
        }
    }

    private void updateIndicator(int position) {
        if (binding.indicatorContainer.getChildCount() == 0) {
            return;
        }
        for (int i = 0; i < binding.indicatorContainer.getChildCount(); i++) {
            ImageView indicator = (ImageView) binding.indicatorContainer.getChildAt(i);
            indicator.setImageResource(i == position ? R.drawable.indicator_selected : R.drawable.indicator_normal);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    private void setupContentList() {
        contentAdapter = new ContentAdapter(new ContentAdapter.OnItemClickListener() {
            @Override
            public void onVideoPlayClicked(ArrayList<HaveFunRes.HaveFunBean> videoList, String currentUrl, int position) {
                if (videoList.isEmpty()) {
                    return;
                }
                videoPlayButtonClick();
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_EXPLORE_VIDEO_LIST)
                        .withSerializable(EXTRA_VIDEO_LIST_URL, videoList)
                        .withInt(EXTRA_VIDEO_PLAY_POSITION_URL, position)
                        .withSerializable(EXTRA_TRANSITION_NAME, currentUrl).navigation();
            }

            @Override
            public void onPictureClicked(HaveFunRes.HaveFunBean item) {
                imageClick(item.getId()+"");
                ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_EXPLORE_PICTURE_DETAIL)
                        .withSerializable(EXTRA_IMAGE_URL, item.getIvUrl())
                        .withSerializable(EXTRA_TRANSITION_NAME, item.getIvUrl())
                        .navigation();
            }

            @Override
            public void linkUrlClicked(String url) {
                jumpToWeb(url);
            }
        });

        binding.contentRecyclerView.setAdapter(contentAdapter);
        binding.contentRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        binding.scroll.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View view, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                ExploreApplication.scrollY = scrollY;
                ExploreApplication.scrollX = scrollX;
            }
        });
    }

    private void jumpToWeb(String routePath) {
        if (TextUtils.isEmpty(routePath)) {
            return;
        }
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri content_url = Uri.parse(routePath);
        intent.setData(content_url);
        startActivity(intent);
    }

    private boolean isDataLoaded = false;

    private void observeViewModel() {
        if (isDataLoaded) {
            return;
        }
        binding.progress.setVisibility(View.VISIBLE);
        binding.line.setVisibility(View.GONE);
        mViewModel.bannerItems.observe(getViewLifecycleOwner(), bannerItems -> {
            LogUtils.e("getBannerItems---viewModel");
            isDataLoaded = true;
            binding.progress.setVisibility(View.GONE);
            binding.line.setVisibility(View.VISIBLE);
            if (bannerItems != null && !bannerItems.isEmpty()) {


                //接口数据需要反馈给埋点，且只执行一次
                if (bannerAdapter.getRealItemCount() == 0){
                    String banner1ModelNumber = "";
                    String banner2ModelNumber= "";
                    //第1个modelNumber
                    if (null!=bannerItems.get(0)){
                        banner1ModelNumber = bannerItems.get(0).getmModelNumber();
                    }
                    //第2个modelNumber
                    if (null!=bannerItems.get(1)){
                        banner2ModelNumber = bannerItems.get(1).getmModelNumber();
                    }

                    saleBannerExposure(banner1ModelNumber,banner2ModelNumber);
                }


                binding.bannerViewpager.setVisibility(View.VISIBLE);
                binding.indicatorContainer.setVisibility(View.VISIBLE);
                bannerAdapter.setBanners(bannerItems);
                setupIndicators(bannerItems.size());
                binding.bannerViewpager.setCurrentItem(Integer.MAX_VALUE / 2, false);
                updateIndicator(0);
                startAutoScroll();
            } else {
                binding.bannerViewpager.setVisibility(View.GONE);
                binding.indicatorContainer.setVisibility(View.GONE);
                binding.line.setVisibility(View.GONE);
            }

        });

        mViewModel.contentItems.observe(getViewLifecycleOwner(), contentItems -> {
            if (contentItems != null) {
                //对于折叠机型处理
                if (isTableTopPosture() && ExploreApplication.onDestroyView && BaseApplication.networkAvailable) {
                    LogUtils.e(TAG, "折叠机型处理补发接口一次");
                    mViewModel.refresh();
                    ExploreApplication.onDestroyView = false;
                }

                if (!contentItems.isEmpty()){
                    contentAdapter.setContentItems(contentItems);
                    mViewModel.showFootView.postValue(true);
                }else {
                    mViewModel.showFootView.postValue(false);
                }

                binding.scroll.post(() -> {
                    binding.scroll.scrollTo(ExploreApplication.scrollX, ExploreApplication.scrollY);
                });
            }else {
                mViewModel.showFootView.postValue(false);
            }
        });
        mViewModel.isNetworkAvailable.observe(getActivity(), new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isNetworkAvailable) {
                if (isNetworkAvailable) {
                    binding.constraintNetwork.setVisibility(View.GONE);
                    binding.scroll.setVisibility(View.VISIBLE);
                } else {
                    if (contentAdapter != null) {
                        if (contentAdapter.getItemCount() == 0) {
                            binding.scroll.setVisibility(View.GONE);
                            binding.constraintNetwork.setVisibility(View.VISIBLE);
                        }
                        //对于折叠机型处理
                        if (ExploreApplication.onDestroyView) {
                            binding.scroll.setVisibility(View.GONE);
                            binding.constraintNetwork.setVisibility(View.VISIBLE);
                            ExploreApplication.onDestroyView = false;
                        }

                    }
                }


            }
        });
    }

    private void setupIndicators(int count) {
        binding.indicatorContainer.removeAllViews();

        for (int i = 0; i < count; i++) {
            ImageView indicator = new ImageView(requireContext());
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    getResources().getDimensionPixelSize(R.dimen.indicator_size),
                    getResources().getDimensionPixelSize(R.dimen.indicator_size));
            params.setMargins(
                    getResources().getDimensionPixelSize(R.dimen.indicator_margin),
                    0,
                    getResources().getDimensionPixelSize(R.dimen.indicator_margin),
                    0);
            indicator.setLayoutParams(params);
            indicator.setImageResource(R.drawable.indicator_normal);
            binding.indicatorContainer.addView(indicator);
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        if (autoScrollHandler != null) {
            autoScrollHandler.removeCallbacksAndMessages(null);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (autoScrollHandler != null && bannerAdapter != null && bannerAdapter.getRealItemCount() > 0) {
            startAutoScroll();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (autoScrollHandler != null) {
            autoScrollHandler.removeCallbacksAndMessages(null);
        }
        if (contentAdapter != null) {
            contentAdapter.clearBoundViewHolders();
        }
        requireActivity().unregisterComponentCallbacks(this);
    }

    @Override
    protected void onLowMemoryProcessed() {
    }

    @Override
    protected Class<? extends ExploreViewModel> getViewModelClass() {
        return ExploreViewModel.class;
    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {
    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }

    private void initTrace() {
        stayEleId = "1";
        pageId = "551";
        mouduleId = "106";
        pageResouce = "1_9";
        nextButtoneleid = "2";
    }

    private void saleBanner1Click(String modelNumber) {
        String eleId = "2";
        String MODEL_NUMBER_KEY = "model_number";
        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(MODEL_NUMBER_KEY, modelNumber);
        Utils.sendClickTraceNew(this.getContext(), mouduleId, pageId, pageResouce, eleId, expandMap);
    }

    private void saleBanner2Click(String modelNumber) {
        String eleId = "3";
        String MODEL_NUMBER_KEY = "model_number";
        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(MODEL_NUMBER_KEY, modelNumber);
        Utils.sendClickTraceNew(this.getContext(), mouduleId, pageId, pageResouce, eleId, expandMap);
    }

    private void clickNewProductBanner() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "4", "1");
    }

    private void bannerSwipeClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "5", "1");
    }

    private void videoPlayButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "6", "1");
    }

    private void imageClick(String imageId) {
        String eleId = "7";
        String IMAGE_ID_KEY = "image_id";
        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(IMAGE_ID_KEY, imageId);
        Utils.sendClickTraceNew(this.getContext(), mouduleId, pageId, pageResouce, eleId, expandMap);

    }

    /**
     * 产品促销卡片1曝光
     * Context context, String moduleId, String pageId, String pageSource, HashMap<String, String> expandMap
     */
    private void saleBannerExposure(String modelNumber1,String modelNumber2){
        String eleId = "10";
        String BANNER1_KEY = "banner1";
        String BANNER2_KEY = "banner2";
        String NEW_PRODUCT1_KEY = "banner3";
        //默认写死
        String NEW_PRODUCT1_VALUE = "new";
        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(BANNER1_KEY, modelNumber1);
        expandMap.put(BANNER2_KEY, modelNumber2);
        expandMap.put(NEW_PRODUCT1_KEY, NEW_PRODUCT1_VALUE);

        Utils.sendExposureContainEleId(BaseApplication.getInstance(),mouduleId,pageId,pageResouce,eleId,expandMap);
    }


    public boolean isTableTopPosture() {
        return  FoldableDeviceCodes.isFoldableDevice(Build.MANUFACTURER,Build.MODEL);
    }
}
