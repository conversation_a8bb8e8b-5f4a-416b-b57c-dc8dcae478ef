package com.chervon.module_explore.adapter;

import static androidx.recyclerview.widget.RecyclerView.NO_POSITION;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.utils.VideoPositionManager;
import com.chervon.module_explore.data.MediaPlaybackState;
import com.chervon.module_explore.data.VideoModel;
import com.chervon.module_explore.databinding.ItemVideoListBinding;
import com.chervon.module_explore.viewmodel.MediaPlayerViewModel2;
import com.chervon.module_explore.widget.VideoSeekBar;
import com.google.android.exoplayer2.ExoPlayer;

import java.util.ArrayList;
import java.util.List;

public class VideoListAdapter extends RecyclerView.Adapter<VideoListAdapter.VideoViewHolder> {
    private static final String TAG = "VideoListAdapter";
    private static List<VideoModel> videos = new ArrayList<>();
    private int selectedPosition = -1;
    private LifecycleOwner lifecycleOwner;
    private Application mApplication;
    private RecyclerView recyclerView;

    private  VideoAdapterCallBack videoAdapterCallBack;
    public VideoListAdapter(Application application, List<VideoModel> video,
                            LifecycleOwner lifecycleOwner,
                            VideoAdapterCallBack videoCallBack) {
        this.mApplication = application;
        this.videos = video;
        this.lifecycleOwner = lifecycleOwner;
        this.videoAdapterCallBack = videoCallBack;
        NetworkUtils.registerNetworkStatusChangedListener(networkChangedListener);
    }

    private final NetworkUtils.OnNetworkStatusChangedListener networkChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
        @Override
        public void onDisconnected() {
            if (selectedPosition != -1) {
                notifyItemChanged(selectedPosition);
            }
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {
            if (selectedPosition != -1) {
                notifyItemChanged(selectedPosition);
            }
        }
    };

    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemVideoListBinding binding = ItemVideoListBinding.inflate(LayoutInflater.from(parent.getContext()),
                parent, false);
        MediaPlayerViewModel2 viewModel = new MediaPlayerViewModel2(mApplication);
        return new VideoViewHolder(binding, viewModel, lifecycleOwner);
    }

    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        VideoModel video = videos.get(position);
        
        // 设置视频数据
        holder.viewModel.setPlay(video);
        
        if (position == selectedPosition) {
            // 当前选中的视频
            if (holder.viewModel.getPlayer() == null) {
                holder.viewModel.initializePlayer();
            }
            holder.bind(holder.viewModel, true);
            
            // 预加载相邻视频
            preloadAdjacentVideos(position);
            
            // 确保视频开始播放
            if (holder.viewModel.getPlayer() != null && !holder.viewModel.getPlayer().isPlaying()) {
                holder.viewModel.playVideo();
            }
        } else if (position == selectedPosition - 1 || position == selectedPosition + 1) {
            // 相邻位置，预加载但不播放
            if (holder.viewModel.getPlayer() == null) {
                holder.viewModel.initializePlayer();
                ExoPlayer player = holder.viewModel.getPlayer();
                if (player != null) {
                    player.prepare();
                    player.pause();
                }
            }
            holder.bind(holder.viewModel, false);
        } else {
            // 其他位置，保持当前状态
            holder.bind(holder.viewModel, false);
        }
    }

    private void preloadAdjacentVideos(int currentPosition) {
        // 预加载上一个视频
        if (currentPosition > 0) {
            preloadVideo(currentPosition - 1);
        }
        
        // 预加载下一个视频
        if (currentPosition + 1 < videos.size()) {
            preloadVideo(currentPosition + 1);
        }
    }

    private void preloadVideo(int position) {
        RecyclerView.ViewHolder holder = recyclerView != null ? 
            recyclerView.findViewHolderForAdapterPosition(position) : null;
            
        if (holder instanceof VideoViewHolder) {
            VideoViewHolder videoHolder = (VideoViewHolder) holder;
            VideoModel video = videos.get(position);
            videoHolder.viewModel.setPlay(video);
            if (videoHolder.viewModel.getPlayer() == null) {
                videoHolder.viewModel.initializePlayer();
                ExoPlayer player = videoHolder.viewModel.getPlayer();
                if (player != null) {
                    player.prepare();
                    player.pause();
                }
            }
            videoHolder.bind(videoHolder.viewModel, false);
        }
    }

    @Override
    public void onViewRecycled(@NonNull VideoViewHolder holder) {
        super.onViewRecycled(holder);
        holder.onDetached();
    }

    @Override
    public int getItemCount() {
        return videos.size();
    }

    public void setVideos(List<VideoModel> videos) {
        this.videos = videos;
        notifyDataSetChanged();
    }

    public void setSelectedPosition(int position) {
        int oldPosition = selectedPosition;
        selectedPosition = position;
        
        // 通知更新当前位置和前一个位置
        notifyItemChanged(oldPosition);
        notifyItemChanged(position);
        
        // 预加载相邻视频
        if (position > 0) {
            notifyItemChanged(position - 1);
        }
        if (position + 1 < videos.size()) {
            notifyItemChanged(position + 1);
        }
    }

    public int getSelectedPosition() {
        return selectedPosition;
    }

    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        this.recyclerView = recyclerView;
    }

    @Override
    public void onDetachedFromRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onDetachedFromRecyclerView(recyclerView);
        this.recyclerView = null;
    }

    public  class VideoViewHolder extends RecyclerView.ViewHolder {
        private final ItemVideoListBinding binding;
        public final MediaPlayerViewModel2 viewModel;
        private final Handler handler;
        private final Runnable progressRunnable;
        private final LifecycleOwner lifecycleOwner;

        VideoViewHolder(ItemVideoListBinding binding, MediaPlayerViewModel2 viewModel, LifecycleOwner lifecycleOwner) {
            super(binding.getRoot());
            this.binding = binding;
            this.viewModel = viewModel;
            this.lifecycleOwner = lifecycleOwner;
            binding.setViewModel(viewModel);

            handler = new Handler(Looper.getMainLooper());
            progressRunnable = new Runnable() {
                @Override
                public void run() {
                    updateProgress();
                    handler.postDelayed(this, 200);
                }
            };

            setupPlayerView();
            setupControlButton();
            setupSeekBar();
            observePlaybackState();
            observeNetworkState();
            addTouchListener(binding);
        }

        private void setupPlayerView() {
            viewModel.getPlayerLiveData().observe(lifecycleOwner, exoPlayer -> {
                if (exoPlayer != null) {
                    binding.playerView.setPlayer(exoPlayer);
                    binding.playerView.setKeepContentOnPlayerReset(true);
                    binding.playerView.setUseArtwork(true);
                    LogUtils.i(TAG, "播放器实例已更新");
                }
            });
        }

        private void setupControlButton() {
            binding.iconControl.setOnClickListener(v -> {
                MediaPlaybackState state = viewModel.getPlaybackState().getValue();
                if (state != null && state.isEnded) {
                    ExoPlayer player = viewModel.getPlayer();
                    if (player != null) {
                        player.seekTo(0);
                    }
                }
                
                // 检查视频是否已缓存
                VideoModel video = videos.get(getBindingAdapterPosition());
                String videoUrl = video.getVideoUrl().getIvUrl();
                boolean isCached = videoUrl != null && BaseApplication.videoCacheManager.isCached(videoUrl);
                
                if (isCached || NetworkUtils.isConnected()) {
                    viewModel.playVideo();
                    binding.iconControl.setVisibility(View.GONE);
                    LogUtils.i(TAG, isCached ? "播放缓存视频" : "播放在线视频");
                } else {
                    LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                }
            });
        }

        private void setupSeekBar() {
            binding.videoSeekBar.setOnSeekBarChangeListener(new VideoSeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(VideoSeekBar seekBar, float progress, boolean fromUser) {
                    if (fromUser) {
                        if (null!=videoAdapterCallBack){
                            videoAdapterCallBack.videoDragButtonClickCallBack();
                        }
                        ExoPlayer player = viewModel.getPlayer();
                        if (player != null) {
                            binding.videoSeekBar.setProgress(progress);
                        }
                    }
                }

                @Override
                public void onStartTrackingTouch(VideoSeekBar seekBar) {
                    ExoPlayer player = viewModel.getPlayer();
                    if (player != null) {
                        player.pause();
                    }
                }

                @Override
                public void onStopTrackingTouch(VideoSeekBar seekBar) {
                    ExoPlayer player = viewModel.getPlayer();
                    if (player != null) {
                        // 检查视频是否已缓存
                        VideoModel video = videos.get(getBindingAdapterPosition());
                        String videoUrl = video.getVideoUrl().getIvUrl();
                        boolean isCached = videoUrl != null && BaseApplication.videoCacheManager.isCached(videoUrl);
                        
                        if (isCached || NetworkUtils.isConnected()) {
                            long duration = player.getDuration();
                            long newPosition = (long) (seekBar.getProgress() * duration);
                            player.seekTo(newPosition);
                            player.play();
                            LogUtils.i(TAG, isCached ? "播放缓存视频" : "播放在线视频");
                        } else {
                            LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                        }
                    }
                }
            });
        }

        private void observePlaybackState() {
            viewModel.getPlaybackState().observe(lifecycleOwner, state -> {
                boolean isLoading = state.isBuffering || state.loading || state.playbackException != null;
                
                if (isLoading) {
                    binding.progress.setVisibility(View.VISIBLE);
                    binding.iconControl.setVisibility(View.GONE);
                } else {
                    binding.progress.setVisibility(View.GONE);

                    if (state.isPlaying && !state.isEnded) {
                        binding.iconControl.setVisibility(View.GONE);
                    } else {
                        binding.iconControl.setVisibility(View.VISIBLE);
                        
                        // 如果视频播放结束，自动播放下一个
                        if (state.isEnded) {
                            int currentPosition = getBindingAdapterPosition();
                            if (currentPosition != NO_POSITION) {
                                // 清除当前视频的保存位置
                                VideoModel video = videos.get(currentPosition);
                                String videoUrl = video.getVideoUrl().getIvUrl();
                                if (videoUrl != null) {
                                    BaseApplication.getInstance().videoPositionManager.remove(videoUrl);
                                }
                                
                                // 如果不是最后一个视频，自动播放下一个
                                if (currentPosition < videos.size() - 1) {
                                    RecyclerView recyclerView = (RecyclerView) itemView.getParent();
                                    if (recyclerView != null) {
                                        recyclerView.smoothScrollToPosition(currentPosition + 1);
                                    }
                                }
                            }
                        }
                    }
                }
            });
        }

        private void observeNetworkState() {
            viewModel.getNetworkAvailable().observe(lifecycleOwner, isNetworkAvailable -> {
                if (!isNetworkAvailable) {
                    // 检查视频是否已缓存
                    if (NO_POSITION == getBindingAdapterPosition()){
                        return;
                    }
                    VideoModel video = videos.get(getBindingAdapterPosition());
                    String videoUrl = video.getVideoUrl().getIvUrl();
                    boolean isCached = videoUrl != null && BaseApplication.videoCacheManager.isCached(videoUrl);
                    
                    ExoPlayer player = viewModel.getPlayer();
                    if (player != null && player.isPlaying()) {
                        if (!isCached) {
                            // 如果视频未缓存，暂停播放并显示加载进度
                            player.pause();
                            binding.progress.setVisibility(View.VISIBLE);
                            LogUtils.i(TAG, "网络断开，视频未缓存，暂停播放");
                        } else {
                            // 如果视频已缓存，继续播放
                            binding.progress.setVisibility(View.GONE);
                            LogUtils.i(TAG, "网络断开，但视频已缓存，继续播放");
                        }
                    }
                } else {
                    MediaPlaybackState state = viewModel.getPlaybackState().getValue();
                    if (state != null && !state.isBuffering && !state.loading) {
                        binding.progress.setVisibility(View.GONE);
                        if (state.isPlaying) {
                            ExoPlayer player = viewModel.getPlayer();
                            if (player != null) {
                                player.play();
                            }
                        }
                    }
                }
            });
        }

        private void handlePlayerClick() {
            // 检查视频是否已缓存
            VideoModel video = videos.get(getBindingAdapterPosition());
            String videoUrl = video.getVideoUrl().getIvUrl();
            boolean isCached = videoUrl != null && BaseApplication.videoCacheManager.isCached(videoUrl);
            
            if (!isCached && !NetworkUtils.isConnected()) {
                LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                return;
            }
            
            MediaPlaybackState state = viewModel.getPlaybackState().getValue();
            if (state != null) {
                if (!state.loading && !state.isBuffering && state.playbackException == null) {
                    boolean isCurrentlyPlaying = state.isPlaying;
                    viewModel.onPlayerClicked();

                    if (isCurrentlyPlaying) {
                        binding.iconControl.setVisibility(View.VISIBLE);
                        if (null!=videoAdapterCallBack){
                            videoAdapterCallBack.videoPauseClickCallBack();
                        }
                    } else {
                        binding.iconControl.setVisibility(View.GONE);
                    }
                    
                    if (!isCurrentlyPlaying) {
                        LogUtils.i(TAG, isCached ? "播放缓存视频" : "播放在线视频");
                    }
                }
            }
        }

        private void updateProgress() {
            ExoPlayer player = viewModel.getPlayer();
            if (player != null) {
                try {
                    MediaPlaybackState state = viewModel.getPlaybackState().getValue();
                    if (state != null && (state.isPlaying || state.isEnded)) {
                        long position = player.getCurrentPosition();
                        long duration = player.getDuration();
                        if (duration > 0) {
                            if (state.isEnded) {
                                binding.videoSeekBar.setProgress(1.0f);
                            } else {
                                binding.videoSeekBar.setProgress((float) position / duration);
                            }
                            binding.videoSeekBar.setDuration(duration);
                        }
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "更新进度失败: " + e.getMessage());
                }
            }
        }

        private void addTouchListener(ItemVideoListBinding binding){
            binding.playerView.setOnTouchListener(new View.OnTouchListener() {
                private float startX, startY;
                private static final int CLICK_ACTION_THRESHOLD = 10;
                private boolean isClick = true;

                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    // 记录每个事件类型
                    LogUtils.d(TAG, "触摸事件: " + getActionString(event.getAction()));

                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            startX = event.getX();
                            startY = event.getY();
                            isClick = true;
                            LogUtils.d(TAG, "DOWN事件: 位置(" + startX + "," + startY + ")");
                            return true; // 返回true表示我们处理整个触摸序列

//                        case MotionEvent.ACTION_MOVE:
//                            // 如果移动距离超过阈值，认为不是点击
//                            float moveX = event.getX();
//                            float moveY = event.getY();
//                            float deltaX = moveX - startX;
//                            float deltaY = moveY - startY;
//
//                            if (isClick && (Math.abs(deltaX) > CLICK_ACTION_THRESHOLD ||
//                                    Math.abs(deltaY) > CLICK_ACTION_THRESHOLD)) {
//                                isClick = false;
//                                LogUtils.d(TAG, "移动超过阈值，不再是点击: deltaX=" + deltaX + ", deltaY=" + deltaY);
//                            }
//                            return true; // 继续处理

                        case MotionEvent.ACTION_UP:
                            float endX = event.getX();
                            float endY = event.getY();
                            float diffX = endX - startX;
                            float diffY = endY - startY;

                            LogUtils.d(TAG, "UP事件: 位置(" + endX + "," + endY + "), 差值(" + diffX + "," + diffY + ")");

                            // 获取屏幕尺寸
                            int screenWidth = mApplication.getResources().getDisplayMetrics().widthPixels;
                            int screenHeight = mApplication.getResources().getDisplayMetrics().heightPixels;
                            float oneFifthScreenWidth = screenWidth / 5.0f;
                            float oneFifthScreenHeight = screenHeight / 5.0f;

                            // 检查滑动方向和距离
                            // X轴: 从左往右滑动时，endX > startX，所以diffX > 0
                            // Y轴: 从上往下滑动时，endY > startY，所以diffY > 0

                            // 判断滑动方向
                            boolean isHorizontalSwipe = Math.abs(diffX) > Math.abs(diffY);
                            boolean isVerticalSwipe = !isHorizontalSwipe;

                            // 判断具体方向
                            boolean isRightDirection = diffX > 0;
                            boolean isUpDirection = diffY < 0; // 上滑时，endY < startY，所以diffY < 0
                            boolean isDownDirection = diffY > 0; // 下滑时，endY > startY，所以diffY > 0

                            // 判断滑动距离是否超过阈值
                            boolean isHorizontalLongEnough = Math.abs(diffX) > oneFifthScreenWidth;
                            boolean isVerticalLongEnough = Math.abs(diffY) > oneFifthScreenHeight;

                            LogUtils.d(TAG, "滑动分析 - 水平滑动: " + isHorizontalSwipe +
                                    ", 竖直滑动: " + isVerticalSwipe +
                                    ", 右滑方向: " + isRightDirection +
                                    ", 上滑方向: " + isUpDirection +
                                    ", 下滑方向: " + isDownDirection +
                                    ", X距离足够: " + isHorizontalLongEnough +
                                    ", Y距离足够: " + isVerticalLongEnough +
                                    ", diffX=" + diffX + ", diffY=" + diffY);

                            // 处理不同方向的滑动
//                            if (!isClick) {
//                                if (isHorizontalSwipe && isRightDirection && isHorizontalLongEnough) {
//                                    // 满足右滑条件，退出Activity
//                                    LogUtils.i(TAG, "右滑退出，滑动距离: " + Math.abs(diffX) +
//                                            ", 屏幕五分之一: " + oneFifthScreenWidth);
//                                 if (null!=videoAdapterCallBack){
//                                     videoAdapterCallBack.viewDragRightCallBack();
//                                     return true;
//                                 }else {
//                                     return false;
//                                 }
//                                }
//                            }else {
//                                handlePlayerClick();
//                                return true;
//                            }

                            if (isClick) {
                                handlePlayerClick();
                            }
                            break;

                        case MotionEvent.ACTION_CANCEL:
                            LogUtils.d(TAG, "触摸事件被取消");
                            break;
                    }
                    return false; // 消费所有事件
                }

                // 获取事件类型的字符串表示
                private String getActionString(int action) {
                    switch(action) {
                        case MotionEvent.ACTION_DOWN: return "ACTION_DOWN";
                        case MotionEvent.ACTION_MOVE: return "ACTION_MOVE";
                        case MotionEvent.ACTION_UP: return "ACTION_UP";
                        case MotionEvent.ACTION_CANCEL: return "ACTION_CANCEL";
                        default: return "ACTION_" + action;
                    }
                }
            });
        }


        void bind(MediaPlayerViewModel2 video, boolean isSelected) {
            binding.setViewModel(video);
            binding.executePendingBindings();
            ExoPlayer player = video.getPlayer();
            
            if (isSelected) {
                handler.post(progressRunnable);
                binding.iconControl.setVisibility(View.GONE);
                binding.playerView.setPlayer(player);
                if (player != null) {
                    video.playVideo();
                } else {
                    video.initializePlayer();
                }
            } else {
                handler.removeCallbacks(progressRunnable);
                if (player != null) {
                    player.pause();
                    // 保持PlayerView显示最后一帧
                    binding.playerView.setKeepContentOnPlayerReset(true);
                    binding.playerView.setPlayer(player);
                }
                binding.iconControl.setVisibility(View.VISIBLE);
            }
        }

        void onDetached() {
            handler.removeCallbacks(progressRunnable);
        }
    }

    @Override
    public void onViewDetachedFromWindow(@NonNull VideoViewHolder holder) {
        super.onViewDetachedFromWindow(holder);
        holder.onDetached();
    }

    public void release() {
        NetworkUtils.unregisterNetworkStatusChangedListener(networkChangedListener);
        // 释放所有ViewHolder中的播放器资源
        if (recyclerView != null) {
            for (int i = 0; i < recyclerView.getChildCount(); i++) {
                RecyclerView.ViewHolder holder = recyclerView.findViewHolderForAdapterPosition(i);
                if (holder instanceof VideoViewHolder) {
                    VideoViewHolder videoHolder = (VideoViewHolder) holder;
                    if (videoHolder.viewModel != null) {
                        videoHolder.viewModel.onDestroy(lifecycleOwner);
                    }
                }
            }
        }
    }

    public interface VideoAdapterCallBack{
        void videoPauseClickCallBack();
        void videoDragButtonClickCallBack();
    }

}
