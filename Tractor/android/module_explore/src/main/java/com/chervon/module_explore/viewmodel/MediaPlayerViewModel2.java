package com.chervon.module_explore.viewmodel;

import android.app.Application;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;
import com.chervon.libBase.utils.SingleLiveEvent;
import com.chervon.libBase.utils.VideoPositionManager;
import com.chervon.module_explore.ExploreApplication;
import com.chervon.module_explore.data.MediaPlaybackState;
import com.chervon.module_explore.data.VideoModel;
import com.chervon.module_explore.network.NetworkMonitor;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.MediaSource;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class MediaPlayerViewModel2 extends BaseViewModel {
    private static final String TAG = "MediaPlayerViewModel2";
    private ExoPlayer player;
    private final MutableLiveData<ExoPlayer> playerLiveData = new MutableLiveData<>();
    public VideoModel videoData;
    // 播放列表相关变量
    private final MutableLiveData<Boolean> isPlaying = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isEnded = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> isBuffering = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> showControls = new MutableLiveData<>(false);
    private final MutableLiveData<PlaybackException> playbackException = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isNetworkAvailable = new MutableLiveData<>(true);
    private final MutableLiveData<Long> currentPosition = new MutableLiveData<>(0L);
    private final MutableLiveData<Long> duration = new MutableLiveData<>(0L);
    private boolean wasPlayingBeforeNetworkLoss = false;
    private Application mApplication;

    // 用于更新播放进度的Handler和Runnable
    private final Handler progressHandler = new Handler(Looper.getMainLooper());
    private final Runnable progressRunnable = new Runnable() {
        @Override
        public void run() {
            if (player != null && player.isPlaying()) {
                currentPosition.setValue(player.getCurrentPosition());
                progressHandler.postDelayed(this, 1000); // 每秒更新一次
            }
        }
    };

    // 网络断开期间用户设置的播放位置
    private boolean userSetPositionDuringNetworkLoss = false;
    private long userPositionMs = 0;

    private final MediatorLiveData<MediaPlaybackState> playbackState = new MediatorLiveData<>();
    private final SingleLiveEvent<Void> hideControlsEvent = new SingleLiveEvent<>();
    private MutableLiveData<Boolean> loading = new MutableLiveData<>(false);

    // 网络监听器
    private NetworkMonitor networkMonitor;


    // 记录Fragment是否可见
    private boolean isFragmentVisible = false;
    // 记录Fragment是否获得焦点
    private boolean isFragmentFocused = false;

    public MediaPlayerViewModel2(@NonNull Application application) {
        super(application);

        playbackState.addSource(isPlaying, playing -> updatePlaybackState());
        playbackState.addSource(isEnded, ended -> updatePlaybackState());
        playbackState.addSource(isBuffering, buffering -> updatePlaybackState());
        playbackState.addSource(showControls, controls -> updatePlaybackState());
        playbackState.addSource(playbackException, exception -> updatePlaybackState());
        playbackState.addSource(loading, isLoading -> updatePlaybackState());
        playbackState.addSource(isNetworkAvailable, networkAvailable -> updatePlaybackState());
        this.mApplication = application;
    }

    public VideoModel getCurrentVideoUrl() {
        return videoData;
    }

    public void setPlay(VideoModel video) {
        this.videoData = video;
        setVideoUrl(video.getVideoUrl().getIvUrl());
    }



    private void handleNetworkAvailable() {
        new Handler(Looper.getMainLooper()).post(() -> {
            isNetworkAvailable.setValue(true);

            String url = videoData.getVideoUrl().getIvUrl();
            if (url != null) {
                // 检查是否需要重新初始化播放器
                if (player == null || playbackException.getValue() != null) {
                    LogUtils.i(TAG, "网络恢复，重新初始化播放器");
                    playbackException.setValue(null);
                    initializePlayer();

                    // 恢复播放位置
                    if (userSetPositionDuringNetworkLoss && userPositionMs > 0) {
                        LogUtils.i(TAG, "从用户设置的位置开始播放：" + userPositionMs + "ms");
                        new Handler().postDelayed(() -> {
                            if (player != null) {
                                player.seekTo(userPositionMs);
                                if (wasPlayingBeforeNetworkLoss) {
                                    player.setPlayWhenReady(true);
                                }
                            }
                            userSetPositionDuringNetworkLoss = false;
                        }, 500);
                    }
                    return;
                }

                // 检查视频是否已缓存
                boolean isCached = BaseApplication.getInstance().videoPositionManager.get(url)!=null;
                LogUtils.i(TAG, "网络恢复，视频缓存状态：" + (isCached ? "已缓存" : "未缓存"));

                // 处理播放位置和状态
                if (userSetPositionDuringNetworkLoss && player != null && userPositionMs > 0) {
                    player.seekTo(userPositionMs);
                    LogUtils.i(TAG, "从用户设置的位置继续播放：" + userPositionMs + "ms");
                    userSetPositionDuringNetworkLoss = false;

                    if (wasPlayingBeforeNetworkLoss) {
                        player.setPlayWhenReady(true);
                        wasPlayingBeforeNetworkLoss = false;
                    }
                } else if (wasPlayingBeforeNetworkLoss && player != null) {
                    player.setPlayWhenReady(true);
                    wasPlayingBeforeNetworkLoss = false;
                }
            }

            // 更新加载状态
            if (loading.getValue() != null && loading.getValue()) {
                Boolean isBufferingValue = isBuffering.getValue();
                if (isBufferingValue == null || !isBufferingValue) {
                    loading.setValue(false);
                }
            }
        });
    }

    private void handleNetworkLost() {
        new Handler(Looper.getMainLooper()).post(() -> {
            isNetworkAvailable.setValue(false);

            if (player != null && player.isPlaying()) {
                wasPlayingBeforeNetworkLoss = true;
                // 检查当前视频是否已缓存
                String url = videoData.getVideoUrl().getIvUrl();
                if (url != null && BaseApplication.getInstance().videoPositionManager.get(url)!=null) {
                    // 如果视频已缓存，继续播放
                    LogUtils.i(TAG, "网络断开，但视频已缓存，继续播放");
                    loading.setValue(false);
                } else {
                    // 如果视频未缓存，暂停播放
                    player.setPlayWhenReady(false);
                    loading.setValue(true);
                    LogUtils.i(TAG, "网络断开，视频未缓存，暂停播放");
                }
            }
        });
    }

    private void updatePlaybackState() {
        boolean playing = isPlaying.getValue() != null && isPlaying.getValue();
        boolean ended = isEnded.getValue() != null && isEnded.getValue();
        boolean buffering = isBuffering.getValue() != null && isBuffering.getValue();
        boolean controls = showControls.getValue() != null && showControls.getValue();
        boolean loadingState = loading.getValue() != null && loading.getValue();
        boolean networkAvailable = isNetworkAvailable.getValue() != null && isNetworkAvailable.getValue();

        PlaybackException playException = playbackException.getValue();

        if (!playing || ended || !networkAvailable) {
            controls = true;
        }

        playbackState.setValue(new MediaPlaybackState(playing, ended, buffering, controls, loadingState, playException));
    }


    public void setVideoUrl(String url) {
        if (!TextUtils.isEmpty(url)) {
            // 释放旧的资源
            releaseResources();
            
            // 初始化网络监听器（如果还没有初始化）
            if (networkMonitor == null) {
                networkMonitor = new NetworkMonitor(mApplication, isNetworkAvailable);
                networkMonitor.setCallback(new NetworkMonitor.NetworkStateCallback() {
                    @Override
                    public void onNetworkAvailable() {
                        handleNetworkAvailable();
                    }

                    @Override
                    public void onNetworkLost() {
                        handleNetworkLost();
                    }
                });
            }

            // 检查网络状态
            ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
                @Override
                public Boolean doInBackground() {
                    return NetworkUtils.isAvailable();
                }

                @Override
                public void onSuccess(Boolean result) {
                    isNetworkAvailable.setValue(result);
                    if (result && isFragmentVisible) {
                        initializePlayer();
                    } else {
                        loading.setValue(false);
                    }
                }
            });
        }
    }

    private void releaseResources() {
        if (player != null) {
            player.stop();
            player.release();
            player = null;
        }
        progressHandler.removeCallbacks(progressRunnable);
        isPlaying.setValue(false);
        isEnded.setValue(false);
        isBuffering.setValue(false);
        loading.setValue(false);
    }

    public void initializePlayer() {
        String url = videoData.getVideoUrl().getIvUrl();
        if (url != null) {
            // 释放旧的播放器
            releaseResources();

            try {
                // 创建新的播放器
                MediaSource.Factory mediaSourceFactory = BaseApplication.videoCacheManager.getMediaSourceFactory();
                if (mediaSourceFactory != null) {
                    player = new ExoPlayer.Builder(getApplication())
                            .setMediaSourceFactory(mediaSourceFactory)
                            .build();
                    LogUtils.i(TAG, "创建支持缓存的播放器实例");
                } else {
                    player = new ExoPlayer.Builder(getApplication()).build();
                    LogUtils.i(TAG, "创建标准播放器实例（无缓存）");
                }

                playerLiveData.setValue(player);

                // 设置播放器监听器
                player.addListener(new Player.Listener() {
                    @Override
                    public void onPlaybackStateChanged(int state) {
                        isBuffering.setValue(state == Player.STATE_BUFFERING);
                        isEnded.setValue(state == Player.STATE_ENDED);
                        
                        if (state == Player.STATE_ENDED) {
                            loading.setValue(false);
                            isPlaying.setValue(false);
                            // 清除当前视频的保存位置
                            if (videoData != null && videoData.getVideoUrl() != null) {
                                String url = videoData.getVideoUrl().getIvUrl();
                                if (url != null) {
                                    BaseApplication.getInstance().videoPositionManager.remove(url);
                                }
                            }
                            LogUtils.i(TAG, "视频播放结束,清除保存的播放位置");
                        } else if (state == Player.STATE_READY) {
                            loading.setValue(false);
                            if (isFragmentVisible && isFragmentFocused) {
                                player.play();
                                isPlaying.setValue(true);
                            }
                            LogUtils.i(TAG, "视频准备就绪");
                            
                        }
                    }

                    @Override
                    public void onIsLoadingChanged(boolean isLoading) {
                        loading.setValue(isLoading);
                        LogUtils.i(TAG, "加载状态改变：" + isLoading);
                    }

                    @Override
                    public void onPlayerError(PlaybackException error) {
                        playbackException.setValue(error);
                        if (error.errorCode == PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED ||
                                error.errorCode == PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT) {
                            isNetworkAvailable.setValue(false);
                        }
                        loading.setValue(false);
                        isPlaying.setValue(false);
                        LogUtils.e(TAG, "播放错误：" + error.getErrorCodeName());
                    }

                    @Override
                    public void onIsPlayingChanged(boolean playing) {
                        isPlaying.setValue(playing);
                        if (playing) {
                            loading.setValue(false);
                            progressHandler.post(progressRunnable);
                            if (player.getDuration() > 0) {
                                duration.setValue(player.getDuration());
                            }
                        } else {
                            progressHandler.removeCallbacks(progressRunnable);
                        }
                        LogUtils.i(TAG, "播放状态改变：" + playing);
                    }
                });

                // 设置媒体源并准备播放
                MediaItem mediaItem = MediaItem.fromUri(Uri.parse(url));
                player.setMediaItem(mediaItem);
                player.prepare();
                player.setPlayWhenReady(isFragmentVisible && isFragmentFocused);
                long savedPosition = BaseApplication.getInstance().videoPositionManager.get(url);
                LogUtils.i(TAG, "恢复上次播放位置：" + savedPosition + "ms");

                if (savedPosition > 0) {
                    if (player.getCurrentPosition() != savedPosition){
                        player.seekTo(savedPosition);
                    }
                    LogUtils.i(TAG, "恢复上次播放位置：" + savedPosition + "ms");
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "初始化播放器失败: " + e.getMessage());
                loading.setValue(false);
            }
        }
    }

    public ExoPlayer getPlayer() {
        return player;
    }

    public LiveData<ExoPlayer> getPlayerLiveData() {
        return playerLiveData;
    }

    public void onPlayerClicked() {
        if (player != null) {
            if (player.isPlaying()) {
                player.pause();
                showControls.setValue(true);
                isPlaying.setValue(false);
                loading.setValue(false);
            } else {
                String url = videoData.getVideoUrl().getIvUrl();
                boolean isCached = url != null && BaseApplication.videoCacheManager.isCached(url);
                boolean hasNetwork = isNetworkAvailable.getValue() != null && isNetworkAvailable.getValue();

                if (isCached || hasNetwork) {
                    if (playbackException.getValue() != null && url != null) {
                        playbackException.setValue(null);
                        MediaItem mediaItem = MediaItem.fromUri(Uri.parse(url));
                        player.setMediaItem(mediaItem);
                        player.prepare();
                    }
                    
                    player.play();
                    showControls.setValue(false);
                    isPlaying.setValue(true);
                    loading.setValue(false);
                    LogUtils.i(TAG, isCached ? "播放缓存视频" : "播放在线视频");
                } else {
                    LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                    loading.setValue(false);
                }
            }
        }
    }

    public void playVideo() {
        if (player != null) {
            String url = videoData.getVideoUrl().getIvUrl();
            boolean isCached = url != null && BaseApplication.videoCacheManager.isCached(url);
            boolean hasNetwork = isNetworkAvailable.getValue() != null && isNetworkAvailable.getValue();

            if (isCached || hasNetwork) {
                // 如果播放器处于结束状态，先重置到开始位置
                if (player.getPlaybackState() == Player.STATE_ENDED) {
                    player.seekTo(0);
                }
                
                // 确保播放器已准备好
                if (player.getPlaybackState() == Player.STATE_IDLE) {
                    player.prepare();
                }
                
                player.play();
                showControls.setValue(false);
                isPlaying.setValue(true);
                loading.setValue(false);
                LogUtils.i(TAG, isCached ? "直接播放缓存视频" : "直接播放在线视频");
            } else {
                LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                loading.setValue(false);
            }
        }
    }

    /**
     * 供Fragment直接调用的方法，内部转发到生命周期方法
     */
    public void onResume() {
        onResume(null);
    }

    /**
     * 供Fragment直接调用的方法，内部转发到生命周期方法
     */
    public void onPause() {
        onPause(null);
    }

    @Override
    public void onCreate(@NotNull LifecycleOwner lifecycleOwner) {
        // Fragment创建时的初始化工作已在构造函数中完成
    }

    @Override
    public void onStart(@NotNull LifecycleOwner lifecycleOwner) {
        // Fragment变为可见状态
        isFragmentVisible = true;
        LogUtils.i(TAG, "Fragment变为可见状态");
    }

    @Override
    public void onResume(@NotNull LifecycleOwner lifecycleOwner) {
        // Fragment获得焦点
        isFragmentFocused = true;
        LogUtils.i(TAG, "Fragment获得焦点");
        
        // 如果Fragment可见且获得焦点，且之前在播放，则恢复播放
        if (isFragmentVisible && player != null && isPlaying.getValue() != null && isPlaying.getValue()) {
            player.setPlayWhenReady(true);
            LogUtils.i(TAG, "恢复视频播放");
        }
    }

    @Override
    public void onPause(@NotNull LifecycleOwner lifecycleOwner) {
        // Fragment失去焦点
        isFragmentFocused = false;
        LogUtils.i(TAG, "Fragment失去焦点");
        
        // 保存当前播放位置
        if (player != null) {
            String videoId = String.valueOf(videoData.getVideoUrl().getId());
            long currentPosition = player.getCurrentPosition();
            LogUtils.i(TAG, "保存播放位置：" + currentPosition + "ms");
            
            // 记录当前是否在播放并暂停
            wasPlayingBeforeNetworkLoss = player.isPlaying();
            if (player.isPlaying()) {
                player.pause();
            }
            player.setPlayWhenReady(false);
            LogUtils.i(TAG, "暂停视频播放");
        }
    }

    @Override
    public void onStop(@NotNull LifecycleOwner lifecycleOwner) {
        // Fragment变为不可见状态
        isFragmentVisible = false;
        LogUtils.i(TAG, "Fragment变为不可见状态");
        
        // Fragment不可见时，停止加载和播放
        if (player != null) {
            player.stop();
            loading.setValue(false);
            LogUtils.i(TAG, "停止视频加载和播放");
        }
    }

    @Override
    public void onDestroy(@NotNull LifecycleOwner lifecycleOwner) {
        try {
            // 先暂停播放
            if (player != null && player.isPlaying()) {
                player.pause();
            }

            // 保存最后的播放位置
            if (player != null && videoData != null && videoData.getVideoUrl() != null) {
                String videoId = String.valueOf(videoData.getVideoUrl().getId());
                long currentPosition = player.getCurrentPosition();
                LogUtils.i(TAG, "保存最后播放位置：" + currentPosition + "ms");
            }

            // Fragment销毁时释放资源
            LogUtils.i(TAG, "Fragment销毁，释放资源");
            
            // 移除进度更新回调
            if (progressHandler != null) {
                progressHandler.removeCallbacks(progressRunnable);
            }
            
            // 释放播放器
            if (player != null) {
                player.stop();
                player.clearMediaItems();
                player.release();
                player = null;
                playerLiveData.setValue(null);
            }

            // 释放网络监听器
            if (networkMonitor != null) {
                networkMonitor.release();
                networkMonitor = null;
            }

            // 清除状态
            isPlaying.setValue(false);
            isBuffering.setValue(false);
            isEnded.setValue(false);
            loading.setValue(false);
            currentPosition.setValue(0L);
            duration.setValue(0L);
            
        } catch (Exception e) {
            LogUtils.e(TAG, "销毁资源时发生错误: " + e.getMessage());
        }
    }

    public LiveData<Boolean> getNetworkAvailable() {
        return isNetworkAvailable;
    }

    public LiveData<MediaPlaybackState> getPlaybackState() {
        return playbackState;
    }

    public SingleLiveEvent<Void> getHideControlsEvent() {
        return hideControlsEvent;
    }


    public LiveData<Long> getCurrentPosition() {
        return currentPosition;
    }

    public LiveData<Long> getDuration() {
        return duration;
    }

    /**
     * 设置网络断开期间的播放位置
     * @param positionMs 用户设置的播放位置（毫秒）
     */
    public void setUserPositionDuringNetworkLoss(long positionMs) {
        userSetPositionDuringNetworkLoss = true;
        userPositionMs = positionMs;
        LogUtils.i(TAG, "记录网络断开时用户设置的播放位置：" + positionMs + "ms");
    }
}
