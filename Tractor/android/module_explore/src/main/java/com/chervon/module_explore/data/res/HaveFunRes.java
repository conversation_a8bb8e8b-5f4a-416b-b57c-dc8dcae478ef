package com.chervon.module_explore.data.res;

import java.io.Serializable;
import java.util.List;

/**
 * have fun接口解析类
 */
public class HaveFunRes {

    private List<HaveFunBean> list;

    public HaveFunRes() {
    }

    public HaveFunRes(List<HaveFunBean> list) {
        this.list = list;
    }

    public List<HaveFunBean> getList() {
        return list;
    }

    public void setList(List<HaveFunBean> list) {
        this.list = list;
    }

    public static class HaveFunBean implements Serializable {
        /**
         * id : 0
         * contentNo :
         * avatarUrl :
         * title :
         * text :
         * linkInText :
         * type :
         * ivUrl :
         */

        private int id;
        private String contentNo;
        private String avatarUrl;
        private String title;
        private String text;
        private String linkInText;
        private String type;
        private String ivUrl;

        private String coverPageUrl;

        public HaveFunBean() {
        }

        public HaveFunBean(int id, String contentNo, String avatarUrl, String title, String text, String linkInText, String type, String ivUrl,String coverPageUrl) {
            this.id = id;
            this.contentNo = contentNo;
            this.avatarUrl = avatarUrl;
            this.title = title;
            this.text = text;
            this.linkInText = linkInText;
            this.type = type;
            this.ivUrl = ivUrl;
            this.coverPageUrl = coverPageUrl;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getContentNo() {
            return contentNo;
        }

        public void setContentNo(String contentNo) {
            this.contentNo = contentNo;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getLinkInText() {
            return linkInText;
        }

        public void setLinkInText(String linkInText) {
            this.linkInText = linkInText;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getIvUrl() {
            return ivUrl;
        }

        public void setIvUrl(String ivUrl) {
            this.ivUrl = ivUrl;
        }

        public String getCoverPageUrl() {
            return coverPageUrl;
        }

        public void setCoverPageUrl(String coverPageUrl) {
            this.coverPageUrl = coverPageUrl;
        }
    }
}
