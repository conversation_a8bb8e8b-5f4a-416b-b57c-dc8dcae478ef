package com.chervon.module_explore.activity;


import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationWithExpandTrace;
import static com.chervon.libRouter.RouterConstants.EXTRA_VIDEO_LIST_URL;
import static com.chervon.libRouter.RouterConstants.EXTRA_VIDEO_PLAY_POSITION_URL;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import com.chervon.module_explore.data.res.HaveFunRes;
import com.google.android.exoplayer2.ExoPlayer;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libRouter.RouterConstants;
import com.chervon.module_explore.R;
import com.chervon.module_explore.data.MediaPlaybackState;
import com.chervon.module_explore.data.VideoModel;
import com.chervon.module_explore.databinding.ActivityMediaPreviewBinding;
import com.chervon.module_explore.viewmodel.MediaPlayerViewModel;
import com.chervon.module_explore.widget.VideoSeekBar;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @Author: 184862
 * @CreateDate: 2025/3/10
 * @UpdateDate: 2025/3/10
 */

@Route(path = RouterConstants.ACTIVITY_URL_EXPLORE_VIDEO_DETAIL)
public class MediaPreviewActivity extends BaseActivity<MediaPlayerViewModel> {

    private static final String TAG = "MediaPreviewActivity--";
    private ActivityMediaPreviewBinding binding;
    private ArrayList<HaveFunRes.HaveFunBean> videoArrayList = new ArrayList();
    private int playPosition = 0;
    private List<VideoModel> videoModels = new ArrayList<>();
    // 创建Handler用于定时更新
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final Runnable progressRunnable = new Runnable() {
        @Override
        public void run() {
            updateProgress();
            // 设置更新频率，官方默认是1000ms/帧率
            handler.postDelayed(this, 1000);
        }
    };



    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);
        super.onCreate(savedInstanceState);
        // 获取ViewModel
        binding.setLifecycleOwner(this);

        videoArrayList = (ArrayList<HaveFunRes.HaveFunBean>) getIntent().getSerializableExtra(EXTRA_VIDEO_LIST_URL);
        playPosition = getIntent().getIntExtra(EXTRA_VIDEO_PLAY_POSITION_URL,0);
        for (HaveFunRes.HaveFunBean video : videoArrayList) {
            videoModels.add(new VideoModel(video));
        }



        // 观察播放器实例的变化，以便在网络恢复等情况下更新播放器
        mViewModel.getPlayerLiveData().observe(this, exoPlayer -> {
            if (exoPlayer != null) {
                // 更新播放器视图的播放器实例
                binding.playerView.setPlayer(exoPlayer);
                LogUtils.i(TAG, "播放器实例已更新");
            }
        });

        binding.imgBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                videoReturnButtonClick();
                // 计算并上报完成率
                ExoPlayer player = mViewModel.getPlayer();
                if (player != null) {
                    long position = player.getCurrentPosition();
                    long duration = player.getDuration();
                    if (duration > 0) {
                        float rate = (float) position / duration * 100;
                        String completionRate = String.format("%.2f", rate);
                        String videoId = videoModels.get(mViewModel.getCurrentPlayingIndex()).getVideoUrl().getId()+"";
                        videoCompletionRate(completionRate, videoId);
                    }
                }

                finish();
            }
        });

        // 监听触摸事件，区分点击、右滑和上滑
        binding.playerView.setOnTouchListener(new View.OnTouchListener() {
            private float startX, startY;
            private static final int CLICK_ACTION_THRESHOLD = 10;
            private boolean isClick = true;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                // 记录每个事件类型
                LogUtils.d(TAG, "触摸事件: " + getActionString(event.getAction()));

                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        startX = event.getX();
                        startY = event.getY();
                        isClick = true;
                        LogUtils.d(TAG, "DOWN事件: 位置(" + startX + "," + startY + ")");
                        return true; // 返回true表示我们处理整个触摸序列

                    case MotionEvent.ACTION_MOVE:
                        // 如果移动距离超过阈值，认为不是点击
                        float moveX = event.getX();
                        float moveY = event.getY();
                        float deltaX = moveX - startX;
                        float deltaY = moveY - startY;

                        if (isClick && (Math.abs(deltaX) > CLICK_ACTION_THRESHOLD ||
                                Math.abs(deltaY) > CLICK_ACTION_THRESHOLD)) {
                            isClick = false;
                            LogUtils.d(TAG, "移动超过阈值，不再是点击: deltaX=" + deltaX + ", deltaY=" + deltaY);
                        }
                        return true; // 继续处理

                    case MotionEvent.ACTION_UP:
                        float endX = event.getX();
                        float endY = event.getY();
                        float diffX = endX - startX;
                        float diffY = endY - startY;

                        LogUtils.d(TAG, "UP事件: 位置(" + endX + "," + endY + "), 差值(" + diffX + "," + diffY + ")");

                        // 获取屏幕尺寸
                        int screenWidth = getResources().getDisplayMetrics().widthPixels;
                        int screenHeight = getResources().getDisplayMetrics().heightPixels;
                        float oneFifthScreenWidth = screenWidth / 5.0f;
                        float oneFifthScreenHeight = screenHeight / 5.0f;

                        // 检查滑动方向和距离
                        // X轴: 从左往右滑动时，endX > startX，所以diffX > 0
                        // Y轴: 从上往下滑动时，endY > startY，所以diffY > 0

                        // 判断滑动方向
                        boolean isHorizontalSwipe = Math.abs(diffX) > Math.abs(diffY);
                        boolean isVerticalSwipe = !isHorizontalSwipe;

                        // 判断具体方向
                        boolean isRightDirection = diffX > 0;
                        boolean isUpDirection = diffY < 0; // 上滑时，endY < startY，所以diffY < 0
                        boolean isDownDirection = diffY > 0; // 下滑时，endY > startY，所以diffY > 0

                        // 判断滑动距离是否超过阈值
                        boolean isHorizontalLongEnough = Math.abs(diffX) > oneFifthScreenWidth;
                        boolean isVerticalLongEnough = Math.abs(diffY) > oneFifthScreenHeight;

                        LogUtils.d(TAG, "滑动分析 - 水平滑动: " + isHorizontalSwipe +
                                ", 竖直滑动: " + isVerticalSwipe +
                                ", 右滑方向: " + isRightDirection +
                                ", 上滑方向: " + isUpDirection +
                                ", 下滑方向: " + isDownDirection +
                                ", X距离足够: " + isHorizontalLongEnough +
                                ", Y距离足够: " + isVerticalLongEnough +
                                ", diffX=" + diffX + ", diffY=" + diffY);

                        // 处理不同方向的滑动
                        if (!isClick) {
                            if (isHorizontalSwipe && isRightDirection && isHorizontalLongEnough) {
                                // 满足右滑条件，退出Activity
                                LogUtils.i(TAG, "右滑退出，滑动距离: " + Math.abs(diffX) +
                                        ", 屏幕五分之一: " + oneFifthScreenWidth);
                                // 计算并上报完成率
                                ExoPlayer player = mViewModel.getPlayer();
                                if (player != null) {
                                    long position = player.getCurrentPosition();
                                    long duration = player.getDuration();
                                    if (duration > 0) {
                                        float rate = (float) position / duration * 100;
                                        String completionRate = String.format("%.2f", rate);
                                        String videoId = videoModels.get(mViewModel.getCurrentPlayingIndex()).getVideoUrl().getId()+"";
                                        videoCompletionRate(completionRate, videoId);
                                    }
                                }
                                VideoReturnGestureClick(); // 添加右滑返回埋点
                                finish();
                                return true;
                            } else if (isVerticalSwipe && isUpDirection && isVerticalLongEnough) {
                                // 检查是否是最后一个视频
                                if (mViewModel.isLastVideo()) {
                                    LogUtils.i(TAG, "已是最后一个视频，忽略上滑");
                                    return true;
                                }

                                // 满足上滑条件，加载下一个视频
                                LogUtils.i(TAG, "上滑加载下一视频，滑动距离: " + Math.abs(diffY) +
                                        ", 屏幕五分之一: " + oneFifthScreenHeight);
                                // 计算并上报完成率
                                ExoPlayer player = mViewModel.getPlayer();
                                if (player != null) {
                                    long position = player.getCurrentPosition();
                                    long duration = player.getDuration();
                                    if (duration > 0) {
                                        float rate = (float) position / duration * 100;
                                        String completionRate = String.format("%.2f", rate);
                                        String videoId = videoModels.get(mViewModel.getCurrentPlayingIndex()).getVideoUrl().getId()+"";
                                        videoCompletionRate(completionRate, videoId);
                                    }
                                }
                                videoSwipeUpDownGestureClick(); // 添加上滑埋点
                                loadNextVideo();
                                return true;
                            } else if (isVerticalSwipe && isDownDirection && isVerticalLongEnough) {
                                // 检查是否是第一个视频
                                if (mViewModel.isFirstVideo()) {
                                    LogUtils.i(TAG, "已是第一个视频，忽略下滑");
                                    return true;
                                }

                                // 满足下滑条件，加载上一个视频
                                LogUtils.i(TAG, "下滑加载上一视频，滑动距离: " + Math.abs(diffY) +
                                        ", 屏幕五分之一: " + oneFifthScreenHeight);
                                // 计算并上报完成率
                                ExoPlayer player = mViewModel.getPlayer();
                                if (player != null) {
                                    long position = player.getCurrentPosition();
                                    long duration = player.getDuration();
                                    if (duration > 0) {
                                        float rate = (float) position / duration * 100;
                                        String completionRate = String.format("%.2f", rate);
                                        String videoId = videoModels.get(mViewModel.getCurrentPlayingIndex()).getVideoUrl().getId()+"";
                                        videoCompletionRate(completionRate, videoId);
                                    }
                                }
                                videoSwipeUpDownGestureClick(); // 添加下滑埋点
                                loadPreviousVideo();
                                return true;
                            }
                        } else if (isClick) {
                            // 是点击事件，处理点击逻辑
                            LogUtils.i(TAG, "检测到点击事件，处理点击");
                            handlePlayerClick();
                            return true;
                        }
                        break;

                    case MotionEvent.ACTION_CANCEL:
                        LogUtils.d(TAG, "触摸事件被取消");
                        break;
                }
                return true; // 消费所有事件
            }

            // 获取事件类型的字符串表示
            private String getActionString(int action) {
                switch(action) {
                    case MotionEvent.ACTION_DOWN: return "ACTION_DOWN";
                    case MotionEvent.ACTION_MOVE: return "ACTION_MOVE";
                    case MotionEvent.ACTION_UP: return "ACTION_UP";
                    case MotionEvent.ACTION_CANCEL: return "ACTION_CANCEL";
                    default: return "ACTION_" + action;
                }
            }
        });

        // 设置控制图标点击监听
        binding.iconControl.setOnClickListener(v -> {
            // 获取当前播放状态
            MediaPlaybackState state = mViewModel.getPlaybackState().getValue();
            if (state != null && state.isEnded) {
                // 如果视频已结束，需要先将播放位置重置到开始
                ExoPlayer player = mViewModel.getPlayer();
                if (player != null) {
                    player.seekTo(0);
                }
            }
            // 调用播放方法
            mViewModel.playVideo();
            // 手动隐藏控制图标
            binding.iconControl.setVisibility(View.GONE);
            LogUtils.i(TAG, "点击控制图标，继续播放视频");
        });

        // 观察播放状态
        mViewModel.getPlaybackState().observe(this, state -> {
            // 先判断是否处于加载状态

            boolean isLoading = state.isBuffering || state.loading || state.playbackException != null;
            LogUtils.e(TAG,"state.isBuffering-"+state.isBuffering,
                    "state.loading--"+state.loading,
                    "state.playbackException--"+state.playbackException);
            if (isLoading) {
                // 加载状态 - 显示加载指示器，隐藏控制图标
                binding.progress.setVisibility(View.VISIBLE);
                binding.iconControl.setVisibility(View.GONE); // 加载时隐藏暂停键
            } else {
                // 非加载状态 - 隐藏加载指示器
                binding.progress.setVisibility(View.GONE);

                // 根据播放状态决定是否显示控制图标
                if (state.isPlaying && !state.isEnded) {
                    // 播放中 - 隐藏控制图标
                    binding.iconControl.setVisibility(View.GONE);
                } else {
                    // 暂停或播放结束 - 显示控制图标
                    binding.iconControl.setVisibility(View.VISIBLE);
                }
            }
        });

        // 观察网络状态
        mViewModel.getNetworkAvailable().observe(this, isNetworkAvailable -> {
            if (!isNetworkAvailable) {
                // 网络断开，显示提示
                binding.progress.setVisibility(View.VISIBLE);
            } else {
                // 网络恢复
                // 视频会由ViewModel自动处理继续播放逻辑
                MediaPlaybackState state = mViewModel.getPlaybackState().getValue();
                if (state != null && !state.isBuffering && !state.loading) {
                    binding.progress.setVisibility(View.GONE);
                }
            }
        });

        binding.videoSeekBar.setOnSeekBarChangeListener(new VideoSeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(VideoSeekBar seekBar, float progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(VideoSeekBar seekBar) {
                // 恢复进度更新
                // 先移除之前的回调以避免重复
                handler.removeCallbacks(progressRunnable);
                // 启动新的进度更新
                handler.post(progressRunnable);

                // 用户开始拖动时，暂时停止进度更新
                // 可以选择在此暂停视频播放
                ExoPlayer player = mViewModel.getPlayer();
                if (player != null) {
                    player.pause();
                }
            }

            @Override
            public void onStopTrackingTouch(VideoSeekBar seekBar) {
                // 用户结束拖动，触发埋点
                videoDragButtonClick();
                
                // 获取当前播放器实例和网络状态
                ExoPlayer player = mViewModel.getPlayer();
                boolean isNetworkAvailable = mViewModel.getNetworkAvailable().getValue() != null &&
                        mViewModel.getNetworkAvailable().getValue();

                if (seekBar.getProgress() < 0) {
                    return;
                }

                try {
                    // 计算新位置
                    long duration = player != null ? player.getDuration() : 0;
                    if (duration <= 0 && binding.videoSeekBar.getDuration() > 0) {
                        // 如果播放器没有准备好，但进度条已有总时长信息，使用进度条的时长
                        duration = binding.videoSeekBar.getDuration();
                    }

                    if (duration > 0) {
                        int newPosition = (int) (seekBar.getProgress() * duration);

//                        if (!isNetworkAvailable) {
                            // 网络不可用时，记录用户设置的位置，以便网络恢复后使用

                            // 调用ViewModel中的方法记录位置
                            mViewModel.setUserPositionDuringNetworkLoss(newPosition);

//                            // 更新UI以反映用户操作
//                            return;
//                        }

                        if (player != null) {
                            player.seekTo(newPosition);
                            player.play();
                        }
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "设置播放进度失败: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 加载下一个视频 - 使用ViewModel的播放列表功能
     */
    private void loadNextVideo() {
        try {
            // 重置进度条
            binding.videoSeekBar.setProgress(0);

            // 显示加载指示器
            binding.progress.setVisibility(View.VISIBLE);
            binding.iconControl.setVisibility(View.GONE);

            // 调用ViewModel播放下一个视频
            boolean success = mViewModel.playNextVideo();

            // 显示提示信息
            if (success) {
            } else {
                LogUtils.w(TAG, "无法加载下一个视频");
                binding.progress.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "加载下一个视频时出错: " + e.getMessage());
            binding.progress.setVisibility(View.GONE);
        }
    }

    /**
     * 加载上一个视频 - 使用ViewModel的播放列表功能
     */
    private void loadPreviousVideo() {
        try {
            // 重置进度条
            binding.videoSeekBar.setProgress(0);

            // 显示加载指示器
            binding.progress.setVisibility(View.VISIBLE);
            binding.iconControl.setVisibility(View.GONE);

            // 调用ViewModel播放上一个视频
            boolean success = mViewModel.playPreviousVideo();

            // 显示提示信息
            if (success) {
            } else {
                LogUtils.w(TAG, "无法加载上一个视频");
                binding.progress.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "加载上一个视频时出错: " + e.getMessage());
            binding.progress.setVisibility(View.GONE);
        }
    }

    /**
     * 处理播放器的点击事件
     */
    private void handlePlayerClick() {
        // 获取当前播放状态
        MediaPlaybackState state = mViewModel.getPlaybackState().getValue();
        if (state != null) {
            // 只有在非加载状态下才响应点击事件
            if (!state.loading && !state.isBuffering && state.playbackException == null) {
                // 获取当前播放状态（用于确定下一个显示状态）
                boolean isCurrentlyPlaying = state.isPlaying;

                // 切换播放状态
                mViewModel.onPlayerClicked();

                // 如果当前正在播放，点击后应该暂停并显示控制图标
                if (isCurrentlyPlaying) {
                    binding.iconControl.setVisibility(View.VISIBLE);
                    LogUtils.i(TAG, "视频暂停，显示控制图标");
                    videoPauseClick(); // 添加暂停埋点
                } else {
                    binding.iconControl.setVisibility(View.GONE);
                    LogUtils.i(TAG, "视频播放，隐藏控制图标");
                }
            } else {
                LogUtils.i(TAG, "正在加载中，忽略点击事件");
            }
        }
    }

    private void updateProgress() {
        ExoPlayer player = mViewModel.getPlayer();
        if (player != null) {
            try {
            // 更新进度，包括视频结束时
            MediaPlaybackState state = mViewModel.getPlaybackState().getValue();
            if (state != null && (state.isPlaying || state.isEnded)) {
                long position = player.getCurrentPosition();
                long duration = player.getDuration();
                if (duration > 0) {
                    // 如果视频结束，直接设置进度为100%
                    if (state.isEnded) {
                        binding.videoSeekBar.setProgress(1.0f);
                    } else {
                        binding.videoSeekBar.setProgress((float) position / duration);
                    }
                    binding.videoSeekBar.setDuration(duration);
                }
            }
            } catch (Exception e) {
                LogUtils.e(TAG, "更新进度失败: " + e.getMessage());
            }
        }
    }

    @Override
    protected void onUnRegister() {
        mViewModel.onPause();
        handler.removeCallbacks(progressRunnable);
    }

    @Override
    protected void onRegister() {
        // 设置播放列表到ViewModel中，默认播放第一个视频
        mViewModel.setPlaylist(videoModels, playPosition);
        // 初始设置播放器
        binding.playerView.setPlayer(mViewModel.getPlayer());
        mViewModel.onResume();
        handler.post(progressRunnable);
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.activity_media_preview;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (ActivityMediaPreviewBinding) viewDataBinding;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
    }

    @Override
    protected Class<? extends MediaPlayerViewModel> getViewModelClass() {
        return MediaPlayerViewModel.class;
    }
    private void initTrace() {
        stayEleId = "1";
        pageId = "552";
        mouduleId = "106";
        pageResouce = "1_9_551";
        nextButtoneleid = "2";
    }

    /**
     * 视屏暂停点击
     */
    private void videoPauseClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "2", "1");
    }

    /**
     * 视频进度按钮点击
     */
    private void videoDragButtonClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "3", "1");
    }

    /**
     * 视频返回按钮点击
     */
    private void videoReturnButtonClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "4", "1");
    }

    /**
     * 视频返回手势点击
     */
    private void VideoReturnGestureClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "5", "1");
    }

    /**
     * 视频上下滑动手势点击
     */
    private void videoSwipeUpDownGestureClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "6", "1");
    }

    private void videoCompletionRate(String completionRate,String video_id){
        String eleId = "7";
        String COMPLETION_RATE_KEY = "completion_rate";
        String VIDEO_ID_KEY = "video_id";
        HashMap<String,String> expandMap= new HashMap();
        expandMap.put(COMPLETION_RATE_KEY,completionRate);
        expandMap.put(VIDEO_ID_KEY,video_id);

        sendDurationWithExpandTrace(this,mouduleId,pageId,pageResouce,"",eleId,expandMap);
    }

    @Override
    public void onBackPressed() {
        // 计算并上报完成率
        videoReturnButtonClick(); // 添加返回埋点
        ExoPlayer player = mViewModel.getPlayer();
        if (player != null) {
            long position = player.getCurrentPosition();
            long duration = player.getDuration();
            if (duration > 0) {
                float rate = (float) position / duration * 100;
                String completionRate = String.format("%.2f", rate);
                String videoId = videoModels.get(mViewModel.getCurrentPlayingIndex()).getVideoUrl().getId()+"";
                videoCompletionRate(completionRate, videoId);
            }
        }
        super.onBackPressed();
    }
}
