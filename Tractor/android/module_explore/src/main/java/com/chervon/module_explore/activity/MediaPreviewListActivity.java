package com.chervon.module_explore.activity;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationWithExpandTrace;
import static com.chervon.libRouter.RouterConstants.EXTRA_VIDEO_LIST_URL;
import static com.chervon.libRouter.RouterConstants.EXTRA_VIDEO_PLAY_POSITION_URL;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libRouter.RouterConstants;
import com.chervon.module_explore.R;
import com.chervon.module_explore.adapter.VideoListAdapter;
import com.chervon.module_explore.data.VideoModel;
import com.chervon.module_explore.data.res.HaveFunRes;
import com.chervon.module_explore.databinding.ActivityMediaPreviewListBinding;
import com.chervon.module_explore.viewmodel.MediaPlayerViewModel2;
import com.google.android.exoplayer2.ExoPlayer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Route(path = RouterConstants.ACTIVITY_URL_EXPLORE_VIDEO_LIST)
public class MediaPreviewListActivity extends BaseActivity<MediaPlayerViewModel2> implements VideoListAdapter.VideoAdapterCallBack {

    private static final String TAG = "MediaPreviewListActivity--";
    private ActivityMediaPreviewListBinding binding;
    private ArrayList<HaveFunRes.HaveFunBean> videoArrayList = new ArrayList<>();
    private List<VideoModel> videoModels = new ArrayList<>();
    private VideoListAdapter adapter;
    private LinearLayoutManager layoutManager;
    private boolean isInitialScroll = true;
    private ExoPlayer currentPlayer = null;
    private String currentVideoId = null;
    private String currentVideoUrl= null;
    private int playPosition;

    private int lastIdlePosition = -1;

   private boolean savePositionSwitch = true;
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        ARouter.getInstance().inject(this);
        super.onCreate(savedInstanceState);
        binding.setLifecycleOwner(this);
        initTrace();
        videoArrayList = (ArrayList<HaveFunRes.HaveFunBean>) getIntent().getSerializableExtra(EXTRA_VIDEO_LIST_URL);
        playPosition = getIntent().getIntExtra(EXTRA_VIDEO_PLAY_POSITION_URL, 0);

        for (HaveFunRes.HaveFunBean video : videoArrayList) {
            videoModels.add(new VideoModel(video));
        }

        binding.imgBack.setOnClickListener(view -> {
            if (!savePositionSwitch){
                BaseApplication.getInstance().videoPositionManager.clear();
            }
            videoReturnButtonClick();
            // 计算并上报完成率
            setVideoCompletionRate();
            // 确保在返回按钮点击时释放播放器资源
            if (adapter != null) {
                adapter.release();
            }
            mViewModel.onDestroy(this);
            finish();
        });
        // 初始化RecyclerView
        setupRecyclerView();

        // 设置数据和初始位置
        if (playPosition >= 0 && playPosition < videoModels.size()) {
            // 先设置选中位置
            adapter.setSelectedPosition(playPosition);
            // 再设置数据
            adapter.setVideos(videoModels);
            // 直接滚动到指定位置
            binding.recyclerView.scrollToPosition(playPosition);

            // 等待布局完成后初始化播放器
            binding.recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    // 移除监听器，避免重复调用
                    binding.recyclerView.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                    // 确保布局完成后再初始化播放器
                    RecyclerView.ViewHolder viewHolder = binding.recyclerView.findViewHolderForAdapterPosition(playPosition);
                    if (viewHolder instanceof VideoListAdapter.VideoViewHolder) {
                        VideoListAdapter.VideoViewHolder videoHolder = (VideoListAdapter.VideoViewHolder) viewHolder;
                        VideoModel video = videoModels.get(playPosition);
                        String videoUrl = video.getVideoUrl().getIvUrl();

                        // 设置当前播放器和视频信息
                        currentPlayer = videoHolder.viewModel.getPlayer();
                        currentVideoId = video.getVideoUrl().getId() + "";
                        currentVideoUrl = videoUrl;

                        // 检查视频是否已缓存
                        boolean isCached = videoUrl != null && BaseApplication.videoCacheManager.isCached(videoUrl);

                        if (videoHolder.viewModel.getPlayer() == null) {
                            videoHolder.viewModel.initializePlayer();
                            // 重新获取初始化后的播放器
                            currentPlayer = videoHolder.viewModel.getPlayer();
                        }

                        if (isCached || NetworkUtils.isConnected()) {
                            videoHolder.viewModel.playVideo();
                            LogUtils.i(TAG, isCached ? "播放缓存视频" : "播放在线视频");
                        } else {
                            LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                        }
                    }
                }
            });
        } else {
            // 如果没有指定位置，只设置数据
            adapter.setVideos(videoModels);
        }

        mViewModel.onStart(this);
        mViewModel.onResume(this);
    }

    private void setupRecyclerView() {
        // 从Application获取视频播放位置管理器实例
        // 先创建空的adapter
        adapter = new VideoListAdapter(BaseApplication.getInstance(), new ArrayList<>(), this, this);
        // 注册网络状态监听
        NetworkUtils.registerNetworkStatusChangedListener(networkChangedListener);

        // 设置布局管理器
        layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false) {
            @Override
            public RecyclerView.LayoutParams generateDefaultLayoutParams() {
                return new RecyclerView.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                );
            }

            @Override
            public boolean canScrollVertically() {
                return true;
            }

            @Override
            public int scrollVerticallyBy(int dy, RecyclerView.Recycler recycler, RecyclerView.State state) {
                int position = findFirstVisibleItemPosition();
                View firstChild = binding.recyclerView.getChildAt(0);

                if (firstChild != null && isInitialScroll) {
                    // 只在初始滚动时限制边界滑动
//                    if ((position == getItemCount() - 1 && dy > 0) || // 最后一个item禁止上滑
//                            (position == 0 && dy < 0)) { // 第一个item禁止下滑
//                        return 0;
//                    }
                }
                return super.scrollVerticallyBy(dy, recycler, state);
            }
        };
        binding.recyclerView.setLayoutManager(layoutManager);

        PagerSnapHelper snapHelper = new PagerSnapHelper();
        snapHelper.attachToRecyclerView(binding.recyclerView);

        // 设置RecyclerView基本属性
        binding.recyclerView.setNestedScrollingEnabled(false);
        binding.recyclerView.setItemViewCacheSize(1);
        binding.recyclerView.setHasFixedSize(true);
        binding.recyclerView.setAdapter(adapter);

        // 添加滚动监听
        binding.recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            private float lastY = 0;
            private boolean isScrollingDown = false;
            private boolean isManualScroll = false;

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {

                if (null==adapter){
                    return;
                }

                if (newState == RecyclerView.SCROLL_STATE_DRAGGING) {
                    LogUtils.e(TAG,"----RecyclerView.SCROLL_STATE_DRAGGING");
                    lastIdlePosition = -1;
                    isManualScroll = true;
                    int currentPosition = layoutManager.findFirstVisibleItemPosition();
                    RecyclerView.ViewHolder viewHolder = recyclerView.findViewHolderForAdapterPosition(currentPosition);
                    if (viewHolder instanceof VideoListAdapter.VideoViewHolder) {
                        VideoListAdapter.VideoViewHolder videoHolder = (VideoListAdapter.VideoViewHolder) viewHolder;
                        MediaPreviewListActivity.this.currentPlayer = videoHolder.viewModel.getPlayer();
                        VideoModel currentVideo = videoModels.get(currentPosition);
                        currentVideoId = currentVideo.getVideoUrl().getId() + "";
                        currentVideoUrl = currentVideo.getVideoUrl().getIvUrl();

                    }
                    lastY = recyclerView.getY();
                } else if (newState == RecyclerView.SCROLL_STATE_IDLE) {

                    int currentPosition = layoutManager.findFirstVisibleItemPosition();

                    // 如果当前位置与上次IDLE时的位置相同，说明是PagerSnapHelper的对齐行为，直接返回
                    if (currentPosition == lastIdlePosition) {
                        return;
                    }
                    lastIdlePosition = currentPosition;
                    LogUtils.e(TAG,"----RecyclerView.SCROLL_STATE_IDLE");
                    int lastPosition = adapter.getSelectedPosition();

                    // 如果位置没有改变，不触发重新播放
                    if (currentPosition == lastPosition) {
                        return;
                    }else {
                        if (currentPlayer != null) {
                            currentPlayer.pause();
                        }
                    }
                    // 使用之前保存的播放器信息上报完成率
                    setVideoCompletionRate();

                    // 如果是手动滑动，发送埋点
                    if (isManualScroll) {
                        videoSwipeUpDownGestureClick();
                        isManualScroll = false;
                    }
                    // 设置选中位置
                    adapter.setSelectedPosition(currentPosition);
                    // 等待布局稳定后再初始化播放器
                    binding.recyclerView.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                        @Override
                        public void onGlobalLayout() {
                            binding.recyclerView.getViewTreeObserver().removeOnGlobalLayoutListener(this);

                            RecyclerView.ViewHolder viewHolder = recyclerView.findViewHolderForAdapterPosition(currentPosition);
                            if (viewHolder instanceof VideoListAdapter.VideoViewHolder) {
                                VideoListAdapter.VideoViewHolder videoHolder = (VideoListAdapter.VideoViewHolder) viewHolder;
                                VideoModel video = videoModels.get(currentPosition);
                                String videoUrl = video.getVideoUrl().getIvUrl();

                                // 设置当前播放器和视频信息
                                currentPlayer = videoHolder.viewModel.getPlayer();
                                currentVideoId = video.getVideoUrl().getId() + "";
                                currentVideoUrl = videoUrl;

                                // 检查视频是否已缓存
                                boolean isCached = videoUrl != null && BaseApplication.videoCacheManager.isCached(videoUrl);

                                if (videoHolder.viewModel.getPlayer() == null) {
                                    videoHolder.viewModel.initializePlayer();
                                    // 重新获取初始化后的播放器
                                    currentPlayer = videoHolder.viewModel.getPlayer();
                                }

                                if (isCached || NetworkUtils.isConnected()) {
                                    videoHolder.viewModel.playVideo();
                                    LogUtils.i(TAG, isCached ? "播放缓存视频" : "播放在线视频");
                                } else {
                                    LogUtils.i(TAG, "无网络且视频未缓存，无法播放");
                                }
                            }
                            // 初始滚动完成后，取消滑动限制
                            isInitialScroll = false;
                        }
                    });
                }
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                isScrollingDown = dy < 0;
                int position = layoutManager.findFirstVisibleItemPosition();
                if (null!=adapter){
                    updateScrollState(position);
                }
            }
        });
    }

    private void updateScrollState(int position) {
        if (position == 0) {
            binding.recyclerView.setOverScrollMode(RecyclerView.OVER_SCROLL_NEVER);
        } else if (position == adapter.getItemCount() - 1) {
            binding.recyclerView.setOverScrollMode(RecyclerView.OVER_SCROLL_NEVER);
        } else {
            binding.recyclerView.setOverScrollMode(RecyclerView.OVER_SCROLL_ALWAYS);
        }
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.activity_media_preview_list;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (ActivityMediaPreviewListBinding) viewDataBinding;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
    }

    @Override
    protected Class<? extends MediaPlayerViewModel2> getViewModelClass() {
        return MediaPlayerViewModel2.class;
    }

    private void initTrace() {
        stayEleId = "1";
        pageId = "552";
        mouduleId = "106";
        pageResouce = "1_9_552";
        nextButtoneleid = "2";
    }

    @Override
    protected void onResume() {
        super.onResume();
        mViewModel.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 暂停当前播放
        if (currentPlayer != null && currentPlayer.isPlaying()) {
            currentPlayer.pause();
        }
        mViewModel.onPause();
    }

    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {
    }

    private final NetworkUtils.OnNetworkStatusChangedListener networkChangedListener = new NetworkUtils.OnNetworkStatusChangedListener() {
        @Override
        public void onDisconnected() {
            // 网络断开时，检查当前播放的视频是否已缓存
            int currentPosition = layoutManager.findFirstVisibleItemPosition();
            if (currentPosition != RecyclerView.NO_POSITION && currentPosition < videoModels.size()) {
                VideoModel video = videoModels.get(currentPosition);
                String videoUrl = video.getVideoUrl().getIvUrl();
                if (videoUrl != null && !BaseApplication.videoCacheManager.isCached(videoUrl)) {
                    // 如果视频未缓存，暂停播放
                    for (int i = 0; i < binding.recyclerView.getChildCount(); i++) {
                        View child = binding.recyclerView.getChildAt(i);
                        RecyclerView.ViewHolder holder = binding.recyclerView.getChildViewHolder(child);
                        if (holder instanceof VideoListAdapter.VideoViewHolder) {
                            VideoListAdapter.VideoViewHolder videoHolder = (VideoListAdapter.VideoViewHolder) holder;
                            int position = holder.getAdapterPosition();
                            if (position == currentPosition) {
                                ExoPlayer player = videoHolder.viewModel.getPlayer();
                                if (player != null && player.isPlaying()) {
                                    player.pause();
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }

        @Override
        public void onConnected(NetworkUtils.NetworkType networkType) {
            // 网络恢复时，如果当前视频暂停，可以恢复播放
            int currentPosition = layoutManager.findFirstVisibleItemPosition();
            if (currentPosition != RecyclerView.NO_POSITION && currentPosition < videoModels.size()) {
                for (int i = 0; i < binding.recyclerView.getChildCount(); i++) {
                    View child = binding.recyclerView.getChildAt(i);
                    RecyclerView.ViewHolder holder = binding.recyclerView.getChildViewHolder(child);
                    if (holder instanceof VideoListAdapter.VideoViewHolder) {
                        VideoListAdapter.VideoViewHolder videoHolder = (VideoListAdapter.VideoViewHolder) holder;
                        int position = holder.getAdapterPosition();
                        if (position == currentPosition) {
                            ExoPlayer player = videoHolder.viewModel.getPlayer();
                            if (player != null && !player.isPlaying()) {
                                player.play();
                            }
                            break;
                        }
                    }
                }
            }
        }
    };

    @Override
    protected void onDestroy() {
        try {
            // 先暂停当前播放
            if (currentPlayer != null && currentPlayer.isPlaying()) {
                currentPlayer.pause();
            }
            
            // 释放所有播放器资源
            if (adapter != null) {
                adapter.release();
                adapter = null;
            }
            
            // 释放ViewModel资源
            if (mViewModel != null) {
                mViewModel.onDestroy(this);
            }
            
            // 注销网络监听
            if (networkChangedListener != null) {
                NetworkUtils.unregisterNetworkStatusChangedListener(networkChangedListener);
            }
            
            // 清除播放器引用
            currentPlayer = null;
            
            LogUtils.i(TAG, "Activity销毁，释放所有资源");
        } catch (Exception e) {
            LogUtils.e(TAG, "销毁资源时发生错误: " + e.getMessage());
        } finally {
            super.onDestroy();
        }
    }

    @Override
    public void onBackPressed() {
        try {
            // 先暂停当前播放
            if (currentPlayer != null && currentPlayer.isPlaying()) {
                currentPlayer.pause();
            }
            
            videoReturnButtonClick();
            // 计算并上报完成率
            setVideoCompletionRate();
            
            // 释放所有播放器资源
            if (adapter != null) {
                adapter.release();
                adapter = null;
            }
            
            // 释放ViewModel资源
            if (mViewModel != null) {
                mViewModel.onDestroy(this);
            }
            
            // 清除播放器引用
            currentPlayer = null;
            
            LogUtils.i(TAG, "返回键退出，释放所有资源");
        } catch (Exception e) {
            LogUtils.e(TAG, "返回时释放资源发生错误: " + e.getMessage());
        } finally {
            super.onBackPressed();
        }
    }

    @Override
    public void videoPauseClickCallBack() {
        videoPauseClick();
    }

    @Override
    public void videoDragButtonClickCallBack() {
        videoDragButtonClick();
    }


    /**
     * 视频上下滑动手势点击
     */
    private void videoSwipeUpDownGestureClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, "6", "1");
    }

    /**
     * 视频返回手势点击
     */
    private void videoReturnGestureClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, "5", "1");
    }

    /**
     * 视频返回按钮点击
     */
    private void videoReturnButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, "4", "1");
    }

    public void setVideoCompletionRate() {
        // 获取当前播放视频信息
        if (TextUtils.isEmpty(currentVideoId)) {
            return;
        }
        if (null == currentPlayer) {
            return;
        }

        long position = currentPlayer.getCurrentPosition();
        long duration = currentPlayer.getDuration();

        // 总是记录播放位置
        if (position < duration) {
            saveVideoPosition(currentVideoUrl, position);
        }

        // 计算完成率
        if (duration > 0) {
            float rate = (float) position / duration * 100;
            if (rate >= 100) {
                rate = 100;
            }
            String completionRate = String.format("%.2f", rate);
            videoCompletionRate(completionRate, currentVideoId);
        }
    }

    private void videoCompletionRate(String completionRate, String video_id) {
        String eleId = "7";
        String COMPLETION_RATE_KEY = "completion_rate";
        String VIDEO_ID_KEY = "video_id";
        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(COMPLETION_RATE_KEY, completionRate);
        expandMap.put(VIDEO_ID_KEY, video_id);

        sendDurationWithExpandTrace(this, mouduleId, pageId, pageResouce, "", eleId, expandMap);
    }

    /**
     * 视频进度按钮点击
     */
    private void videoDragButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, "3", "1");
    }


    /**
     * 视屏暂停点击
     */
    private void videoPauseClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, "2", "1");
    }

    private void saveVideoPosition(String url,long position){
        if (TextUtils.isEmpty(url)){
            return;
        }

        BaseApplication.getInstance().videoPositionManager.put(url,position);
    }
}
