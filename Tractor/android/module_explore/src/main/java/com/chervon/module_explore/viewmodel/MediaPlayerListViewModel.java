package com.chervon.module_explore.viewmodel;

import android.app.Application;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;
import com.chervon.libBase.utils.SingleLiveEvent;
import com.chervon.libBase.utils.VideoPositionManager;
import com.chervon.module_explore.ExploreApplication;
import com.chervon.module_explore.cache.VideoCacheManager;
import com.chervon.module_explore.data.MediaPlaybackState;
import com.chervon.module_explore.data.VideoModel;
import com.chervon.module_explore.network.NetworkMonitor;
import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.MediaSource;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

public class MediaPlayerListViewModel extends BaseViewModel {

    private static final String TAG = "MediaPlayerListViewModel";
    private ExoPlayer player;

    public MediaPlayerListViewModel(@NonNull Application application) {
        super(application);
    }

    public void setPlayer(ExoPlayer player) {
        this.player = player;
    }


    @Override
    public void onCreate(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onStart(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onResume(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onPause(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onStop(@NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onDestroy(@NotNull LifecycleOwner lifecycleOwner) {
        if (player != null) {
            player.stop();
            player.release();
            player = null;
        }
    }
}
