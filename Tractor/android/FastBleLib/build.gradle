apply plugin: 'com.android.library'

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 30
        versionCode 1
        versionName "1.0.0"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}

task makeAAR(type: Copy) {
    from('build/outputs/aar/')
    into('build/aarFloder/')
    include('FastBleLib-release.aar')
    rename ('FastBleLib-release.aar', 'FastBLE_CVN.aar' )
}
makeAAR.dependsOn(build)
