package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.model.AppConstants.PERMISSION_ALBUM_UNAUTHORIZED;
import static com.chervon.libBase.model.AppConstants.PERMISSION_CAMERA_UNAUTHORIZED;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.CameraUtil.CODE_GALLERY;
import static com.chervon.libBase.utils.CameraUtil.CODE_TAKE_PHOTO;
import static com.chervon.libBase.utils.CameraUtil.TYPE_TAKE_PHOTO;
import static com.chervon.libBase.utils.CameraUtil.getMediaFileUri;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_FROM_REVIEW_MANAGER;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;
import static com.chervon.moudleDeviceManage.ui.DeviceRegisterFragment.FILE_UPLOAD_BUTTON_CLICK;

import static com.chervon.moudleDeviceManage.ui.HomeActivity.getRealPathFromUri;
import static com.chervon.moudleUserCenter.data.repository.FeedBackRepo.HAS_FEEDBACK;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseDataBindingAdapter;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.PhotoSelectItemClick;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.ui.adapter.PhotoSelectAdapter;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.libBase.model.FeedBackMoreEntry;
import com.chervon.moudleUserCenter.data.FeedBackQuestionUIState;
import com.chervon.moudleUserCenter.data.FeedBackUIState;
import com.chervon.moudleUserCenter.data.HasFeedBackUIState;
import com.chervon.moudleUserCenter.data.req.HasFeedBackReq;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityFeedbackQuestionBinding;
import com.chervon.moudleUserCenter.ui.adapter.FeedbackDeviceAdapter;
import com.chervon.moudleUserCenter.ui.adapter.FeedbackMoreAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.FeedBackViewModel;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.engine.UriToFileTransformEngine;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.SandboxTransformUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;


import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.ui
 * @ClassName: MessageSettingActivity
 * @Description: 消息设置页面
 * @Author: langmeng
 * @CreateDate: 2022/8/29 15:49
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/8/29 15:49
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_FEEDBACK_QUESTION)
public class FeedBackQuestionActivity extends BaseActivity<FeedBackViewModel> implements PhotoSelectItemClick {
  private static final String TAG = "FeedBackQuestionActivity";
  private UsercenterActivityFeedbackQuestionBinding mViewDataBinding;
  private BaseDataBindingAdapter adapter;
  private FeedbackDeviceAdapter mFeedbackDeviceAdapter;
  private FeedbackMoreAdapter mFeedbackMoreAdapter;
  private List<String> url = new ArrayList<String>();
  private PhotoSelectAdapter mPhotoSelectAdapter;
  private ResultListenner resultListenner;
  private boolean fromReviewManger = false;
  int wordLimitNum = 500;
  //默认最大的图片选择数量
  private final String DEFAULT_MAX_PHOTO_SIZE = "/4";
  //默认初始化图片数量
  private final int DEFAULT_URL_PHOTO_SIZE = 1;
  protected Dialog mProgressDialog;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {}

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_feedback_question;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (UsercenterActivityFeedbackQuestionBinding) viewDataBinding;
    mViewDataBinding.setViewModel(mViewModel);
    mViewDataBinding.usercenterActivityUserdetailMobileEdit.setHorizontallyScrolling(true);
    mViewDataBinding.usercenterActivityUserdetailMobileEdit.setSingleLine(true);
    mViewDataBinding.usercenterActivityUserdetailMobileEdit.setMarqueeRepeatLimit(-1);
    mViewDataBinding.usercenterActivityUserdetailMobileEdit.setFocusable(true);
    mViewDataBinding.usercenterActivityUserdetailMobileEdit.setFocusableInTouchMode(true);
    mViewDataBinding.usercenterActivityUserdetailMobileEdit.setSelected(true);
    mViewDataBinding.btnLogin.setEnabled(false);

    mViewDataBinding.edQuestionDescrp.addTextChangedListener(mTextWatcher);
  }

  TextWatcher mTextWatcher = new TextWatcher() {
    private CharSequence enterWords;
    private int selectionStart;
    private int selectionEnd;
    private int enteredWords;

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
      enterWords = s;
    }

    @Override
    public void afterTextChanged(Editable s) {
      enteredWords = s.length();
      mViewDataBinding.tvNum.setText(enteredWords + "/500");
      selectionStart = mViewDataBinding.edQuestionDescrp.getSelectionStart();
      selectionEnd = mViewDataBinding.edQuestionDescrp.getSelectionEnd();
      if (enterWords.length() > wordLimitNum) {
        s.delete(selectionStart - 1, selectionEnd);
        int tempSelection = selectionEnd;
        mViewDataBinding.edQuestionDescrp.setText(s);
        mViewDataBinding.edQuestionDescrp.setSelection(tempSelection);
      }

      s = mViewDataBinding.edQuestionDescrp.getText();
      if (s != null && !TextUtils.isEmpty(s.toString())) {
        mViewDataBinding.btnLogin.setEnabled(true);
      } else {
        mViewDataBinding.btnLogin.setEnabled(false);
      }

    }
  };

  @Override
  protected void initData(Bundle savedInstanceState) {
    String sourceType =  "RN";
    String categoryType = "device";
    initTrace();
    mViewModel.setFragmentManager(this.getSupportFragmentManager());
    Bundle bundle = getIntent().getExtras();
    Object prevData = bundle.get(KEY_PREV_DATA);
    String key_from = bundle.getString(KEY_FROM);
    if (!TextUtils.isEmpty(key_from)){
      if (KEY_FROM_REVIEW_MANAGER.equals(key_from)){
        fromReviewManger  = true;
      }
    }
    String userId = UserInfo.get().getId();
    int hasFeedBackType = -1;
    //查询反馈的类型：1 设备，2 app
    int hasFeedBackTypeWithDevice = 1;
    int hasFeedBackTypeWithApp = 2;
    String hasFeedBackWithDeviceId = "";
    FeedBackQuestionUIState feedBackQuestionUIState = new FeedBackQuestionUIState();
    //RN面板跳转过来
    if (!TextUtils.isEmpty(key_from)&&sourceType.equals(key_from)){

        String productId =  bundle.getString(RouterConstants.KEY_PRODUCTID);
        String nickName = bundle.getString(RouterConstants.KEY_NICKNAME);
        String deviceId =  bundle.getString(RouterConstants.KEY_DEVICEID);
        String commodityModel = bundle.getString(RouterConstants.KEY_COMMODITYMODEL);
        String sn = bundle.getString(RouterConstants.KEY_SN);

        feedBackQuestionUIState.setCategory1(categoryType);
        feedBackQuestionUIState.setCategory2(TextUtils.isEmpty(nickName)?"":nickName);
        feedBackQuestionUIState.setDeviceId(TextUtils.isEmpty(deviceId)?"":deviceId);
        feedBackQuestionUIState.setDeviceSn(TextUtils.isEmpty(sn)?"":sn);
        feedBackQuestionUIState.setCommodityModel(TextUtils.isEmpty(commodityModel)?"":commodityModel);
        feedBackQuestionUIState.setProductId(TextUtils.isEmpty(productId)?"":productId);
        mViewModel.showTitle.setValue(TextUtils.isEmpty(nickName)?"":nickName);

      hasFeedBackType = hasFeedBackTypeWithDevice;
      hasFeedBackWithDeviceId = deviceId;

    }else if (prevData instanceof DeviceInfo) {
      DeviceInfo deviceInfo = (DeviceInfo) prevData;
      feedBackQuestionUIState.setCategory1(categoryType);
      feedBackQuestionUIState.setCategory2(deviceInfo.getNickName());
      feedBackQuestionUIState.setDeviceId(deviceInfo.getDeviceId());
      feedBackQuestionUIState.setDeviceSn(deviceInfo.getSn());
      feedBackQuestionUIState.setCommodityModel(deviceInfo.getCommodityModel());
      feedBackQuestionUIState.setProductId(deviceInfo.getProductId());
      mViewModel.showTitle.setValue(deviceInfo.getNickName());

      hasFeedBackType = hasFeedBackTypeWithDevice;
      hasFeedBackWithDeviceId = deviceInfo.getDeviceId();
    } else {
      FeedBackMoreEntry feedBackMoreEntry = (FeedBackMoreEntry) prevData;
      feedBackQuestionUIState.setCategory1(feedBackMoreEntry.getCategory1());
      feedBackQuestionUIState.setCategory2(feedBackMoreEntry.getCategory2());
      mViewModel.showTitle.setValue(feedBackMoreEntry.getQuestionTitle());

      hasFeedBackType = hasFeedBackTypeWithApp;
      hasFeedBackWithDeviceId = "";
    }

    mViewModel.questionUIStateMutableLiveData.setValue(feedBackQuestionUIState);

    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.getFeedbackQuestionTitle(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));
    mViewDataBinding.setViewModel(mViewModel);
    mViewModel.questionUIStateMutableLiveData.observe(this, new Observer<FeedBackQuestionUIState>() {
      @Override
      public void onChanged(FeedBackQuestionUIState feedBackQuestionUIState) {
        if (!isFinishing()) {
          ProgressHelper.hideProgressView(FeedBackQuestionActivity.this);
        }

        if (feedBackQuestionUIState.getState() == RESPONSE_SUCCESS) {
          // 更新本地feed状态
          upgradeReview();
          DialogUtil.showFeedBackSuccessDialog(FeedBackQuestionActivity.this, LanguageStrings.app_setting_success_text());
          mViewDataBinding.btnLogin.postDelayed(new Runnable() {
            @Override
            public void run() {
             DialogUtil.clearDialog();
              finish();
            }
          }, 2000);

        } else if (feedBackQuestionUIState.getState() == RESPONSE_FAIL) {
          DialogUtil.showFaceBackWarningDialog(FeedBackQuestionActivity.this,TextUtils.isEmpty(feedBackQuestionUIState.getMessage())?
            LanguageStrings.app_OTA_upgradefailed_textview_text()
            :feedBackQuestionUIState.getMessage());
        }
      }
    });
    mViewModel.submitBtLiveData.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          mViewDataBinding.btnLogin.setClickable(false);
          ProgressHelper.showProgressView(FeedBackQuestionActivity.this, 0);
          mViewModel.submitBtLiveData.postValue(false);
          sendBottonClick();
        }
      }
    });

    mViewModel.messageSettingClick.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        sendBottonClick();
      }
    });
    mViewModel.mLiveData.observe(this, new Observer<FeedBackUIState>() {
      @Override
      public void onChanged(FeedBackUIState feedBackUIState) {
        if (feedBackUIState.getDeviceInfos() != null && feedBackUIState.getDeviceInfos().size() > 0) {
          mFeedbackDeviceAdapter.setData(feedBackUIState.getDeviceInfos());
          mFeedbackDeviceAdapter.notifyDataSetChanged();
        }
      }
    });
    initPhotoSelectAdapter();

    if (null == mProgressDialog) {
      mProgressDialog = DialogUtil.showRnLoadingDialog(this);
    }else {
      mProgressDialog.dismiss();
      mProgressDialog.show();
    }
    mViewModel.hasFeedback(new HasFeedBackReq(hasFeedBackType,hasFeedBackWithDeviceId,userId));

    mViewModel.hasFeedMutableLiveData.observe(this, new Observer<HasFeedBackUIState>() {
      @Override
      public void onChanged(HasFeedBackUIState uiState) {
        if (null!=mProgressDialog){
          mProgressDialog.dismiss();
        }
        if (uiState.getState() == HAS_FEEDBACK){
          IntentFeedBackService.getInstance().intentFeedBack(FeedBackQuestionActivity.this,uiState,pageResouce);
        }else if (uiState.getState() == RESPONSE_FAIL){
          if (!TextUtils.isEmpty(uiState.getMessage())){
            ToastUtils.showShort(uiState.getMessage());
          }
        }
      }
    });
  }


  private void initPhotoSelectAdapter() {
    GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 4, RecyclerView.VERTICAL, false);
    mViewDataBinding.rvImageOption.setLayoutManager(gridLayoutManager);
    if (url.size() == 0) {
      url.add("");
    }
    mPhotoSelectAdapter = new PhotoSelectAdapter(this, this, url);
    mPhotoSelectAdapter.setHasStableIds(true);
    mViewDataBinding.rvImageOption.setAdapter(mPhotoSelectAdapter);
  }

  @Override
  protected Class<? extends FeedBackViewModel> getViewModelClass() {
    return FeedBackViewModel.class;
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }


  private void initTrace() {
    stayEleId = "3";
    pageId = "163";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  public void sendBottonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  @Override
  public void onItemClick(Object v) {


  }


  public void previewReceip(String url) {
    DialogUtil.showPhotoDialog(this, url, null);
  }

  public void uploadReceip() {
    this.setResultListenner(new ResultListenner() {

      @Override
      public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data, String imgUrl) {
        if (tempFile != null && imgUrl == null) {
          //   url.clear();
          if (url.size() > 3) {
            url.add(3, tempFile.getAbsolutePath());
          } else {
            url.add(url.size() - 1, tempFile.getAbsolutePath());
          }

          mPhotoSelectAdapter.setDatas(url);
          mPhotoSelectAdapter.notifyDataSetChanged();
        } else {
          //  url.clear();
          if (url.size() > 3) {
            url.add(3, imgUrl);
          } else {
            url.add(url.size() - 1, imgUrl);
          }
          checkItemSelect();
          mPhotoSelectAdapter.setDatas(url);
          mPhotoSelectAdapter.notifyDataSetChanged();
        }
        mViewModel.setUrls(url);
        int photoSize = 0;
        if (url.size() != 0) {
          photoSize = (url.size() - 1);
        }
        mViewDataBinding.tvImageNum.setText(photoSize + "/4");
      }
    });

    DialogUtil.showBottomCardDialog(this, new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (view.getId() == com.chervon.moudleDeviceManage.R.id.llTakePhoto) {
          goToTakePhotoPage();
        } else if (view.getId() == com.chervon.moudleDeviceManage.R.id.llGallery) {
          if (Build.VERSION.SDK_INT >= 33) {

            String[] permissions = new String[]{
              Manifest.permission.READ_MEDIA_IMAGES};

            RxPermissions rxPermissions = new RxPermissions(FeedBackQuestionActivity.this);
            rxPermissions.requestEach(permissions)
              .subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                  if (permission.granted) {
                    getToOpenGalleyWith13();
                  } else {
                    showPermissionError(permission.name);
                  }
                }
              });
          } else {
            getToOpenGalley();
          }
        }
      }
    });
  }


  private void getToOpenGalley() {
    final String[] permissionsGroup = new String[]{
      Manifest.permission.WRITE_EXTERNAL_STORAGE,
      Manifest.permission.READ_EXTERNAL_STORAGE
    };

    if (Build.VERSION.SDK_INT >= 23) {

      RxPermissions rxPermissions = new RxPermissions(this);
      rxPermissions.setLogging(true);
      rxPermissions.requestEachCombined(permissionsGroup)
        .subscribe(new Consumer<Permission>() {
          @Override
          public void accept(Permission permission) throws Exception {
            if (permission.granted) {
              //处理允许权限后的操作
              Intent i = new Intent();
              i.setAction(Intent.ACTION_PICK);
              i.setType("image/*");
              startActivityForResult(i, CODE_GALLERY);
              return;
            } else if (permission.shouldShowRequestPermissionRationale) {
              //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
              //处理用户点击禁止后的操作
              showPermissionError(permission.name);
            } else {
              showPermissionError(permission.name);
            }

          }
        });


    } else {
      Intent i = new Intent();
      i.setAction(Intent.ACTION_PICK);
      i.setType("image/*");
      startActivityForResult(i, CODE_GALLERY);
    }
  }

  private void getToOpenGalleyWith13() {
    if (isDestroyed() || isFinishing()) {
      return;
    }

    PictureSelector.create(this)
      .openSystemGallery(SelectMimeType.ofImage())
      .setSelectionMode(SelectModeConfig.SINGLE)
      .setSkipCropMimeType()
      .setSandboxFileEngine(new UriToFileTransformEngine() {
        @Override
        public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
          //Android 10 沙盒资源访问
          if (call != null) {
            String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
            call.onCallback(srcPath, sandboxPath);
          }
        }
      })
      .setSandboxFileEngine(new UriToFileTransformEngine() {
        @Override
        public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
          if (call != null) {
            String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
            call.onCallback(srcPath, sandboxPath);
          }
        }
      }).forSystemResultActivity(new OnResultCallbackListener<LocalMedia>() {
      @Override
      public void onResult(ArrayList<LocalMedia> result) {
        LocalMedia localMedia = result.get(0);
        String picUrl = localMedia.getSandboxPath();
        if (url.size() > 3) {
          url.add(3, picUrl);
        } else {
          url.add(url.size() - 1, picUrl);
        }
        int photoSize = 0;
        if (url.size() != 0) {
          photoSize = (url.size() - 1);
        }
        mPhotoSelectAdapter.setDatas(url);
        mPhotoSelectAdapter.notifyDataSetChanged();
        mViewDataBinding.tvImageNum.setText(photoSize + DEFAULT_MAX_PHOTO_SIZE);
        mViewModel.setUrls(url);

      }

      @Override
      public void onCancel() {

      }
    });
  }

  private void goToTakePhotoPage() {
    final String[] permissionsGroup = new String[]{
      Manifest.permission.CAMERA
    };
    if (Build.VERSION.SDK_INT >= 23) {

      RxPermissions rxPermissions = new RxPermissions(this);
      rxPermissions.setLogging(true);
      rxPermissions.requestEachCombined(permissionsGroup)
        .subscribe(new Consumer<Permission>() {
          @Override
          public void accept(Permission permission) throws Exception {
            if (permission.granted) {
              //处理允许权限后的操作
              selectPicFromCamera(FeedBackQuestionActivity.this);
              return;
            } else if (permission.shouldShowRequestPermissionRationale) {
              //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
              //处理用户点击禁止后的操作
              showPermissionError(permission.name);
            } else {
              showPermissionError(permission.name);
            }

          }
        });


    } else {
      Intent takeIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
      Uri photoUri = getMediaFileUri(TYPE_TAKE_PHOTO);
      takeIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
      startActivityForResult(takeIntent, CODE_TAKE_PHOTO);
    }
  }


  private void showPermissionError(String permissionText) {
    if (!isFinishing()) {
      int errorCode = permissionText.equalsIgnoreCase(Manifest.permission.CAMERA)?PERMISSION_CAMERA_UNAUTHORIZED:PERMISSION_ALBUM_UNAUTHORIZED;

      DialogUtil.cameraAndalbumAlertDialog(this,errorCode,
        LanguageStrings.app_modifyavator_gosetting_textview_text(),
        LanguageStrings.app_setting_clearcachecancle_button_text(),


        new View.OnClickListener() {
          @Override
          public void onClick(View view) {
            // onConfirmed 跳转到系统设置
            goIntentSetting();
          }
        }, new View.OnClickListener() {
          @Override
          public void onClick(View view) {
            //onCanceled
            finish();
          }
        });
    }
  }

  /**
   * 跳转到三星系统设置。其他机型暂无适配计划
   */
  private void goIntentSetting() {
    Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
    Uri uri = Uri.fromParts("package", getPackageName(), null);
    intent.setData(uri);
    try {
      startActivity(intent);
    } catch (Exception e) {

    }
  }


  File tempFile;

  protected void selectPicFromCamera(Activity activity) {

    Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

    File file = new File(Environment.getExternalStorageDirectory(), Environment.DIRECTORY_PICTURES);
    if (!file.exists()) {
      file.mkdirs();
    }


    tempFile = new File(file.getAbsolutePath() + "/" + System.currentTimeMillis() + ".jpg");
    LogUtils.e(tempFile.getAbsolutePath());


    Uri uri = FileProvider.getUriForFile(activity, getBaseContext().getPackageName() + ".provider", tempFile);

    List<ResolveInfo> resInfoList = activity.getPackageManager()
      .queryIntentActivities(cameraIntent, PackageManager.MATCH_DEFAULT_ONLY);
    for (ResolveInfo resolveInfo : resInfoList) {
      String packageName = resolveInfo.activityInfo.packageName;
      activity.grantUriPermission(packageName, uri, Intent.FLAG_GRANT_WRITE_URI_PERMISSION
        | Intent.FLAG_GRANT_READ_URI_PERMISSION);
    }
    //Uri.fromFile(tempFile)
    cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, uri);
    startActivityForResult(cameraIntent, CODE_TAKE_PHOTO);

  }


  @Override
  protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
    super.onActivityResult(requestCode, resultCode, data);{
      try {
        if (resultCode == RESULT_OK) {
          String imgUrl = null;
          if (data != null && data.getData() != null) {
            Uri uri = data.getData();
            imgUrl = getRealPathFromUri(this, uri);
          }
          if (resultListenner != null) {
            resultListenner.onActivityResult(requestCode, resultCode, data, imgUrl);
          }
        }
      }catch (Exception e){
        LogUtils.e(TAG,e.getMessage().toString());
      }

    }
  }

  public void setResultListenner(ResultListenner resultListenner) {
    this.resultListenner = resultListenner;

  }


  public boolean checkItemSelect() {
    return true;
  }


  @Override
  public void onItemClick(Object v, PhotoSelectAdapter adapter) {
    int id = ((View) v).getId();
    if (id == com.chervon.moudleDeviceManage.R.id.ivfooter) {
      uploadReceip();
      sendBaseTraceClick(FILE_UPLOAD_BUTTON_CLICK);
    } else if (id == com.chervon.moudleDeviceManage.R.id.ivDeviceIcon) {
      int index = (int) ((View) v).getTag();
      previewReceip(url.get(index));
    }
  }

  @Override
  public void onItemDelClick(Object uiState) {
    mViewDataBinding.tvImageNum.setText((url.size() - DEFAULT_URL_PHOTO_SIZE) + DEFAULT_MAX_PHOTO_SIZE);

  }

  private void upgradeReview(){
    if (fromReviewManger){
      User user = UserInfo.get();
      user.setHasFeedBack(fromReviewManger+"");
      UserInfo.set(user);
    }
  }
}
