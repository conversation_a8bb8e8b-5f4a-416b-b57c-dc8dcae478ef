package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.APPConstants.DEVICE_SHARE_TYPE_ACCEPT;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_SHARE_SELECT_POSITION;

import android.os.Bundle;
import android.os.Parcelable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityShareCenterBinding;
import com.chervon.libNetwork.event.ShareUpdateEvent;
import com.chervon.moudleUserCenter.ui.viewmodel.ShareCenterViewModel;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: ShareCenterActivity
 * @Description: 设备分享中心
 * @Author: wuxd
 * @CreateDate: 2024/8/6
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_SHARE_CENTER)
public class ShareCenterActivity extends BaseActivity<ShareCenterViewModel> {

  private UsercenterActivityShareCenterBinding binding;
  private final String[] tabs = {LanguageStrings.app_sharedevice_share_textview_text(),LanguageStrings.app_sharedevice_accept_textview_text()};
  private final ShareListFragment[] fragments = {new ShareListFragment(ShareListFragment.ShareListType.SHARE),new ShareListFragment(ShareListFragment.ShareListType.ACCEPT)};
  private static final String MODULE_ID = "13";
  private static final String PAGE_ID = "461";
  private static final String EVENT_BACK = "1";
  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_share_center;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityShareCenterBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_usercenter_sharedevice_textview_text(),
            view -> onBackPressed()));
    binding.setViewModel(mViewModel);
    mViewModel.setFragmentManager(getSupportFragmentManager());
    initTabs();
    initObserver();
  }
  private void initTabs(){
    binding.viewPager.setAdapter(new FragmentStateAdapter(getSupportFragmentManager(), this.getLifecycle()) {
      @NonNull
      @Override
      public Fragment createFragment(int position) {
        return fragments[position];
      }
      @Override
      public int getItemCount() {
        return tabs.length;
      }
    });
    binding.viewPager.setSaveEnabled(false);
    new TabLayoutMediator(binding.tabLayout,binding.viewPager,((tab, position) -> tab.setText(tabs[position]))).attach();
    if(null != getIntent().getExtras()) {
      if(getIntent().getExtras().getInt(KEY_SHARE_SELECT_POSITION)==DEVICE_SHARE_TYPE_ACCEPT) {
        TabLayout.Tab tab = binding.tabLayout.getTabAt(1);
        if(null != tab) {
          tab.select();
        }
      }
    }
  }

  public void refreshData(){
    mViewModel.getCount();
  }

  @Subscribe(threadMode = ThreadMode.MAIN)
  public void onUpdate(ShareUpdateEvent event) {
    refreshData();
  }

  private void initObserver(){
    mViewModel.message.observe(this, ToastUtils::showShort);
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackClickTrace();
  }

  private void sendBackClickTrace(){
    sendClickTrace(this, MODULE_ID, PAGE_ID, ((BaseApplication) this.getApplication()).getCurrentPageResouce(), EVENT_BACK, "1");
  }

  @Override
  protected Class<? extends ShareCenterViewModel> getViewModelClass() {
    return ShareCenterViewModel.class;
  }


  private void initTrace() {
    stayEleId = "";
    pageId = null;
    mouduleId = null;
    nextButtoneleid = "";
  }

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    if(!EventBus.getDefault().isRegistered(this)) {
      EventBus.getDefault().register(this);
    }
  }

  @Override
  protected void onResume() {
    super.onResume();
    refreshData();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    if(EventBus.getDefault().isRegistered(this)) {
      EventBus.getDefault().unregister(this);
    }
  }
}
