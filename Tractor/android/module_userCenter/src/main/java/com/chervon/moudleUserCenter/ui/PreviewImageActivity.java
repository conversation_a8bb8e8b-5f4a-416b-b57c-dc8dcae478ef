package com.chervon.moudleUserCenter.ui;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityPreviewBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterPreviewViewModel;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.engine.CropFileEngine;
import com.luck.picture.lib.engine.UriToFileTransformEngine;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.photoview.PhotoView;
import com.luck.picture.lib.utils.SandboxTransformUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.yalantis.ucrop.UCrop;
import com.yalantis.ucrop.UCropImageEngine;

import java.util.ArrayList;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: PreviewImageActivity
 * @Description: 图片预览
 * @Author: LangMeng
 * @CreateDate: 2022/6/28 17:18
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/28 17:18
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_PREVIEW_IMAGEL)
public class PreviewImageActivity extends BaseActivity<UserCenterPreviewViewModel> {

  private static final String MODIFY_BUTTON_CLICK = "2";
  private UsercenterActivityPreviewBinding binding;
  private PhotoView photoView;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_preview;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {

    binding = (UsercenterActivityPreviewBinding) viewDataBinding;

    //浅色模式
    BarUtils.setStatusBarLightMode(this, false);
    Window window = getWindow();
    window.setStatusBarColor(getResources().getColor(R.color.black));

    photoView = findViewById(R.id.usercenter_activity_preview_photoview);
    if (!this.isFinishing()) {
      Glide.with(this)
        .load(UserInfo.get().getPhoto())
        .placeholder(R.drawable.ic_defult_show_img)
        .error(R.drawable.ic_defult_show_img)
        .into(photoView);

    }
    TextView rightbtn = findViewById(R.id.custom_crop_right_btn);
    rightbtn.setText(LanguageStrings.app_modifyavator_modify_textview_text());

    rightbtn.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        //右侧按钮
        DialogUtil.showBottomCardDialogEditProfile(PreviewImageActivity.this, new View.OnClickListener() {
          @Override
          public void onClick(View view) {
            if (view.getId() == com.chervon.moudleDeviceManage.R.id.llTakePhoto) {
              takePhoto();
            } else if (view.getId() == com.chervon.moudleDeviceManage.R.id.llGallery) {
              if (Build.VERSION.SDK_INT >= 33) {

                String[] permissions = new String[]{
                  Manifest.permission.READ_MEDIA_IMAGES};

                RxPermissions rxPermissions = new RxPermissions(PreviewImageActivity.this);
                rxPermissions.requestEach(permissions)
                  .subscribe(new Consumer<Permission>() {
                    @Override
                    public void accept(Permission permission) throws Exception {
                      if (permission.granted){
                        openGallery();
                      }

                    }
                  });
              } else {
                openGallery();
              }

            }

          }
        });
        sendBaseTraceClick(MODIFY_BUTTON_CLICK);
      }
    });

    findViewById(R.id.custom_crop_left_btn).setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {

        //左侧按钮
        finish();
        sendBackTrace();
      }
    });
  }

  /**
   * @return void
   * @method openGallery
   * @description 图库选择流程
   * @date: 2022/6/28 17:53
   * @author: LangMeng
   */
  private void openGallery() {
    if (isDestroyed() || isFinishing()) {
      return;
    }
    try {

      if (Build.VERSION.SDK_INT <= 33){

        PictureSelector.create(this)
          .openSystemGallery(SelectMimeType.ofImage())
          .setSelectionMode(SelectModeConfig.SINGLE)
          .setCropEngine(new MyCropFileEngine())
          .forSystemResult(new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(ArrayList<LocalMedia> result) {
              String imgPath = result.get(0).getCutPath();
              Glide.with(PreviewImageActivity.this)
                .load(FileUtils.getFileByPath(imgPath))
                .into(photoView);
            }

            @Override
            public void onCancel() {

            }
          });



      }else {


        PictureSelector.create(ActivityUtils.getTopActivity())
          .openSystemGallery(SelectMimeType.ofImage())
          .setSelectionMode(SelectModeConfig.SINGLE)
          .setSkipCropMimeType()

          .setSandboxFileEngine(new UriToFileTransformEngine() {
            @Override
            public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
              //Android 10 沙盒资源访问
              if (call != null) {
                String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                call.onCallback(srcPath, sandboxPath);
              }
            }
          })
          .setCropEngine(new MyCropFileEngine())
          .setSandboxFileEngine(new UriToFileTransformEngine() {
            @Override
            public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
              if (call != null) {
                String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                call.onCallback(srcPath, sandboxPath);
              }
            }
          }).forSystemResult(new OnResultCallbackListener<LocalMedia>() {
          @Override
          public void onResult(ArrayList<LocalMedia> result) {
            String imgPath = result.get(0).getCutPath();
            Glide.with(PreviewImageActivity.this)
              .load(FileUtils.getFileByPath(imgPath))
              .into(photoView);
          }

          @Override
          public void onCancel() {

          }
        });

      }



    } catch (Exception e) {

    }

  }


  private void takePhoto() {
    if (isDestroyed() || isFinishing()) {
      return;
    }
    try {
      PictureSelector.create(ActivityUtils.getTopActivity())
        .openCamera(SelectMimeType.ofImage())
        .setSandboxFileEngine(new UriToFileTransformEngine() {
          @Override
          public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
            //Android 10 沙盒资源访问
            if (call != null) {
              String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
              call.onCallback(srcPath, sandboxPath);
            }
          }
        })
        .setCropEngine(new MyCropFileEngine())
        .setSandboxFileEngine(new UriToFileTransformEngine() {
          @Override
          public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
            if (call != null) {
              String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
              call.onCallback(srcPath, sandboxPath);
            }
          }
        })
        .forResult(new OnResultCallbackListener<LocalMedia>() {
          @Override
          public void onResult(ArrayList<LocalMedia> result) {
            String imgPath = result.get(0).getCutPath();
            Glide.with(PreviewImageActivity.this)
              .load(FileUtils.getFileByPath(imgPath))
              .into(photoView);
          }

          @Override
          public void onCancel() {
            //取消
          }
        });
    } catch (Exception e) {

    }

  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    binding.setUserCenterPreviewViewModel(mViewModel);
  }

  @Override
  protected Class<? extends UserCenterPreviewViewModel> getViewModelClass() {
    return UserCenterPreviewViewModel.class;
  }


  /**
   * 配制UCrop，可根据需求自我扩展
   *
   * @return
   */
  private static UCrop.Options buildOptions() {
    UCrop.Options options = new UCrop.Options();
    options.setHideBottomControls(true);
    //是否禁止裁剪gif和webp
    options.isForbidCropGifWebp(true);
    //裁剪宽高比
    options.withAspectRatio(1, 1);
    //是否显示网格
    options.setShowCropGrid(false);
    return options;
  }


  private void initTrace() {
    stayEleId = "3";
    pageId = "156";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  public static class MyCropFileEngine implements CropFileEngine {
    @Override
    public void onStartCrop(Fragment fragment, Uri srcUri, Uri destinationUri, ArrayList<String> dataSource, int requestCode) {
      //裁剪
      UCrop uCrop = UCrop.of(srcUri, destinationUri, dataSource);
      uCrop.setImageEngine(new UCropImageEngine() {
        @Override
        public void loadImage(Context context, String url, ImageView imageView) {
          Glide.with(context).load(url).override(180, 180).into(imageView);
        }

        @Override
        public void loadImage(Context context, Uri url, int maxWidth, int maxHeight, UCropImageEngine.OnCallbackListener<Bitmap> call) {
        }
      });
      uCrop.withOptions(buildOptions());
//                      uCrop.start(fragment.getActivity(), fragment, requestCode);
      //跳转到我们的activity，而不是用uCrop自带的跳转到UCropActivity
      Intent uCropIntent = uCrop.getIntent(fragment.getActivity());
      uCropIntent.setClass(fragment.getActivity(), UserCenterCropActivity.class);
      fragment.startActivityForResult(uCropIntent, requestCode);
    }
  }


}
