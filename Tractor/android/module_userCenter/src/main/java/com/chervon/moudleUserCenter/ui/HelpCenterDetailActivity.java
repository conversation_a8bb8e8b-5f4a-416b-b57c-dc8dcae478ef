package com.chervon.moudleUserCenter.ui;


import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.dialog.ConfirmAlertDialog;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.libDB.entities.CommonProblemRespone;
import com.chervon.moudleUserCenter.data.HelpCenterUIState;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityHelpCenterDetailBinding;
import com.chervon.moudleUserCenter.ui.adapter.HelpCenterAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.HelpCenterViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */


@Route(path = RouterConstants.ACTIVITY_HELP_CENTER_DETAIL)
public class HelpCenterDetailActivity extends BaseActivity<HelpCenterViewModel> implements ItemClick {
  public static final String TAG = "DeviceSearchFragment";
  private HelpCenterAdapter mAdapter;
  private UsercenterActivityHelpCenterDetailBinding mViewDataBinding;
  private CommonProblemRespone commonProblemRespone;

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_help_center_detail;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (UsercenterActivityHelpCenterDetailBinding) viewDataBinding;
    mViewDataBinding.rvDeviceList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
    mViewDataBinding.setPresenter(this);
    //mViewDataBinding.rvDeviceList.addItemDecoration(new DividerItemDecoration(this.getActivity(), LinearLayoutManager.VERTICAL));
    List<CommonProblemRespone> list = new ArrayList<CommonProblemRespone>();
//      list.add(new CommonProblemRespone("割草机001", "割草机001", 555));
//      list.add(new CommonProblemRespone("割草机001", "割草机001", 555));
//      list.add(new CommonProblemRespone("割草机001", "割草机001", 555));
    mAdapter = new HelpCenterAdapter(this, this, list);
    mAdapter.setHasStableIds(true);
    mViewDataBinding.rvDeviceList.setAdapter(mAdapter);

    Bundle bundle = this.getIntent().getExtras();
    commonProblemRespone = (CommonProblemRespone) bundle.getSerializable(KEY_PREV_DATA);
    updateUiDetail(commonProblemRespone);


    mViewDataBinding.setUiState(commonProblemRespone);
    mViewModel.getRecommentProblem(commonProblemRespone.helpFaqId);
    mViewModel.problemDetail(commonProblemRespone.helpFaqId);

    String pattern =  Utils.getAppSettingOfDate().equals(Utils.NA_MM_DD_YYYY)?"MM/dd/yyyy":"dd/MM/yyyy";

    SimpleDateFormat sdf = new SimpleDateFormat(pattern, Locale.CHINA);

    Date date = new Date();
    date.setTime(Long.valueOf(commonProblemRespone.createTime));
    mViewDataBinding.tvDate.setText(sdf.format(date));
  }

  @NonNull
  private String updateUiDetail(CommonProblemRespone commonProblemRespone) {
    String[] model = commonProblemRespone.getModel();
    String models = LanguageStrings.app_devicecode_modeno_textview_text() + " ";
    for (int i = 0; model != null && i < model.length; i++) {
      models = models + model[i];
      if (i != model.length - 1) {
        models = models + ";";
      }
    }


    setThunmupColor(commonProblemRespone);
    mViewDataBinding.tvTitle2.setText(models);
    return models;
  }

  private void setThunmupColor(CommonProblemRespone commonProblemRespone) {
    ColorStateList colorStateList = ColorStateList.valueOf(Color.parseColor("#FF000000"));
    mViewDataBinding.tvThumbsupNum.setTextColor(getResources().getColor(R.color.colorthird));
    if (commonProblemRespone.isPraised()) {
      colorStateList = ColorStateList.valueOf(Color.parseColor("#FF77BC1F"));
      mViewDataBinding.tvThumbsupNum.setTextColor(getResources().getColor(R.color.colorButtonNormal));
    }
    mViewDataBinding.ivThumbsUp.setImageTintList(colorStateList);
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    {
      //  mViewDataBinding.setPresenter(this);
      //mViewModel.searchHistoryDevice();
      mViewModel.toolbarData.setValue(new ToolbarData(
        LanguageStrings.app_helpcenterdetail_title_textview_text(),
        new View.OnClickListener() {
          @Override
          public void onClick(View view) {
            finish();
            sendBackTrace();
          }
        }));
      mViewDataBinding.setHelpCenterViewModel(mViewModel);
      mViewModel.mLiveData.observe(this, new Observer<HelpCenterUIState>() {
        @Override
        public void onChanged(HelpCenterUIState uiState) {
          List<CommonProblemRespone> deviceInfos = uiState.getCommonProblemRespone();
          if (deviceInfos != null && deviceInfos.size() > 0) {
            mAdapter.setData(deviceInfos);
            mAdapter.notifyDataSetChanged();
          }
          if (uiState.getProblemDetail() != null) {
            if (TextUtils.isEmpty(uiState.getProblemDetail().helpFaqId)) {
              finish();
            }
            mViewDataBinding.setUiState(uiState.getProblemDetail());
            updateUiDetail(uiState.getProblemDetail());
          }
        }
      });


    }
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }


  @Override
  protected Class getViewModelClass() {
    return HelpCenterViewModel.class;
  }


  @Override
  public void onItemClick(Object uiState) {
    mViewModel.saveSearchToHistory();
//        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
//        controller.navigate(R.id.action_deviceSearchFragment_to_deviceGuidePageOneFragment);

    if (uiState instanceof String) {
      mViewModel.thumbsUpRecommod(String.valueOf(uiState), commonProblemRespone.getHelpFaqId());
    } else {
      ARouter.getInstance().build(RouterConstants.ACTIVITY_HELP_CENTER_DETAIL).withSerializable(KEY_PREV_DATA, (CommonProblemRespone) uiState).navigation();
    }


  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

  }


  public void searchDevice() {

  }

  public void clearHistory() {
    ConfirmAlertDialog.show(this.getSupportFragmentManager(),
      LanguageStrings.getClearsearchhistory(),
      LanguageStrings.getBaseCancel(),
      LanguageStrings.getSearchdone(), new Consumer<Boolean>() {
        @Override
        public void accept(Boolean aBoolean) throws Exception {
          if (aBoolean) {
            mViewModel.clearHistory();
          } else {

          }

        }
      });
  }

  public void cancel() {
    finish();
  }

  public void clearText() {

  }


  @Override
  public void onPause() {
    super.onPause();
  }


  @Override
  public void onPointerCaptureChanged(boolean hasCapture) {
    super.onPointerCaptureChanged(hasCapture);
  }

  public void clickThumbsUp(String id) {
    if (commonProblemRespone.isPraised()) {
      commonProblemRespone.setPraised(false);
      commonProblemRespone.setPraiseCount(commonProblemRespone.getPraiseCount() - 1);
    } else {
      commonProblemRespone.setPraised(true);
      commonProblemRespone.setPraiseCount(commonProblemRespone.getPraiseCount() + 1);
    }
    setThunmupColor(commonProblemRespone);
    mViewDataBinding.setUiState(commonProblemRespone);
    mViewModel.singlethumbsUp(id);
  }


  private void initTrace() {
    stayEleId = "2";
    pageId = "104";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

}
