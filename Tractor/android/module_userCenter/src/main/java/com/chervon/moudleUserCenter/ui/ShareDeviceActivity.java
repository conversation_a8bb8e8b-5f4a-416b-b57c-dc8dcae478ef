package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEICON;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEID;
import static com.chervon.moudleUserCenter.utils.Constants.EMPTY;
import static com.chervon.moudleUserCenter.utils.Constants.TRACE_EVENT_ID;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.inputmethod.EditorInfo;

import androidx.databinding.ViewDataBinding;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.ui.widget.InlineTipEditTextEu;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.ToastExtend;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityDeviceShareBinding;
import com.chervon.libNetwork.event.ShareUpdateEvent;
import com.chervon.moudleUserCenter.ui.viewmodel.ShareDeviceViewModel;

import org.greenrobot.eventbus.EventBus;

import java.lang.ref.WeakReference;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: ShareDeviceActivity
 * @Description: 设备分享
 * @Author: wuxd
 * @CreateDate: 2024/8/6
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_SHARE_DEVICE)
public class ShareDeviceActivity extends BaseActivity<ShareDeviceViewModel> {


  private UsercenterActivityDeviceShareBinding binding;
  private WeakReference<LoadingDialog> loadingDialogWeakReference;
  private static final String SPACE = " ";
  private long dialogShowTime;
  private String dialogPageId;
  private String dialogStayEleId;
  private static final String EVENT_SHARE= "2";
  private static final String EVENT_CONFIRM = "2";
  private static final String EVENT_CANCEL = "1";
  /**
   * 0: 未注册；1：分享成功
   */
  private static final int SHARE_STATE_NO_REGISTER = 0;
  private static final int SHARE_STATE_SUCCESS = 1;
  private static final String MODULE_ID = "13";
  private static final String PAGE_ID = "463";
  private static final String STAY_ELE_ID = "3";
  private static final String DIALOG_PAGE_ID = "464";
  private static final String DIALOG_STAY_ELE_ID = "3";

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_device_share;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityDeviceShareBinding) viewDataBinding;
    binding.setViewModel(mViewModel);
    binding.setActivity(this);
    binding.setMessageText(LanguageStrings.app_sharedevice_enteremail_textview_text());
    if(null != getIntent().getExtras() ) {
      Glide.with(this).load(null !=getIntent().getExtras().getString(KEY_DEVICEICON)?getIntent().getExtras().getString(KEY_DEVICEICON):"")
              .priority(Priority.HIGH).placeholder(com.chervon.moudleDeviceManage.R.drawable.ic_device_default)
              .into(binding.ivDevice);
    }
    binding.ietMail.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if(!hasFocus){
        if(!CommonUtils.checkEmail(binding.ietMail.getInlineTipEditTextString())){
          mViewModel.inlineMessage.setValue(LanguageStrings.app_sharedevice_emailerror1_textview_text());
        } else {
          mViewModel.inlineMessage.setValue("");
        }
      }
    });
    binding.ietMail.setOnEditorActionListener((textView, i, keyEvent) -> {
      if(i == EditorInfo.IME_ACTION_DONE) {
        binding.ietMail.clearFocus();
      }
      return true;
    });
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    initObserver();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_usercenter_sharedevice_textview_text(),
            view -> {
              onBackPressed();
            }));
  }

  private void initObserver(){
    mViewModel.inlineMessage.observe(this, s -> {
      dismissLoading();
      if(TextUtils.isEmpty(s)) {
        binding.ietMail.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, null, EMPTY, EMPTY);
      } else {
        binding.ietMail.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, s, EMPTY, EMPTY);
      }
    });
    mViewModel.shareState.observe(this, integer -> {
      if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
        loadingDialogWeakReference.get().dismiss();
      }
      if(integer == SHARE_STATE_NO_REGISTER) {
        sendExposure(this, mouduleId, dialogPageId, pageResouce);
        dialogShowTime = System.currentTimeMillis();
        DialogUtil.simpleConfirmDialogForShare(this,EMPTY,
                binding.ietMail.getInlineTipEditTextString()+SPACE+LanguageStrings.app_sharedevice_invitemessage_textview_text(),
                LanguageStrings.app_sharedevice_invite_textview_text(),
                LanguageStrings.app_base_cancel_button_text(), view -> {
                    showLoading();
                    mViewModel.inviteUser(null !=getIntent().getExtras().getString(KEY_DEVICEID)?getIntent().getExtras().getString(KEY_DEVICEID):EMPTY,
                            binding.ietMail.getInlineTipEditTextString());
                  sendClickTrace(this, mouduleId, dialogPageId, pageResouce, EVENT_CONFIRM, TRACE_EVENT_ID);
                  sendDurationTrace(this, mouduleId, dialogPageId, pageResouce, EMPTY, dialogStayEleId, System.currentTimeMillis() - dialogShowTime);
                }, view -> {
                  sendClickTrace(this, mouduleId, dialogPageId, pageResouce, EVENT_CANCEL, TRACE_EVENT_ID);
                  sendDurationTrace(this, mouduleId, dialogPageId, pageResouce, EMPTY, dialogStayEleId, System.currentTimeMillis() - dialogShowTime);
                });
      } else if(integer == SHARE_STATE_SUCCESS) {
        ToastExtend.showSuccess();
        EventBus.getDefault().post(new ShareUpdateEvent());
        finish();
      }
    });
    mViewModel.inviteState.observe(this, aBoolean -> {
      dismissLoading();
      if(aBoolean) {
        ToastExtend.showSuccess();
        EventBus.getDefault().post(new ShareUpdateEvent());
        finish();
      }
    });
    mViewModel.message.observe(this, s ->{
      dismissLoading();
      ToastUtils.showShort(s);
    });
  }

  public void shareOnClick(){
    sendBaseTraceClick(EVENT_SHARE);
    if(!CommonUtils.checkEmail(binding.ietMail.getInlineTipEditTextString())){
      mViewModel.inlineMessage.setValue(LanguageStrings.app_sharedevice_emailerror1_textview_text());
    } else if(binding.ietMail.getInlineTipEditTextString().equals(UserInfo.get().getEmail())){
      mViewModel.inlineMessage.setValue(LanguageStrings.app_sharedevice_emailerror3_textview_text());
    } else {
      mViewModel.inlineMessage.setValue("");
      if(null != getIntent().getExtras() ) {
        showLoading();
        mViewModel.addShare(null !=getIntent().getExtras().getString(KEY_DEVICEID)?getIntent().getExtras().getString(KEY_DEVICEID):EMPTY,
                binding.ietMail.getInlineTipEditTextString());
      }
    }
  }

  private void showLoading() {
    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));
  }

  private void dismissLoading() {
    if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
      loadingDialogWeakReference.get().dismiss();
    }
  }

  public void clearFocus(){
    binding.ietMail.clearFocus();
  }


  @Override
  protected Class<? extends ShareDeviceViewModel> getViewModelClass() {
    return ShareDeviceViewModel.class;
  }

  private void initTrace() {
    stayEleId = STAY_ELE_ID;
    pageId = PAGE_ID;
    mouduleId = MODULE_ID;
    nextButtoneleid = "";
    dialogPageId =DIALOG_PAGE_ID;
    dialogStayEleId = DIALOG_STAY_ELE_ID;
  }


}
