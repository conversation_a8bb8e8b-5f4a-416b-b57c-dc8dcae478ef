package com.chervon.moudleUserCenter.ui;

import static com.chervon.moudleUserCenter.ui.SettingActivity.compareVersion;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.VersionInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityVersionInfoBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterVersionInfoViewModel;




/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterFragment
 * @Description: version info activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_VERSION_INFO)
public class VersionInfoActivity extends BaseActivity<UserCenterVersionInfoViewModel> {

  private static final String VERSION_UPGRADE_BUTTON_CLICK = "2";
  private UsercenterActivityVersionInfoBinding binding;
  private ToolbarData toolbarData;
  private static final String BLANK_SPACE = "$$";
  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_version_info;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityVersionInfoBinding) viewDataBinding;
    toolbarData = new ToolbarData(LanguageStrings.app_version_currentversion_textview_text(), new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        finish();
        sendBackTrace();
      }
    });
    mViewModel.toolbarData.setValue(toolbarData);
    mViewModel.getVersionInfo();
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    binding.setUserCenterVersionInfoModel(mViewModel);
    mViewModel.versionInfoMutableLiveData.observe(this, new Observer<VersionInfo>() {
      @Override
      public void onChanged(VersionInfo versionInfo) {

        if (null==versionInfo){
          return;
        }

        String version = versionInfo.getVersion();

        if(TextUtils.isEmpty(version)){
          versionInfo.setVersion(LanguageStrings.app_version_latestversion_textview_text());
        }else{
          versionInfo.setVersion(LanguageStrings.app_version_latestversion_textview_text() + " V"+version);
        }

        String updateContent = versionInfo.getUpdateContent();

        if(TextUtils.isEmpty(updateContent)){
          versionInfo.setUpdateContent("");
          binding.usercenterActivityVersioninfoRelasenode.setVisibility(View.INVISIBLE);
        }else{
          binding.usercenterActivityVersioninfoRelasenode.setVisibility(View.VISIBLE);
          updateContent = updateContent.replace(BLANK_SPACE, "\n");
          versionInfo.setUpdateContent(updateContent);
          binding.setVersionInfo(versionInfo);
        }



        boolean  isBigVersion=false;

        if(TextUtils.isEmpty(version)){

        }else{
          String currentVersion= AppUtils.getAppVersionName();
          int versionReturn=  compareVersion(currentVersion,version);
          if(versionReturn<0){
            isBigVersion=true;
          }
        }
        if(isBigVersion){
          versionInfo.setVersion(LanguageStrings.app_version_latestversion_textview_text() + " V"+version);
          binding.usercenterActivityVersioninfoBt.setVisibility(View.VISIBLE);
          binding.usercenterActivityVersioninfoLastversion.setVisibility(View.VISIBLE);
          binding.usercenterActivityVersioninfoContenttitle.setVisibility(View.VISIBLE);
        }else{
          versionInfo.setUpdateContent("");
          binding.usercenterActivityVersioninfoLastversion.setVisibility(View.GONE);
          binding.usercenterActivityVersioninfoContenttitle.setVisibility(View.GONE);
          binding.usercenterActivityVersioninfoBt.setVisibility(View.GONE);
        }
      }
    });
  }


  public  void goGoogleMarket(final Context context) {
    try {
      Intent intent = new Intent(Intent.ACTION_VIEW);
      intent.setData(Uri.parse("market://details?id=" + getPackageName()));
      //intent.setPackage(GoogleMarket.GOOGLE_PLAY);//这里对应的是谷歌商店，跳转别的商店改成对应的即可
      intent.setPackage("com.android.vending");
      if (intent.resolveActivity(context.getPackageManager()) != null) {
        LogUtils.i("leslyversion 有应用市场");
        context.startActivity(intent);
      } else {//没有应用市场，通过浏览器跳转到Google Play
        LogUtils.i("leslyversion 无应用市场");
        Intent intent2 = new Intent(Intent.ACTION_VIEW);
        intent2.setData(Uri.parse("https://play.google.com/store/apps/details?id=" + getPackageName()));
        if (intent2.resolveActivity(context.getPackageManager()) != null) {
          context.startActivity(intent2);
        } else {
          //没有Google Play 也没有浏览器
          LogUtils.i("leslyversion 无Google play 也没有浏览器");
          //Toast.makeText(context,"无Google play,也没有浏览器",Toast.LENGTH_SHORT).show();
        }
      }
    } catch (ActivityNotFoundException activityNotFoundException1) {
      LogUtils.e( "GoogleMarket Intent not found");
    }
  }


  @Override
  protected Class<? extends UserCenterVersionInfoViewModel> getViewModelClass() {
    return UserCenterVersionInfoViewModel.class;
  }

  public void onVersionViewClick(View view){
    if(view.getId() == R.id.usercenter_activity_versioninfo_bt){
      sendBaseTraceClick(VERSION_UPGRADE_BUTTON_CLICK);

      goGoogleMarket(this);
    }
  }



  private void initTrace() {
    stayEleId = "3";
    pageId = "94";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }
}
