package com.chervon.moudleUserCenter.ui;


import static com.chervon.libBase.utils.APPConstants.DEVICE_SHARE_TYPE_ACCEPT;
import static com.chervon.libBase.utils.APPConstants.DEVICE_SHARE_TYPE_SHARE;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;
import static com.chervon.moudleUserCenter.utils.Constants.EMPTY;
import static com.chervon.moudleUserCenter.utils.Constants.TRACE_EVENT_ID;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.ToastExtend;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.data.ShareDeviceData;
import com.chervon.moudleUserCenter.data.ShareListUiState;
import com.chervon.moudleUserCenter.databinding.UsercenterFragmentShareListBinding;
import com.chervon.libNetwork.event.ShareUpdateEvent;
import com.chervon.moudleUserCenter.ui.adapter.ShareCenterAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.ShareListViewModel;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: ShareCenterFragment
 * @Description: 设备分享
 * @Author: wuxd
 * @CreateDate: 2024/8/6
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version: 1.0
 */
public class ShareListFragment extends BaseEnhanceFragment<ShareListViewModel, UsercenterFragmentShareListBinding> {

  private static final String TAG = "ShareListFragment";
  /**
   *  0 Share; 1 Accept
   */
  public enum ShareListType{
    // 0 share
    SHARE,
    // 1 accept
    ACCEPT
  }
  private ShareListType type;
  private ShareCenterAdapter adapter;
  private WeakReference<LoadingDialog> loadingDialogWeakReference;
  private long dialogShowTime;
  private String dialogPageId;
  private String dialogStayEleId;
  private static final String EVENT_CLICK = "2";
  private static final String EVENT_ACCEPT = "1";
  private static final String EVENT_DELETE = "2";
  private static final String EVENT_CONFIRM = "1";
  private static final String EVENT_CANCEL = "2";
  private static final String MODULE_ID = "13";
  private static final String SHARE_PAGE_ID = "461";
  private static final String ACCEPT_PAGE_ID = "466";
  private static final String STAY_ELE_ID = "3";
  private static final String ACCEPT_DIALOG_PAGE_ID = "467";
  private static final String ACCEPT_DIALOG_STAY_ELE_ID = "3";
  private boolean isFirst = true;

  public ShareListFragment(ShareListType type) {
    this.type = type;
  }
  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    initObserver();
  }

  @Override
  protected int getLayoutId() {
    return R.layout.usercenter_fragment_share_list;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    if(null == mViewModel.uiState.getValue()) {
      mViewModel.uiState.setValue(new ShareListUiState());
    }
    mViewDataBinding.setViewModel(mViewModel);
    mViewDataBinding.setController(this);
    if(ShareListType.SHARE == type) {
      mViewModel.uiState.getValue().setShareType(DEVICE_SHARE_TYPE_SHARE);
      adapter = new ShareCenterAdapter(requireContext(),DEVICE_SHARE_TYPE_SHARE);
      adapter.setListener(new ShareCenterAdapter.ShareOnClick() {
        @Override
        public void onClick(ShareDeviceData data) {
          sendBaseTraceClick(EVENT_CLICK);
          mViewModel.toShareDeviceDetail(data.getDeviceId(),data.getNickName(),data.getDeviceIcon());
        }

        @Override
        public void onLongClick(ShareDeviceData data) {}
      });
    } else {
      mViewModel.uiState.getValue().setShareType(DEVICE_SHARE_TYPE_ACCEPT);
      adapter = new ShareCenterAdapter(requireContext(),DEVICE_SHARE_TYPE_ACCEPT);
      adapter.setListener(new ShareCenterAdapter.ShareOnClick() {
        @Override
        public void onClick(ShareDeviceData data) {
          showLoading();
          sendBaseTraceClick(EVENT_ACCEPT);
          mViewModel.acceptShare(String.valueOf(data.getShareId()));
        }

        @Override
        public void onLongClick(ShareDeviceData data) {
          sendBaseTraceClick(EVENT_DELETE);
          sendExposure(getActivity(), mouduleId, dialogPageId, pageResouce);
          dialogShowTime = System.currentTimeMillis();
          DialogUtil.simpleConfirmDialog3(requireContext(),EMPTY,
                  LanguageStrings.app_sharedevice_dialog2_textview_text(), LanguageStrings.getNetworkconfigconfirmed(),
                  LanguageStrings.app_base_cancel_button_text(), view -> {
                      showLoading();
                      mViewModel.removeShareAccept(String.valueOf(data.getShareId()));
                    sendClickTrace(getActivity(), mouduleId, dialogPageId, pageResouce, EVENT_CONFIRM, TRACE_EVENT_ID);
                    sendDurationTrace(getActivity(), mouduleId, dialogPageId, pageResouce, "", dialogStayEleId, System.currentTimeMillis() - dialogShowTime);
                  }, view -> {
                    sendClickTrace(getActivity(), mouduleId, dialogPageId, pageResouce, EVENT_CANCEL, TRACE_EVENT_ID);
                    sendDurationTrace(getActivity(), mouduleId, dialogPageId, pageResouce, "", dialogStayEleId, System.currentTimeMillis() - dialogShowTime);
                  },"");
        }
      });
    }
    mViewDataBinding.recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
    mViewDataBinding.recyclerView.setAdapter(adapter);
  }

  public void refreshData(){
    if(ShareListType.SHARE == type) {
      mViewModel.getShareDeviceList();
    } else {
      mViewModel.getShareAcceptList();
    }
  }

  @Subscribe(threadMode = ThreadMode.MAIN)
  public void onUpdate(ShareUpdateEvent event) {
    refreshData();
  }

  private void initObserver() {
    mViewModel.uiState.observe(this, uiState -> {
      if(null != adapter && null != uiState.getShareDeviceDataList()) {
        dismissLoading();
        adapter.setData(uiState.getShareDeviceDataList(),true);
      }
    });
    mViewModel.message.observe(this, s -> {
      dismissLoading();
      ToastUtils.showShort(s);
    });
    mViewModel.needUpdate.observe(this,b ->{
      if(b) {
        ToastExtend.showSuccess();
        EventBus.getDefault().post(new ShareUpdateEvent());
      }
    });
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {
  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  public void onResume() {
    super.onResume();
    if(isFirst){
      showLoading();
      isFirst = false;
    }
    if((ShareListType.ACCEPT == type)) {
      if(getActivity() != null && getActivity() instanceof ShareCenterActivity) {
        ((ShareCenterActivity)getActivity()).refreshData();
      }
    }
    refreshData();
  }

  @Override
  protected Class<? extends ShareListViewModel> getViewModelClass() {
    return ShareListViewModel.class;
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  private void initTrace() {
    if(ShareListType.SHARE == type) {
      stayEleId = STAY_ELE_ID;
      pageId = SHARE_PAGE_ID;
    } else {
      stayEleId = STAY_ELE_ID;
      pageId = ACCEPT_PAGE_ID;
      dialogPageId =ACCEPT_DIALOG_PAGE_ID;
      dialogStayEleId = ACCEPT_DIALOG_STAY_ELE_ID;
    }
    mouduleId = MODULE_ID;
    nextButtoneleid = "";
  }

  private void showLoading() {
    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getChildFragmentManager()));
  }

  private void dismissLoading() {
    if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
      loadingDialogWeakReference.get().dismiss();
    }
  }

  @Override
  public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
    super.onViewCreated(view, savedInstanceState);
    if(!EventBus.getDefault().isRegistered(this)) {
      EventBus.getDefault().register(this);
    }
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    if(EventBus.getDefault().isRegistered(this)) {
      EventBus.getDefault().unregister(this);
    }
  }
}
