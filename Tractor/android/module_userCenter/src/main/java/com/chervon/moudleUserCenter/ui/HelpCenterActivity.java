package com.chervon.moudleUserCenter.ui;

import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.widget.refresh.PullRefreshLayout2;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.libDB.entities.CommonProblemRespone;
import com.chervon.moudleUserCenter.data.HelpCenterUIState;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityHelpCenterBinding;
import com.chervon.moudleUserCenter.ui.adapter.HelpCenterAdapter2;
import com.chervon.moudleUserCenter.ui.viewmodel.HelpCenterViewModel;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */

@Route(path = RouterConstants.ACTIVITY_HELP_CENTER)
public class HelpCenterActivity extends BaseActivity<HelpCenterViewModel> implements ItemClick {
    public static final String TAG = "HelpCenterActivity";
    private static final String LIST_ITEMS_BUTTON_CLICK = "2";
    private static final String CONFIRM_BUTTON_CLICK = "1";
    private HelpCenterAdapter2 mAdapter;
    private UsercenterActivityHelpCenterBinding mViewDataBinding;
    private int pages = 0;

    @Override
    protected Integer getLayoutId() {
        return R.layout.usercenter_activity_help_center;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding = (UsercenterActivityHelpCenterBinding) viewDataBinding;
        mViewDataBinding.rvDeviceList.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));

        mAdapter = new HelpCenterAdapter2(this, this);
        mAdapter.setHasStableIds(true);
        mViewDataBinding.rvDeviceList.setAdapter(mAdapter);
        mViewDataBinding.srlCotainer.setOnLoadListener(new PullRefreshLayout2.OnLoadListener() {
            @Override
            public void onLoad() {

                mViewModel.getCommonProblem(false, pages);
            }
        });

    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        mViewDataBinding.setUiState(new ManualSNUIState());
        mViewDataBinding.setPresenter(this);
        mViewModel.toolbarData.setValue(new ToolbarData(
                LanguageStrings.app_helpcenter_title_textview_text(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        finish();
                    }
                }));

        mViewDataBinding.setHelpCenterViewModel(mViewModel);

        mViewModel.mLiveData.observe(this, new Observer<HelpCenterUIState>() {
            @Override
            public void onChanged(HelpCenterUIState uiState) {
                mViewModel.showLoading.postValue(false);
                mViewDataBinding.srlCotainer.setLoading(false);
                List<CommonProblemRespone> commonProblemResponeList = uiState.getCommonProblemRespone();
                if (commonProblemResponeList != null && commonProblemResponeList.size() > 0) {
                    pages = uiState.getPages();
                    if (uiState.getPageNumber() == 1) {
                        mAdapter.setData(commonProblemResponeList, true);
                    } else {
                        mAdapter.setData(commonProblemResponeList, false);
                    }
                }
            }
        });
    }

    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {

    }


    @Override
    protected Class getViewModelClass() {
        return HelpCenterViewModel.class;
    }


    @Override
    public void onItemClick(Object uiState) {
        sendBaseTraceClick(LIST_ITEMS_BUTTON_CLICK);


        if (uiState instanceof String) {
            mViewModel.thunbmUp(String.valueOf(uiState));
        } else {
            mViewModel.saveSearchToHistory();
            ARouter.getInstance().build(RouterConstants.ACTIVITY_HELP_CENTER_DETAIL).withSerializable(KEY_PREV_DATA, (CommonProblemRespone) uiState).navigation();

        }


    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {
    }


    public void cancel() {
        finish();
    }

    public void clearText() {
        mViewDataBinding.etSearch.setText("");
    }


    @Override
    public void onPause() {
        super.onPause();
    }


    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        super.onPointerCaptureChanged(hasCapture);
    }


    public void goToSearch() {
        sendBaseTraceClick(CONFIRM_BUTTON_CLICK);
        ARouter.getInstance().build(RouterConstants.ACTIVITY_HELP_CENTER_SEARCH).navigation();
    }

    @Override
    protected void onResume() {
        super.onResume();
        //修复 Unable to add window  IOT-11847
        mViewModel.getCommonProblem(true, pages);
    }


    private void initTrace() {
        stayEleId = "3";
        pageId = "103";
        mouduleId = "11";
        nextButtoneleid = "2";
    }


}
