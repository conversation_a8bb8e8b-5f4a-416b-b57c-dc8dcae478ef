package com.chervon.moudleUserCenter.ui;

import static com.chervon.moudleUserCenter.ui.viewmodel.UserCenterSettingViewModel.SMART_CONNECT_BUTTON_CLICK;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import androidx.appcompat.widget.SwitchCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.User;
import com.chervon.libDB.entities.VersionInfo;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.request.SmartScanSetRequest;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libNetwork.http.model.result.SmartScanBean;
import com.chervon.libNetwork.http.model.result.SmartScanSetBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivitySettingBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterSettingViewModel;
import java.lang.ref.WeakReference;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterFragment
 * @Description: setting activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_SETTING)
public class SettingActivity extends BaseActivity<UserCenterSettingViewModel> {
  private WeakReference<LoadingDialog> loadingDialogWeakReference;
  private UsercenterActivitySettingBinding binding;


  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_setting;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivitySettingBinding) viewDataBinding;

//    if (BuildConfig.EVN.equals("na")){
//      binding.language.setVisibility(View.GONE);
//    }else {
//      binding.language.setVisibility(View.VISIBLE);
//
//    }

    //初始化按钮状态
    //binding.usercenterActivitySettingAutoConnectSwitchbutton.setChecked(UserInfo.get().isSmartScanState());
    binding.switchSmart.setChecked(UserInfo.get().isSmartScanState());
    Log.e("获取-SmartScanState ", String.valueOf(UserInfo.get().isSmartScanState()));

    mViewModel.switchSmartLiveData.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        binding.switchSmart.setChecked(aBoolean);
      }
    });

    binding.switchSmart.setOnTouchListener(new View.OnTouchListener() {
      @Override
      public boolean onTouch(View view, MotionEvent motionEvent) {
        if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
          mViewModel.traceClickLiveData.postValue(SMART_CONNECT_BUTTON_CLICK);
          SwitchCompat switchButton = (SwitchCompat) view;
          loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));

          ApiService.instance()
            .setSmartScan(new SmartScanSetRequest(!switchButton.isChecked()))
            .subscribe(new HttpObserver<SmartScanSetBean>() {
              @Override
              protected void Next(SmartScanSetBean entity) {
                User user = UserInfo.get();
                user.setSmartScanState(entity.isEntry());
                UserInfo.set(user);
                switchButton.setChecked(entity.isEntry());
                if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
                  loadingDialogWeakReference.get().dismiss();
                }
              }

              @Override
              public void Error(ErrorBean errorBean) {
                super.Error(errorBean);
                if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
                  loadingDialogWeakReference.get().dismiss();
                }
              }
            });
        }
        return true;
      }
    });


    mViewModel.loginOutLiveData.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          UserInfo.clear();
          BaseApplication.showCurrentFragmentLayoutId = -1;
          mViewModel.loginOutLiveData.setValue(false);
        }

      }
    });


  }

  @Override
  protected void initData(Bundle savedInstanceState) {

    initTrace();
    mViewModel.setFragmentManager(getSupportFragmentManager());

    binding.setUserCenterSettingModel(mViewModel);
    mViewModel.getVersionInfo();
    mViewModel.versionInfoMutableLiveData.observe(this, new Observer<VersionInfo>() {
      @Override
      public void onChanged(VersionInfo versionInfo) {

        if (versionInfo == null) {
          versionInfo = new VersionInfo();
        }
        if (versionInfo.getUpdateContent() == null || "".equals(versionInfo.getUpdateContent())) {
          versionInfo.setUpdateContent("");
        }
        String version = versionInfo.getVersion();
        boolean isBigVersion = false;
        if (TextUtils.isEmpty(version)) {

        } else {
          String currentVersion = AppUtils.getAppVersionName();
          int versionReturn = compareVersion(currentVersion, version);
          if (versionReturn < 0) {
            isBigVersion = true;
          }


        }
        if (isBigVersion) {
          binding.ivCorner.setVisibility(View.VISIBLE);
        } else {
          binding.ivCorner.setVisibility(View.GONE);
        }

      }
    });

    mViewModel.traceClickLiveData.observe(this, new Observer<String>() {
      @Override
      public void onChanged(String aBoolean) {
        if (!TextUtils.isEmpty(aBoolean)) {
          sendBaseTraceClick(aBoolean);
        }
      }
    });
  }

  @Override
  protected void onResume() {
    super.onResume();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_setting_title_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          finish();
          sendBackTrace();
        }
      }));


    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));

    ApiService.instance().getSmartScan().subscribe(new HttpObserver<SmartScanBean>() {
      @Override
      protected void Next(SmartScanBean entity) {
        //更新数据存储

        if (entity.getEntry() != null) {
          User user = UserInfo.get();
          user.setSmartScanState(entity.getEntry().isInfo());
          UserInfo.set(user);
          mViewModel.switchSmartLiveData.postValue(entity.getEntry().isInfo());
        }
        if (loadingDialogWeakReference.get() != null) {
          loadingDialogWeakReference.get().dismiss();
        }

      }
    });
  }

  @Override
  protected Class<? extends UserCenterSettingViewModel> getViewModelClass() {
    return UserCenterSettingViewModel.class;
  }

  private void initTrace() {
    stayEleId = "11";
    pageId = "90";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  public static int compareVersion(String v1, String v2) {
    if (v1.equals(v2)) {
      return 0;
    }
    String[] version1Array = v1.split("[._]");
    String[] version2Array = v2.split("[._]");
    int index = 0;
    int minLen = Math.min(version1Array.length, version2Array.length);
    long diff = 0;

    while (index < minLen
      && (diff = Long.parseLong(version1Array[index])
      - Long.parseLong(version2Array[index])) == 0) {
      index++;
    }
    if (diff == 0) {
      for (int i = index; i < version1Array.length; i++) {
        if (Long.parseLong(version1Array[i]) > 0) {
          return 1;
        }
      }

      for (int i = index; i < version2Array.length; i++) {
        if (Long.parseLong(version2Array[i]) > 0) {
          return -1;
        }
      }
      return 0;
    } else {
      return diff > 0 ? 1 : -1;
    }
  }
}
