package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityUserInfoBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterUserInfoViewModel;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterFragment
 * @Description: userinfo activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_USER_INFO)
public class UserInfoActivity extends BaseActivity<UserCenterUserInfoViewModel> {
  private static final String CHANGE_PASSWORD_BUTTON_CLICK = "3";


  private UsercenterActivityUserInfoBinding binding;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_user_info;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityUserInfoBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_userDetail_accountsettings_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));

    binding.setViewModel(mViewModel);

    mViewModel.setFragmentManager(getSupportFragmentManager());

    mViewModel.traceClick.observe(this, new Observer<String>() {
      @Override
      public void onChanged(String s) {
        if (!TextUtils.isEmpty(s)) {
          sendBaseTraceClick(s);
        }
      }
    });


    mViewModel.loginOutLiveData.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          UserInfo.clear();
          mViewModel.loginOutLiveData.setValue(false);
          AwsMqttService.getInstance().stopAwsService();
        }

      }
    });
  }


  @Override
  protected Class<? extends UserCenterUserInfoViewModel> getViewModelClass() {
    return UserCenterUserInfoViewModel.class;
  }


  private void initTrace() {
    stayEleId = "4";
    pageId = "155";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


}
