package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.moudleUserCenter.ui.viewmodel.UserCenterCancleAccountViewModel.CANCLEACCOUNT_FAIL;
import static com.chervon.moudleUserCenter.ui.viewmodel.UserCenterCancleAccountViewModel.CANCLEACCOUNT_SUCCESS;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityCancleAccountBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterCancleAccountViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterFragment
 * @Description: logout activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_CANCLE_ACCOUNT)
public class CancleAccountActivity extends BaseActivity<UserCenterCancleAccountViewModel> {

  private UsercenterActivityCancleAccountBinding binding;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_cancle_account;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityCancleAccountBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_userDetail_accountcancel_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          finish();
          sendBackTrace();
        }
      }));
    mViewModel.ResponseStateClickLiveData.observe(this, new Observer<Integer>() {
      @Override
      public void onChanged(Integer integer) {
        if (integer.equals(CANCLEACCOUNT_SUCCESS)) {
          sendClickTrace(CancleAccountActivity.this, mouduleId, "160", pageResouce, "4", "1");
          mViewModel.ResponseStateClickLiveData.setValue(0);
        } else if (integer.equals(CANCLEACCOUNT_FAIL)) {
          sendClickTrace(CancleAccountActivity.this, mouduleId, "160", pageResouce, "5", "1");
        }
      }
    });

    mViewModel.traceClickLiveData.observe(this, new Observer<String>() {
      @Override
      public void onChanged(String s) {
        if (!TextUtils.isEmpty(s)) {
          sendBaseTraceClick(s);
        }
      }
    });

    mViewModel.setFragmentManager(getSupportFragmentManager());

    binding.setUserCenterCancleAccountModel(mViewModel);
  }

  @Override
  protected Class<? extends UserCenterCancleAccountViewModel> getViewModelClass() {
    return UserCenterCancleAccountViewModel.class;
  }

  private void initTrace() {
    stayEleId = "3";
    pageId = "160";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  @Override
  protected void onResume() {
    super.onResume();

  }

  @Override
  protected void onPause() {
    super.onPause();
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }
}
