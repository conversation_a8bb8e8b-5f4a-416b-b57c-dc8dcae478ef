package com.chervon.moudleUserCenter.ui;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityAboutBinding;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityVersionInfoBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterAboutViewModel;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterVersionInfoViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterAboutActivity
 * @Description: 关于我们页面
 * @Author: langmeng
 * @CreateDate: 2022/9/3 14:52
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/3 14:52
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_ABOUT)
public class UserCenterAboutActivity extends BaseActivity<UserCenterAboutViewModel> {

    private UsercenterActivityAboutBinding binding;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.usercenter_activity_about;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {

        binding = (UsercenterActivityAboutBinding) viewDataBinding;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
      initTrace();
        mViewModel.toolbarData.setValue(new ToolbarData(
                LanguageStrings.app_about_title_textview_text(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        finish();
                      sendBackTrace();
                    }
                }));

        binding.setUserCenterAboutModel(mViewModel);
    }

    @Override
    protected Class<? extends UserCenterAboutViewModel> getViewModelClass() {
        return UserCenterAboutViewModel.class;
    }


  private void initTrace() {
    stayEleId = "2";
    pageId = "95";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }
}
