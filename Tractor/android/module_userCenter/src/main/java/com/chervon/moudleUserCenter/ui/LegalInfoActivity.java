package com.chervon.moudleUserCenter.ui;

import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;

import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityLanguageBinding;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityLegalInfoBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterLanguageViewModel;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterLegalInfoViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: LegalInfoActivity
 * @Description: legal info activity
 * @Author: LangMeng
 * @CreateDate: 2022/6/28 14:47
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/28 14:47
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_LEGAL_INFO)
public class LegalInfoActivity extends BaseActivity<UserCenterLegalInfoViewModel> {
  int from;
  private UsercenterActivityLegalInfoBinding binding;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_legal_info;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityLegalInfoBinding) viewDataBinding;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_privacy_title_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          finish();
          sendBackTrace();
        }
      }));
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      from = bundle.getInt(KEY_FROM);
      if (from == R.id.controlPanel) {
        String productId = bundle.getString(KEY_PREV_DATA);
        mViewModel.setProcuctId(productId);
        binding.item1.setText(LanguageStrings.app_privacy_privacyPolicy_textview_text());
        binding.item2.setText(LanguageStrings.app_privacy_userAgreement_textview_text());
        binding.item3.setText(LanguageStrings.app_privacy_withdraw_authorize_textview_text());
        binding.item4.setText(LanguageStrings.app_privacy_cancelServices_textview_text());


      } else {
        ToolbarData toolbarData = mViewModel.toolbarData.getValue();
        toolbarData.setTitle(LanguageStrings.appPrivacyagreementuserTitle());
        mViewModel.toolbarData.setValue(toolbarData);

        binding.item1.setText(LanguageStrings.appPrivacyagreementuserPrivacyagreement());
        binding.item2.setText(LanguageStrings.appPrivacyagreementuserUseragreement());
        binding.item3.setText(LanguageStrings.appPrivacyagreementuserRevokeagreement());
        binding.item4.setText(LanguageStrings.appPrivacyagreementuserCcelaccount());
      }
    }

    mViewModel.traceClickLiveData.observe(this, new Observer<String>() {
      @Override
      public void onChanged(String s) {
        if(!TextUtils.isEmpty(s)){
          sendBaseTraceClick(s);
        }
      }
    });

    mViewModel.setFragmentManager(getSupportFragmentManager());

    binding.setUserCenterLegalIfnoModel(mViewModel);
  }

  @Override
  protected Class<? extends UserCenterLegalInfoViewModel> getViewModelClass() {
    return UserCenterLegalInfoViewModel.class;
  }


  private void initTrace() {
    stayEleId = "6";
    pageId = "97";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }
}
