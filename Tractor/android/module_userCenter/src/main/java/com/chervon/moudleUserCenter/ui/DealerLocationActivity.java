package com.chervon.moudleUserCenter.ui;


import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.SeekBar;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.DeviceDictEntry;
import com.chervon.libBase.model.DictItem;
import com.chervon.libBase.model.DictNode;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DealerInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityDealerLocationBinding;
import com.chervon.moudleUserCenter.ui.adapter.DealerInfoAdapter;
import com.chervon.moudleUserCenter.ui.adapter.DealerCategoryAdapter;
import com.chervon.moudleUserCenter.ui.adapter.DealerItemClick;
import com.chervon.moudleUserCenter.ui.viewmodel.DealerLocationViewModel;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.Task;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.ComponentFilter;
import com.google.maps.model.GeocodingResult;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: DealerLocationActivity
 * @Description:
 * @Author: hyman
 * @CreateDate: 2022/11/29 上午10:14
 * @UpdateUser: hyman
 * @UpdateDate: 2024/8/16
 * @UpdateRemark: DealerLocation迭代--增加手动切换zipcode和范围搜索
 * @Version: 1.1.0
 */

@Route(path = RouterConstants.ACTIVITY_DEALER_LOCATION)
public class DealerLocationActivity extends BaseActivity<DealerLocationViewModel> implements OnMapReadyCallback, GoogleMap.OnMarkerClickListener, DealerItemClick, GoogleMap.OnCameraMoveCanceledListener {
    private String TAG = "DealerLocationActivity---";
    private static final int PERMISSIONS_REQUEST_ACCESS_FINE_LOCATION = 88;
    private static final String LOWES_BUTTON_CLICK = "2";
    private static final String ACE_HARDWARE_BUTTON_CLICK = "3";
    private static final String DEALER_BUTTON_CLICK = "4";
    private static final String SERVICE_LOCATION_BUTTON_CLICK = "5";
    private static final String ALL_BUTTON_CLICK = "6";
    private Handler mHandler = new Handler();
    private UsercenterActivityDealerLocationBinding mBinding;
    private ToolbarData mToolbarData;
    private DealerCategoryAdapter categoryAdapter;
    private DealerInfoAdapter dealerInfoAdapter;
    private boolean locationPermissionGranted;
    private GoogleMap map;
    private FusedLocationProviderClient fusedLocationProviderClient;
    private Location lastKnownLocation;
    private DictItem[] categoryDictItems;
    private List<Marker> markerList = new ArrayList<>();
    private List<DealerInfo> currentDealerInfoList = new ArrayList<>();
    private String mapKey = null;

    @Override
    protected Integer getLayoutId() {
        return R.layout.usercenter_activity_dealer_location;
    }

    @Override
    protected void onRegister() {
        getLocationPermission();
    }

    @Override
    protected void onUnRegister() {

    }


    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mBinding = (UsercenterActivityDealerLocationBinding) viewDataBinding;
        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(this);
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.map);
        mapFragment.getMapAsync(DealerLocationActivity.this);

        mToolbarData = new ToolbarData(LanguageStrings.app_usercenter_dealer_location_textview_text(), new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                goBack();
            }
        });
        mBinding.setToolbarData(mToolbarData);
        //顶部 category
        categoryAdapter = new DealerCategoryAdapter(this, new DictNode[]{}, this);
        mBinding.categoryRecy.setLayoutManager(new LinearLayoutManager(this, RecyclerView.HORIZONTAL, false));
        mBinding.categoryRecy.setAdapter(categoryAdapter);

        //底部 经销商信息
        dealerInfoAdapter = new DealerInfoAdapter(this);
        dealerInfoAdapter.setClick(this);
        mBinding.dealerRecy.setLayoutManager(new LinearLayoutManager(this));
        mBinding.dealerRecy.setAdapter(dealerInfoAdapter);

        mBinding.setViewModel(mViewModel);

        setSeekBar();

        addZipCodeListener();

        ProgressHelper.showProgressView(this, 0);

        mViewModel.getSecretKey();


    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        mBinding.setPresenter(this);
        mViewModel.getCategoryNames();
        mViewModel.category.observe(this, new Observer<DeviceDictEntry>() {
            @Override
            public void onChanged(DeviceDictEntry deviceDictEntry) {
                if (deviceDictEntry != null) {
                    categoryDictItems = deviceDictEntry.getEntry();
                    if (categoryDictItems.length > 0) {
                        categoryAdapter.setData(categoryDictItems[0].getNodes());
                        categoryAdapter.notifyDataSetChanged();
                        if (null!=map){
                            map.clear();
                        }
                        dealerInfoAdapter.clearDealer();
                        mViewModel.setSelectedCategory(categoryAdapter.getCurrentCateGory().getLabel());
                        mViewModel.getDealerInfo(true);
                    }
                }
            }
        });
        mViewModel.dealerInfoLiveData.observe(this, new Observer<List<DealerInfo>>() {

            @Override
            public void onChanged(List<DealerInfo> dealerInfos) {
                ProgressHelper.hideProgressView(DealerLocationActivity.this);

                if (!currentDealerInfoList.isEmpty()){
                    currentDealerInfoList.clear();
                }

                if (dealerInfos != null && !dealerInfos.isEmpty()) {
                    currentDealerInfoList.addAll(dealerInfos);
                    dealerInfoAdapter.setDealerInfo(currentDealerInfoList);
                    mViewModel.setShowDefaultView(false);
                    if (null!=map){
                        map.clear();
                    }
                    markerList.clear();
                    try {
                        for (int i = 0; i < dealerInfos.size(); i++) {
                            DealerInfo dealerInfo = dealerInfos.get(i);
                            LatLng latlng = new LatLng(Double.valueOf(dealerInfo.getLat()), Double.valueOf(dealerInfo.getLng()));
                            Marker markerPath = map.addMarker(new MarkerOptions().position(latlng).icon(getBitMapDescriptor(R.drawable.ic_location_red)).title(dealerInfo.getName()));
                            markerPath.setVisible(true);
                            markerPath.setTag(i);
                            markerList.add(markerPath);

                        }


                    } catch (Exception e) {
                        LogUtils.e(TAG, e.getMessage() + "");
                    }
                } else {
                    ToastUtils.showShort(LanguageStrings.app_productdealers_dataempty_textview_text());
                    showDefaultLayout();
                }
            }
        });

        mBinding.nestedScrollView.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                LogUtils.e("measuredHeight--->onScrollChange", scrollY);
                mBinding.dealerRecy.setMinimumHeight(mBinding.nestedScrollView.getMeasuredHeight()
                        - mBinding.constraintSeek.getHeight());
            }
        });

        mViewModel.secretKeyLiveData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String secretKey) {
                mViewModel.getMapKeyInfo(secretKey);
            }
        });

        mViewModel.mapKeyInfoLiveData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String netMapKey) {
                mapKey = netMapKey;
            }
        });

    }

    private void showDefaultLayout() {
        if (null != map) {
            map.clear();
        }
        mViewModel.setShowDefaultView(true);
    }

    /**
     * 设置进度条
     * 2023-8月新增需求
     */
    private void setSeekBar() {

        mBinding.seekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                mViewModel.setDistance(progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                ProgressHelper.showProgressView(DealerLocationActivity.this, 0, true);
                dealerInfoAdapter.clearDealer();
                mViewModel.getDealerInfo(true);
            }
        });
    }


    private void addZipCodeListener() {
        mBinding.editZipCode.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {

                if (!hasFocus) {
                    KeyboardUtils.hideSoftInput(mBinding.editZipCode);
                }
            }
        });
        KeyboardUtils.registerSoftInputChangedListener(this, new KeyboardUtils.OnSoftInputChangedListener() {
            @Override
            public void onSoftInputChanged(int height) {
                String zipCode = mBinding.editZipCode.getText().toString();
                if (height == 0 && !TextUtils.isEmpty(zipCode)) {
                    ProgressHelper.showProgressView(DealerLocationActivity.this, 0, true);
                    dealerInfoAdapter.clearDealer();
                    mViewModel.clearLocation();
                    getLocationFromZipcode(zipCode);
                } else if (height == 0 && TextUtils.isEmpty(zipCode)) {
                    getDeviceLocation();
                }
            }
        });

    }

    /**
     * 根据zipcode逆地理出经纬度
     * 2023-8月新增需求
     *
     * @param zipcode
     */

    private void getLocationFromZipcode(String zipcode) {
        final int indexOfFirst = 0;
        final String countryCodeNa = "US";
        final String countryCodeCa = "CA";
        final int usLength = 5;
        try {
            if (TextUtils.isEmpty(mapKey)){
                LogUtils.e(TAG,"error-----MapKey is empty");
                return;
            }
            GeoApiContext context = new GeoApiContext.Builder().apiKey(mapKey).build();
            String countryCode = "";

            if (TextUtils.isEmpty(zipcode)) {
                ProgressHelper.hideProgressView(DealerLocationActivity.this);
                ToastUtils.showShort(LanguageStrings.app_dealer_zipcodenotfound_textview_text());
                LogUtils.e("Location", "Unable to get location from zipcode");
                return;
            } else {
                if (zipcode.length() <= usLength) {
                    countryCode = countryCodeNa;
                } else {
                    countryCode = countryCodeCa;
                }
            }

            //根据Google Map SDK进行逆地理编码
            GeocodingResult[] results = GeocodingApi.newRequest(context).components(ComponentFilter.country(countryCode), ComponentFilter.postalCode(zipcode)).await();
            if (results.length > 0) {
                com.google.maps.model.LatLng latLng = results[indexOfFirst].geometry.location;
                map.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(latLng.lat, latLng.lng), DEFAULT_ZOOM));
                mViewModel.setLocation(latLng.lat, latLng.lng);
                mViewModel.getDealerInfo(true);
                dealerInfoAdapter.clearDealer();
            } else {
                ProgressHelper.hideProgressView(DealerLocationActivity.this);
                ToastUtils.showShort(LanguageStrings.app_dealer_zipcodenotfound_textview_text());
                showDefaultLayout();
                LogUtils.e("Location", "Unable to get location from zipcode");
            }
        } catch (Exception e) {
            ProgressHelper.hideProgressView(DealerLocationActivity.this);
            ToastUtils.showShort(LanguageStrings.app_dealer_zipcodenotfound_textview_text());
            e.printStackTrace();
        }


    }

    private void getLocationPermission() {
        if (PermissionUtils.isGranted(android.Manifest.permission.ACCESS_FINE_LOCATION)) {
            locationPermissionGranted = true;
            if (!isLocationEnabled() || !locationPermissionGranted) {
                mViewModel.setShowLocationError(true);
            } else {
                getDeviceLocation();
                mViewModel.setShowLocationError(false);
            }
        } else {
            mViewModel.setShowLocationError(true);
            //如果zipcode也是空，那么展示空页面
            if (TextUtils.isEmpty(mBinding.editZipCode.getText().toString())){
                mViewModel.clearLocation();
                showDefaultLayout();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        locationPermissionGranted = false;
        if (requestCode == PERMISSIONS_REQUEST_ACCESS_FINE_LOCATION) {
            // If request is cancelled, the result arrays are empty.
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                locationPermissionGranted = true;
            }
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
        updateLocationUI();
    }


    @Override
    public void onMapReady(GoogleMap map) {
        this.map = map;
        updateLocationUI();
        int delayOneSecond = 1000;
        mBinding.iBMyLocation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (null != map) {
                    map.clear();
                }
                dealerInfoAdapter.clearDealer();
                mViewModel.clearLocation();
                mViewModel.clearZipCode();
                //增加延迟，解决数据粘性问题
                mHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        getLocationPermission();
                    }
                },delayOneSecond);
            }
        });

        map.setOnMarkerClickListener(this);

    }


    private void updateLocationUI() {
        if (map == null) {
            return;
        }
        try {
            if (locationPermissionGranted) {
                map.setMyLocationEnabled(true);
                map.getUiSettings().setMyLocationButtonEnabled(false);
            } else {
                map.setMyLocationEnabled(false);
                map.getUiSettings().setMyLocationButtonEnabled(false);
                lastKnownLocation = null;
                getLocationPermission();
            }
        } catch (SecurityException e) {
            LogUtils.e(TAG, "updateLocationUI is Exception---" + e.getMessage());
        }
    }

    int DEFAULT_ZOOM = 11;

    private void getDeviceLocation() {

        try {
            //如果当前zipcode内容非空定位功能不可使用，除非用户手动点击定位点
            if (!TextUtils.isEmpty(mBinding.editZipCode.getText().toString())){
                return;
            }

            if (locationPermissionGranted) {
                fusedLocationProviderClient.getLocationAvailability();
                Task<Location> locationResult = fusedLocationProviderClient.getLastLocation();
                locationResult.addOnCompleteListener(this, new OnCompleteListener<Location>() {
                    @Override
                    public void onComplete(@NonNull Task<Location> task) {
                        if (task.isSuccessful()) {
                            lastKnownLocation = task.getResult();
                            if (lastKnownLocation != null) {
                                if (null!=dealerInfoAdapter){
                                    dealerInfoAdapter.clearDealer();
                                }
                                map.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude()), DEFAULT_ZOOM));
                                mViewModel.setLocation(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude());
                                mViewModel.getDealerInfo(true);
                            } else {
                                showDefaultLayout();
                            }
                        }
                    }
                }).addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(@NonNull Exception e) {
                        if (lastKnownLocation != null) {
                            map.moveCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude()), DEFAULT_ZOOM));
                            mViewModel.setLocation(lastKnownLocation.getLatitude(), lastKnownLocation.getLongitude());
                            mViewModel.getDealerInfo(true);
                        } else {
                            showDefaultLayout();
                        }
                    }
                });
            }
        } catch (SecurityException e) {
            Log.e("Exception: %s", e.getMessage(), e);
        }
    }


    /**
     * Called when the user clicks a marker.
     */
    @Override
    public boolean onMarkerClick(final Marker marker) {

        Integer clickCount = (Integer) marker.getTag();

        for (int i = 0; i < markerList.size(); i++) {
            markerList.get(i).setIcon(getBitMapDescriptor(R.drawable.ic_location_red));
        }
        marker.setIcon(getBitMapDescriptor(R.drawable.ic_location_red_big));

        if (clickCount != null) {
            if (dealerInfoAdapter.getDealerInfo() != null) {
                try {
                    DealerInfo markDealerInfo = currentDealerInfoList.get(clickCount);
                    if (markDealerInfo != null) {
                        List<DealerInfo> list = new ArrayList<>();
                        list.add(markDealerInfo);
                        dealerInfoAdapter.setDealerInfo(list);
                    }

                } catch (Exception e) {
                    LogUtils.e(TAG, "onMarkerClick is error " + e.getMessage());
                }

            }


        }
        return true;
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        super.onPointerCaptureChanged(hasCapture);
    }


    public BitmapDescriptor getBitMapDescriptor(int id) {
        Drawable vectorDrawable = ResourcesCompat.getDrawable(this.getResources(), id, null);
        if (vectorDrawable == null) {
            Log.e("BitmapHelper", "Resource not found");
            return BitmapDescriptorFactory.defaultMarker();
        }
        Bitmap bitmap = Bitmap.createBitmap(vectorDrawable.getIntrinsicWidth(), vectorDrawable.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        vectorDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        vectorDrawable.draw(canvas);
        return BitmapDescriptorFactory.fromBitmap(bitmap);
    }

    @Override
    public void onWebClick(String url) {
        try {
            Intent intent = new Intent();
            intent.setAction("android.intent.action.VIEW");
            Uri content_url = Uri.parse(url);
            intent.setData(content_url);
            startActivity(intent);
        } catch (Exception e) {
            LogUtils.i("DeviceLocationActivity------" + e.getMessage());
        }
    }

    @Override
    public void onDirectionClick(String lat, String lng) {

        if (isAvilible(this, "com.google.android.apps.maps")) {
            Uri gmmIntentUri = Uri.parse("google.navigation:q=" + Double.valueOf(lat) + "," + Double.valueOf(lng) + "");
            Intent mapIntent = new Intent(Intent.ACTION_VIEW, gmmIntentUri);
            mapIntent.setPackage("com.google.android.apps.maps");
            this.startActivity(mapIntent);
        } else {

            Uri uri = Uri.parse("market://details?id=com.google.android.apps.maps");
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            this.startActivity(intent);
        }


    }


    @Override
    public void onTabClick(String category) {
        if (category == null) {
            return;
        }
        String category_all = "All";
        String lowes = "Lowe's";
        String aceHardware = "Ace Hardware";
        String serviceLocation = "Service Location";
        String dealer = "Dealer";
        DictNode[] dictNodes = categoryDictItems[0].getNodes();
        if (dictNodes.length > 0 && dictNodes[0] != null && dictNodes[0].getLabel() != null && category.contains(lowes)) {
            sendBaseTraceClick(LOWES_BUTTON_CLICK);
        } else if (dictNodes.length > 1 && dictNodes[1] != null && dictNodes[1].getLabel() != null && category.contains(aceHardware)) {
            sendBaseTraceClick(ACE_HARDWARE_BUTTON_CLICK);
        } else if (dictNodes.length > 2 && dictNodes[2] != null && dictNodes[2].getLabel() != null && category.contains(serviceLocation)) {
            sendBaseTraceClick(SERVICE_LOCATION_BUTTON_CLICK);
        } else if (dictNodes.length > 3 && dictNodes[3] != null && dictNodes[3].getLabel() != null && category.contains(dealer)) {
            sendBaseTraceClick(DEALER_BUTTON_CLICK);
        } else if (category.contains(category_all)) {
            sendBaseTraceClick(ALL_BUTTON_CLICK);
            category = "";
        }

        mViewModel.setSelectedCategory(TextUtils.isEmpty(category) ? category_all : category);
        ProgressHelper.showProgressView(this, 0, true);
        dealerInfoAdapter.clearDealer();
        mViewModel.getDealerInfo(true);
    }


    /*
     * 检查手机上是否安装了指定的软件
     * @param context
     * @param packageName：应用包名
     * @return
     */
    public static boolean isAvilible(Context context, String packageName) {
        // 获取packagemanager
        final PackageManager packageManager = context.getPackageManager();
        // 获取所有已安装程序的包信息
        List<PackageInfo> packageInfos = packageManager.getInstalledPackages(0);
        // 用于存储所有已安装程序的包名
        List<String> packageNames = new ArrayList<String>();
        // 从pinfo中将包名字逐一取出，压入pName list中
        if (packageInfos != null) {
            for (int i = 0; i < packageInfos.size(); i++) {
                String packName = packageInfos.get(i).packageName;
                packageNames.add(packName);
            }
        }
        // 判断packageNames中是否有目标程序的包名，有TRUE，没有FALSE
        return packageNames.contains(packageName);
    }

    @Override
    public void onCameraMoveCanceled() {

    }


    public void goToPermissionPage() {
        PermissionUtils.launchAppDetailsSettings();
    }


    public boolean isLocationEnabled() {
        int locationMode = 0;
        String locationProviders;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            try {
                locationMode = Settings.Secure.getInt(getContentResolver(), Settings.Secure.LOCATION_MODE);
            } catch (Settings.SettingNotFoundException e) {
                e.printStackTrace();
                return false;
            }
            return locationMode != Settings.Secure.LOCATION_MODE_OFF;
        } else {
            locationProviders = Settings.Secure.getString(getContentResolver(), Settings.Secure.LOCATION_PROVIDERS_ALLOWED);
            return !TextUtils.isEmpty(locationProviders);
        }
    }


    private void initTrace() {
        stayEleId = "7";
        pageId = "102";
        mouduleId = "11";
        nextButtoneleid = "2";
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        sendBackTrace();
    }

    @Override
    protected Class<? extends DealerLocationViewModel> getViewModelClass() {
        return DealerLocationViewModel.class;
    }

    @SuppressLint("RestrictedApi")
    private void goBack() {
        sendBackTrace();
        finish();
    }
}
