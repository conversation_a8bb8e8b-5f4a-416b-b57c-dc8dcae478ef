package com.chervon.moudleUserCenter.ui;

import androidx.appcompat.widget.SwitchCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Autowired;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NotificationUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.result.BaseResult;
import com.chervon.libNetwork.http.model.result.ErrorBean;
import com.chervon.libNetwork.http.model.result.GetMessageSwitchResult;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityMessageSettingBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterMessageSettingViewModel;
import com.suke.widget.SwitchButton;

import java.lang.ref.WeakReference;
import java.util.HashMap;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: NotifiManagerActivity
 * @Description: message setting activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_MESSAGE_SETTING)
public class MessageSettingActivity extends BaseActivity<UserCenterMessageSettingViewModel> {

  private static final String MARKETING_MESSAGE_BUTTON_CLICK = "2";
  private UsercenterActivityMessageSettingBinding binding;
  private WeakReference<LoadingDialog> loadingDialogWeakReference ;
    @Override
    protected void onUnRegister() {
    }

    @Override
    protected void onRegister() {
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.usercenter_activity_message_setting;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (UsercenterActivityMessageSettingBinding) viewDataBinding;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
      initTrace();
      initSwitch();
        mViewModel.toolbarData.setValue(new ToolbarData(
                LanguageStrings.app_messagesetting_title_textview_text(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        finish();  sendBackTrace();
                    }
                }));

        binding.setUsercenterMessageSettingViewModel(mViewModel);
    }

    @Override
    protected void onResume() {
        super.onResume();
      loadingDialogWeakReference=new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));

        ApiService.instance().getMessageSwitch().subscribe(new HttpObserver<GetMessageSwitchResult>() {
            @Override
            protected void Next(GetMessageSwitchResult entity) {
              if(loadingDialogWeakReference!=null&&loadingDialogWeakReference.get()!=null){
                loadingDialogWeakReference.get().dismiss();
              }
                //更新数据
                User user = UserInfo.get();
                user.setSystemMessageSwitch(entity.getEntry().getSystemMessageSwitch() == 1);
                user.setDeviceMessageSwitch(entity.getEntry().getDeviceMessageSwitch() == 1);
                user.setMarketingMessageSwitch(entity.getEntry().getMarketingMessageSwitch() == 1);
                UserInfo.set(user);
                initSwitch();
            }

            @Override
            public void Error(ErrorBean errorBean) {
                super.Error(errorBean);
              if(loadingDialogWeakReference!=null&&loadingDialogWeakReference.get()!=null){
                loadingDialogWeakReference.get().dismiss();
              }
                initSwitch();
            }
        });
    }

    /**
     * @method initSwitch
     * @description 初始化按钮状态
     * @date: 2022/8/31 14:33
     * @author: langmeng
     * @return void
     */
    private void initSwitch() {

        if (NotificationUtils.areNotificationsEnabled()) {

            //消息通知打开
          binding.usercenterActivityMessageSettingSystemSwitchbt.setEnabled(true);
          binding.usercenterActivityMessageSettingDeviceSwitchbt.setEnabled(true);
          binding.usercenterActivityMessageSettingMarketingSwitchbt.setEnabled(true);

          binding.switchSmartsystem.setChecked(UserInfo.get().isSystemMessageSwitch());
          binding.switchSmartdevice.setChecked(UserInfo.get().isDeviceMessageSwitch());
          binding.switchSmartmarket.setChecked(UserInfo.get().isMarketingMessageSwitch());


      } else {
            //消息通知关闭
          binding.usercenterActivityMessageSettingSystemSwitchbt.setEnabled(false);
          binding.usercenterActivityMessageSettingDeviceSwitchbt.setEnabled(false);
          binding.usercenterActivityMessageSettingMarketingSwitchbt.setEnabled(false);
        }

        //监听系统消息渠道按钮状态
        binding.switchSmartsystem.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                  SwitchCompat switchButton = (SwitchCompat) view;
                    changeSwitch(switchButton,0);
                }
                return true;
            }
        });
       // 监听设备消息渠道按钮状态
        binding.switchSmartdevice.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                  SwitchCompat switchButton = (SwitchCompat) view;
                    changeSwitch(switchButton,1);
                }
                return true;
            }
        });

      //监听营销消息渠道按钮状态
      binding.switchSmartmarket.setOnTouchListener(new View.OnTouchListener() {
        @Override
        public boolean onTouch(View view, MotionEvent motionEvent) {

          if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
            SwitchCompat switchButton = (SwitchCompat) view;
            changeSwitch(switchButton,2);
            sendBaseTraceClick(MARKETING_MESSAGE_BUTTON_CLICK);
          }
          return true;
        }
      });

    }

    /**
     * @method changeSwitch
     * @description desc
     * @date: 2022/9/5 15:44
     * @author: langmeng
     * @param switchButton 开关按钮
     * @param type 0 系统消息开关 1 设备消息开关 2营销消息开关
     * @return void
     */
  private void changeSwitch(SwitchCompat switchButton, int type) {
    LoadingDialog loadingDialog = LoadingDialog.showDialog(getSupportFragmentManager());
    //1开启 0不开启
    int state = 0;
    if (switchButton.isChecked()) {
      state = 0;
    } else {
      state = 1;
    }
    String key = "";
    switch (type) {
      case 0:
        key = "systemMessageSwitch";
        break;
      case 1:
        key = "deviceMessageSwitch";
        break;
      default:
        key = "marketingMessageSwitch";
        break;
    }
    HashMap<String, Integer> requestMap = new HashMap<>();
    requestMap.put(key, state);
    int finalState = state;
    ApiService.instance().setMessageSwitch(requestMap).subscribe(new HttpObserver<BaseResult>() {
      @Override
      protected void Next(BaseResult entity) {
        User user = UserInfo.get();
        switch (type) {
          case 0:
            user.setSystemMessageSwitch(finalState == 1);
            break;
          case 1:
            user.setDeviceMessageSwitch(finalState == 1);
            break;
          default:
            user.setMarketingMessageSwitch(finalState == 1);
            break;
        }
        UserInfo.set(user);
        switchButton.setChecked(finalState == 1);
        loadingDialog.dismiss();
      }

      @Override
      public void Error(ErrorBean errorBean) {
        super.Error(errorBean);
        loadingDialog.dismiss();
      }
    });
  }
    @Override
    protected Class<? extends UserCenterMessageSettingViewModel> getViewModelClass() {
        return UserCenterMessageSettingViewModel.class;
    }


  private void initTrace() {
    stayEleId = "3";
    pageId = "91";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  @Override
  protected void onPostResume() {
    super.onPostResume();
    binding.switchSmartsystem.setChecked(UserInfo.get().isSystemMessageSwitch());
    binding.switchSmartdevice.setChecked(UserInfo.get().isDeviceMessageSwitch());
    binding.switchSmartmarket.setChecked(UserInfo.get().isMarketingMessageSwitch());
  }
}
