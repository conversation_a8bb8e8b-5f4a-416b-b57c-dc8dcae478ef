package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.bumptech.glide.Glide;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.adapter.GridSpaceItemDecoration;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.libBase.model.FeedBackMoreEntry;
import com.chervon.moudleUserCenter.data.FeedBackUIState;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityFeedbackBinding;
import com.chervon.moudleUserCenter.ui.adapter.FeedbackDeviceAdapter;
import com.chervon.moudleUserCenter.ui.adapter.FeedbackMoreAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.FeedBackViewModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.ui
 * @ClassName: MessageSettingActivity
 * @Description: 消息设置页面
 * @Author: langmeng
 * @CreateDate: 2022/8/29 15:49
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/8/29 15:49
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_FEEDBACK)
public class FeedBackActivity extends BaseActivity<FeedBackViewModel> implements ItemClick {
  private UsercenterActivityFeedbackBinding mViewDataBinding;

  private FeedbackDeviceAdapter mFeedbackDeviceAdapter;
  private FeedbackMoreAdapter mFeedbackMoreAdapter;
  private List<DeviceInfo> allDeviceInfoList;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_feedback;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (UsercenterActivityFeedbackBinding) viewDataBinding;


    mViewDataBinding.llFooter.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View v) {
        Object tag = v.getTag();
        if (tag == null) {
          mViewDataBinding.tvMore.setVisibility(View.GONE);
          Glide.with(FeedBackActivity.this).load(R.drawable.ic_up_arrow).into(mViewDataBinding.ivArrow);
          mFeedbackDeviceAdapter.setData(allDeviceInfoList);
          v.setTag(true);
        } else {
          mViewDataBinding.tvMore.setVisibility(View.VISIBLE);
          Glide.with(FeedBackActivity.this).load(R.drawable.ic_down_arrow).into(mViewDataBinding.ivArrow);
          mFeedbackDeviceAdapter.setData(allDeviceInfoList.subList(0, 9));
          v.setTag(null);
        }
        mFeedbackDeviceAdapter.notifyDataSetChanged();
      }
    });
    mViewDataBinding.deviceFeedBackRv.setLayoutManager(new GridLayoutManager(this, 3, GridLayoutManager.VERTICAL, false));
    //mViewDataBinding.rvFoundDevices.addItemDecoration(new DividerItemDecoration(this.getActivity(), GridLayoutManager.VERTICAL));
    mViewDataBinding.deviceFeedBackRv.addItemDecoration(new GridSpaceItemDecoration(3,
      22,
      22));
    List<DeviceInfo> list = new ArrayList<>();
    mFeedbackDeviceAdapter = new FeedbackDeviceAdapter(this, this, list);
    mFeedbackDeviceAdapter.setHasStableIds(true);
    mViewDataBinding.deviceFeedBackRv.setAdapter(mFeedbackDeviceAdapter);

    List<FeedBackMoreEntry> feedBackMoreEntries = new ArrayList<>();
    feedBackMoreEntries.add(new FeedBackMoreEntry(LanguageStrings.getFeedbackAppissues(), "more", "app"));
    LinearLayoutManager linearLayoutManager = new LinearLayoutManager(this);
    mViewDataBinding.rvFeedBackMore.setLayoutManager(linearLayoutManager);
    mFeedbackMoreAdapter=new FeedbackMoreAdapter(this,this,feedBackMoreEntries);
    mViewDataBinding.rvFeedBackMore.setAdapter(mFeedbackMoreAdapter);
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    mViewDataBinding.setViewModel(mViewModel);
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_usercenter_feedback_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));


    View toolbar=  findViewById(R.id.messagecenter_activity_message_center_toolbar);
    toolbar.findViewById(R.id.toolbarDividie).setVisibility(View.GONE);
    toolbar.setBackgroundResource(R.color.colorNewBackground);

    mViewModel. mHistoryBtClick.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if(aBoolean){
          sendBottonClick();
        }
      }
    });

    mViewModel.mLiveData.observe(this, new Observer<FeedBackUIState>() {
      @Override
      public void onChanged(FeedBackUIState feedBackUIState) {
        if(feedBackUIState.getDeviceInfos()!=null&&feedBackUIState.getDeviceInfos().size()>0){
          mViewDataBinding.llDefault.setVisibility(View.GONE);
          allDeviceInfoList=feedBackUIState.getDeviceInfos();

          if(feedBackUIState.getDeviceInfos().size()>9){
            mViewDataBinding.llFooter.setVisibility(View.VISIBLE);
            mFeedbackDeviceAdapter.setData(allDeviceInfoList.subList(0,9));
          }else{
            mViewDataBinding.llFooter.setVisibility(View.GONE);
            mFeedbackDeviceAdapter.setData(feedBackUIState.getDeviceInfos());
          }
          mFeedbackDeviceAdapter.notifyDataSetChanged();

        }else{
          mViewDataBinding.llDefault.setVisibility(View.VISIBLE);
          mViewDataBinding.llFooter.setVisibility(View.GONE);
        }
      }
    });
    mViewModel.getDeviceList();
    mViewModel.getMoreQuestion();

    mViewModel.isShowRedHat.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if(aBoolean){
          mViewDataBinding.ivRedDot.setVisibility(View.VISIBLE);
        }else{
          mViewDataBinding.ivRedDot.setVisibility(View.GONE);
        }

      }
    });


  }

  @Override
  protected void onResume() {
    super.onResume();
    mViewModel.hasNewFeedBackMessage();

  }

  @Override
  protected Class<? extends FeedBackViewModel> getViewModelClass() {
    return FeedBackViewModel.class;
  }



  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }



  private void initTrace() {
    stayEleId="3";
    pageId="162";
    mouduleId="11";
    nextButtoneleid ="2";
  }

  public void sendBottonClick() {
    sendClickTrace(this,mouduleId,pageId,pageResouce, nextButtoneleid,"1");
  }

  @Override
  public void onItemClick(Object uiState) {
   ARouter.getInstance().build(RouterConstants.ACTIVITY_FEEDBACK_QUESTION).withSerializable(KEY_PREV_DATA, (Serializable) uiState).navigation();
  }

  @Override
  public void onItemClick(Object uiState, DeviceInfo deviceInfo) {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_FEEDBACK_QUESTION).navigation();
  }
  @Override
  public void onWindowFocusChanged(boolean hasFocus) {
    super.onWindowFocusChanged(hasFocus);
    if (hasFocus) {
      UiHelper.setSystemUIColor(getWindow());
    }
  }


}
