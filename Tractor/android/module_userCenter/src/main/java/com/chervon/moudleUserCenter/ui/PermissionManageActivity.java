package com.chervon.moudleUserCenter.ui;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.PermissionUtils;
import com.chervon.libBase.model.PermissionInfo;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityPermissionManageBinding;
import com.chervon.moudleUserCenter.databinding.UsercenterActivitySettingBinding;
import com.chervon.moudleUserCenter.ui.adapter.PermissionAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterSettingViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: PermissionManageActivity
 * @Description: PermissionManage
 * @Author: wangheng
 * @CreateDate: 2022/8/25 17:25
 * @UpdateUser:
 * @UpdateDate: 2022/8/25 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_PERMISSION_MANAGE)
public class PermissionManageActivity extends BaseActivity<UserCenterSettingViewModel> implements ItemClick {
  private static final String GEOGRAPHIC_POSITION_BUTTON_CLICK = "2";
  private static final String BLUETOOTH_PERMISSION_BUTTON_CLICK = "3";
  private static final String CAMERA_PERMISSIONS_BUTTON_CLICK = "4";
  private static final String PHOTO_PERMISSIONS_BUTTON_CLICK = "5";
  private UsercenterActivityPermissionManageBinding binding;
    private PermissionAdapter mAdapter;
    private PermissionInfo permissionLocation;
    private PermissionInfo permissionBluetooth;
    private PermissionInfo permissionCamera;
    private PermissionInfo permissionPhoto;
    private List<PermissionInfo> list;







  @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.usercenter_activity_permission_manage;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        binding = (UsercenterActivityPermissionManageBinding) viewDataBinding;


    }

    @Override
    protected void initData(Bundle savedInstanceState) {
      initTrace();
      mViewModel.toolbarData.setValue(new ToolbarData(
               LanguageStrings.app_setting_systempermissionimanagement_textview_text(),
                new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                      sendBackTrace();
                        finish();
                    }
                }));

        binding.setUserCenterSettingModel(mViewModel);

        LinearLayoutManager gridLayoutManager = new LinearLayoutManager(this,RecyclerView.VERTICAL, false);
        binding.rvDeviceList.setLayoutManager(gridLayoutManager);


        list = new ArrayList<PermissionInfo>();
         permissionLocation=   new PermissionInfo(LanguageStrings.getLocationTitle(),LanguageStrings.getLocationContent(),LanguageStrings.getSystemPermissionGrant());
          permissionBluetooth=   new PermissionInfo(LanguageStrings.getBluetoothtitle(),LanguageStrings.getBluetoothContent(),LanguageStrings.getSystemPermissionGrant());
          permissionCamera=   new PermissionInfo(LanguageStrings.getCameraTitle(),LanguageStrings.getCameraContent(),LanguageStrings.getSystemPermissionGrant());
          permissionPhoto=   new PermissionInfo(LanguageStrings.getphotoTitle(),LanguageStrings.getphotoContent(),LanguageStrings.getSystemPermissionGrant());
        list.add(permissionLocation);
        list.add(permissionBluetooth);
        list.add(permissionCamera);
        list.add(permissionPhoto);
        mAdapter = new PermissionAdapter(this, this, list);
        mAdapter.setHasStableIds(true);
    //    binding.rvDeviceList.addItemDecoration(new DividerItemDecoration(this, LinearLayoutManager.VERTICAL));
        binding.rvDeviceList.setAdapter(mAdapter);

    }

    @Override
    protected Class<? extends UserCenterSettingViewModel> getViewModelClass() {
        return UserCenterSettingViewModel.class;
    }

    @Override
    public void onItemClick(Object uiState) {
        PermissionUtils.launchAppDetailsSettings();
    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {
      PermissionUtils.launchAppDetailsSettings();
      Integer position= Integer.parseInt(uiState.toString());
if(position==0){
  sendBaseTraceClick(GEOGRAPHIC_POSITION_BUTTON_CLICK);
}else if(position==1){
  sendBaseTraceClick(BLUETOOTH_PERMISSION_BUTTON_CLICK);

}else if(position==2){
  sendBaseTraceClick(CAMERA_PERMISSIONS_BUTTON_CLICK);
}else if(position==3){
  sendBaseTraceClick(PHOTO_PERMISSIONS_BUTTON_CLICK);
}
    }


    @Override
    protected void onResume() {
        super.onResume();

        checkPermission(permissionLocation, Manifest.permission.ACCESS_COARSE_LOCATION);

        if (Build.VERSION.SDK_INT >= 31) {
            checkPermission(permissionBluetooth,Manifest.permission.BLUETOOTH_SCAN);
        }else{
            checkPermission(permissionBluetooth,Manifest.permission.ACCESS_COARSE_LOCATION);
        }


        checkPermission(permissionCamera,Manifest.permission.CAMERA);
        checkPermission(permissionPhoto,Manifest.permission.READ_EXTERNAL_STORAGE);
        mAdapter.notifyDataSetChanged();
    }

    private void checkPermission(PermissionInfo permissionInfo,String s) {
        PackageManager pm = getPackageManager();
        boolean permission = (PackageManager.PERMISSION_GRANTED ==
                pm.checkPermission(s
                        , this.getPackageName()));
        if (permission) {
            permissionInfo.setPermission(LanguageStrings.getSystemPermissionAllowed() );
        } else {
            permissionInfo.setPermission(LanguageStrings.getSystemPermissionGrant());
        }
    }

  private void initTrace() {
    stayEleId = "6";
    pageId = "96";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }
}
