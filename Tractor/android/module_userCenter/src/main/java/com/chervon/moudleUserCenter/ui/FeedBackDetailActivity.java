package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.model.AppConstants.PERMISSION_ALBUM_UNAUTHORIZED;
import static com.chervon.libBase.model.AppConstants.PERMISSION_CAMERA_UNAUTHORIZED;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.CameraUtil.CODE_GALLERY;
import static com.chervon.libBase.utils.CameraUtil.CODE_TAKE_PHOTO;
import static com.chervon.libBase.utils.CameraUtil.TYPE_TAKE_PHOTO;
import static com.chervon.libBase.utils.CameraUtil.getMediaFileUri;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_CREATE_TIME;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_MESSAGETYPE;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_UUID;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;
import static com.chervon.moudleDeviceManage.ui.HomeActivity.getRealPathFromUri;
import static com.chervon.moudleUserCenter.data.repository.FeedBackRepo.EDIT_REPLY_FAIL;
import static com.chervon.moudleUserCenter.data.repository.FeedBackRepo.EDIT_REPLY_SUCCESS;
import static com.chervon.moudleUserCenter.data.repository.FeedBackRepo.FEED_BACK_MESSAGE_EXCEPTION;
import static com.chervon.moudleUserCenter.data.repository.FeedBackRepo.REPLY_FAIL;
import static com.chervon.moudleUserCenter.data.repository.FeedBackRepo.REPLY_SUCCESS;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.FeedBackReplyRequest;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.PhotoSelectInterface;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.data.CloseFeedBackUIState;
import com.chervon.moudleUserCenter.data.FeedBackDetailData;
import com.chervon.moudleUserCenter.data.FeedBackDetailUIState;
import com.chervon.moudleUserCenter.data.ReplyEntry;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityFeedbackDetailBinding;
import com.chervon.moudleUserCenter.ui.adapter.FeedBackCallBack;
import com.chervon.moudleUserCenter.ui.adapter.FeedBackDetailAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.FeedBackViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.config.SelectModeConfig;
import com.luck.picture.lib.engine.UriToFileTransformEngine;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.SandboxTransformUtils;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleMessageCenter.ui
 * @ClassName: MessageSettingActivity
 * @Description: 消息设置页面
 * @Author: langmeng
 * @CreateDate: 2022/8/29 15:49
 * @UpdateUser: hyman
 * @UpdateDate: 2024/9/3
 * @UpdateRemark: 增加回复弹出框内容保留
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_FEEDBACK_DETAIL)
public class FeedBackDetailActivity extends BaseActivity<FeedBackViewModel> implements FeedBackCallBack, PhotoSelectInterface {
    private UsercenterActivityFeedbackDetailBinding mViewDataBinding;
    private ResultListenner resultListenner;
    private FeedBackDetailAdapter feedBackDetailAdapter;
    private List<String> url = new ArrayList<String>();
    BottomSheetDialog mBottomSheetDialog;
    private String feedBackId;
    private String uuid;
    private String createTime;
    private String messageType;
    private FeedBackReplyRequest feedBackReplyRequest;
    private String USER_TAG = "user";
    private String QUESTION_TAG = "question_tag";
    private static final int FEEDBACK_DIALOG_TYPE_REPLY = 0;
    private static final int FEEDBACK_DIALOG_TYPE_EDIT = 1;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.usercenter_activity_feedback_detail;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding = (UsercenterActivityFeedbackDetailBinding) viewDataBinding;
        mViewModel.showNoContent.postValue(true);
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        initTrace();
        feedBackId = getIntent().getExtras().getString(KEY_PREV_DATA);
        uuid = getIntent().getExtras().getString(KEY_PREV_UUID);
        createTime = getIntent().getExtras().getString(KEY_PREV_CREATE_TIME);
        messageType = getIntent().getExtras().getString(KEY_PREV_MESSAGETYPE);

        mViewModel.toolbarData.setValue(new ToolbarData(LanguageStrings.getFeedbackdetailsTitleString(), new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                sendBackTrace();
                finish();
            }
        }));

        mViewDataBinding.setViewModel(mViewModel);

        mViewModel.detailUIStateMutableLiveData.observe(this, new Observer<FeedBackDetailUIState>() {
            @Override
            public void onChanged(FeedBackDetailUIState feedBackDetailUIState) {
                ProgressHelper.hideProgressView(FeedBackDetailActivity.this);
                List<ReplyEntry> replyEntryList = new ArrayList<>();
                if (feedBackDetailUIState.getState() == RESPONSE_SUCCESS) {
                    FeedBackDetailData feedBackDetailEntry = feedBackDetailUIState.feedBackDetailData;
                    if (feedBackDetailEntry != null) {
                        String[] pictures = feedBackDetailEntry.getFeedbackPictures();
                       String mCategory2 = feedBackDetailUIState.feedBackDetailData.getCategory2();

                        ReplyEntry feedBackQuestion = new ReplyEntry();
                        feedBackQuestion.setReplyContent(feedBackDetailEntry.getFeedbackContent());
                        feedBackQuestion.setReplyId(feedBackDetailEntry.getFeedbackId());
                        feedBackQuestion.setReplyPictures(pictures);
                        feedBackQuestion.setReplyTime(feedBackDetailEntry.getFeedbackTime());
                        feedBackQuestion.setOriginFeedBack(true);
                        feedBackQuestion.setReplyType(USER_TAG);

                        String replayState = feedBackDetailUIState.feedBackDetailData.getReplyState();
                        replyEntryList.add(feedBackQuestion);
                        if (!feedBackDetailEntry.getReplies().isEmpty()){
                            replyEntryList.addAll(feedBackDetailEntry.getReplies());
                        }

                        mViewModel.showNoContent.postValue(false);
                        feedBackDetailAdapter.setData(replyEntryList,mCategory2,replayState);
                        mViewDataBinding.feedBackRecyclerView.post(new Runnable() {
                            @Override
                            public void run() {
                                mViewDataBinding.feedBackRecyclerView.scrollToPosition(feedBackDetailAdapter.getItemCount() - 1);
                            }
                        });
                    }
                } else if (feedBackDetailUIState.getState() == REPLY_SUCCESS) {
                    DialogUtil.showSuccessDialog(FeedBackDetailActivity.this, LanguageStrings.app_setting_success_text());
                    mViewModel.getFeedBackDetail(feedBackId);
                    feedBackReplyRequest = null;
                }else if (feedBackDetailUIState.getState() == REPLY_FAIL){
                    if (!TextUtils.isEmpty(feedBackDetailUIState.getMessage())) {
                        DialogUtil.showFaceBackWarningDialog(FeedBackDetailActivity.this,feedBackDetailUIState.getMessage());
                    }
                } else if (feedBackDetailUIState.getState() == EDIT_REPLY_SUCCESS){
                    mViewModel.getFeedBackDetail(feedBackId);
                    feedBackReplyRequest = null;
                }else if (feedBackDetailUIState.getState() == EDIT_REPLY_FAIL){
                    if (!TextUtils.isEmpty(feedBackDetailUIState.getMessage())) {
                        DialogUtil.showFaceBackWarningDialog(FeedBackDetailActivity.this,feedBackDetailUIState.getMessage());
                    }
                }else if (feedBackDetailUIState.getState() == RESPONSE_FAIL) {
                    if (!TextUtils.isEmpty(feedBackDetailUIState.getMessage())) {
                        if (FEED_BACK_MESSAGE_EXCEPTION.equals(feedBackDetailUIState.getMessage())) {
                            mViewModel.showNoContent.postValue(false);
                        } else {
                            ToastUtils.showLong(feedBackDetailUIState.getMessage());
                        }
                    }
                }

            }
        });

        mViewModel.closeThreadLiveData.observe(this, new Observer<CloseFeedBackUIState>() {
            @Override
            public void onChanged(CloseFeedBackUIState uiState) {
                ProgressHelper.hideProgressView(FeedBackDetailActivity.this);
                if (uiState.getState() == RESPONSE_SUCCESS){
                    ProgressHelper.showProgressView(FeedBackDetailActivity.this, 0);
                    mViewModel.getFeedBackDetail(feedBackId);
                }else if (uiState.getState()==RESPONSE_FAIL){
                    if (!TextUtils.isEmpty(uiState.getMessage())){
                        ToastUtils.showShort(uiState.getMessage());
                    }
                }
            }
        });

        ProgressHelper.showProgressView(FeedBackDetailActivity.this, 0);
        mViewModel.getFeedBackDetail(feedBackId);
        if (!TextUtils.isEmpty(uuid) && !TextUtils.isEmpty(messageType) && !TextUtils.isEmpty(createTime)) {
            mViewModel.getMessageDetail(messageType, createTime, uuid);
        }

        feedBackDetailAdapter = new FeedBackDetailAdapter(this,this);
        mViewDataBinding.feedBackRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        mViewDataBinding.feedBackRecyclerView.setAdapter(feedBackDetailAdapter);
    }

    @Override
    public void feedBackReplyClick(ReplyEntry replyEntry) {
        if (null == feedBackReplyRequest) {
            feedBackReplyRequest = new FeedBackReplyRequest();
            feedBackReplyRequest.setReplyId(replyEntry.getReplyId());
        }
        FeedBackDetailUIState feedBackDetailUIState = mViewModel.detailUIStateMutableLiveData.getValue();
        FeedBackDetailData feedBackDetailData = feedBackDetailUIState.getFeedBackDetailData();
        if (feedBackDetailData != null) {
            feedBackReplyRequest.setFeedbackId(feedBackDetailData.getFeedbackId());
        } else {
            feedBackReplyRequest.setFeedbackId("");
        }
        goToReply(FEEDBACK_DIALOG_TYPE_REPLY);
    }

    @Override
    public void editFeedBack(ReplyEntry replyEntry) {
        if (null == feedBackReplyRequest) {
            feedBackReplyRequest = new FeedBackReplyRequest();
            feedBackReplyRequest.setReplyId(replyEntry.getReplyId());
            feedBackReplyRequest.setReplyPictures(replyEntry.getReplyPictures());
            feedBackReplyRequest.setReplyContent(replyEntry.getReplyContent());
            feedBackReplyRequest.setOriginFeedBack(replyEntry.isOriginFeedBack());
        }
        FeedBackDetailUIState feedBackDetailUIState = mViewModel.detailUIStateMutableLiveData.getValue();
        FeedBackDetailData feedBackDetailData = feedBackDetailUIState.getFeedBackDetailData();
        if (feedBackDetailData != null) {
            feedBackReplyRequest.setFeedbackId(feedBackDetailData.getFeedbackId());
        } else {
            feedBackReplyRequest.setFeedbackId("");
        }
        sendEditFeedBackClick();
        goToReply(FEEDBACK_DIALOG_TYPE_EDIT);
    }

    /**
     * 反馈图片预览
     * @param url
     */
    @Override
    public void previewPicClick(String url) {
        DialogUtil.showPhotoDialog(this, url, null);
    }

    @Override
    public void closeTheThread(ReplyEntry replyEntry) {
        sendCloseThreadClick();
        mViewModel.closeFeedback(feedBackId);
    }

    @Override
    protected Class<? extends FeedBackViewModel> getViewModelClass() {
        return FeedBackViewModel.class;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        sendBackTrace();
    }


    /**
     * @param type--0:feedback回复，1：编辑反馈
     */
    public void goToReply(int type) {
        mBottomSheetDialog = new DialogUtil().showBottomReplyDialog(this, new DialogUtil.DialogDismissListener() {
            @Override
            public void dialogResult(FeedBackReplyRequest request) {
                feedBackReplyRequest = request;
            }
        }, this, feedBackReplyRequest,type);

        sendButtonReplyClick();
    }


    @Override
    public void goToTakePhotoPage() {
        final String[] permissionsGroup = new String[]{Manifest.permission.CAMERA};


        if (Build.VERSION.SDK_INT >= 24) {

            RxPermissions rxPermissions = new RxPermissions(this);
            rxPermissions.setLogging(true);
            rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                    if (permission.granted) {
                        //处理允许权限后的操作
                        selectPicFromCamera();
                        return;
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                        //处理用户点击禁止后的操作
                        showPermissionError(permission.name);
                    } else {
                        showPermissionError(permission.name);
                    }

                }
            });


        } else {
            Intent takeIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            Uri photoUri = getMediaFileUri(TYPE_TAKE_PHOTO);
            takeIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
            startActivityForResult(takeIntent, CODE_TAKE_PHOTO);
        }
    }

    protected void selectPicFromCamera() {


        if (isFinishing()) {
            return;
        }
        try {
            PictureSelector.create(this).openCamera(SelectMimeType.ofImage()).setSandboxFileEngine(new UriToFileTransformEngine() {
                @Override
                public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                    //Android 10 沙盒资源访问
                    if (call != null) {
                        String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                        call.onCallback(srcPath, sandboxPath);
                    }
                }
            }).setSandboxFileEngine(new UriToFileTransformEngine() {
                @Override
                public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                    if (call != null) {
                        String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                        call.onCallback(srcPath, sandboxPath);
                    }
                }
            }).forResultActivity(new OnResultCallbackListener<LocalMedia>() {
                @Override
                public void onResult(ArrayList<LocalMedia> result) {
                    String imgPath = result.get(0).getSandboxPath();
                    if (url.size() > 3) {
                        url.add(3, imgPath);
                    } else {
                        url.add(0, imgPath);
                    }

                    if (null != resultListenner) {
                        resultListenner.onActivityResult(RESULT_OK, RESULT_OK, null, imgPath);
                    }


                    mViewModel.setUrls(url);
                }

                @Override
                public void onCancel() {
                    //取消
                }
            });
        } catch (Exception e) {

        }


    }

    @Override
    public void openGalley() {

        if (Build.VERSION.SDK_INT >= 33) {

            String[] permissions = new String[]{Manifest.permission.READ_MEDIA_IMAGES};

            RxPermissions rxPermissions = new RxPermissions(this);
            rxPermissions.requestEach(permissions).subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                    if (permission.granted) {
                        getToOpenGalleyWith13();
                    } else {
                        showPermissionError(permission.name);
                    }
                }
            });
        } else {
            getToOpenGalley();
        }

    }

    private void getToOpenGalleyWith13() {
        if (isFinishing()) {
            return;
        }

        PictureSelector.create(this).openSystemGallery(SelectMimeType.ofImage()).setSelectionMode(SelectModeConfig.SINGLE).setSkipCropMimeType().setSandboxFileEngine(new UriToFileTransformEngine() {
            @Override
            public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                //Android 10 沙盒资源访问
                if (call != null) {
                    String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                    call.onCallback(srcPath, sandboxPath);
                }
            }
        }).setSandboxFileEngine(new UriToFileTransformEngine() {
            @Override
            public void onUriToFileAsyncTransform(Context context, String srcPath, String mineType, OnKeyValueResultCallbackListener call) {
                if (call != null) {
                    String sandboxPath = SandboxTransformUtils.copyPathToSandbox(context, srcPath, mineType);
                    call.onCallback(srcPath, sandboxPath);
                }
            }
        }).forSystemResultActivity(new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(ArrayList<LocalMedia> result) {
                String imgPath = result.get(0).getSandboxPath();
                if (url.size() > 3) {
                    url.add(3, imgPath);
                } else {
                    url.add(0, imgPath);
                }

                if (null != resultListenner) {
                    resultListenner.onActivityResult(RESULT_OK, RESULT_OK, null, imgPath);
                }


                mViewModel.setUrls(url);
            }

            @Override
            public void onCancel() {

            }
        });
    }


    private void getToOpenGalley() {

        final String[] permissionsGroup = new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
        if (Build.VERSION.SDK_INT >= 23) {

            RxPermissions rxPermissions = new RxPermissions(this);
            rxPermissions.setLogging(true);
            rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
                @Override
                public void accept(Permission permission) throws Exception {
                    if (permission.granted) {
                        //处理允许权限后的操作
                        Intent i = new Intent();
                        i.setAction(Intent.ACTION_PICK);
                        i.setType("image/*");
                        startActivityForResult(i, CODE_GALLERY);
                        return;
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                        //处理用户点击禁止后的操作
                        showPermissionError(permission.name);
                    } else {
                        showPermissionError(permission.name);
                    }
                }
            });

        } else {
            Intent i = new Intent();
            i.setAction(Intent.ACTION_PICK);
            i.setType("image/*");
            startActivityForResult(i, CODE_GALLERY);
        }
    }


    private void showPermissionError(String permissionText) {

        if (!isFinishing()) {
            int errorCode = permissionText.equalsIgnoreCase(Manifest.permission.CAMERA)?PERMISSION_CAMERA_UNAUTHORIZED:PERMISSION_ALBUM_UNAUTHORIZED;

            DialogUtil.cameraAndalbumAlertDialog(this, errorCode, LanguageStrings.app_modifyavator_gosetting_textview_text(), LanguageStrings.app_setting_clearcachecancle_button_text(), new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // onConfirmed 跳转到系统设置
                    goIntentSetting();
                }
            }, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                }
            });
        }
    }

    /**
     * 跳转到三星系统设置。其他机型暂无适配计划
     */
    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        try {
            startActivity(intent);
        } catch (Exception e) {

        }
    }

    File tempFile;


    @Override
    public void setResultListenner(ResultListenner resultListenner) {
        this.resultListenner = resultListenner;

    }

    @Override
    public void replyFeedBack(FeedBackReplyRequest feedBackReplyRequest, List<String> urls,int type) {
        //如果回复内容为空那么就拦截掉此次请求，并且弹出框不隐藏
        if (null == feedBackReplyRequest) {
            return;
        }
        if (TextUtils.isEmpty(feedBackReplyRequest.getReplyContent())) {
            return;
        }

        if (mBottomSheetDialog != null && mBottomSheetDialog.isShowing()) {
            mBottomSheetDialog.hide();
        }

        ProgressHelper.showProgressView(this, 0, true);
        if (type == FEEDBACK_DIALOG_TYPE_REPLY){
            mViewModel.feedBackReply(feedBackReplyRequest, urls);
        }else if (type == FEEDBACK_DIALOG_TYPE_EDIT){
            mViewModel.editFeedBackReply(feedBackReplyRequest, urls);
        }
        sendSendPublishButtonClick();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);{
            if (resultCode == RESULT_OK) {

                String imgUrl = null;
                if (data != null && data.getData() != null) {
                    Uri uri = data.getData();
                    Log.d("onActivityResult", getRealPathFromUri(this, uri));
                    imgUrl = getRealPathFromUri(this, uri);
                    Log.d("onActivityResult", imgUrl);

                }
                if (resultListenner != null) {
                    if (tempFile != null && imgUrl == null) {
                        imgUrl = tempFile.getAbsolutePath();
                    }

                    if (url.size() > 3) {
                        url.add(3, imgUrl);
                    } else {
                        url.add(0, imgUrl);
                    }
                    mViewModel.setUrls(url);
                    resultListenner.onActivityResult(requestCode, resultCode, data, imgUrl);
                }
            }
        }
    }

    private void initTrace() {
        stayEleId = "4";
        pageId = "165";
        mouduleId = "11";
        nextButtoneleid = "2";
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mBottomSheetDialog != null) {
            mBottomSheetDialog.dismiss();
            mBottomSheetDialog = null;
        }

    }

    @Override
    public void previewPhoto(String url) {
        previewReceipt(url);
    }

    public void previewReceipt(String url) {
        DialogUtil.showPhotoDialog(this, url, null);
    }


    public void sendSendPublishButtonClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, "3", "1");
    }

    private void sendCloseThreadClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "6", "1");
    }
    private void sendEditFeedBackClick(){
        sendClickTrace(this, mouduleId, pageId, pageResouce, "5", "1");
    }
    public void sendButtonReplyClick() {
        sendClickTrace(this, mouduleId, pageId, pageResouce, nextButtoneleid, "1");
    }
}
