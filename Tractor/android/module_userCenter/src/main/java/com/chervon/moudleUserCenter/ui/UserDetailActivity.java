package com.chervon.moudleUserCenter.ui;

import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.BottomSelectListData;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.BottomSelectListDialog;
import com.chervon.libBase.ui.widget.InlineTipEditText2;
import com.chervon.libBase.ui.widget.InlineVerificationHelper2;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityUserDetailBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterUserDetailViewModel;

import java.util.ArrayList;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterFragment
 * @Description: user detail activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_USER_DETAIL)
public class UserDetailActivity extends BaseActivity<UserCenterUserDetailViewModel> {
  private static final String SAVE_BUTTON_CLICK = "2";
  private UsercenterActivityUserDetailBinding mViewDataBinding;
  private InlineVerificationHelper2 mInlineVerificationHelper;

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_user_detail;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {

    mViewDataBinding = (UsercenterActivityUserDetailBinding) viewDataBinding;

  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_userinfo_account_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          finish();
          sendBackTrace();
        }
      }));

    mViewModel.traceClickLiveData.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          sendBaseTraceClick(SAVE_BUTTON_CLICK);
        }
      }
    });
    mViewModel.setFragmentManager(getSupportFragmentManager());
    mViewDataBinding.setUserCenterUserDetailModel(mViewModel);

    mInlineVerificationHelper = new InlineVerificationHelper2(mViewDataBinding.usercenterActivityUserdetailSave);


    mViewDataBinding.ietFirstName.setmTextChangedListenner(new InlineTipEditText2.TextChangedListenner() {
      @Override
      public void textChanged(String firstName) {
        if (!mViewModel.firstName.equals(firstName)){
          mViewModel.firstName = firstName;
        }
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInlineAccount();
        }
      }
    });

    mViewDataBinding.ietLastName.setmTextChangedListenner(new InlineTipEditText2.TextChangedListenner() {
      @Override
      public void textChanged(String lastName) {
        if (!mViewModel.lastName.equals(lastName)){
          mViewModel.lastName = lastName;
        }
        if (mInlineVerificationHelper != null) {
          mInlineVerificationHelper.checkInlineAccount();
        }

      }
    });

    mInlineVerificationHelper
      .buildFirstName(mViewDataBinding.ietFirstName)
      .buildLastName(mViewDataBinding.ietLastName);

  }

  @Override
  protected Class<? extends UserCenterUserDetailViewModel> getViewModelClass() {
    return UserCenterUserDetailViewModel.class;
  }

  private void initTrace() {
    stayEleId = "3";
    pageId = "89";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

}
