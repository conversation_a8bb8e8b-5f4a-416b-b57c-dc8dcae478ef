package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.DEL_DEVIVE_SUCCESS;
import static com.chervon.moudleDeviceManage.data.repository.HomeRepo.RESPONSE_SUCCESS;
import static com.chervon.moudleUserCenter.ui.FeedBackHelper.formateFeedBackTime;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.SparseArray;
import android.util.SparseBooleanArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.ConfirmAlertDialog;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.ui.widget.refresh.MessageListPullRefreshLayout;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.data.FeedBackHistoryDataItem;
import com.chervon.moudleUserCenter.data.FeedBackHistoryUIState;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityFeedbackHistoryBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.FeedBackViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import io.reactivex.functions.Consumer;

/**
 * RecyclerView multiple choice
 *
 * <AUTHOR>
 * @version 1.1
 * @date 2017/1/21
 * @since 1.0
 */
@Route(path = RouterConstants.ACTIVITY_FEEDBACK_HISTORY)
public class FeedBackHistoryActivity extends BaseActivity<FeedBackViewModel> {
  private List<FeedBackHistoryDataItem> feedBackHistoryDataItems = new ArrayList<>();
  private MultipleChoiceAdapter adapter;
  private UsercenterActivityFeedbackHistoryBinding mViewDataBinding;

  public interface OnItemClickListener {
    void onItemClick(View view, int position, long id);
  }

  public class InternalViewHolder extends RecyclerView.ViewHolder {
    public TextView textView;
    public TextView tvItemTime;
    public CheckBox checkBox;
    public View arrowView;
    public TextView tvUnRead;
    public ImageView img_arrow_donw;

    public InternalViewHolder(View itemView) {
      super(itemView);
      textView = (TextView) itemView.findViewById(R.id.tvItemContent);
      tvItemTime = (TextView) itemView.findViewById(R.id.tvItemTime);
      checkBox = (CheckBox) itemView.findViewById(R.id.cbItem);
      arrowView = itemView.findViewById(R.id.messagecenter_messagelist_item_in);
      tvUnRead = itemView.findViewById(R.id.tvUnRead);
      img_arrow_donw = itemView.findViewById(R.id.img_arrow_donw);
    }
  }


  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_feedback_history;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding = (UsercenterActivityFeedbackHistoryBinding) viewDataBinding;
    mViewDataBinding.messagePullRefresh.setOnRefreshListener(new MessageListPullRefreshLayout.OnRefreshListener() {
      @Override
      public void onRefresh() {
        //  mViewModel.getFeedBackHistoryDatas(1,200);
        mViewDataBinding.messagePullRefresh.postDelayed(new Runnable() {
          @Override
          public void run() {
            mViewDataBinding.messagePullRefresh.setRefreshing(false);
          }
        }, 1000);
      }
    });
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    mViewDataBinding.setViewModel(mViewModel);
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.getFeedBackHistoryTitleString(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));

    adapter = new MultipleChoiceAdapter(this, feedBackHistoryDataItems);
    mViewDataBinding.recyclerView.setLayoutManager(new LinearLayoutManager(this));
    mViewDataBinding.recyclerView.setAdapter(adapter);
    mViewModel.historyUIStateMutableLiveData.observe(this, new Observer<FeedBackHistoryUIState>() {
      @Override
      public void onChanged(FeedBackHistoryUIState feedBackHistoryUIState) {
        ProgressHelper.hideProgressView(FeedBackHistoryActivity.this);
        if (feedBackHistoryUIState.getState() == RESPONSE_SUCCESS) {
          adapter.addDatas(feedBackHistoryUIState.pageData.getList());
        } else if (feedBackHistoryUIState.getState() == DEL_DEVIVE_SUCCESS) {
          mViewModel.getFeedBackHistoryDatas(1, 200);
          mViewDataBinding.ivFeedBackList.setText(LanguageStrings.getFeedbackHistoryEditString());
          mViewDataBinding.btnDel.setVisibility(View.GONE);
        } else {
          if (feedBackHistoryUIState.getState() == RESPONSE_FAIL) {
            if (!TextUtils.isEmpty(feedBackHistoryUIState.getMessage())) {
              ToastUtils.showLong(feedBackHistoryUIState.getMessage());
            }
          }
        }
        if (adapter.getItemCount() > 0) {
          mViewDataBinding.llDefault.setVisibility(View.GONE);
        } else {
          mViewDataBinding.llDefault.setVisibility(View.VISIBLE);
        }
      }
    });

    if (!isFinishing()) {
      ProgressHelper.showProgressView(this, 0, true);
    }
    // mViewModel.getFeedBackHistoryDatas(1,200);
    // Toast.makeText(getApplicationContext(), "默认选中项：" + print(adapter.getCheckedItemPositions()), Toast.LENGTH_SHORT).show();
    adapter.setOnItemClickListener(new OnItemClickListener() {
      @Override
      public void onItemClick(View view, int position, long id) {

        ARouter.getInstance().build(RouterConstants.ACTIVITY_FEEDBACK_DETAIL).withString(KEY_PREV_DATA, adapter.getItem(position).getFeedbackId()).navigation();
        //   Toast.makeText(getApplicationContext(), "Click item position = " + (position + 1) + " , Checked item positions = " + print(adapter.getCheckedItemPositions()), Toast.LENGTH_SHORT).show();
      }
    });


  }

  @Override
  protected Class<? extends FeedBackViewModel> getViewModelClass() {
    return FeedBackViewModel.class;
  }



  public void editSwitch(View view) {
    adapter.isEdit = !adapter.isEdit;
    adapter.notifyDataSetChanged();
    if (adapter.isEdit) {
      sendBaseTraceClick("2");
      ((TextView) view).setText(LanguageStrings.getFeedbackHistoryCancelString());
      mViewDataBinding.btnDel.setVisibility(View.VISIBLE);
      if (adapter.checkStates != null && adapter.checkStates.size() > 0) {
        mViewDataBinding.btnDel.setEnabled(true);
      } else {
        mViewDataBinding.btnDel.setEnabled(false);
      }
    } else {
      ((TextView) view).setText(LanguageStrings.getFeedbackHistoryEditString());
      mViewDataBinding.btnDel.setVisibility(View.GONE);
      adapter.clearSelect();
      sendExitDeleteButtonClick();
    }
  }


  private class MultipleChoiceAdapter extends RecyclerView.Adapter<InternalViewHolder> {
    private final LayoutInflater layoutInflater;
    private List<FeedBackHistoryDataItem> data;

    private OnItemClickListener onItemClickListener;
    private SparseBooleanArray checkStates;
    private boolean isEdit = false;

    public MultipleChoiceAdapter(Context context, List<FeedBackHistoryDataItem> data) {
      layoutInflater = LayoutInflater.from(context);
      this.data = data;
      // 默认没有选择任何item
      checkStates = new SparseBooleanArray(0);
    }

    public FeedBackHistoryDataItem getItem(int position) {
      return data.get(position);
    }

    @Override
    public InternalViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
      return new InternalViewHolder(layoutInflater.inflate(R.layout.usercenter_activity_item_multiple_choice, parent, false));
    }

    @Override
    public void onBindViewHolder(InternalViewHolder holder, @SuppressLint("RecyclerView") final int position) {
      holder.itemView.setOnClickListener(new View.OnClickListener() {
        @Override
        public void onClick(View v) {
          if (onItemClickListener != null) {
            if (isEdit) {
              adapter.check(position);
            } else {
              onItemClickListener.onItemClick(v, position, getItemId(position));
            }
          }
        }
      });

      if (isEdit) {
        holder.checkBox.setVisibility(View.VISIBLE);
        holder.arrowView.setVisibility(View.GONE);
      } else {
        holder.checkBox.setVisibility(View.GONE);
        holder.arrowView.setVisibility(View.VISIBLE);
      }

      if (checkStates.get(position)) {
        holder.checkBox.setChecked(true);
      } else {
        holder.checkBox.setChecked(false);
      }
      holder.textView.setText(getItem(position).getFeedbackContent());
      String feedBackTime = formateFeedBackTime(getItem(position).getAppShowTime(), getItem(position).getCategory2());
      holder.tvItemTime.setText(feedBackTime);

      holder.tvUnRead.setText(LanguageStrings.app_feedback_historyunread_textview_text());
      if (getItem(position).getShowRed() == 1) {
        holder.tvUnRead.setVisibility(View.VISIBLE);
        holder.img_arrow_donw.setVisibility(View.VISIBLE);
      } else {
        holder.tvUnRead.setVisibility(View.INVISIBLE);
        holder.img_arrow_donw.setVisibility(View.GONE);
      }

    }


    @Override
    public long getItemId(int position) {
      return position;
    }

    @Override
    public int getItemCount() {
      return data.size();
    }

    public void setOnItemClickListener(@NonNull OnItemClickListener listener) {
      onItemClickListener = listener;
    }

    public void setDefaultCheckedItemPositions(int... positions) {
      for (int item : positions) {
        checkStates.put(item, true);
      }
    }

    public void check(int position) {
      // 如果当前item已选中了，则从集合中删除，否则选择该item
      if (checkStates.get(position)) {
        checkStates.delete(position);
      } else {
          checkStates.put(position, true);
      }

      if (adapter.checkStates != null && adapter.checkStates.size() > 0) {
        mViewDataBinding.btnDel.setEnabled(true);
      } else {
        mViewDataBinding.btnDel.setEnabled(false);
      }
      notifyDataSetChanged();
    }

    public SparseBooleanArray getCheckedItemPositions() {
      return checkStates;
    }

    public SparseArray<String> getCheckedItemIds() {
      SparseArray<String> stringSparseArray = new SparseArray<>();
      for (int i = 0; i < checkStates.size(); i++) {
        if (checkStates.valueAt(i)) {
          stringSparseArray.put(i, data.get(checkStates.keyAt(i)).getFeedbackId());
        }
      }
      return stringSparseArray;
    }

    public void addDatas(List<FeedBackHistoryDataItem> list) {
      isEdit = false;
      data = list;
      notifyDataSetChanged();
    }

    public void clearSelect() {
      checkStates.clear();
    }
  }

  public void delFeedBacks(View view) {
    ConfirmAlertDialog.show(this.getSupportFragmentManager(),
      LanguageStrings.app_feedbackhistory_confirmdelete_textview_text(),
      LanguageStrings.app_smartadddevice_cancel_button_text(),
      LanguageStrings.app_devicelist_deletedevicealertok_button_text(), new Consumer<Boolean>() {
        @Override
        public void accept(Boolean aBoolean) throws Exception {
          if (aBoolean) {
            mViewModel.deleteFeedBackDatas(adapter.getCheckedItemIds());
            adapter.clearSelect();
          }
        }
      });
    sendDeleteButtonClick();
  }

  private void initTrace() {
    stayEleId = "5";
    pageId = "164";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  public void sendDeleteButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 3 + "", "1");
  }

  public void sendExitDeleteButtonClick() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, 4 + "", "1");
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  @Override
  protected void onResume() {
    super.onResume();
    mViewModel.getFeedBackHistoryDatas(1, 200);
  }
}
