package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.moudleUserCenter.ui.viewmodel.UserCenterUnitViewModel.IMPERIAL;
import static com.chervon.moudleUserCenter.ui.viewmodel.UserCenterUnitViewModel.METRIC;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.AppSettingEntry;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityUnitBinding;
import com.chervon.moudleUserCenter.ui.adapter.UnitAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterUnitViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: language setting Activity
 * @Description: logout activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_UNIT)
public class UnitSettingActivity extends BaseActivity<UserCenterUnitViewModel> implements UnitAdapter.CheckBoxClickListener {

  private UsercenterActivityUnitBinding binding;
  private UnitAdapter adapter;


  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_unit;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityUnitBinding) viewDataBinding;

    adapter = new UnitAdapter(this, this);
    binding.usercenterActivityUnitRecyclerview.setLayoutManager(new LinearLayoutManager(this));
    binding.usercenterActivityUnitRecyclerview.setAdapter(adapter);
  }

  @Override
  protected Class<? extends UserCenterUnitViewModel> getViewModelClass() {
    return UserCenterUnitViewModel.class;
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();

    binding.setUserCenterUnitModel(mViewModel);
    //观察者合集
    addOberver();
    //获取默认或者已经存储的单位
    mViewModel.getDefaultUnit();
  }


  private void addOberver() {
    mViewModel.unit.observe(this, new Observer<String>() {
      @Override
      public void onChanged(String unit) {

        adapter.setData(unit.equals(METRIC) ? 0 : 1);

      }
    });
  }

  @Override
  public void checkBoxClick(int position) {
    adapter.setData(position);
    AppSettingEntry  appSettingEntry = SoftRoomDatabase.getDatabase(this).appSettingDao().getSetting();
    if (null==appSettingEntry){
      appSettingEntry = new AppSettingEntry();
    }
    appSettingEntry.setUnit(position == 0 ? METRIC : IMPERIAL);
    SoftRoomDatabase.getDatabase(this).appSettingDao().insertAppSetting(appSettingEntry);

    //埋点相关
    if (position == 0){
      //METRIC
      traceUnitMetricUnit();
    }else {
      // IMPERIAL
      traceUnitImperialUnit();
    }
  }

  @Override
  protected void onResume() {
    super.onResume();
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_setting_unit_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendClickTrace(UnitSettingActivity.this, mouduleId, pageId, pageResouce, "3", "1");

          finish();
        }
      }));
  }


  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }


  private void initTrace() {
    stayEleId = "4";
    pageId = "416";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  /**
   * 切换公制埋点
   */
  private void traceUnitMetricUnit(){
    String module_id = "11";
    String page_id = "416";
    String mod_id = "0";
    String ele_id = "2";
    String pageRes = pageResouce;
    sendClickTraceNew(UnitSettingActivity.this, module_id, page_id, pageRes, ele_id,mod_id);
  }
  /**
   * 切换英制埋点
   */
  private void traceUnitImperialUnit(){

    String module_id = "11";
    String page_id = "416";
    String mod_id = "0";
    String ele_id = "1";
    String pageRes = pageResouce;
    sendClickTraceNew(UnitSettingActivity.this, module_id, page_id, pageRes, ele_id,mod_id);
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendClickTrace(UnitSettingActivity.this, mouduleId, pageId, pageResouce, "3", "1");
  }


}
