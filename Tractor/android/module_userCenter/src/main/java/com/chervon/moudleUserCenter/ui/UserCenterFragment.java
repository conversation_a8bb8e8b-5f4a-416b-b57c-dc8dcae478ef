package com.chervon.moudleUserCenter.ui;


import static com.chervon.libBase.utils.Utils.sendClickTrace;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.launcher.ARouter;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.result.SmartScanBean;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.databinding.UsercenterFragmentBinding;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterViewModel;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: UserCenterFragment
 * @Description: user center fragment
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/8/6
 * @UpdateRemark: 添加设备分享
 * @Version: 1.1
 */
public class UserCenterFragment extends BaseEnhanceFragment<UserCenterViewModel, UsercenterFragmentBinding> {

  public static final Integer ACCOUNT_SETTINGS_BUTTON_CLICK = 1;
  public static final Integer PERSONAL_DATA_BUTTON_CLICK = 2;
  public static final Integer SYSTEM_SETTINGS_BUTTON_CLICK = 3;
  public static final Integer DEALER_INFORMATION_BUTTON_CLICK = 4;
  public static final Integer HELP_CENTER_BUTTON_CLICK = 5;
  public static final Integer FEEDBACK_PAGE_BUTTON_CLICK = 6;
  //新增
  public static final Integer CONFIGURATION_FILE_BUTTON_CLICK = 9;
  //设备分享埋点
  public static final Integer SHARE_DEVICE_BUTTON_CLICK = 10;

  private static final String TAG = "UserCenterFragment";


  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    mViewModel.traceClickLiveData.observe(this, new Observer<Integer>() {
      @Override
      public void onChanged(Integer value) {
        if (value != null) {
          sendBaseTraceClick(value + "");
        }
      }
    });
  }

  @Override
  protected int getLayoutId() {
    return R.layout.usercenter_fragment;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding.setUserCenterViewModel(mViewModel);

    if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
      mViewDataBinding.usercenterTitleServiceTv.setVisibility(View.GONE);
      mViewDataBinding.services.setVisibility(View.GONE);
    }

  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  public void onResume() {
    super.onResume();
    if (isAdded()) {
      ((BaseActivity) getActivity()).setPageSource(pageResouce);
    }
    mViewDataBinding.usercenterTitleServiceTv.setText(LanguageStrings.app_usercenter_services_textview_text());
    mViewDataBinding.item1.setText(LanguageStrings.app_usercenter_acount_textview_text());
    mViewDataBinding.item2.setText(LanguageStrings.app_usercenter_setting_textview_text());
    mViewDataBinding.item3.setText(LanguageStrings.app_usercenter_dealer_textview_text());
    mViewDataBinding.item4.setText(LanguageStrings.app_usercenter_help_center_textview_text());
    mViewDataBinding.item5.setText(LanguageStrings.app_usercenter_feedback_textview_text());
    mViewDataBinding.itemShare.setText(LanguageStrings.app_usercenter_sharedevice_textview_text());
    //show user icon
    Glide.with(this)
      .load(UserInfo.get().getPhoto())
      .apply(RequestOptions.bitmapTransform(new CircleCrop()))
      .placeholder(R.drawable.ic_default_avatar)
      .error(R.drawable.ic_default_avatar)
      .into(mViewDataBinding.usercenterFragmentUsericon);

    mViewModel.updateUi();
    mViewDataBinding.setUserCenterViewModel(mViewModel);


    ApiService.instance().getSmartScan().subscribe(new HttpObserver<SmartScanBean>() {
      @Override
      protected void Next(SmartScanBean entity) {
        //更新数据存储

        if (entity.getEntry() != null) {
          User user = UserInfo.get();
          user.setSmartScanState(entity.getEntry().isInfo());
          UserInfo.set(user);

        }


      }
    });
  }

  @Override
  protected Class<? extends UserCenterViewModel> getViewModelClass() {
    return UserCenterViewModel.class;
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  private void initTrace() {
    stayEleId = "8";
    pageId = "88";
    mouduleId = "11";
    nextButtoneleid = "2";
  }


  @Override
  public void onDestroy() {
    super.onDestroy();
  }
}
