package com.chervon.moudleUserCenter.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEICON;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICEID;
import static com.chervon.libRouter.RouterConstants.KEY_DEVICENAME;
import static com.chervon.moudleUserCenter.utils.Constants.EMPTY;
import static com.chervon.moudleUserCenter.utils.Constants.TRACE_EVENT_ID;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.ui.dialog.LoadingDialog;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.ToastExtend;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.data.DeviceSubData;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityDeviceShareDetailBinding;
import com.chervon.libNetwork.event.ShareUpdateEvent;
import com.chervon.moudleUserCenter.ui.adapter.ShareDeviceSubAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.ShareDeviceDetailViewModel;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.ref.WeakReference;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: ShareDeviceDetailActivity
 * @Description: 设备分享详情
 * @Author: wuxd
 * @CreateDate: 2024/8/6
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_SHARE_DEVICE_DETAIL)
public class ShareDeviceDetailActivity extends BaseActivity<ShareDeviceDetailViewModel> {

  private UsercenterActivityDeviceShareDetailBinding binding;
  private ShareDeviceSubAdapter adapter;
  private WeakReference<LoadingDialog> loadingDialogWeakReference;
  private long dialogShowTime;
  private String dialogPageId;
  private String dialogStayEleId;
  private static final String EVENT_SHARE= "2";
  private static final String EVENT_DELETE = "3";
  private static final String EVENT_CONFIRM = "1";
  private static final String EVENT_CANCEL = "2";
  private static final String MODULE_ID = "13";
  private static final String PAGE_ID = "462";
  private static final String STAY_ELE_ID = "4";
  private static final String DIALOG_PAGE_ID = "465";
  private static final String DIALOG_STAY_ELE_ID = "3";
  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_device_share_detail;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityDeviceShareDetailBinding) viewDataBinding;
    binding.setViewModel(mViewModel);
    binding.setActivity(this);
    binding.setShareBtnText(LanguageStrings.app_sharedevice_share_textview_text());
    binding.recyclerView.setLayoutManager(new LinearLayoutManager(this));
    adapter = new ShareDeviceSubAdapter(this);
    binding.recyclerView.setAdapter(adapter);
    adapter.setListener(new ShareDeviceSubAdapter.OnClick() {
      @Override
      public void onClick(DeviceSubData data) {}

      @Override
      public void onLongClick(DeviceSubData data) {
        sendBaseTraceClick(EVENT_DELETE);
        sendExposure(ShareDeviceDetailActivity.this, mouduleId, dialogPageId, pageResouce);
        dialogShowTime = System.currentTimeMillis();
        DialogUtil.simpleConfirmDialog3(ShareDeviceDetailActivity.this,EMPTY,
                LanguageStrings.app_sharedevice_dialog1_textview_text(), LanguageStrings.getNetworkconfigconfirmed(),
                LanguageStrings.app_base_cancel_button_text(), view -> {
                  loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));
                  mViewModel.removeSub(String.valueOf(data.getShareId()));
                  sendClickTrace(ShareDeviceDetailActivity.this, mouduleId, dialogPageId, pageResouce, EVENT_CONFIRM, TRACE_EVENT_ID);
                  sendDurationTrace(ShareDeviceDetailActivity.this, mouduleId, dialogPageId, pageResouce, EMPTY, dialogStayEleId, System.currentTimeMillis() - dialogShowTime);
                }, view -> {
                  sendClickTrace(ShareDeviceDetailActivity.this, mouduleId, dialogPageId, pageResouce, EVENT_CANCEL, TRACE_EVENT_ID);
                  sendDurationTrace(ShareDeviceDetailActivity.this, mouduleId, dialogPageId, pageResouce, EMPTY, dialogStayEleId, System.currentTimeMillis() - dialogShowTime);
                },"");
      }
    });
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();
    initObserver();
    showLoading();
    String name = EMPTY;
    DeviceInfo deviceInfo = new DeviceInfo();
    deviceInfo.setDeviceIcon(EMPTY);
    deviceInfo.setDeviceName(EMPTY);
    if(null != getIntent().getExtras() ) {
      if(null ==getIntent().getExtras().getString(KEY_DEVICEICON) || null ==getIntent().getExtras().getString(KEY_DEVICENAME)) {
        if(null !=getIntent().getExtras().getString(KEY_DEVICEID)) {
          DeviceInfo local = SoftRoomDatabase.getDatabase(this).deviceDao().getDeviceById(getIntent().getExtras().getString(KEY_DEVICEID));
          if(null != local) {
            deviceInfo = local;
          }
        }
      }
      Glide.with(this).load(null !=getIntent().getExtras().getString(KEY_DEVICEICON)?getIntent().getExtras().getString(KEY_DEVICEICON):deviceInfo.getDeviceIcon())
              .priority(Priority.HIGH).placeholder(com.chervon.moudleDeviceManage.R.drawable.ic_device_default)
              .into(binding.ivDevice);
      name = getIntent().getExtras().getString(KEY_DEVICENAME) != null ? getIntent().getExtras().getString(KEY_DEVICENAME) : deviceInfo.getNickName();
    }
    mViewModel.toolbarData.setValue(new ToolbarData(
            LanguageStrings.app_usercenter_sharedevice_textview_text(),
            view -> {
              onBackPressed();
            }));
  }

  @Override
  protected Class<? extends ShareDeviceDetailViewModel> getViewModelClass() {
    return ShareDeviceDetailViewModel.class;
  }

  private void refreshData(){
    if(null != getIntent().getExtras()) {
      mViewModel.getSubList(null != getIntent().getExtras().getString(KEY_DEVICEID)?getIntent().getExtras().getString(KEY_DEVICEID):EMPTY);
    }
  }

  @Subscribe(threadMode = ThreadMode.MAIN)
  public void onUpdate(ShareUpdateEvent event) {
    refreshData();
  }

  private void initObserver() {
    mViewModel.sub.observe(this, deviceSubData -> {
      dismissLoading();
      adapter.setData(deviceSubData.getDeviceSubDataList(),true);
    });
    mViewModel.message.observe(this, s -> {
      dismissLoading();
      ToastUtils.showShort(s);
    });
    mViewModel.needUpdate.observe(this,b->{
      if(b) {
        ToastExtend.showSuccess();
        EventBus.getDefault().post(new ShareUpdateEvent());
      }
    });
  }

  public void shareOnClick(){
    if(null != getIntent().getExtras()) {
      sendBaseTraceClick(EVENT_SHARE);
      mViewModel.toShareDevice(null != getIntent().getExtras().getString(KEY_DEVICEID)?getIntent().getExtras().getString(KEY_DEVICEID):EMPTY,
              null != getIntent().getExtras().getString(KEY_DEVICEICON)?getIntent().getExtras().getString(KEY_DEVICEICON):EMPTY);
    }
  }

  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    if(!EventBus.getDefault().isRegistered(this)) {
      EventBus.getDefault().register(this);
    }
  }

  @Override
  protected void onResume() {
    super.onResume();
    refreshData();
  }

  @Override
  protected void onDestroy() {
    super.onDestroy();
    if(EventBus.getDefault().isRegistered(this)) {
      EventBus.getDefault().unregister(this);
    }
  }

  private void showLoading() {
    loadingDialogWeakReference = new WeakReference<>(LoadingDialog.showDialog(getSupportFragmentManager()));
  }

  private void dismissLoading() {
    if (loadingDialogWeakReference != null && loadingDialogWeakReference.get() != null) {
      loadingDialogWeakReference.get().dismiss();
    }
  }

  private void initTrace() {
    stayEleId = STAY_ELE_ID;
    pageId = PAGE_ID;
    mouduleId = MODULE_ID;
    nextButtoneleid = "";
    dialogPageId =DIALOG_PAGE_ID;
    dialogStayEleId = DIALOG_STAY_ELE_ID;
  }


}
