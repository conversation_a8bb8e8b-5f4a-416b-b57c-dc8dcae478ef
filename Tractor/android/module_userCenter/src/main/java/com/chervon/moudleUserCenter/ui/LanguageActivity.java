package com.chervon.moudleUserCenter.ui;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.LanguageInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleUserCenter.R;
import com.chervon.moudleUserCenter.data.LanguageItemData;
import com.chervon.moudleUserCenter.databinding.UsercenterActivityLanguageBinding;
import com.chervon.moudleUserCenter.ui.adapter.LanguageAdapter;
import com.chervon.moudleUserCenter.ui.viewmodel.UserCenterLanguageViewModel;
import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: language setting Activity
 * @Description: logout activity
 * @Author: LangMeng
 * @CreateDate: 2022/5/6 17:25
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/5/6 17:25
 * @UpdateRemark: new class
 * @Version: 1.0
 */
@Route(path = RouterConstants.ACTIVITY_URL_LANGUAGE)
public class LanguageActivity extends BaseActivity<UserCenterLanguageViewModel> implements LanguageAdapter.LanguageItemClick {

  private UsercenterActivityLanguageBinding binding;
  private LanguageAdapter adapter;
  private List<LanguageItemData> dataList = new ArrayList<>();

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected Integer getLayoutId() {
    return R.layout.usercenter_activity_language;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    binding = (UsercenterActivityLanguageBinding) viewDataBinding;
    mViewModel.toolbarData.setValue(new ToolbarData(
      LanguageStrings.app_language_title_textview_text(),
      new View.OnClickListener() {
        @Override
        public void onClick(View view) {
          sendBackTrace();
          finish();
        }
      }));
  }

  @Override
  protected void initData(Bundle savedInstanceState) {
    initTrace();

    mViewModel.setFragmentManager(getSupportFragmentManager());
    binding.setUserCenterLanguageModel(mViewModel);

    binding.usercenterActivityLanguageRecyclerview.setLayoutManager(new LinearLayoutManager(this));
    adapter = new LanguageAdapter(this, this);
    binding.usercenterActivityLanguageRecyclerview.setAdapter(adapter);
    binding.usercenterActivityLanguageRecyclerview.setItemAnimator(null);

  }

  @Override
  protected void onResume() {
    super.onResume();

    getCheckLanguage();
  }

  private void getCheckLanguage(){

    //拉取到语言列表前后台切换不做语言列表刷新
    if (adapter.getList().size()!=0){
      return;
    }

    List<LanguageInfo> allLanguageInfo = SoftRoomDatabase.getDatabase(this).languageInfoDao().getAllLanguageInfo();
    //填充语言列表数据
    for (LanguageInfo languageInfo : allLanguageInfo) {
      LanguageItemData languageItemData = new LanguageItemData();
      languageItemData.setLanguageCode(languageInfo.getType());
      languageItemData.setLanguageName(languageInfo.getContent());
      MyLang.getInstance().getCurrentLocaleInfo();
      //匹配当前语言设置
      if (MyLang.getInstance().getCurrentLocaleInfo().getLangCode().equals(languageInfo.getType())) {
        languageItemData.setChecked(true);
        mViewModel.setCheckIteData(languageItemData);
      }
      dataList.add(languageItemData);
    }
    adapter.setData(dataList);

  }

  @Override
  public void itemClick(LanguageItemData languageItemData) {
    mViewModel.setCheckIteData(languageItemData);
  }


  @Override
  protected Class<? extends UserCenterLanguageViewModel> getViewModelClass() {
    return UserCenterLanguageViewModel.class;
  }


  private void initTrace() {
    stayEleId = "2";
    pageId = "92";
    mouduleId = "11";
    nextButtoneleid = "2";
  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

}
