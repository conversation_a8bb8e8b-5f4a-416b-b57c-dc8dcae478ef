package com.chervon.moudleConfigNet.data.repository;

import static com.chervon.libBase.model.AppConstants.NET_WORK_EXCEPTION;
import static com.chervon.libBase.model.AppConstants.Z6_BLENAME_LENGTH;
import static com.chervon.libBase.model.AppConstants.Z6_HEAD;
import static com.chervon.libBase.model.AppConstants.ZT_01;
import static com.chervon.libBase.model.AppConstants.ZT_02;
import static com.chervon.libBase.model.AppConstants.ZT_03;
import static com.chervon.libBase.utils.APPConstants.EDIT_CONTENT_IS_NULL;
import static com.chervon.libBase.utils.Utils.hexString2String;
import static com.chervon.libBase.utils.Utils.z6DeviceIdAppendWithZero;
import static com.chervon.libBase.utils.Utils.z6DeviceIdHex2digit;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.BIND_SUCCESS;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_SUCCESS;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.MAIN_USER_ALREADY_BIND_FAIL;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.MATCH_DEVICE_ID_FAIL;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.NET_MODE_WIFI;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.REGISTER_SCAN_DEVICE_SUCCESS;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.RESPONSE_FAIL;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.RESPONSE_SUCCESS;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.SCAN_DEVICE_TIME_OUT;
import static com.chervon.moudleConfigNet.ui.WifiConnectionFragment.SYNC_SUCCESS;
import static com.chervon.moudleConfigNet.ui.WifiConnectionFragment.WIFI_DEVICE_CONNECT_SUCCESS;

import android.app.Application;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.le.ScanResult;
import android.content.Context;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.CBleDevice;
import com.chervon.libBase.model.NoIotResultUIState;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.dao.ProductDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.moudleConfigNet.BuildConfig;
import com.chervon.moudleConfigNet.data.api.ConfigNetApi;
import com.chervon.libBase.model.ProductsParams;
import com.chervon.moudleConfigNet.data.model.BindingEntry;
import com.chervon.libBase.model.SnScanEntry;
import com.chervon.moudleConfigNet.ui.NoIotResultFragment;
import com.chervon.moudleConfigNet.ui.state.DeviceNotFoundUIState;
import com.chervon.moudleConfigNet.ui.state.DeviceSearchUIState;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.libBase.model.ProductEntry;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.moudleConfigNet.ui.state.ScanNearByDevicesUIState;
import com.clj.fastble.BleManager;
import com.clj.fastble.callback.BleGattCallback;
import com.clj.fastble.callback.BleNotifyCallback;
import com.clj.fastble.callback.BleWriteCallback;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.data.BleScanState;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;
import com.google.gson.Gson;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;
import io.reactivex.observers.DefaultObserver;
import io.reactivex.schedulers.Schedulers;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.data.repository
 * @ClassName: BlueToothConfigNetRepo
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/11 下午5:21
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/7/3
 * @UpdateRemark: 添加欧洲CRM同步设备deviceId匹配
 * @Version: 1.1
 */
public class BlueToothConfigNetRepo implements IBlueToothConfigNetRepo {
  private static final String TAG = "BlueToothConfigNetRepo";
  public static final int STATUS_ENABLE = 8;

  private HashMap<String, BleDevice> mBleDeviceMap = new HashMap<String, BleDevice>();
  private DeviceDao mDeviceDao;
  private ProductDao mProductDao;

  private static final String EVN_SIT = "sit_na";
  private static final String EVN_PRE = "pre_na";
  private static final String EVN_SIT_EU = "sit_eu";
  private static final String EVN_PRE_EU = "pre_eu";
  private static final String EVN_PROD_NA = "prod_na";
  private static final String EVN_PROD_EU = "prod_eu";

  private String wifi_evn = "01";

  final String send = "send";
  final String receiver = "receiver";
  private String sendSTR = "";
  private BleDevice mBleDevice;
  private static final int EGO_PRODUCT_CODE = 1;
  private static final int FLEET_PRODUCT_CODE = 2;
  private static final String MAIN_USER_ALREADY_BIND_RESPONSE_CODE = "1020062009";
  private Context context;
  public BlueToothConfigNetRepo() {
  }

  public BlueToothConfigNetRepo(Context context) {
    SoftRoomDatabase db = SoftRoomDatabase.getDatabase(context.getApplicationContext());
    this.context = context;
    mDeviceDao = db.deviceDao();
    mProductDao = db.productDao();

  }

  public void scanDevicesByProduct2(MutableLiveData<ScanNearByDevicesUIState> liveData, ProductInfo productInfo) {
    ArrayList<List<DeviceInfo>> deviceInfoInfos = new ArrayList<>();
    ArrayList<DeviceInfo> scanDeviceInfoInfos = new ArrayList<>();
    ConfigNetApi configNetApi = new ConfigNetApi();

    BluetoothService.getInstance().setTag(true, TAG, new BluetoothService.DeviceFindListener() {
      @Override
      public void findDevice(CBleDevice bleDevice) {

        // TODO Z6  判断是否Z6设备

        boolean isScanedZ6 = scanResultOldZ6(bleDevice, productInfo, deviceInfoInfos, scanDeviceInfoInfos, liveData, configNetApi);
        if (isScanedZ6) {
          return;
        }

        String snCodeString = getSnCodeString(bleDevice.getCBleDevice());
        //判断是否是 泉峰设备
        if (BluetoothConection.isEGO(bleDevice.getCBleDevice().getScanRecord()) &&
                BluetoothConection.isEGOBroadcasting(bleDevice.getCBleDevice().getScanRecord(), snCodeString)) {

          if (productInfo != null && !TextUtils.isEmpty(productInfo.getProductSnCode()) &&
                  !productInfo.getProductSnCode().equals(snCodeString)) {
            return;
          }
          //过滤已经匹配deviceId失败的mac
          if (null != liveData.getValue() && liveData.getValue().getFilterMac().contains(bleDevice.getCBleDevice().getMac().toUpperCase(Locale.ROOT))) {
            return;
          }
          DeviceInfo deviceInfo = createDeviceInfo(bleDevice.getCBleDevice());
          String stateString = getDeviceStateString(bleDevice.getCBleDevice());
          if ("di".equalsIgnoreCase(stateString)) {
            deviceInfo.setStatus(8);
          }
          mBleDevice = bleDevice.getCBleDevice();
          scanDeviceInfoInfos.add(deviceInfo);

          //获取产品显示信息
          getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);

        }
      }

      @Override
      public void scanTimeOut() {
        mBleDeviceMap.clear();
        if (scanDeviceInfoInfos.size() <= 0) {
          ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
          if (nearByDevicesUIState == null) {
            nearByDevicesUIState = new ScanNearByDevicesUIState();
          }
          nearByDevicesUIState.setScanTimeOut(true);
          nearByDevicesUIState.setDeviceFound(false);
          nearByDevicesUIState.setDevices(deviceInfoInfos);
          liveData.postValue(nearByDevicesUIState);
        }
      }
    });
  }
  // TODO Z6  扫描结果处理

  private boolean scanResultOldZ6(CBleDevice bleDevice, ProductInfo productInfo, ArrayList<List<DeviceInfo>> deviceInfoInfos
          , ArrayList<DeviceInfo> scanDeviceInfoInfos, MutableLiveData<ScanNearByDevicesUIState> liveData, ConfigNetApi configNetApi) {


    if (bleDevice != null) {
      String bleName = bleDevice.getName();
      LogUtils.d(TAG + "scanResultOldZ6_bleName" + bleName);
      if (!TextUtils.isEmpty(bleName) && bleName.contains(Z6_HEAD) && bleName.length() == Z6_BLENAME_LENGTH) {
        String devieId = bleName.substring(2);
        //snCode
        String hexSnCodeStr = devieId.substring(0, 6);
        String header = hexString2String(hexSnCodeStr);
        String numberStr1 = z6DeviceIdAppendWithZero(devieId.substring(6, 8))
                + z6DeviceIdAppendWithZero(devieId.substring(8, 10))
                + z6DeviceIdAppendWithZero(devieId.substring(10, 12))
                + z6DeviceIdHex2digit(devieId.substring(12, 18));
        String end = hexString2String(devieId.substring(18, 20));
        devieId = header + numberStr1 + end;
        String snCodeString = devieId.substring(1, 5);
        if (!ZT_01.equalsIgnoreCase(snCodeString)
                && !ZT_02.equalsIgnoreCase(snCodeString)
                && !ZT_03.equalsIgnoreCase(snCodeString)) {
          return false;
        }
        if (productInfo != null && !TextUtils.isEmpty(productInfo.getProductSnCode()) &&
                !productInfo.getProductSnCode().equals(snCodeString)) {
          return false;
        }
        DeviceInfo deviceInfo = createDeviceInfo(bleDevice.getCBleDevice());
        deviceInfo.setStatus(STATUS_ENABLE);
        deviceInfo.setDeviceId(devieId);
        deviceInfo.setSn(snCodeString);
        mBleDevice = bleDevice.getCBleDevice();
        scanDeviceInfoInfos.add(deviceInfo);
        //获取产品显示信息
        getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);

        return true;

      }
    }
    return false;
  }

  public void scanDevicesByProductByWifi(MutableLiveData<ScanNearByDevicesUIState> liveData, ProductInfo productInfo, DeviceInfo deviceInfo) {
    ArrayList<List<DeviceInfo>> deviceInfoInfos = new ArrayList<>();
    ArrayList<DeviceInfo> scanDeviceInfoInfos = new ArrayList<>();
    ConfigNetApi configNetApi = new ConfigNetApi();
    BluetoothService.getInstance().setTag(true, TAG, new BluetoothService.DeviceFindListener() {
      @Override
      public void findDevice(CBleDevice bleDevice) {
        LogUtils.dTag(TAG, "scanDevicesByProduct_扫描中", bleDevice.getCBleDevice().getName());
        String snCodeString = getSnCodeString(bleDevice.getCBleDevice());
        //判断是否是 泉峰设备

        if (productInfo != null && !TextUtils.isEmpty(productInfo.getProductSnCode()) && !productInfo.getProductSnCode().equals(snCodeString)) {
          return;
        }
        if (null != liveData.getValue()&& liveData.getValue().getFilterMac().contains(bleDevice.getMac().toUpperCase(Locale.ROOT))) {
          return;
        }
//          DeviceInfo deviceInfo = createDeviceInfo(bleDevice.getCBleDevice());
        String stateString = getDeviceStateString(bleDevice.getCBleDevice());
        if ("di".equalsIgnoreCase(stateString)) {
          deviceInfo.setStatus(8);
        }
        if (deviceInfo.getMac().equals(deviceInfo.getMac())) {

          scanDeviceInfoInfos.add(deviceInfo);
          //获取产品显示信息
          getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
        }

      }

      @Override
      public void scanTimeOut() {
        mBleDeviceMap.clear();
        if (scanDeviceInfoInfos.size() <= 0) {
          ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
          if (nearByDevicesUIState == null) {
            nearByDevicesUIState = new ScanNearByDevicesUIState();
          }
          nearByDevicesUIState.setDeviceFound(false);
          nearByDevicesUIState.setDevices(deviceInfoInfos);
          liveData.postValue(nearByDevicesUIState);
        }
      }
    });
  }

  @Deprecated
  public void scanDevicesByProduct(MutableLiveData<ScanNearByDevicesUIState> liveData, ProductInfo productInfo) {
    BluetoothConection.getInstance().scanConfig(10000);
    if (BleScanState.STATE_SCANNING == BleManager.getInstance().getScanSate()) {
      //停止扫描
      BleManager.getInstance().cancelScan();

    }


    ArrayList<List<DeviceInfo>> deviceInfoInfos = new ArrayList<>();
    ArrayList<DeviceInfo> scanDeviceInfoInfos = new ArrayList<>();
    ConfigNetApi configNetApi = new ConfigNetApi();
    BluetoothConection.getInstance().scanDevices(new BluetoothConection.BleScanDeviceCallBack() {
      @Override
      public void onScanStarted(boolean success) {
        LogUtils.d("scanDevicesByProduct_onScanStarted");
      }

      @Override
      public void onScanning(BleDevice bledevice) {
        LogUtils.dTag(TAG, "scanDevicesByProduct_扫描中", bledevice.getName());
        String snCodeString = getSnCodeString(bledevice);
        //判断是否是 泉峰设备
        if (BluetoothConection.isEGO(bledevice.getScanRecord()) && BluetoothConection.isEGOBroadcasting(bledevice.getScanRecord(), snCodeString)) {
          LogUtils.iTag(TAG, "BLE_EGO_SCAN_productInfo_", productInfo.getProductSnCode());
          LogUtils.iTag(TAG, "BLE_EGO_SCAN_snCodeString", snCodeString);
          LogUtils.iTag(TAG, "BLE_EGO_SCAN_getProductSnCode", productInfo.getProductSnCode());
          if (productInfo != null &&
                  !TextUtils.isEmpty(productInfo.getProductSnCode()) &&
                  !productInfo.getProductSnCode().equals(snCodeString)) {
            return;
          }
          DeviceInfo deviceInfo = createDeviceInfo(bledevice);
          String stateString = getDeviceStateString(bledevice);
          if ("di".equalsIgnoreCase(stateString)) {
            deviceInfo.setStatus(8);
          }

          if (BleScanState.STATE_SCANNING == BleManager.getInstance().getScanSate()) {
            //停止扫描
            BleManager.getInstance().cancelScan();

          }
          //  deviceInfoInfos.add(deviceInfo);
          scanDeviceInfoInfos.add(deviceInfo);
          //获取产品显示信息
          getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);

        }

      }

      @Override
      public void onScanFinished(List<BleDevice> scanResultList) {
        if (scanResultList.size() == 0) {
          BluetoothConection.getInstance().scanDevices(new BluetoothConection.BleScanDeviceCallBack() {
            @Override
            public void onScanStarted(boolean success) {
              LogUtils.d("scanDevicesByProduct_onScanStarted");
            }

            @Override
            public void onScanning(BleDevice bleDevice) {
              LogUtils.dTag(TAG, "scanDevicesByProduct_扫描中", bleDevice.getName());


            }

            @Override
            public void onScanFinished(List<BleDevice> scanResultList) {
              ConfigNetApi configNetApi = new ConfigNetApi();
              mBleDeviceMap.clear();
              ArrayList<List<DeviceInfo>> deviceInfoInfos = new ArrayList<>();
              ArrayList<DeviceInfo> scanDeviceInfoInfos = new ArrayList<>();
              List<BleDevice> scanResultBleDevices = scanResultList;
              for (int i = 0; scanResultBleDevices != null && i < scanResultBleDevices.size(); i++) {
                BleDevice bledevice = scanResultBleDevices.get(i);
                String snCodeString = getSnCodeString(bledevice);
                //判断是否是 泉峰设备
                if (BluetoothConection.isEGO(bledevice.getScanRecord()) && BluetoothConection.isEGOBroadcasting(bledevice.getScanRecord(), snCodeString)) {
                  LogUtils.iTag(TAG, "BLE_EGO_SCAN_productInfo_", productInfo.getProductSnCode());
                  LogUtils.iTag(TAG, "BLE_EGO_SCAN_snCodeString", snCodeString);
                  LogUtils.iTag(TAG, "BLE_EGO_SCAN_getProductSnCode", productInfo.getProductSnCode());
                  if (!productInfo.getProductSnCode().equals(snCodeString)) {
                    continue;
                  }
                  DeviceInfo deviceInfo = createDeviceInfo(bledevice);
                  String stateString = getDeviceStateString(bledevice);
                  if ("di".equalsIgnoreCase(stateString)) {
                    deviceInfo.setStatus(8);
                  }
                  //  deviceInfoInfos.add(deviceInfo);
                  scanDeviceInfoInfos.add(deviceInfo);
                  //获取产品显示信息
                  getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
                  break;
                }
              }
              if (scanDeviceInfoInfos.size() <= 0) {
                ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
                if (nearByDevicesUIState == null) {
                  nearByDevicesUIState = new ScanNearByDevicesUIState();
                }
                nearByDevicesUIState.setDeviceFound(false);
                nearByDevicesUIState.setDevices(deviceInfoInfos);
                liveData.postValue(nearByDevicesUIState);
              }


            }
          });
        } else {
          mBleDeviceMap.clear();
          if (scanDeviceInfoInfos.size() <= 0) {
            ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
            if (nearByDevicesUIState == null) {
              nearByDevicesUIState = new ScanNearByDevicesUIState();
            }
            nearByDevicesUIState.setDeviceFound(false);
            nearByDevicesUIState.setDevices(deviceInfoInfos);
            liveData.postValue(nearByDevicesUIState);
          }
        }


      }
    });
  }

  private String getDeviceStateString(BleDevice bledevice) {
    String stateStr = BluetoothConection.getDIStringCode(bledevice.getScanRecord());

    LogUtils.iTag(TAG, "BLE_EGO_stateStr", stateStr);
    if (TextUtils.isEmpty(stateStr)) {
      stateStr = "0";
    }
    //转bytes
    byte[] snCodeBytes = ConvertUtils.hexString2Bytes(stateStr);
    //deviceId string
    String state = ConvertUtils
            .bytes2String(snCodeBytes)
            .replaceAll("\\n", "")
            .replaceAll("\\u0000", "");
    LogUtils.iTag(TAG, "BLE_EGO_SCAN_stateStr", state);
    return state;

  }

  @NonNull
  public DeviceInfo createDeviceInfo(BleDevice bledevice) {
    DeviceInfo deviceInfo = new DeviceInfo();
    deviceInfo.setMac(bledevice.getMac());
    // deviceInfo.setDeviceName(bledevice.getName());
    return deviceInfo;
  }

  private void getProductInfo(ConfigNetApi configNetApi, ArrayList<List<DeviceInfo>> deviceInfoInfos,
                              String snCodeString, DeviceInfo deviceInfo, MutableLiveData<ScanNearByDevicesUIState> liveData) {

    if (deviceInfoInfos == null) {
      deviceInfoInfos = new ArrayList<>();
    }

    ArrayList<List<DeviceInfo>> finalDeviceInfoInfos = deviceInfoInfos;
    configNetApi.getProductDetail(snCodeString).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductInfo>>() {
              @Override
              public void onNext(HttpResponse<ProductInfo> deviceDetailHttpResponse) {
                ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
                if (nearByDevicesUIState == null) {
                  nearByDevicesUIState = new ScanNearByDevicesUIState();
                }
                if (deviceDetailHttpResponse.status && !TextUtils.isEmpty(deviceDetailHttpResponse.response.getId())) {
                  ProductInfo productInfo = deviceDetailHttpResponse.response;
                  if (productInfo.getProductIcon() != null) {
                    deviceInfo.setIconUrl(productInfo.getProductIcon());
                  }

                  //判断是否是fleet产品
                  if (!isEGOProduct(productInfo.getBusinessType())) {
                    ToastUtils.showLong(LanguageStrings.app_product_only_available_ego_fleet_app());
                    return;
                  }

                  deviceInfo.setProductId(productInfo.getId());
                  deviceInfo.setProductType(productInfo.getProductType());
                  deviceInfo.setCommunicateMode(productInfo.getNetworkModes());
                  deviceInfo.setDeviceName(productInfo.getCommodityModel());
                  deviceInfo.setCommodityModel(productInfo.getCommodityModel());
                  nearByDevicesUIState.setDeviceFound(true);


                  if (finalDeviceInfoInfos.size() == 0) {
                    List<DeviceInfo> deviceInfoList = new ArrayList<DeviceInfo>();
                    deviceInfoList.add(deviceInfo);
                    finalDeviceInfoInfos.add(deviceInfoList);
                  } else {
                    for (int i = 0; i < finalDeviceInfoInfos.size(); i++) {
                      List<DeviceInfo> currentList = finalDeviceInfoInfos.get(i);
                      DeviceInfo temp = currentList.get(0);
                      if (temp.getProductId().equalsIgnoreCase(deviceInfo.getProductId())) {
                        if (deviceInfo.isButtonClick()) {
                          currentList.add(0, deviceInfo);
                        } else {
                          currentList.add(deviceInfo);
                        }

                        break;
                      } else if (i == finalDeviceInfoInfos.size() - 1) {
                        List<DeviceInfo> deviceInfoList = new ArrayList<DeviceInfo>();
                        deviceInfoList.add(deviceInfo);
                        finalDeviceInfoInfos.add(deviceInfoList);
                        break;
                      }
                    }
                  }
                  LogUtils.d(TAG, "getProductInfo_HttpResponse", deviceDetailHttpResponse.response.getProductName());
                  nearByDevicesUIState.setDevices(finalDeviceInfoInfos);
                  liveData.postValue(nearByDevicesUIState);

                } else {
                  //     nearByDevicesUIState.setDeviceFound(false);
                }

              }

              @Override
              public void onError(Throwable e) {
                LogUtils.eTag(TAG, e.getMessage(), "error");
              }

              @Override
              public void onComplete() {

              }
            });
  }

  private void getProductInfoWithNoFleet(ConfigNetApi configNetApi, ArrayList<List<DeviceInfo>> deviceInfoInfos,
                                         String snCodeString, DeviceInfo deviceInfo, MutableLiveData<ScanNearByDevicesUIState> liveData) {

    if (TextUtils.isEmpty(snCodeString)){
      return;
    }
    //过滤隐藏设备
    ProductInfo productInfo = SoftRoomDatabase.getDatabase(BaseApplication.getInstance()).productDao().getProductInfo(snCodeString);
    if (null==productInfo){
      return;
    }

    if (deviceInfoInfos == null) {
      deviceInfoInfos = new ArrayList<>();
    }

    ArrayList<List<DeviceInfo>> finalDeviceInfoInfos = deviceInfoInfos;
    configNetApi.getProductDetail(snCodeString).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductInfo>>() {
              @Override
              public void onNext(HttpResponse<ProductInfo> deviceDetailHttpResponse) {
                ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
                if (nearByDevicesUIState == null) {
                  nearByDevicesUIState = new ScanNearByDevicesUIState();
                }
                if (deviceDetailHttpResponse.status && !TextUtils.isEmpty(deviceDetailHttpResponse.response.getId())) {
                  ProductInfo productInfo = deviceDetailHttpResponse.response;
                  if (productInfo.getProductIcon() != null) {
                    deviceInfo.setIconUrl(productInfo.getProductIcon());
                  }

                  //判断是否是fleet产品
                  if (!isEGOProduct(productInfo.getBusinessType())) {

                    return;
                  }

                  deviceInfo.setProductId(productInfo.getId());
                  deviceInfo.setProductType(productInfo.getProductType());
                  deviceInfo.setCommunicateMode(productInfo.getNetworkModes());
                  deviceInfo.setDeviceName(productInfo.getCommodityModel());
                  deviceInfo.setCommodityModel(productInfo.getCommodityModel());
                  nearByDevicesUIState.setDeviceFound(true);


                  if (finalDeviceInfoInfos.size() == 0) {
                    List<DeviceInfo> deviceInfoList = new ArrayList<DeviceInfo>();
                    deviceInfoList.add(deviceInfo);
                    finalDeviceInfoInfos.add(deviceInfoList);
                  } else {
                    for (int i = 0; i < finalDeviceInfoInfos.size(); i++) {
                      List<DeviceInfo> currentList = finalDeviceInfoInfos.get(i);
                      DeviceInfo temp = currentList.get(0);
                      if (temp.getProductId().equalsIgnoreCase(deviceInfo.getProductId())) {
                        if (deviceInfo.isButtonClick()) {
                          currentList.add(0, deviceInfo);
                        } else {
                          currentList.add(deviceInfo);
                        }

                        break;
                      } else if (i == finalDeviceInfoInfos.size() - 1) {
                        List<DeviceInfo> deviceInfoList = new ArrayList<DeviceInfo>();
                        deviceInfoList.add(deviceInfo);
                        finalDeviceInfoInfos.add(deviceInfoList);
                        break;
                      }
                    }
                  }
                  LogUtils.d(TAG, "getProductInfo_HttpResponse", deviceDetailHttpResponse.response.getProductName());
                  nearByDevicesUIState.setDevices(finalDeviceInfoInfos);
                  liveData.postValue(nearByDevicesUIState);

                } else {
                  //     nearByDevicesUIState.setDeviceFound(false);
                }

              }

              @Override
              public void onError(Throwable e) {
                LogUtils.eTag(TAG, e.getMessage(), "error");
              }

              @Override
              public void onComplete() {

              }
            });
  }

  /**
   * 判断是不是已经绑定过的Z6设备
   * @param cBledevice 设备device
   * @return true 是已经绑定的设备  false 未绑定过的z6设备
   */
  private boolean isBindZ6Device(CBleDevice cBledevice) {
    if (cBledevice != null) {
      String bleName = cBledevice.getName();
      if (!TextUtils.isEmpty(bleName) && bleName.contains(Z6_HEAD) && bleName.length() == Z6_BLENAME_LENGTH) {
        String deviceId = bleName.substring(2);
        String hexSnCodeStr = deviceId.substring(2, 6);
        String header = hexString2String(hexSnCodeStr);
        String numberStr1 = z6DeviceIdAppendWithZero(deviceId.substring(6, 8))
                + z6DeviceIdAppendWithZero(deviceId.substring(8, 10))
                + z6DeviceIdAppendWithZero(deviceId.substring(10, 12))
                + z6DeviceIdHex2digit(deviceId.substring(12, 18));
        String end = hexString2String(deviceId.substring(18, 20));
        deviceId = "X" + header + numberStr1 + end;
        if( null == mDeviceDao){
          return false;
        }
        DeviceInfo deviceByMac = mDeviceDao.getDeviceById(deviceId);
        if (null != deviceByMac) {
          return true;
        }else {
          return false;
        }
      }
    }
    return false;
  }

  public void scanDevices2(MutableLiveData<ScanNearByDevicesUIState> liveData) {

    ArrayList<List<DeviceInfo>> deviceInfoInfos = new ArrayList<>();
    ArrayList<DeviceInfo> scanedDevices = new ArrayList<>();
    BluetoothService.getInstance().PERMISSION_GRANTED = true;
    BluetoothService.getInstance().setTag(true, TAG, new BluetoothService.DeviceFindListener() {
      @Override
      public void findDevice(CBleDevice cBledevice) {
        if(isBindZ6Device(cBledevice)){
          return;
        }
        //过滤已扫描到的设备
        for (DeviceInfo device : scanedDevices) {
          if (device.getMac().equals(cBledevice.getCBleDevice().getMac())) {
            return;
          }
        }

        // TODO Z6

        String bleName = cBledevice.getCBleDevice().getName();
        if (!TextUtils.isEmpty(bleName) && bleName.contains(Z6_HEAD) && bleName.length() == Z6_BLENAME_LENGTH) {
          scanDevicesOldZ6(cBledevice.getCBleDevice(), deviceInfoInfos, scanedDevices, liveData);
          return;
        }

        LogUtils.d("isMainThread_onScanning" + (Thread.currentThread() == Looper.getMainLooper().getThread()));
        ConfigNetApi configNetApi = new ConfigNetApi();
        String snCodeString = getSnCodeString(cBledevice.getCBleDevice());
        boolean isDeviceClickButton = BluetoothConection.isEGOBroadcasting(cBledevice.getCBleDevice().getScanRecord(), snCodeString);
        DeviceInfo deviceInfo = createDeviceInfo(cBledevice.getCBleDevice());
        deviceInfo.setSn(snCodeString);
        String stateString = getDeviceStateString(cBledevice.getCBleDevice());
        if ("di".equalsIgnoreCase(stateString)) {
          deviceInfo.setStatus(8);
        }
        deviceInfo.setButtonClick(isDeviceClickButton);

        LogUtils.d("ondeviceScaned_snCodeString" + snCodeString + "mac" + deviceInfo.getMac() + "DeviceName" + deviceInfo.getDeviceName() + "snCodeString" + snCodeString);
        List<DeviceInfo> deviceInfos = mDeviceDao.getDevices();
        if (deviceInfos != null && deviceInfos.size() > 0) {
          for (int i = 0; i < deviceInfos.size(); i++) {
            String mac = deviceInfos.get(i).getMac();
            if (!TextUtils.isEmpty(deviceInfo.getMac()) && !TextUtils.isEmpty(mac) && mac.equalsIgnoreCase(deviceInfo.getMac())) {
              return;
            } else {

            }
          }
          LogUtils.d("ondeviceScaned" + deviceInfo.toString());
          scanedDevices.add(deviceInfo);
          //     deviceInfoInfos.add(deviceInfo);
          getProductInfoWithNoFleet(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
        } else {
          scanedDevices.add(deviceInfo);
          //  deviceInfoInfos.add(deviceInfo);
          getProductInfoWithNoFleet(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
        }

      }

      @Override
      public void scanTimeOut() {
        if (scanedDevices.size() == 0) {
          mBleDeviceMap.clear();
          ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
          if (nearByDevicesUIState == null) {
            nearByDevicesUIState = new ScanNearByDevicesUIState();
          }
          if (deviceInfoInfos.size() == 0) {
            nearByDevicesUIState.setDeviceFound(false);
            nearByDevicesUIState.setState(SCAN_DEVICE_TIME_OUT);
            if (scanedDevices.size() > 0) {
              nearByDevicesUIState.setState(CONNECT_FAIL);
              nearByDevicesUIState.setMessage(NET_WORK_EXCEPTION);
            }
          } else {
            nearByDevicesUIState.setDeviceFound(true);
          }

          nearByDevicesUIState.setDevices(deviceInfoInfos);
          liveData.postValue(nearByDevicesUIState);
        }
      }
    });


  }

  // TODO Z6

  private void scanDevicesOldZ6(BleDevice bleDevice, ArrayList<List<DeviceInfo>> deviceInfoInfos, ArrayList<DeviceInfo> scanedDevices, MutableLiveData<ScanNearByDevicesUIState> liveData) {
    if (bleDevice != null) {
      String bleName = bleDevice.getName();
      if (!TextUtils.isEmpty(bleName) && bleName.contains(Z6_HEAD) && bleName.length() == Z6_BLENAME_LENGTH) {
        String devieId = bleName.substring(2);


        //snCode
        String hexSnCodeStr = devieId.substring(2, 6);

        String header = hexString2String(hexSnCodeStr);
        String numberStr1 = z6DeviceIdAppendWithZero(devieId.substring(6, 8));


        String snCodeString = header + numberStr1;

        ConfigNetApi configNetApi = new ConfigNetApi();
        DeviceInfo deviceInfo = createDeviceInfo(bleDevice);
        deviceInfo.setSn(snCodeString);
        //     deviceInfo.setDeviceId(devieId);
        deviceInfo.setStatus(8);
        deviceInfo.setButtonClick(true);

        List<DeviceInfo> deviceInfos = mDeviceDao.getDevices();
        if (deviceInfos != null && deviceInfos.size() > 0) {
          for (int i = 0; i < deviceInfos.size(); i++) {
            String mac = deviceInfos.get(i).getMac();
            if (!TextUtils.isEmpty(deviceInfo.getMac()) && !TextUtils.isEmpty(mac) && mac.equalsIgnoreCase(deviceInfo.getMac())) {
              return;
            } else {

            }
          }
          LogUtils.d("ondeviceScaned" + deviceInfo.toString());
          scanedDevices.add(deviceInfo);
          //     deviceInfoInfos.add(deviceInfo);
          getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
        } else {
          scanedDevices.add(deviceInfo);
          //  deviceInfoInfos.add(deviceInfo);
          getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
        }
      }
    }
  }

  @Deprecated
  @Override
  public void scanDevices(MutableLiveData<ScanNearByDevicesUIState> liveData) {
    ArrayList<List<DeviceInfo>> deviceInfoInfos = new ArrayList<>();
    ArrayList<DeviceInfo> scanedDevices = new ArrayList<>();
    BluetoothConection.getInstance().scanConfig();
    BluetoothConection.getInstance().scanDevices(new BluetoothConection.BleScanDeviceCallBack() {
      @Override
      public void onScanStarted(boolean success) {
        LogUtils.d("onScanStarted");
      }

      @Override
      public void onScanning(BleDevice bledevice) {

        LogUtils.d("isMainThread_onScanning" + (Thread.currentThread() == Looper.getMainLooper().getThread()));
        //判断是否是 泉峰设备
        if (BluetoothConection.isEGO(bledevice.getScanRecord())) {
          ConfigNetApi configNetApi = new ConfigNetApi();
          String snCodeString = getSnCodeString(bledevice);
          boolean isDeviceClickButton = BluetoothConection.isEGOBroadcasting(bledevice.getScanRecord(), snCodeString);
          DeviceInfo deviceInfo = createDeviceInfo(bledevice);
          String stateString = getDeviceStateString(bledevice);
          if ("di".equalsIgnoreCase(stateString)) {
            deviceInfo.setStatus(8);
          }
          deviceInfo.setButtonClick(isDeviceClickButton);

          LogUtils.d("ondeviceScaned_snCodeString" + snCodeString + "mac" + deviceInfo.getMac() + "DeviceName" + deviceInfo.getDeviceName() + "snCodeString" + snCodeString);
          List<DeviceInfo> deviceInfos = mDeviceDao.getDevices();
          if (deviceInfos != null && deviceInfos.size() > 0) {
            for (int i = 0; i < deviceInfos.size(); i++) {
              String mac = deviceInfos.get(i).getMac();
              if (!TextUtils.isEmpty(deviceInfo.getMac()) && !TextUtils.isEmpty(mac) && mac.equalsIgnoreCase(deviceInfo.getMac())) {
                return;
              } else {

              }
            }
            LogUtils.d("ondeviceScaned" + deviceInfo.toString());
            scanedDevices.add(deviceInfo);
            //     deviceInfoInfos.add(deviceInfo);
            getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
          } else {
            scanedDevices.add(deviceInfo);
            //  deviceInfoInfos.add(deviceInfo);
            getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
          }


        }
      }

      @Override
      public void onScanFinished(List<BleDevice> scanResultList) {


        if (scanResultList.size() == 0) {
          BluetoothConection.getInstance().scanDevices(new BluetoothConection.BleScanDeviceCallBack() {
            @Override
            public void onScanStarted(boolean success) {
              LogUtils.d("onScanStarted");
            }

            @Override
            public void onScanning(BleDevice bledevice) {
              //判断是否是 泉峰设备
              if (BluetoothConection.isEGO(bledevice.getScanRecord())) {
                ConfigNetApi configNetApi = new ConfigNetApi();
                String snCodeString = getSnCodeString(bledevice);
                boolean isDeviceClickButton = BluetoothConection.isEGOBroadcasting(bledevice.getScanRecord(), snCodeString);
                DeviceInfo deviceInfo = createDeviceInfo(bledevice);
                String stateString = getDeviceStateString(bledevice);
                if ("di".equalsIgnoreCase(stateString)) {
                  deviceInfo.setStatus(8);
                }
                deviceInfo.setButtonClick(isDeviceClickButton);
                LogUtils.d("ondeviceScaned_snCodeString" + snCodeString + "mac" + deviceInfo.getMac() + "DeviceName" + deviceInfo.getDeviceName() + "snCodeString" + snCodeString);
                List<DeviceInfo> deviceInfos = mDeviceDao.getDevices();
                if (deviceInfos != null && deviceInfos.size() > 0) {
                  for (int i = 0; i < deviceInfos.size(); i++) {
                    String mac = deviceInfos.get(i).getMac();
                    if (!TextUtils.isEmpty(deviceInfo.getMac()) && !TextUtils.isEmpty(mac) && mac.equalsIgnoreCase(deviceInfo.getMac())) {
                      return;
                    } else {

                    }
                  }
                  LogUtils.d("ondeviceScaned" + deviceInfo.toString());
                  scanedDevices.add(deviceInfo);
                  //     deviceInfoInfos.add(deviceInfo);
                  getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
                } else {
                  scanedDevices.add(deviceInfo);
                  //  deviceInfoInfos.add(deviceInfo);
                  getProductInfo(configNetApi, deviceInfoInfos, snCodeString, deviceInfo, liveData);
                }


              }
            }

            @Override
            public void onScanFinished(List<BleDevice> scanResultList) {
              LogUtils.d("onScanFinished" + scanResultList.size());
              mBleDeviceMap.clear();
              ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
              if (nearByDevicesUIState == null) {
                nearByDevicesUIState = new ScanNearByDevicesUIState();
              }
              if (deviceInfoInfos.size() == 0) {
                nearByDevicesUIState.setDeviceFound(false);

                if (scanedDevices.size() > 0) {
                  nearByDevicesUIState.setState(CONNECT_FAIL);
                  nearByDevicesUIState.setMessage(NET_WORK_EXCEPTION);
                }
              } else {
                nearByDevicesUIState.setDeviceFound(true);
              }

              nearByDevicesUIState.setDevices(deviceInfoInfos);
              liveData.postValue(nearByDevicesUIState);
            }
          });
        } else {
          LogUtils.d("onScanFinished" + scanResultList.size() + "deviceInfoInfos.size()" + deviceInfoInfos.size());
          mBleDeviceMap.clear();
          ScanNearByDevicesUIState nearByDevicesUIState = liveData.getValue();
          if (nearByDevicesUIState == null) {
            nearByDevicesUIState = new ScanNearByDevicesUIState();
          }
          if (deviceInfoInfos.size() == 0) {
            nearByDevicesUIState.setDeviceFound(false);
            LogUtils.d("onScanFinished_deviceInfoInfos" + deviceInfoInfos.size() + "nearByDevicesUIState" + nearByDevicesUIState.getState());
            if (scanedDevices.size() > 0) {
              LogUtils.d("onScanFinished_scanedDevices" + scanedDevices.size());
              nearByDevicesUIState.setState(CONNECT_FAIL);
              //   nearByDevicesUIState.setMessage(NET_WORK_EXCEPTION);
            }
          } else {
            nearByDevicesUIState.setDeviceFound(true);
          }

          nearByDevicesUIState.setDevices(deviceInfoInfos);
          liveData.postValue(nearByDevicesUIState);
        }

      }
    });
  }

  /**
   * @param bledevice BleDevice
   * @return String
   * @method getSnCodeString
   * @description The data obtained through Bluetooth is then truncated into sncode strings
   * @date: $date$ $time$
   * @author: wangheng
   */
  private String getSnCodeString(BleDevice bledevice) {
    //截取设备sncode

    String snCode = BluetoothConection.getSNCode(bledevice.getScanRecord());
    if (TextUtils.isEmpty(snCode)) {
      snCode = "0";
    }
    //转bytes
    byte[] snCodeBytes = ConvertUtils.hexString2Bytes(snCode);
    //deviceId string
    String snCodeString = ConvertUtils
            .bytes2String(snCodeBytes)
            .replaceAll("\\n", "")
            .replaceAll("\\u0000", "");
    return snCodeString;
  }

  /**
   *
   * @param liveData
   * @param deviceInfo
   * @param deviceId 不为空时匹配deviceId
   */
  public void connectDeviceByBle2(MutableLiveData<ScanNearByDevicesUIState> liveData, DeviceInfo deviceInfo , String deviceId) {
    String mac_address_is_null = "mac address is null";
    if (TextUtils.isEmpty(deviceInfo.getMac())) {
      ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
      scanNearByDevicesUIState.setDeviceConnected(false);
      postLiveData(scanNearByDevicesUIState, liveData, CONNECT_FAIL);
      TracePoints.deviceConnectMacisNull(UserInfo.get().getEmail(), mac_address_is_null);
      return;
    }


    //手动断开连接并关机通讯通道
    if (mBleDevice != null) {
      try {

        BleManager.getInstance().disconnectAllDevice();
        BleManager.getInstance().stopNotify(mBleDevice, BluetoothConection.UUID_SERVICE, BluetoothConection.UUID_NOTIFI);
        Thread.sleep(300);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }

    try {
      BluetoothConection.getInstance().connectDevice(deviceInfo.getMac(), new BleGattCallback() {

        @Override
        public void onStartConnect() {
          LogUtils.iTag(TAG, "onStartConnect");
        }

        @Override
        public void onConnectFail(BleDevice bleDevice, BleException exception) {

          ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
          scanNearByDevicesUIState.setDeviceConnected(false);
          scanNearByDevicesUIState.setState(CONNECT_FAIL);
          LogUtils.iTag(TAG, "onConnectFail");
          Log.d("Connection_test", "onConnectFail" + CONNECT_FAIL);
          liveData.postValue(scanNearByDevicesUIState);
        }

        @Override
        public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
          LogUtils.iTag(TAG, "BLE connect success");
          TracePoints.bleConnectSuccess(UserInfo.get().getEmail(), new Gson().toJson(deviceInfo));

          if (checkProductType(deviceInfo, liveData)) {
            return;
          }


          subNotify(bleDevice, liveData, deviceInfo , deviceId);
        }

        @Override
        public void onDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {
          LogUtils.iTag(TAG, "onDisConnected");
          TracePoints.bleDisConnected(UserInfo.get().getEmail(), new Gson().toJson(deviceInfo));

        }
      });

    } catch (Exception e) {
      ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
      scanNearByDevicesUIState = liveData.getValue();
      scanNearByDevicesUIState.setDeviceConnected(false);
      postLiveData(scanNearByDevicesUIState, liveData, CONNECT_FAIL);
      Log.d("Connection_test", "Exception" + CONNECT_FAIL);
      TracePoints.bleConnectException(UserInfo.get().getEmail(), new Gson().toJson(deviceInfo),e.getMessage().toString());

      //   ProgressHelper.showProgressView(ActivityUtils.getTopActivity(), 0);
    }
  }

  @Override
  public void connectDeviceByBle(MutableLiveData<ScanNearByDevicesUIState> liveData, DeviceInfo deviceInfo) {

    if (TextUtils.isEmpty(deviceInfo.getMac())) {
      ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
      scanNearByDevicesUIState.setDeviceConnected(false);
      postLiveData(scanNearByDevicesUIState, liveData, CONNECT_FAIL);
      return;
    }
    LogUtils.iTag(TAG, "getMac——Connect" + deviceInfo.getMac());

    //手动断开连接并关机通讯通道
    if (mBleDevice != null) {
      try {

        BleManager.getInstance().disconnectAllDevice();
        BleManager.getInstance().stopNotify(mBleDevice, BluetoothConection.UUID_SERVICE, BluetoothConection.UUID_NOTIFI);
        Thread.sleep(300);
      } catch (InterruptedException e) {
        e.printStackTrace();
      }
    }

    try {
      BluetoothConection.getInstance().connectDevice(deviceInfo.getMac(), new BleGattCallback() {

        @Override
        public void onStartConnect() {
          LogUtils.iTag(TAG, "onStartConnect");
        }

        @Override
        public void onConnectFail(BleDevice bleDevice, BleException exception) {

          ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
          scanNearByDevicesUIState.setDeviceConnected(false);
          scanNearByDevicesUIState.setState(CONNECT_FAIL);
          LogUtils.iTag(TAG, "onConnectFail");
          Log.d("Connection_test", "onConnectFail" + CONNECT_FAIL);
          liveData.postValue(scanNearByDevicesUIState);
        }

        @Override
        public void onConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
          LogUtils.iTag(TAG, "BLE connect success");
          if (checkProductType(deviceInfo, liveData)) {
            return;
          }
          subNotify(bleDevice, liveData, deviceInfo ,"");
        }

        @Override
        public void onDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {
          LogUtils.iTag(TAG, "onDisConnected");

        }
      });

    } catch (Exception e) {
      ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
      scanNearByDevicesUIState = liveData.getValue();
      scanNearByDevicesUIState.setDeviceConnected(false);
      postLiveData(scanNearByDevicesUIState, liveData, CONNECT_FAIL);
      Log.d("Connection_test", "Exception" + CONNECT_FAIL);
      //   ProgressHelper.showProgressView(ActivityUtils.getTopActivity(), 0);
    }
  }

  private boolean checkProductType(DeviceInfo deviceInfo, MutableLiveData<ScanNearByDevicesUIState> liveData) {
    String netModes = deviceInfo.getCommunicateMode();
    // netModes=NET_MODE_WIFI;
    if (netModes != null) {
      if (NET_MODE_WIFI.equalsIgnoreCase(netModes)) {
        ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
        deviceInfo.setCommunicateMode(NET_MODE_WIFI);
        scanNearByDevicesUIState.setDeviceClicked(deviceInfo);
        if (deviceInfo.getStatus() == 8) {
          postLiveData(scanNearByDevicesUIState, liveData, CONNECT_SUCCESS);
        } else {
          postLiveData(scanNearByDevicesUIState, liveData, CONNECT_FAIL);
        }

        return false;
      }
    }
    return false;
  }

  /**
   *
   * @param bleDevice
   * @param liveData
   * @param deviceInfo
   * @param deviceId 不为空时匹配
   */
  private void subNotify(BleDevice bleDevice, MutableLiveData<ScanNearByDevicesUIState> liveData, DeviceInfo deviceInfo ,String deviceId) {

    TracePoints.bleSubNotify(UserInfo.get().getEmail(), new Gson().toJson(deviceInfo));

    //通知订阅
    BluetoothConection.getInstance().notifyDevice(
            bleDevice, "BlueToothConfigNetRepo",
            new BleNotifyCallback() {
              @Override
              public void onNotifySuccess() {
                TracePoints.onNotifySuccess(UserInfo.get().getEmail(), new Gson().toJson(deviceInfo));

                // 打开通知操作成功
                sendAuthCMD(bleDevice);

              }

              @Override
              public void onNotifyFailure(BleException exception) {
                // 打开通知操作失败

                ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
                scanNearByDevicesUIState.setState(CONNECT_FAIL);
                liveData.postValue(scanNearByDevicesUIState);
              }

              @Override
              public void onCharacteristicChanged(byte[] data) {
                // 打开通知后，设备发过来的数据将在这里出现
                String dataHexString = HexUtil.formatHexString(data).toUpperCase();

                String command = dataHexString.substring(BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length(),
                        BluetoothConection.DATA_COMMAND_END_INDEX);
                LogUtils.i("BlueToothConfigNetRepo---->", "onCharacteristicChanged-->" + command);


                //判断指令返回
                switch (command) {
                  //身份验证
                  case BluetoothConection.COMMAND_AUTHENTICATION:
                    TracePoints.getAuthSuccess(UserInfo.get().getEmail(),new Gson().toJson(deviceInfo));

                    sendDeviceIdCMD(bleDevice);

                    break;

                  //设备ID
                  case BluetoothConection.COMMAND_DEVICEID:
                    TracePoints.getDeviceSuccess(UserInfo.get().getEmail(),new Gson().toJson(bleDevice));

                    String deviceIDString = getDeviceIDString(dataHexString);
                    ScanNearByDevicesUIState scanNearByDevicesUIState = liveData.getValue();
                    //deviceId 不为空时 匹配deviceId
                    if (!TextUtils.isEmpty(deviceId) && !deviceId.equals(deviceIDString)) {
                      if(null != scanNearByDevicesUIState) {
                        //过滤
                        scanNearByDevicesUIState.addFilterMac(bleDevice.getMac().toUpperCase(Locale.ROOT));
                      }
                      postLiveData(scanNearByDevicesUIState, liveData, MATCH_DEVICE_ID_FAIL);
                      return;
                    }
                    DeviceInfo deviceInfo = setDeviceInfoData(deviceIDString, scanNearByDevicesUIState, bleDevice);
                    scanNearByDevicesUIState.setDeviceClicked(deviceInfo);

                    if (TextUtils.isEmpty(deviceInfo.getDeviceId())) {
                      scanNearByDevicesUIState.setWifiEvn(true);
                      postLiveData(scanNearByDevicesUIState, liveData, CONNECT_FAIL);
                      mBleDeviceMap.put(deviceIDString, bleDevice);
                    } else {
                      scanNearByDevicesUIState.setWifiEvn(true);

                      postLiveData(scanNearByDevicesUIState, liveData, CONNECT_SUCCESS);

                      mBleDeviceMap.put(deviceIDString, bleDevice);
                    }
                    //设备是否回复暂不处理
                    sendWIfiEvn(bleDevice);

                    break;


                  default:

                    break;
                }
              }

            });
  }

  private void sendAuthCMD(BleDevice bleDevice) {
    TracePoints.sendAuthCMD(UserInfo.get().getEmail(), new Gson().toJson(bleDevice));

    //获取身份认证
    BleManager.getInstance().write(bleDevice,
            BluetoothConection.UUID_SERVICE,
            BluetoothConection.UUID_WRITE,
            BluetoothConection.toCMD(BluetoothConection.COMMAND_AUTHENTICATION,
                    BluetoothConection.AUTHENTICATION_APP),
            new BleWriteCallback() {
              @Override
              public void onWriteSuccess(int current, int total, byte[] justWrite) {
                LogUtils.e("BlueToothConfigNetRepo---->onWriteSuccess", HexUtil.formatHexString(justWrite));
              }

              @Override
              public void onWriteFailure(BleException exception) {
                Log.d("Connection_test", "onWriteFailure" + CONNECT_FAIL);
              }
            });

    sendSTR = BluetoothConection.AUTHENTICATION_APP;
  }

  /**
   * 发送认证指令
   *
   * @param bleDevice
   */
  private void sendDeviceIdCMD(BleDevice bleDevice) {
    TracePoints.getDeviceCMD(UserInfo.get().getEmail(),new Gson().toJson(bleDevice));

    //设备ID指令写入
    BleManager.getInstance().write(bleDevice,
            BluetoothConection.UUID_SERVICE,
            BluetoothConection.UUID_WRITE, BluetoothConection.toCMD(BluetoothConection.COMMAND_DEVICEID, ""), new BleWriteCallback() {
              @Override
              public void onWriteSuccess(int current, int total, byte[] justWrite) {
                LogUtils.e("BlueToothConfigNetRepo---->onWriteSuccess", HexUtil.formatHexString(justWrite));

              }

              @Override
              public void onWriteFailure(BleException exception) {
                Log.d(TAG, "onWriteFailure" + CONNECT_FAIL);
              }
            });

    sendSTR = BluetoothConection.COMMAND_DEVICEID;
  }


  /**
   * 同步环境信息
   *
   * @param bleDevice
   */
  private void sendWIfiEvn(BleDevice bleDevice) {
    //------------------------------北美设备WIFI设备环境信息 校验当前环境合法性--------------------------------------------
    //WIFI设备环境信息 校验当前环境合法性
    if (BuildConfig.EVN.contains("na")) {
      //北美三个环境
      switch (BuildConfig.FLAVOR) {
        case EVN_SIT:
          wifi_evn = "01";
          break;
        case EVN_PRE:
          wifi_evn = "02";
          break;
        case EVN_PROD_NA:
          wifi_evn = "03";
          break;
        default:
          break;
      }

    } else {
      //欧洲三个环境
      switch (BuildConfig.FLAVOR) {
        case EVN_SIT_EU:
          wifi_evn = "01";
          break;
        case EVN_PRE_EU:
          wifi_evn = "05";
          break;
        case EVN_PROD_EU:
          wifi_evn = "06";
          break;
        default:
          break;
      }

    }
//------------------------------WIFI设备环境信息 校验当前环境合法性--------------------------------------------

    //WIFI环境指令写入
    BleManager.getInstance().write(bleDevice,
            BluetoothConection.UUID_SERVICE,
            BluetoothConection.UUID_WRITE,
            BluetoothConection.toCMD(BluetoothConection.COMMAND_SET_WIFO_EVN, wifi_evn),
            new BleWriteCallback() {
              @Override
              public void onWriteSuccess(int current, int total, byte[] justWrite) {
              }

              @Override
              public void onWriteFailure(BleException exception) {
                Log.d("Connection_test", "onWriteFailure" + CONNECT_FAIL);
              }
            });


  }

  public void subNotifyAndSyncWifiPassword(MutableLiveData<ScanNearByDevicesUIState> liveData, String ssid, String password) {
    BleDevice bleDevice = BluetoothConection.getInstance().bleDevice;
    BleDevice finalBleDevice = bleDevice;
    LogUtils.e("BlueToothConfigNetRepo---->", "SyncWifiPassword--ssid---" + ssid + "---password--" + password);

    Observer<String> observer = new Observer<String>() {
      @Override
      public void onSubscribe(Disposable d) {
      }

      @Override
      public void onNext(String s) {
        //用于升级--可忽略
        // sendAuthCMD(bleDevice);


        //TODO 正式版本
        //身份校验指令写入
        sendWIfiEvn(bleDevice);

      }

      @Override
      public void onError(Throwable e) {
      }

      @Override
      public void onComplete() {
      }
    };

    //通知订阅
    final ScanNearByDevicesUIState[] scanNearByDevicesUIState = new ScanNearByDevicesUIState[1];
    scanNearByDevicesUIState[0] = liveData.getValue();
    BluetoothConection.getInstance().notifyDevice(
            bleDevice, "BlueToothConfigNetRepo",
            new BleNotifyCallback() {
              @Override
              public void onNotifySuccess() {
                // 打开通知操作成功
                LogUtils.iTag(TAG, "nofify success");
                postLiveData(scanNearByDevicesUIState[0], liveData, WIFI_DEVICE_CONNECT_SUCCESS);
                observer.onNext("nofify success");
              }

              @Override
              public void onNotifyFailure(BleException exception) {
                postLiveData(scanNearByDevicesUIState[0], liveData, CONNECT_FAIL);
                // 打开通知操作失败
                LogUtils.iTag(TAG, "nofify fail" + exception.toString(), exception.getDescription());
                observer.onError(new Throwable(exception.getDescription()));
                Log.d("Connection_test", "onNotifyFailure");
              }

              @Override
              public void onCharacteristicChanged(byte[] data) {
                // 打开通知后，设备发过来的数据将在这里出现
                LogUtils.iTag(TAG, "ble data：" + HexUtil.formatHexString(data));

                String dataHexString = HexUtil.formatHexString(data).toUpperCase();

                String command = dataHexString
                        .substring(
                                BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length(),
                                BluetoothConection.DATA_COMMAND_END_INDEX
                        );
                //判断指令返回
                switch (command) {

                  //身份验证
                  case BluetoothConection.COMMAND_AUTHENTICATION:


                    //设备ID指令写入

                    sendDeviceIdCMD(bleDevice);
                    break;


                  case BluetoothConection.COMMAND_SET_WIFO_EVN:


                    //设备ID指令写入
                    BleManager.getInstance().write(finalBleDevice,
                            BluetoothConection.UUID_SERVICE,
                            BluetoothConection.UUID_WRITE, BluetoothConection.toCMD(BluetoothConection.COMMAND_DEVICEID, ""), new BleWriteCallback() {
                              @Override
                              public void onWriteSuccess(int current, int total, byte[] justWrite) {
                                LogUtils.iTag(TAG, "DeviceID WRITE SUCCESS");
                              }

                              @Override
                              public void onWriteFailure(BleException exception) {
                                Log.d(TAG, "onWriteFailure" + CONNECT_FAIL);
                              }
                            });

                    break;

                  //设备ID
                  case BluetoothConection.COMMAND_DEVICEID:
                    String deviceIDString = getDeviceIDString(dataHexString);


                    DeviceInfo deviceInfo = setDeviceInfoData(deviceIDString, scanNearByDevicesUIState[0], finalBleDevice);
                    scanNearByDevicesUIState[0].setDeviceClicked(deviceInfo);
                    LogUtils.d("ScanNearbyDevicesFragment" + "deviceIDString" + deviceIDString + "mac" + deviceInfo.getMac());
                    StringBuilder wifiInfo = new StringBuilder();
                    try {
                      //做SSID及PASSWORD判断 7月26日 hyman
                      if (TextUtils.isEmpty(ssid) || TextUtils.isEmpty(password)) {
                        return;
                      }
                      String ssidHexStr = BluetoothConection.addZeroForNumRight(ConvertUtils.bytes2HexString(ssid.getBytes("UTF-8")), 64);
                      String passwordHexStr = BluetoothConection.addZeroForNumRight(ConvertUtils.bytes2HexString(password.getBytes("UTF-8")), 64);
                      wifiInfo.append(ssidHexStr).append(passwordHexStr);
                    } catch (UnsupportedEncodingException e) {
                      e.printStackTrace();
                    }

                    BleManager.getInstance().write(finalBleDevice,
                            BluetoothConection.UUID_SERVICE,
                            BluetoothConection.UUID_WRITE, BluetoothConection.toCMD(BluetoothConection.COMMAND_SYNC_WIFI_INFO,
                                    wifiInfo.toString()),
                            new BleWriteCallback() {
                              @Override
                              public void onWriteSuccess(int current, int total, byte[] justWrite) {
                                LogUtils.iTag(TAG, "DeviceID WRITE SUCCESS");
                              }

                              @Override
                              public void onWriteFailure(BleException exception) {
                                Log.d(TAG, "onWriteFailure" + CONNECT_FAIL);
                              }
                            });


                    LogUtils.iTag(TAG, "COMMAND_SYNC_WIFI_INFO_Commod" + wifiInfo.toString());
                    break;

                  case BluetoothConection.COMMAND_SYNC_WIFI_INFO:
                    LogUtils.iTag(TAG, "COMMAND_SYNC_WIFI_INFO" + dataHexString);
                    try {
                      String syncReturnStr = dataHexString
                              .substring(
                                      BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length() + 6,
                                      BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length() + BluetoothConection.DATA_COMMAND_END_INDEX
                              );

                      if ("00".equalsIgnoreCase(syncReturnStr)) {
                        postLiveData(scanNearByDevicesUIState[0], liveData, SYNC_SUCCESS);

                      } else {
                        postLiveData(scanNearByDevicesUIState[0], liveData, CONNECT_FAIL);

                      }
                    } catch (Exception e) {
                      LogUtils.d(e.getMessage());
                      postLiveData(scanNearByDevicesUIState[0], liveData, CONNECT_FAIL);
                    }

                    break;


                  default:
                    break;
                }
              }

            });
  }


  private String getDeviceIDString(String dataHexString) {

    String deviceIDString = "";
    try {
      //截取数据内容
      String deviceID = dataHexString.substring(
              14,
              dataHexString.length() - BluetoothConection.DATA_CRC_LENGTH);
      //转bytes
      byte[] deviceIDBytes = ConvertUtils.hexString2Bytes(deviceID);
      //deviceId string
      deviceIDString = ConvertUtils
              .bytes2String(deviceIDBytes)
              .replaceAll("\\n", "")
              .replaceAll("\\u0000", "");
    } catch (Exception e) {

    }

    return deviceIDString;
  }

  @NonNull
  private DeviceInfo setDeviceInfoData(String deviceIDString, ScanNearByDevicesUIState scanNearByDevicesUIState, BleDevice bleDevice) {
    DeviceInfo deviceInfo = scanNearByDevicesUIState.getDeviceClicked();
    if (!TextUtils.isEmpty(deviceIDString)){
      deviceInfo.setDeviceId(deviceIDString);
    }
    return deviceInfo;
  }

  private void postLiveData(ScanNearByDevicesUIState scanNearByDevicesUIState, MutableLiveData<ScanNearByDevicesUIState> liveData, int connectSuccess) {
    scanNearByDevicesUIState.setState(connectSuccess);
    liveData.postValue(scanNearByDevicesUIState);
  }

  public void bindingDevice(String deviceIDString, DeviceInfo deviceInfo, ScanNearByDevicesUIState finalScanNearByDevicesUIState, MutableLiveData<ScanNearByDevicesUIState> liveData) {
    Log.d("Connection_test", "in_bindingDevice");
    String BIND_SUCCESS_STRING = "bind_success_string";
    String BIND_UNKNOW_EXCEPTION = "bind_unknow_exception";

    TracePoints.bleBindWithNet(UserInfo.get().getEmail(),deviceIDString);

    new ConfigNetApi().addDevice(deviceIDString, deviceInfo.getMac()).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<BindingEntry>>() {
              @Override
              public void onNext(HttpResponse<BindingEntry> httpResponse) {
                if (httpResponse.status) {
                  finalScanNearByDevicesUIState.setDeviceBinding(true);
                  finalScanNearByDevicesUIState.setState(BIND_SUCCESS);
                  finalScanNearByDevicesUIState.setDeviceInfoRegistered(httpResponse.response.getIsDeviceInfoRegistered());
                  finalScanNearByDevicesUIState.setDeviceClicked(deviceInfo);
                  Log.d("Connection_test", "HttpResponseBIND_SUCCESS" + BIND_SUCCESS);
                  LogUtils.d(TAG, "showBindSuccess_bindingDevice" + httpResponse.response.getIsDeviceInfoRegistered());
                  TracePoints.bleBindWithNetSuccess(UserInfo.get().getEmail(),deviceIDString,BIND_SUCCESS_STRING);


                } else if(MAIN_USER_ALREADY_BIND_RESPONSE_CODE.equalsIgnoreCase(httpResponse.responseCode)){
                  //主账号已绑定
                  finalScanNearByDevicesUIState.setState(MAIN_USER_ALREADY_BIND_FAIL);
                  if (!TextUtils.isEmpty(httpResponse.message)) {
                    TracePoints.bleBindWithNetError(UserInfo.get().getEmail(),deviceIDString,httpResponse.message);
                  }
                  finalScanNearByDevicesUIState.setMessage(httpResponse.message);
                  liveData.postValue(finalScanNearByDevicesUIState);
                }else {
                  finalScanNearByDevicesUIState.setState(CONNECT_FAIL);
                  if (!TextUtils.isEmpty(httpResponse.message)) {
                    finalScanNearByDevicesUIState.setMessage(httpResponse.message);
                    TracePoints.bleBindWithNetError(UserInfo.get().getEmail(),deviceIDString,httpResponse.message);
                  }else {
                    TracePoints.bleBindWithNetError(UserInfo.get().getEmail(),deviceIDString,BIND_UNKNOW_EXCEPTION);
                  }
                  Log.d("Connection_test", "HttpResponseCONNECT_FAIL" + CONNECT_FAIL + httpResponse.message);
                }
                finalScanNearByDevicesUIState.setMessage(httpResponse.message);
                liveData.postValue(finalScanNearByDevicesUIState);

              }

              @Override
              public void onError(Throwable e) {
                ProgressHelper.hideProgressView(ActivityUtils.getTopActivity());
                postLiveData(finalScanNearByDevicesUIState, liveData, CONNECT_FAIL);

                Log.d("Connection_test", "HttpResponseonError" + CONNECT_FAIL);
              }

              @Override
              public void onComplete() {
                ProgressHelper.hideProgressView(ActivityUtils.getTopActivity());

              }
            });
  }


  public void bindingDeviceBySn(String sn, String mac, MutableLiveData<ManualSNUIState> liveData) {

    new ConfigNetApi().bindDeviceWithSn(sn, mac).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<BindingEntry>>() {
              @Override
              public void onNext(HttpResponse<BindingEntry> httpResponse) {
                ManualSNUIState manualSNUIState = liveData.getValue();
                if (manualSNUIState == null) {
                  manualSNUIState = new ManualSNUIState();
                }
                if (httpResponse.status) {

                  DeviceInfo deviceInfo = manualSNUIState.getDeviceInfo();
                  if (null != deviceInfo) {
                    deviceInfo.setDeviceId(httpResponse.response.getDeviceId());
                  }

                  manualSNUIState.setState(BIND_SUCCESS);
                  manualSNUIState.setDeviceInfoRegistered(httpResponse.response.getIsDeviceInfoRegistered());

                } else {
                  manualSNUIState.setState(CONNECT_FAIL);
                }
                manualSNUIState.setMessage(httpResponse.message);
                liveData.postValue(manualSNUIState);

              }

              @Override
              public void onError(Throwable e) {
                ProgressHelper.hideProgressView(ActivityUtils.getTopActivity());
                Log.d("Connection_test", "HttpResponseonError" + CONNECT_FAIL);
              }

              @Override
              public void onComplete() {
                ProgressHelper.hideProgressView(ActivityUtils.getTopActivity());

              }
            });
  }


  public void bindingDeviceBySn2(String sn, String mac, MutableLiveData<NoIotResultUIState> liveData) {

    new ConfigNetApi().bindDeviceWithSn(sn, mac).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<BindingEntry>>() {
              @Override
              public void onNext(HttpResponse<BindingEntry> httpResponse) {
                NoIotResultUIState noIotResultUIState = liveData.getValue();
                if (noIotResultUIState == null) {
                  noIotResultUIState = new NoIotResultUIState();
                }
                if (httpResponse.status) {

                  noIotResultUIState.setState(BIND_SUCCESS);
                  noIotResultUIState.setAdded(true);
                  noIotResultUIState.setRegistered(httpResponse.response.getIsDeviceInfoRegistered());

                } else {
                  noIotResultUIState.setState(CONNECT_FAIL);
                }
                noIotResultUIState.setMessage(httpResponse.message);
                liveData.postValue(noIotResultUIState);

              }

              @Override
              public void onError(Throwable e) {
                ProgressHelper.hideProgressView(ActivityUtils.getTopActivity());
                Log.d("Connection_test", "HttpResponseonError" + CONNECT_FAIL);
              }

              @Override
              public void onComplete() {
                ProgressHelper.hideProgressView(ActivityUtils.getTopActivity());

              }
            });
  }

  @Override
  public void initBlueTooth(Application application) {
    BluetoothConection.getInstance().startBluetooth(application);
  }

  @Override
  public void getProducts(MutableLiveData<DeviceNotFoundUIState> liveData) {
    //   ProductsParams params = new ProductsParams(1, 20, "", 3, 2, 1);
    ProductsParams params = new ProductsParams(1, 300);
    new ConfigNetApi().getProducts(params).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductEntry>>() {
              @Override
              public void onNext(HttpResponse<ProductEntry> listHttpResponse) {
                DeviceNotFoundUIState deviceNotFoundUIState = liveData.getValue();
                if (deviceNotFoundUIState == null) {
                  deviceNotFoundUIState = new DeviceNotFoundUIState();
                }

                if (listHttpResponse.status) {
                  ProductInfo[] productInfos = listHttpResponse.response.getList();
                  if (productInfos != null) {
                    deviceNotFoundUIState.setProducts(Arrays.asList(productInfos));
                  }
                  liveData.postValue(deviceNotFoundUIState);
                  SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                    for (int i = 0; productInfos != null && i < productInfos.length; i++) {
                      ProductInfo productInfo = mProductDao.getProductInfo(productInfos[i].getProductSnCode());
                      if (productInfo != null) {
                        productInfos[i].setHistory(productInfo.getHistory());
                        productInfos[i].setHistoryTime(productInfo.getHistoryTime());
                      }
                      if (productInfos[i] != null && !TextUtils.isEmpty(productInfos[i].getId())) {
                        mProductDao.insert(productInfos[i]);
                      }


                    }
                    mProductDao.deleteAll();
                    for (int i = 0; productInfos != null && i < productInfos.length; i++) {

                      if (productInfos[i] != null && !TextUtils.isEmpty(productInfos[i].getId())) {
                        mProductDao.insert(productInfos[i]);
                      }

                    }
                  });
                } else {
                  List<ProductInfo> list = mProductDao.getProducts();
                  if (list != null) {
                    deviceNotFoundUIState.setProducts(list);
                    liveData.postValue(deviceNotFoundUIState);
                  }

                }
              }

              @Override
              public void onError(Throwable throwable) {
                DeviceNotFoundUIState deviceNotFoundUIState = liveData.getValue();
                if (deviceNotFoundUIState == null) {
                  deviceNotFoundUIState = new DeviceNotFoundUIState();
                }
                List<ProductInfo> list = mProductDao.getProducts();
                if (list != null) {
                  deviceNotFoundUIState.setProducts(list);
                  liveData.postValue(deviceNotFoundUIState);
                }
              }

              @Override
              public void onComplete() {
                liveData.getValue();
              }
            });
  }

  public void checkProductisExist(MutableLiveData<ManualSNUIState> liveData, String sn) {

  }


  public void getDeviceInfoBySn(MutableLiveData<ManualSNUIState> liveData, String sn) {

    //判断sn是否未空
    if (TextUtils.isEmpty(sn)) {

      ManualSNUIState value = liveData.getValue();

      if (value == null) {
        value = new ManualSNUIState();
      }

      value.setState(EDIT_CONTENT_IS_NULL);

      liveData.postValue(value);

      return;
    }


    new ConfigNetApi().getDetailBySn(sn).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<DeviceInfo>>() {
              @Override
              public void onNext(HttpResponse<DeviceInfo> deviceDetailHttpResponse) {
                LogUtils.i("lesly sncode3 =   deviceDetailHttpResponse = " + deviceDetailHttpResponse);
                ManualSNUIState value = liveData.getValue();
                if (value == null) {
                  value = new ManualSNUIState();
                }
                if (deviceDetailHttpResponse.status) {
                  DeviceInfo deviceInfo = deviceDetailHttpResponse.response;
                  deviceInfo.setSn(sn.toUpperCase());
                  value.setDeviceInfo(deviceInfo);
                  value.setState(REGISTER_SCAN_DEVICE_SUCCESS);

                } else {
                  value.setState(RESPONSE_FAIL);
                  value.setMessage(deviceDetailHttpResponse.message);
                }

                liveData.postValue(value);
              }

              @Override
              public void onError(Throwable e) {
                LogUtils.eTag(TAG, e.getMessage(), "error");
              }

              @Override
              public void onComplete() {

              }
            });


  }


  @Override
  public void getProductBySn(MutableLiveData<ManualSNUIState> liveData, String sn) {

    //判断sn是否未空
    if (TextUtils.isEmpty(sn)) {

      ManualSNUIState value = liveData.getValue();

      if (value == null) {
        value = new ManualSNUIState();
      }

      value.setState(EDIT_CONTENT_IS_NULL);

      liveData.postValue(value);

      return;
    }

    //执行业务接口
    new ConfigNetApi().getProductBySn(sn).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<SnScanEntry>>() {
              @Override
              public void onNext(HttpResponse<SnScanEntry> deviceInfoHttpResponse) {
                LogUtils.i("lesly", "lesly sncode2   deviceInfoHttpResponse = " + deviceInfoHttpResponse);
                ManualSNUIState value = liveData.getValue();
                if (value == null) {
                  value = new ManualSNUIState();
                }
                value.setSn(sn);
                if (deviceInfoHttpResponse.status) {

                  String pid = deviceInfoHttpResponse.response.getInfo();


                  new ConfigNetApi().getProductDetailByPid(pid).subscribeOn(Schedulers.io())
                          .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductInfo>>() {
                            @Override
                            public void onNext(HttpResponse<ProductInfo> deviceDetailHttpResponse) {
                              LogUtils.i("lesly sncode3 =   deviceDetailHttpResponse = " + deviceDetailHttpResponse);
                              ManualSNUIState value = liveData.getValue();
                              if (value == null) {
                                value = new ManualSNUIState();
                              }
                              if (deviceDetailHttpResponse.status) {
                                ProductInfo productInfo = deviceDetailHttpResponse.response;

                                if (!isEGOProduct(productInfo.getBusinessType())) {
                                  ToastUtils.showLong(LanguageStrings.app_product_only_available_ego_fleet_app());
                                  return;
                                }

                                DeviceInfo deviceInfo = new DeviceInfo();

                                deviceInfo.setSn(sn.toUpperCase());

                                if (productInfo.getProductIcon() != null) {
                                  deviceInfo.setIconUrl(productInfo.getProductIcon());
                                }
                                deviceInfo.setProductId(productInfo.getId());
                                deviceInfo.setProductType(productInfo.getProductType());
                                deviceInfo.setCommunicateMode(productInfo.getNetworkModes());
                                deviceInfo.setCommodityModel(productInfo.getCommodityModel());
                                deviceInfo.setNickName(productInfo.getCommodityModel());
                                value.setDeviceInfo(deviceInfo);
                                value.setState(RESPONSE_SUCCESS);

                              } else {
                                value.setState(RESPONSE_FAIL);
                                value.setMessage(deviceDetailHttpResponse.message);
                              }

                              liveData.postValue(value);
                            }

                            @Override
                            public void onError(Throwable e) {
                              LogUtils.eTag(TAG, e.getMessage(), "error");
                            }

                            @Override
                            public void onComplete() {

                            }
                          });

                } else {
                  value.setState(RESPONSE_FAIL);

                  value.setMessage(deviceInfoHttpResponse.message);
                }

                liveData.postValue(value);
              }

              @Override
              public void onError(Throwable e) {
                LogUtils.i("lesly sncode2 error e = " + e.getMessage());
                liveData.getValue();
              }

              @Override
              public void onComplete() {
                LogUtils.i("lesly sncode2 onComplete ");
                liveData.getValue();
              }
            });
  }


  public void getProductBySn2(MutableLiveData<NoIotResultUIState> liveData, String sn) {


    //执行业务接口
    new ConfigNetApi().getProductBySn(sn).subscribeOn(Schedulers.io())
            .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<SnScanEntry>>() {
              @Override
              public void onNext(HttpResponse<SnScanEntry> deviceInfoHttpResponse) {
                LogUtils.i("lesly", "lesly sncode2   deviceInfoHttpResponse = " + deviceInfoHttpResponse);
                NoIotResultUIState value = liveData.getValue();
                if (value == null) {
                  value = new NoIotResultUIState();
                }

                if (deviceInfoHttpResponse.status) {
                  //  String prodctid = deviceInfoHttpResponse.response.getInfo();
                  String snCode = "";
                  if (sn.length() == 15) { //总长度15
                    snCode = sn.substring(1, 5);
                  } else {  //总长度16
                    snCode = sn.substring(2, 6);
                  }
                  new ConfigNetApi().getProductDetail(snCode).subscribeOn(Schedulers.io())
                          .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse<ProductInfo>>() {
                            @Override
                            public void onNext(HttpResponse<ProductInfo> deviceDetailHttpResponse) {
                              LogUtils.i("lesly sncode3 =   deviceDetailHttpResponse = " + deviceDetailHttpResponse);
                              NoIotResultUIState value = liveData.getValue();
                              if (value == null) {
                                value = new NoIotResultUIState();
                              }
                              if (deviceDetailHttpResponse.status) {
                                DeviceInfo deviceInfo = new DeviceInfo();
                                deviceInfo.setSn(sn);
                                ProductInfo productInfo = deviceDetailHttpResponse.response;
                                if (productInfo.getProductIcon() != null) {
                                  deviceInfo.setIconUrl(productInfo.getProductIcon());
                                }
                                deviceInfo.setProductId(productInfo.getId());
                                deviceInfo.setProductType(productInfo.getProductType());
                                deviceInfo.setCommunicateMode(productInfo.getNetworkModes());
                                deviceInfo.setNickName(productInfo.getCommodityModel());
                                value.setDeviceInfo(deviceInfo);
                                value.setState(RESPONSE_SUCCESS);

                              } else {
                                value.setState(RESPONSE_FAIL);
                                value.setMessage(deviceDetailHttpResponse.message);
                              }

                              liveData.postValue(value);
                            }

                            @Override
                            public void onError(Throwable e) {
                              LogUtils.eTag(TAG, e.getMessage(), "error");
                            }

                            @Override
                            public void onComplete() {

                            }
                          });

                } else {
                  value.setState(RESPONSE_FAIL);
                  value.setMessage(deviceInfoHttpResponse.message);
                }

                liveData.postValue(value);
              }

              @Override
              public void onError(Throwable e) {
                LogUtils.i("lesly sncode2 error e = " + e.getMessage());
                liveData.getValue();
              }

              @Override
              public void onComplete() {
                LogUtils.i("lesly sncode2 onComplete ");
                liveData.getValue();
              }
            });
  }

  public void searchDevice(MutableLiveData<DeviceSearchUIState> liveData, String str) {
    List<ProductInfo> list = new ArrayList<>();
    if (!TextUtils.isEmpty(str)) {
      list = mProductDao.searchDevicesByName(str);
    }
    DeviceSearchUIState deviceSearchUIState = liveData.getValue();
    if (deviceSearchUIState == null) {
      deviceSearchUIState = new DeviceSearchUIState();

    }
    if (list != null) {
      deviceSearchUIState.setCurrentSearchDevices(list);
      liveData.postValue(deviceSearchUIState);
    }

  }

  public void clearHistorySearch(MutableLiveData<DeviceSearchUIState> mLiveData) {
    mProductDao.clearHistoryDevices();
    DeviceSearchUIState deviceSearchUIState = mLiveData.getValue();
    if (deviceSearchUIState != null) {
      deviceSearchUIState.setHistorySearchDevices(new ArrayList<ProductInfo>());
      mLiveData.postValue(deviceSearchUIState);
    }
  }

  public void getHistoryDevice(MutableLiveData<DeviceSearchUIState> liveData) {
    List<ProductInfo> list = mProductDao.getHistoryDevices();
    DeviceSearchUIState deviceSearchUIState = liveData.getValue();
    if (deviceSearchUIState == null) {
      deviceSearchUIState = new DeviceSearchUIState();
    }

    deviceSearchUIState.setHistorySearchDevices(list);
    liveData.postValue(deviceSearchUIState);


  }

  public void saveSearchToHistory(List<ProductInfo> productInfos) {
    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
      for (int i = 0; productInfos != null && i < productInfos.size(); i++) {
        productInfos.get(i).setHistory(1);
        mProductDao.updateProduct(productInfos.get(i));
      }
    });

  }


  public void saveSearchClickToHistory(ProductInfo productInfos) {
    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
      productInfos.setHistory(1);
      productInfos.setHistoryTime(System.currentTimeMillis());
      mProductDao.updateProduct(productInfos);
    });

  }


  public void disConnectBluetooth() {
    BluetoothConection.getInstance().disConnectDevice();
    LogUtils.d("BluetoothConection.getInstance().disConnectDevice()_disConnectBluetooth");
  }

  public void inSertDevice(DeviceInfo deviceInfo) {
    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
      mDeviceDao.insert(deviceInfo);
    });
  }


  public String getUUID(ScanResult result) {
    String UUIDx = UUID
            .nameUUIDFromBytes(result.getScanRecord().getBytes()).toString();
    return UUIDx;
  }

  public boolean isSnExist(String sn) {
    try {
      List<DeviceInfo> infoList = mDeviceDao.getDevices();
      if (infoList != null) {
        for (int i = 0; i < infoList.size(); i++) {
          if (sn.equalsIgnoreCase(infoList.get(i).getSn())) {
            return true;
          }
        }

      }
    } catch (Exception e) {
      return false;
    }

    return false;
  }

  public void addNotIotDevice(MutableLiveData<NoIotResultUIState> liveData, NoIotResultUIState state) {

    if (state.isAdded) {
      state.setState(NoIotResultFragment.ADD_IOT_DONE);
    } else {
      state.setState(NoIotResultFragment.RETRY_ADD);
    }
    liveData.postValue(state);
  }


  public void addNotIotRegister(MutableLiveData<NoIotResultUIState> liveData, NoIotResultUIState state) {

    state.setState(NoIotResultFragment.ADD_IOT_REGISTER);

    liveData.postValue(state);
  }


  /**
   * 判断是否是fleet产品
   *
   * @param list
   * @return
   */
  private boolean isEGOProduct(List<Integer> list) {
    // 1 ego 2 fleet
    boolean isEGOProduct = false;
    List<Integer> businessType = list;
    if (null != businessType) {
      for (Integer integer : businessType) {
        if (integer == EGO_PRODUCT_CODE) {
          isEGOProduct = true;
        }
      }
    } else {
      isEGOProduct = true;
    }
    return isEGOProduct;
  }
}
