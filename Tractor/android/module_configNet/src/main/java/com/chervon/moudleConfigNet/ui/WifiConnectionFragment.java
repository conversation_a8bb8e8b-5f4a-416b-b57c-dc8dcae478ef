package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.BIND_SUCCESS;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_SUCCESS;
import static com.chervon.moudleConfigNet.ui.WifiSelectFragment.WIFI_INFO;

import android.annotation.SuppressLint;
import android.bluetooth.le.ScanResult;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.amazonaws.mobileconnectors.iot.AWSIotMqttClientStatusCallback;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.APPConstants;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;

import com.chervon.moudleConfigNet.data.model.WifiInfoEntity;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentWifiConnectionBinding;
import com.chervon.moudleConfigNet.ui.state.ScanNearByDevicesUIState;
import com.chervon.moudleConfigNet.ui.state.WifiInputUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.DevicesConnectionViewModel;
import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleScanState;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.UUID;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: WifiConnectionFragment
 * @Author: WifiConnectionFragment
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/7/3
 * @UpdateRemark: 区分欧洲CRM同步设备
 * @Version: 1.1
 */
public class WifiConnectionFragment extends BaseEnhanceFragment<DevicesConnectionViewModel, MoudleConfignetFragmentWifiConnectionBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "WifiConnectionFragment";
  public static final int SYNC_SUCCESS = 22;
  public static final int WIFI_DEVICE_CONNECT_SUCCESS = 26;
  public static final int WIFI_DEVICE_ONLINE = 27;
  private static final int AWS_CONNECT_STATUS = 28;
  private static final int BINDING_FAIL = 25;
  private CountDownTimer mDownTimer;
  private DeviceInfo deviceInfo;
  private int from = 0;
  private WifiInputUIState wifiInputUIState;
  boolean isScaned = false;
  private static final long BINDING_TIME_OUT = 1_000 * 120;
  private boolean isBindingTimeOut = false;

  public static final String PARENT_ACTIVITY_NAME = "com.chervon.moudleConfigNet.ui.ConfigNetActivity";
  private Runnable mBindingTimeOutCallBack = new Runnable() {
    @Override
    public void run() {
      LogUtils.d("showFailPage_mBindingTimeOutCallBack");
      showConncetionFail();
      isBindingTimeOut = true;
    }
  };


  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    initPageResouce();
    setTitle(LanguageStrings.app_networkconfigwificonnect_title_textview_text());
    Bundle bundle = getArguments();
    if (bundle != null) {
      from = bundle.getInt(KEY_PREV_FRAGMENT);
      deviceInfo = (DeviceInfo) bundle.getSerializable(PARAMETER_DEVICD);
      wifiInputUIState = (WifiInputUIState) bundle.getSerializable(WIFI_INFO);
    }
    if (deviceInfo == null) {
      deviceInfo = new DeviceInfo();
    }
    initDownTimer();
    showStepOne();
    liveDatasObserver();

    subDeviceInfo();

  }


  private void subDeviceInfo() {
    //超时后禁止重新配网
    AwsMqttService.connectAwsLiveData.observe(this, new Observer<AWSIotMqttClientStatusCallback.AWSIotMqttClientStatus>() {
      @Override
      public void onChanged(AWSIotMqttClientStatusCallback.AWSIotMqttClientStatus status) {

        if (AWSIotMqttClientStatusCallback.AWSIotMqttClientStatus.Connected == status) {
          startBleConnect();
        } else {
          AwsMqttService.getInstance().reConnect();
        }
      }
    });


  }

  /**
   * 在保证AWS正常工作下开启连接
   */
  private void startBleConnect() {
    if (BluetoothConection.getInstance().isBlceRealConnected(deviceInfo.getMac())) {
      BleManager.getInstance().isConnected(deviceInfo.getMac());
      gotoSyncWifiInfo();
    } else {
      LogUtils.d(TAG + "connectDeviceByBle");
      mViewModel.connectDeviceByBle(deviceInfo,"");
    }

    if (!EventBus.getDefault().isRegistered(this)){
      EventBus.getDefault().register(this);
    }


    if (null != deviceInfo) {
      if (!TextUtils.isEmpty(deviceInfo.getDeviceId())) {
        APPConstants.wifi_device_topic_on_line_topic = String.format(AwsMqttService.WIFI_DEVICE_ON_LINE, deviceInfo.getDeviceId());
        AwsMqttService.subscribeTopic(APPConstants.wifi_device_topic_on_line_topic);
      }
    }
  }


  @Subscribe(threadMode = ThreadMode.MAIN)
  public void onDataChangeEvent(IotModelEvent iotModelEvent) {
    //wifi设备离线通知

    if (!isAdded()){
      return;
    }

    if (iotModelEvent.topic.equals(APPConstants.wifi_device_topic_on_line_topic)) {
      LogUtils.e(TAG, "WIFI_DEVICE_ON_LINE");
      ScanNearByDevicesUIState value = mViewModel.mLiveData.getValue();
      if (null != value) {
        if (getActivity().getClass().getName().equals(PARENT_ACTIVITY_NAME)) {
          value.setState(WIFI_DEVICE_ONLINE);
          LogUtils.d(TAG, "showBindSuccess_onDataChangeEvent_isDeviceInfoRegistered"+mViewModel.mLiveData.getValue().isDeviceInfoRegistered());
          mViewModel.mLiveData.postValue(value);
          //  EventBus.getDefault().post(new DeviceOnlineEvent());
        }
      }
    }
  }


  private void gotoSyncWifiInfo() {
    String ssid = wifiInputUIState.getWifiName();
    //路由密码
    String password = wifiInputUIState.getPassword();
    mViewModel.syncWifiInfoByBle(deviceInfo, ssid, password);
  }


  private void liveDatasObserver() {
    mViewModel.mLiveData.postValue(new ScanNearByDevicesUIState());
    mViewModel.mLiveData.observe(this, new Observer<ScanNearByDevicesUIState>() {
      @Override
      public void onChanged(ScanNearByDevicesUIState uiState) {
        if (uiState.getState() == WIFI_DEVICE_CONNECT_SUCCESS) {
          showStepTwo();
          sendWifiConnectSuccessTrace();

        } else if (uiState.getState() == SYNC_SUCCESS) {
          LogUtils.e(TAG, "device sync success");
        } else if (uiState.getState() == WIFI_DEVICE_ONLINE) {
          showStepThree();
          if (((ConfigNetActivity) getActivity()).getIsResetWifi() == 1) {
            mViewDataBinding.btnRtry.removeCallbacks(mBindingTimeOutCallBack);
            isBindingTimeOut = false;
            showStepforth();
          } else {
            mViewModel.bindingDevice(uiState.getDeviceClicked());
          }
        } else if (uiState.getState() == BIND_SUCCESS && !isBindingTimeOut) {
          mViewDataBinding.btnRtry.removeCallbacks(mBindingTimeOutCallBack);
          if(null != mViewModel.mLiveData.getValue()) {
            deviceInfo.setMac(mViewModel.mLiveData.getValue().getDeviceClicked().getMac());
          }
          isBindingTimeOut = false;
          showBindSuccess();
          sendTraceBindSuccess(deviceInfo);
        } else if (uiState.getState() == CONNECT_SUCCESS) {
          LogUtils.d(TAG, "CONNECT_SUCCESS_gotobindingDevice");
          gotoSyncWifiInfo();
        } else if (uiState.getState() == BINDING_FAIL || uiState.getState() == CONNECT_FAIL) {

          LogUtils.d(TAG + "uiState.getState() " + uiState.getState());
          if (uiState.getState() == CONNECT_FAIL) {
            sendClickTrace(WifiConnectionFragment.this.getContext(), mouduleId, "38", pageResouce, "5", "");
          }
          if (!TextUtils.isEmpty(uiState.getMessage())){
            ToastUtils.showShort(uiState.getMessage());
          }
          isBindingTimeOut = false;
          mViewDataBinding.btnRtry.removeCallbacks(mBindingTimeOutCallBack);
          showConncetionFail();
          sendTraceBindFail();
        }

      }
    });
  }

  private void sendWifiConnectSuccessTrace() {
    String deviceId = "";
    if (deviceInfo != null) {
      deviceId = deviceInfo.getDeviceId();
    }

    sendClickTrace(WifiConnectionFragment.this.getContext(), mouduleId, "38", pageResouce, "4", "", "deviceid", deviceId);
  }


  private void showStepOne() {
    if (isAdded()) {
      mViewDataBinding.ivLoading.setVisibility(View.VISIBLE);
      mViewDataBinding.llDeviceConnection.setVisibility(View.VISIBLE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.postDelayed(mBindingTimeOutCallBack, BINDING_TIME_OUT);
    }
  }

  private void showStepTwo() {
    //   mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.VISIBLE);
    if (isAdded()) {
      mViewDataBinding.ivLoading.setVisibility(View.VISIBLE);
      mViewDataBinding.tvScanCode.setText(LanguageStrings.getStepsynchronized());
      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.ic_rd_checked).into(mViewDataBinding.ivDeviceConnection);
      Glide.with(this).load(R.drawable.loading_gray).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivSecondLine);

    }
  }

  private void showStepThree() {
    if (isAdded()) {
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.VISIBLE);
      mViewDataBinding.ivLoading.setVisibility(View.VISIBLE);

      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.VISIBLE);
      mViewDataBinding.tvDeviceBinding.setText(LanguageStrings.getStepconnected());

      Glide.with(this).load(R.drawable.ic_rd_checked).into(mViewDataBinding.ivSecondLine);
      Glide.with(this).load(R.drawable.loading_gray).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivDeviceBindingSuccess);
    }
  }

  private void showStepforth() {

    if (isAdded()) {
      mViewDataBinding.ivLoading.setVisibility(View.GONE);
      mViewDataBinding.ivLoadingStatus.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.ic_rd_checked).into(mViewDataBinding.ivLoadingStatus);
      Glide.with(this).load(R.drawable.ic_rd_checked).into(mViewDataBinding.ivDeviceBindingSuccess);
      mViewDataBinding.btnNext.setVisibility(View.VISIBLE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.tvLoading.setVisibility(View.GONE);
      mViewDataBinding.tvDeviceBindingSuccess.setText(LanguageStrings.getWificonnectStepbinded());
      mViewDataBinding.tvConnect.setText(LanguageStrings.getWifinoticeconnectsuccess());
      mDownTimer.start();

      LogUtils.d(TAG, "showBindSuccess_showStepforth_isDeviceInfoRegistered"+deviceInfo.getInfoStatus());
      if (mViewModel.mLiveData.getValue().isDeviceInfoRegistered()||(!TextUtils.isEmpty(deviceInfo.getInfoStatus())&&"1".equalsIgnoreCase(deviceInfo.getInfoStatus()))) {
        mViewDataBinding.btnNext.setVisibility(View.VISIBLE);
        mViewDataBinding.btnRegistdevice.setVisibility(View.GONE);
        mViewDataBinding.btNotregistdevice.setVisibility(View.GONE);
      } else {
        mViewDataBinding.btnRegistdevice.setVisibility(View.VISIBLE);
        mViewDataBinding.btNotregistdevice.setVisibility(View.VISIBLE);
        mViewDataBinding.btnNext.setVisibility(View.GONE);
      }
    }
  }


  private void showBindSuccess() {
    if (isAdded()) {
      mViewDataBinding.ivLoading.setVisibility(View.GONE);
      mViewDataBinding.ivLoadingStatus.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.ic_rd_checked).into(mViewDataBinding.ivLoadingStatus);
      Glide.with(this).load(R.drawable.ic_rd_checked).into(mViewDataBinding.ivDeviceBindingSuccess);
      mViewDataBinding.btnNext.setVisibility(View.VISIBLE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.tvLoading.setVisibility(View.GONE);
//      mViewDataBinding.tvConnect.setText(LanguageStrings.getWifinoticeconnectsuccess());
      mViewDataBinding.tvDeviceBindingSuccess.setText(LanguageStrings.getWificonnectStepbinded());
      mViewDataBinding.tvConnect.setText(LanguageStrings.getWifinoticeconnectsuccess());

      mDownTimer.start();

      //2024/2/1
//      if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
//        LogUtils.d(TAG, "showBindSuccess_EVN");
//        mViewDataBinding.btnNext.setVisibility(View.VISIBLE);
//        mViewDataBinding.btnRegistdevice.setVisibility(View.GONE);
//        mViewDataBinding.btNotregistdevice.setVisibility(View.GONE);
//
//      } else {
      LogUtils.d(TAG, "showBindSuccess_EVN_isDeviceInfoRegistered"+mViewModel.mLiveData.getValue().isDeviceInfoRegistered());
      if (mViewModel.mLiveData.getValue().isDeviceInfoRegistered()) {
        mViewDataBinding.btnNext.setVisibility(View.VISIBLE);
        mViewDataBinding.btnRegistdevice.setVisibility(View.GONE);
        mViewDataBinding.btNotregistdevice.setVisibility(View.GONE);
      } else {
        mViewDataBinding.btnRegistdevice.setVisibility(View.VISIBLE);
        mViewDataBinding.btNotregistdevice.setVisibility(View.VISIBLE);
        mViewDataBinding.btnNext.setVisibility(View.GONE);
      }
//      }


    }


  }

  private void showConncetionFail() {
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.tvFailtip1.setVisibility(View.VISIBLE);
      mViewDataBinding.tvFailtip1.setText(LanguageStrings.appNetworkconfigwificonnectFaileddescriptionTextviewText());

      // mViewDataBinding.tvFailtip2.setVisibility(View.VISIBLE);
      mViewDataBinding.ivLoading.setVisibility(View.GONE);
      mViewDataBinding.ivLoadingStatus.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.ic_warning).into(mViewDataBinding.ivLoadingStatus);


      mViewDataBinding.tvLoading.setText(LanguageStrings.getWificonnectFailed());
      mViewDataBinding.tvConnect.setText(LanguageStrings.getNoticeconnectfaied());
      mViewDataBinding.btnRtry.setVisibility(View.VISIBLE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
    }
  }


  private void initDownTimer() {
    if (mDownTimer == null) {
      mDownTimer = new CountDownTimer(11_000, 1000) {

        @Override
        public void onTick(long millisUntilFinished) {
          if (null != getActivity()) {
            mViewDataBinding.btnNext.setText(LanguageStrings.app_networkconfigwificonnect_notregistdevice_button_text().toString() + " (" + (millisUntilFinished / 1000) + "s)");
            mViewDataBinding.btNotregistdevice.setText(LanguageStrings.getConnectdone().toString() + " (" + (millisUntilFinished / 1000) + "s)");
          }
        }

        @Override
        public void onFinish() {
          if (null != getActivity()) {
            gotoPanel();
          }
        }
      };
    }
  }

  @SuppressLint("RestrictedApi")
  public void gotoPanel() {
    sendTraceDoneClick();
    if (mDownTimer != null) {
      mDownTimer.cancel();
    }
    //insert db

    inSertDevice(deviceInfo);


    if (((ConfigNetActivity) getActivity()).getIsResetWifi() == 0) {
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL)
              .withSerializable(PARAMETER_DEVICD, deviceInfo)
              .navigation();
    } else {

      getActivity().finish();
    }


  }


  public void inSertDevice(DeviceInfo deviceInfo) {
    DeviceDao mDeviceDao = SoftRoomDatabase.getDatabase(getContext()).deviceDao();
    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
      mDeviceDao.insert(deviceInfo);
    });
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_wifi_connection;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewModel.setFragment(this);
    if (isAdded()) {
      Glide.with(this).load(R.drawable.loading_gray).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivDeviceConnection);
      Glide.with(this).load(R.drawable.device_conn_anima).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivLoading);
    }
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class getViewModelClass() {
    return DevicesConnectionViewModel.class;
  }


  public void retryAgain() {
    sendBaseTraceClick("3");
    from = R.id.wifiInputFragment;
    NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
    controller.popBackStack(from, true);

    Bundle bundle = new Bundle();
    bundle.putSerializable(PARAMETER_DEVICD,deviceInfo);
    WifiInfoEntity wifiInfo=new WifiInfoEntity();
    if(wifiInputUIState!=null||!TextUtils.isEmpty(wifiInputUIState.getWifiName())){
      wifiInfo.setSsid(wifiInputUIState.getWifiName());
    }else{
      wifiInfo.setSsid("");
    }

    bundle.putSerializable(WIFI_INFO, wifiInfo);
    goToNextFragment(from,bundle);

  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    cancel();

    return true;
  }

  public void cancel() {
    mViewModel.disConnectBluetooth();
    if (((ConfigNetActivity) getActivity()).getIsResetWifi() == 0) {
      if (mViewModel.mLiveData.getValue().getState() == BIND_SUCCESS) {
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
      } else {
        if (mFrom ==  R.id.deviceListFragment) {
          if (BleScanState.STATE_SCANNING == BleManager.getInstance().getScanSate()) {
            //停止扫描
            try {
              BleManager.getInstance().cancelScan();
            } catch (Exception e) {
              e.printStackTrace();
            }
          }
          getActivity().finish();
          return;
        }
        NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
        controller.popBackStack(R.id.scanNearbyDevicesFragment, true);
        goToNextFragment(R.id.scanNearbyDevicesFragment);
      }
    } else {
      getActivity().finish();
    }

  }


  private void postBindingFailed() {
    ScanNearByDevicesUIState scanNearByDevicesUIState = mViewModel.mLiveData.getValue();
    scanNearByDevicesUIState.setState(BINDING_FAIL);
    scanNearByDevicesUIState.setDeviceBinding(true);
    mViewModel.mLiveData.postValue(scanNearByDevicesUIState);
  }


  public void clickToRegist() {
    sendConnectButtonClick();
    mDownTimer.cancel();
    ARouter.getInstance().build(ACTIVITY_URL_DEVICE).withSerializable(KEY_PREV_DATA, deviceInfo).withInt(KEY_PREV_FRAGMENT, 12345)
            .navigation();
    getActivity().finish();
  }


  private void initTrace() {
    stayEleId = "4";
    pageId = "39";
    mouduleId = "6";
    nextButtoneleid = "2";
  }

  public void sendConnectButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }



  public void sendTraceBindSuccess(DeviceInfo deviceInfo){
    //6_39_0_5_click
    //expand ----扩充字段 deviceid
    if (null==deviceInfo){
      return;
    }
    if (TextUtils.isEmpty(deviceInfo.getDeviceId())){
      return;
    }
    String deviceId = deviceInfo.getDeviceId();
    String eleId = "5";
    String expandDeviceId = "deviceid";
    HashMap<String,String> expandMap= new HashMap();
    expandMap.put(expandDeviceId,deviceId);
    Utils.sendClickTraceNew(this.getContext(), mouduleId, pageId, pageResouce, eleId, expandMap);

  }

  public void sendTraceBindFail(){
    //    6_39_0_6_click
    String eleId = "6";
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, eleId, "1");
  }

  public void sendTraceDoneClick(){
    //    6_39_0_7_click
    String eleId = "7";
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, eleId, "1");

  }
}
