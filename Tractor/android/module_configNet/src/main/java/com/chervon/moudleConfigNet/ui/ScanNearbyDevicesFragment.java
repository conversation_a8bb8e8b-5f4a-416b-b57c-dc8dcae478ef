package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.DeviceTypeConfig.DEVICE_TYPE_G4;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;
import static com.chervon.libRouter.RouterConstants.SHOW_ALERT_PERMISSION;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.GridLayoutManager;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.DeviceTypeConfig;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentDeviceFoundBinding;
import com.chervon.moudleConfigNet.ui.adapter.DevicesFoundAdapter;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.adapter.GridSpaceItemDecoration;
import com.chervon.moudleConfigNet.ui.adapter.SitesAdapter;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.moudleConfigNet.ui.state.ScanNearByDevicesUIState;
import com.chervon.moudleConfigNet.ui.state.Sites;
import com.chervon.moudleConfigNet.ui.viewmodel.ScanNearbyDevicesViewModel;
import com.google.gson.Gson;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class ScanNearbyDevicesFragment extends BaseEnhanceFragment<ScanNearbyDevicesViewModel, MoudleConfignetFragmentDeviceFoundBinding> implements ItemClick, BaseEnhanceFragment.OnkeyBackListener {
    public static final String TAG = "ScanNearbyDevicesFragment----";
    public static final int BLUETOOTH_PERMISSON_EXCEPTION = 128;
    public static final int BLUETOOTH_PERMISSON_REZEMU = 129;
    public static final int BLUETOOTH_PERMISSON_LOCATION_GRANTED = 131;
    public static final int BLUETOOTH_PERMISSON_LOCATION_UNGRANTED = 132;
    public static final int BLUETOOTH_PERMISSON_GPS_SWITCH_CLOSE = 133;
    public static final int BLUETOOTH_PERMISSON_GPS_SWITCH_OPEN = 134;
    public static final int SCAN_DEVICE_TIME_OUT = 135;

    DevicesFoundAdapter mAdapter;
    private SitesAdapter sitesAdapter;
    private int state;
    private int height;
    int delaytime = 5000;

    @Override
    protected void initDatas(Bundle savedInstanceState) {
        initTrace();
        state = 0;

        if (null != getActivity().getIntent()) {
            Bundle bundle = getActivity().getIntent().getExtras();
            mViewModel.setShowAlertPermission(bundle.getBoolean(SHOW_ALERT_PERMISSION));
            getActivity().getIntent().putExtra(SHOW_ALERT_PERMISSION, false);
        }
        mViewDataBinding.setUiState(new ScanNearByDevicesUIState());
        mViewDataBinding.setPresenter(this);
        mViewModel.setFragment(this);

        mViewModel.mLiveData.observe(this, new Observer<ScanNearByDevicesUIState>() {
            @Override
            public void onChanged(ScanNearByDevicesUIState uiState) {

                LogUtils.e(TAG,"uiState---"+uiState.getState());
                if (mViewDataBinding == null) {
                    return;
                }
                try {
                    if (uiState.isDeviceConnected()) {
                        DeviceInfo deviceSelected = uiState.getDeviceClicked();
                        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE).withInt(KEY_PREV_FRAGMENT, R.id.scanNearbyDevicesFragment).withSerializable(KEY_PREV_DATA, deviceSelected).navigation();
                        return;
                    }
                    if (uiState.isDeviceFound() && uiState.getDevices().size() > 0) {
                        //搜索到设备
                        mAdapter.setData(uiState.getDevices());
                        mAdapter.notifyDataSetChanged();
                        mViewDataBinding.clFound.setVisibility(View.VISIBLE);
                        mViewDataBinding.clScanning.setVisibility(View.GONE);
                        mViewDataBinding.tvScanTitle2.setVisibility(View.GONE);
                        mViewDataBinding.setUiState(uiState);
                        mViewDataBinding.tvLoading.setText(LanguageStrings.getSearchresultDescription());
                        state = 3;
                    } else if (SCAN_DEVICE_TIME_OUT == uiState.getState()) {
                        //未搜索到设备
                        Glide.with(ScanNearbyDevicesFragment.this).load(R.drawable.device_not_found).into(mViewDataBinding.ivLoading);
                        mViewDataBinding.name.setText(LanguageStrings.app_devicesearch_notfounddescription_textview_text());
                        mViewDataBinding.tvLoading.setText(LanguageStrings.app_devicesearch_notfounddescription_textview_text());
                        mViewDataBinding.tvLoading.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                BluetoothService.getInstance().stopPostDevice();
                                rollPage();
                            }
                        }, delaytime);

                        if (uiState != null && uiState.getState() == CONNECT_FAIL && !TextUtils.isEmpty(uiState.getMessage())) {
                            ToastUtils.showLong(uiState.getMessage());
                        }
                    } else if (uiState.isBlePermission() && uiState.isGpsPermission() && uiState.isGpsSwitch()) {

                        mViewDataBinding.notFloatPermissionTip.setVisibility(View.GONE);
                        mViewDataBinding.clScanning.setVisibility(View.VISIBLE);
                        mViewDataBinding.tvLoading.setVisibility(View.VISIBLE);
                        mViewDataBinding.middleDivider.setVisibility(View.VISIBLE);
                        mViewDataBinding.clFound.setVisibility(View.GONE);
                        mViewDataBinding.tvLoading.setText(LanguageStrings.app_devicesearch_closetodevice_textview_text());
                        uiState.setDevices(new ArrayList<>());
                        mAdapter.clearData();
                        state = 0;
                        scanDevice();
                        mViewModel.closeBleEnableDialog();
                    } else if (BLUETOOTH_PERMISSON_EXCEPTION == uiState.getState()) {
                        LogUtils.e(TAG, "蓝牙权限出现异常");
                        //扫描权限异常
                        showPermissionError();
                        mViewModel.openBleEnableAlertError();
                    } else if (BLUETOOTH_PERMISSON_LOCATION_UNGRANTED == uiState.getState()) {
                        LogUtils.e(TAG, "定位权限未授予");

                        showPermissionError();
                        mViewModel.openLocationPermissionDialog();
                    } else if (BLUETOOTH_PERMISSON_GPS_SWITCH_CLOSE == uiState.getState()) {
                        LogUtils.e(TAG, "GPS未打开");
                        mViewModel.openGpsEnableDialog();
                        showPermissionError();
                    } else if (BLUETOOTH_PERMISSON_LOCATION_GRANTED == uiState.getState()) {
                        LogUtils.e(TAG, "定位权限授予");
                        mViewModel.closeLocationPermissionDialog();

                        mViewModel.checkGPSStatus();
                    } else if (BLUETOOTH_PERMISSON_GPS_SWITCH_OPEN == uiState.getState()) {
                        LogUtils.e(TAG, "GPS开关打开");
                        mViewModel.closeGpsEnableDialog();
                        mViewModel.checkBleStatus(getActivity());
                    } else if (BLUETOOTH_PERMISSON_REZEMU == uiState.getState()) {
                        LogUtils.e(TAG, "蓝牙权限正常");
                        mViewModel.closeBleEnableDialog();
                        mViewModel.checkAccessFindLocationPermission();
                    }
                } catch (Exception e) {
                    LogUtils.e(TAG, "exception");

                }

            }
        });
        mViewModel.mSortedProductList.observe(this, new Observer<List<Object>>() {
            @Override
            public void onChanged(List<Object> items) {
                sitesAdapter.setDatas(items);
                sitesAdapter.notifyDataSetChanged();
            }
        });

    }

    private void showPermissionError() {
        ScanNearByDevicesUIState uiState = mViewModel.mLiveData.getValue();
        mViewDataBinding.tvScanTitle2.setVisibility(View.VISIBLE);
        mViewDataBinding.notFloatPermissionTip.setVisibility(View.VISIBLE);
        mViewDataBinding.clScanning.setVisibility(View.GONE);
        mViewDataBinding.clFound.setVisibility(View.GONE);
        mViewDataBinding.tvLoading.setVisibility(View.GONE);
        mViewDataBinding.middleDivider.setVisibility(View.INVISIBLE);
        mViewDataBinding.namePermissionTip.setText(uiState.getMessage());
        uiState.setDevices(new ArrayList<>());
        mAdapter.clearData();
        state = 8;
    }

    private void showCameraPermissionError() {
        if (getActivity() != null && !getActivity().isFinishing()) {
            DialogUtil.simpleConfirmDialog2(getContext(), LanguageStrings.getAppDeviceqrcodescanCameraAuthorizedalerttitle(), LanguageStrings.getAppDeviceqrcodescanCameraAuthorizedalertmessage(), LanguageStrings.app_modifyavator_gosetting_textview_text(), LanguageStrings.app_setting_clearcachecancle_button_text(), new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // onConfirmed 跳转到系统设置
                    goIntentSetting();
                }
            }, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //onCanceled

                }
            });
        }
    }

    /**
     * 跳转到三星系统设置。其他机型暂无适配计划
     */
    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getActivity().getPackageName(), null);
        intent.setData(uri);
        try {
            getContext().startActivity(intent);
        } catch (Exception e) {

        }
    }

    private void scanDevice() {
        if (isAdded() && this.getActivity() != null) {
            Bundle bundle = this.getActivity().getIntent().getExtras();


            if (bundle != null) {
                mPageTo = bundle.getInt(KEY_TO);
                if (mPageTo != R.id.deviceGuidePageOneFragment) {
                    mViewModel.scanningNearbyDevices();
                } else {
                    LogUtils.e(TAG, "not deviceGuidePageOneFragment ");
                }

            } else {
                mViewModel.scanningNearbyDevices();
            }
        } else {
            mViewModel.scanningNearbyDevices();
        }
    }

    @SuppressLint("RestrictedApi")
    private void rollPage() {
        if (mViewDataBinding == null) {
            return;
        }
        height = mViewDataBinding.llScanItem.getBottom() - mViewDataBinding.notFoundTipFloat.getBottom() - mViewDataBinding.scroll.getScrollY();
        TranslateAnimation translateAnimation = new TranslateAnimation(Animation.ABSOLUTE, 0, Animation.ABSOLUTE, 0, Animation.ABSOLUTE, 0F, Animation.ABSOLUTE, -height);
        translateAnimation.setDuration(100);
        translateAnimation.setFillAfter(false);

        translateAnimation.setAnimationListener(new Animation.AnimationListener() {

            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationEnd(Animation animation) {

                if (mViewDataBinding == null) {
                    return;
                }

                mViewDataBinding.notFoundTipFloat1.setVisibility(View.VISIBLE);
                mViewDataBinding.notFoundTipFloat2.setVisibility(View.VISIBLE);
                mViewDataBinding.scroll.smoothScrollBy(0, height);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {

            }
        });
        mViewDataBinding.clContainer.startAnimation(translateAnimation);
        state = 2;
        if (isAdded()) {
            setTitle(LanguageStrings.app_devicesearch_add_textview_text());
        }

    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_device_found;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        UiHelper.showSystemUI(getActivity().getWindow());
        if (isAdded()) {
            ((ConfigNetActivity) getActivity()).setKeyBackListener(this);
        }
        mViewDataBinding.rvFoundDevices.setLayoutManager(new GridLayoutManager(this.getActivity(), 2, GridLayoutManager.VERTICAL, false));
        mViewDataBinding.rvFoundDevices.addItemDecoration(new GridSpaceItemDecoration(2, 22, 22));
        ArrayList<List<DeviceInfo>> list = new ArrayList<>();
        mAdapter = new DevicesFoundAdapter(this.getActivity(), this, list);
        mViewDataBinding.rvFoundDevices.setAdapter(mAdapter);
        Glide.with(this).load(R.drawable.bluetooth_device_add).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivLoading);
        List<Sites> sites = new ArrayList<>();

        Sites sites1 = new Sites();
        List<ProductInfo> list1 = new ArrayList<ProductInfo>();

        sites1.setSites(list1);
        sites1.setName("");
        sites.add(sites1);
        List<Object> items = new ArrayList<>();

        //数据获取之后  将数据循环遍历，放进items集合中，至于服务器返回什么格式的数据，我想看下实体类就应该明白了
        for (int i = 0; i < sites.size(); i++) {
            items.add(sites.get(i));
            for (int k = 0; k < sites.get(i).getSites().size(); k++) {
                items.add(sites.get(i).getSites().get(k));
            }
        }
        //实例化适配器将遍历好的数据放进适配器中
        sitesAdapter = new SitesAdapter(ScanNearbyDevicesFragment.this.getContext(), items);
        //new一个布局管理器，这里是用GridLayoutManager，要区分3列
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getActivity(), 2);
        //下面这个方法很重要，根据position获取当前这条数据是标题还是数据项，来设置他的跨列
        gridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                //适配器中有这么一个方法，根据position获取当前这条数据是标题还是数据项，来设置他的跨列
                switch (sitesAdapter.getItemViewType(position)) {
                    case SitesAdapter.SITES:
                        return 2;
                    case SitesAdapter.SITE:
                        return 1;
                    default:
                        return -1;
                }
            }
        });
        mViewDataBinding.rvCategory.setLayoutManager(gridLayoutManager);

        mViewDataBinding.rvCategory.setAdapter(sitesAdapter);
        //item的点击事件，这里实现，进行具体的操作
        sitesAdapter.setOnItemClickListener(new SitesAdapter.OnItemClickListener() {
            @Override
            public void onClick(View itemview, int position) {
                switch (sitesAdapter.getItemViewType(position)) {
                    case SitesAdapter.SITE:
                        sendBaseTraceClick("5");

                        gotoGuidePage((ProductInfo) sitesAdapter.getItems().get(position));
                        break;
                    case SitesAdapter.SITES:
                        //  ToastUtils.showToast(getActivity(), ((Sites) items.get(position)).getName());
                        break;
                }
            }
        });


        mViewDataBinding.scroll.setOnScrollChangeListener(new View.OnScrollChangeListener() {
            @SuppressLint("RestrictedApi")
            @Override
            public void onScrollChange(View v, int scrollX, int scrollY, int oldScrollX, int oldScrollY) {
                if (mViewDataBinding == null) {
                    return;
                }
                int bottomHeight = mViewDataBinding.tvScanTitle1.getBottom();
                if (state == 0) {
                    if (scrollY > bottomHeight) {
                        setTitle(LanguageStrings.app_devicesearch_add_textview_text());
                        mViewDataBinding.clScanningFloat.setVisibility(View.VISIBLE);
                    } else {
                        setTitle("");
                        mViewDataBinding.clScanningFloat.setVisibility(View.GONE);
                    }
                } else if (state == 2) {
                    int bottomHeight1 = mViewDataBinding.notFoundTipFloat1.getBottom();
                    if (scrollY > bottomHeight1) {
                        setTitle(LanguageStrings.app_devicesearch_add_textview_text());
                        mViewDataBinding.notFoundTipFloat1.setVisibility(View.VISIBLE);
                        mViewDataBinding.clScanningFloat.setVisibility(View.GONE);
                    } else {
                        setTitle("");
                        mViewDataBinding.notFoundTipFloat1.setVisibility(View.GONE);
                    }
                    int bottomHeight2 = mViewDataBinding.divider2.getBottom() - mViewDataBinding.notFoundTipFloat2.getBottom();
                    if (scrollY > bottomHeight2) {
                        setTitle(LanguageStrings.app_devicesearch_add_textview_text());
                        mViewDataBinding.notFoundTipFloat2.setVisibility(View.VISIBLE);
                    } else {

                        mViewDataBinding.notFoundTipFloat2.setVisibility(View.GONE);
                    }

                } else {
                    setTitle("");
                    mViewDataBinding.clScanningFloat.setVisibility(View.GONE);
                }

            }
        });


    }

    @Override
    public void onStart() {
        super.onStart();
        // 从设置页面返回时，重新检查权限状态
        if (Build.VERSION.SDK_INT >= 31) {
            // 对于Android 12+设备，检查"附近的设备"权限状态
            String[] blePermissions = new String[]{
                    Manifest.permission.BLUETOOTH_SCAN,
                    Manifest.permission.BLUETOOTH_CONNECT
            };

            if (!PermissionUtils.isGranted(blePermissions)) {
                // 如果从设置页返回后权限仍未授予，显示明确的权限提示
                ScanNearByDevicesUIState uiState = new ScanNearByDevicesUIState();
                uiState.setMessage(LanguageStrings.app_base_notopenbluetoothalert_textview_text());
                uiState.setBlePermission(false);
                uiState.setState(BLUETOOTH_PERMISSON_EXCEPTION);
                mViewModel.mLiveData.setValue(uiState);
                LogUtils.e(TAG, "从设置返回: 附近的设备权限仍未授予");
            }else {
                BluetoothService.getInstance().PERMISSION_GRANTED = true;
            }
        }
        BluetoothService.getInstance().initListener();
        mViewModel.checkBleStatus(getActivity());
        mViewModel.observerScanStatus();
    }

    private void gotoGuidePage(ProductInfo productInfo) {

        TracePoints.addDeviceWithBleCategory(getContext(), UserInfo.get().getEmail(), productInfo.getProductSnCode());

        if (DeviceTypeConfig.NOT_IOT_DEVICE.equals(productInfo.getProductType())) {
            goToScanCodeWitNotIot(productInfo);
        } else {
            if (DeviceTypeConfig.WIFI_AND_BLE.equalsIgnoreCase(productInfo.getNetworkModes()) || DeviceTypeConfig.G4_AND_BLE.equalsIgnoreCase(productInfo.getNetworkModes()) ||
                    DeviceTypeConfig.DEVICE_TYPE_BLE.equalsIgnoreCase(productInfo.getNetworkModes()) ||
                    DeviceTypeConfig.DEVICE_TYPE_WIFI.equalsIgnoreCase(productInfo.getNetworkModes()) ||
                    DEVICE_TYPE_G4.equalsIgnoreCase(productInfo.getNetworkModes())) {

                NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
                Bundle bundle = new Bundle();
                if (productInfo != null) {
                    ArrayList<ProductInfo> list = new ArrayList<ProductInfo>();
                    list.add(productInfo);
                    bundle.putInt(KEY_PREV_FRAGMENT, R.id.deviceGuidePageOneFragment);
                    bundle.putSerializable(PARAMETER_DEVICD, list);
                    controller.navigate(R.id.deviceGuidePageOneFragment, bundle);

                }


            } else {
                goToScanCodeWitNotIot(productInfo);
            }

        }


    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }


    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return ScanNearbyDevicesViewModel.class;
    }


    public void goToSerialNumber(ScanNearByDevicesUIState uiState) {
        sendManualSnButtonClick();
        ConfigNetActivity.add_device_by_sn = "";
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.navigate(R.id.action_scanNearbyDevicesFragment_to_manualSNFragment);
    }

    public void goToScanCode(ScanNearByDevicesUIState uiState) {
        final String[] permissionsGroup = new String[]{Manifest.permission.CAMERA};
        RxPermissions rxPermissions = new RxPermissions(this);
        rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
            @Override
            public void accept(Permission permission) throws Exception {
                if (permission.granted) {
                    //处理允许权限后的操作
                    sendAcanQRCodeButtonClick();
                    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
                    controller.navigate(R.id.scanCodeFragment);
                } else if (permission.shouldShowRequestPermissionRationale) {
                    //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                    //处理用户点击禁止后的操作
                    showCameraPermissionError();
                } else {
                    showCameraPermissionError();
                }
            }
        });
    }

    public void goToScanCodeWitNotIot(ProductInfo productInfo) {
        sendAcanQRCodeButtonClick();
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        bundle.putString(PARAMETER_PRODUCT_ID, productInfo.getId());
        controller.navigate(R.id.scanCodeFragment, bundle);

    }


    public void goToSearchPage(ScanNearByDevicesUIState uiState) {
        sendCategorySearchButtonClick();
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.navigate(R.id.deviceSearchFragment);
    }


    @Override
    public void onItemClick(Object uiState) {

        sendBluetoothResultsButtonClick();

        TracePoints.addDeviceWithBleScan(getContext(), UserInfo.get().getEmail(), new Gson().toJson(uiState));

        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_PREV_FRAGMENT, R.id.scanNearbyDevicesFragment);
        bundle.putSerializable(PARAMETER_DEVICD, (ArrayList) uiState);
        controller.navigate(R.id.deviceGuidePageOneFragment, bundle);


    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

    }

    @Override
    public void onDestroyView() {
        mAdapter = null;
        sitesAdapter = null;
        super.onDestroyView();
    }


    @Override
    public void onStop() {
        super.onStop();
    }


    @Override
    public boolean OnkeyBack() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
        if (isAdded()) {
            ((ConfigNetActivity) getActivity()).finish();
        }
        return true;
    }


    private void initTrace() {
        stayEleId = "6";
        pageId = "11";
        mouduleId = "6";
        nextButtoneleid = "3";
    }

    private void sendBluetoothResultsButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "2", "1");

    }

    private void sendAcanQRCodeButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
    }


    private void sendCategorySearchButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "4", "1");
    }

    private void sendManualSnButtonClick(){
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "7", "1");
    }


}
