package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.APPConstants.EDIT_CONTENT_IS_NULL;
import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_GEGISTERED;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_RESULT;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_SN;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.BIND_SUCCESS;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.INIT_STATE;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.PRODUCT_IOT;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.RESPONSE_SUCCESS;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentManualSnBinding;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.ManualSNViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.ArrayList;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
//输入SN的Fragment
public class ManualSNFragment extends BaseEnhanceFragment<ManualSNViewModel, MoudleConfignetFragmentManualSnBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "ManualSNFragment";
  private DeviceInfo deviceInfo;
  private BottomSheetDialog mQrcodeDialog;

  private int page_from = 0;
  private DeviceInfo registerDeviceInfo;
  private DeviceRegisterUistate registerUistate;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    mViewModel.setFragment(this);
    ManualSNUIState state = new ManualSNUIState();

    Bundle bundle = getArguments();
    if (bundle != null) {
      page_from = bundle.getInt(KEY_FROM);
      registerDeviceInfo = (DeviceInfo) bundle.getSerializable(PARAMETER_DEVICD);
      registerUistate = (DeviceRegisterUistate) bundle.getSerializable(KEY_PREV_DATA);
      if (null != registerUistate) {
        if (registerUistate.isRegistMore()) {
          state.setFormRegistMore(true);
        }
      }
    }


    state.setSn(ConfigNetActivity.add_device_by_sn);
    mViewDataBinding.setUiState(state);
    mViewDataBinding.setPresenter(mViewModel);

  }


  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_manual_sn;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    UiHelper.showSystemUI(getActivity().getWindow());
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class getViewModelClass() {
    return ManualSNViewModel.class;
  }

  public void showQrNotFind(ManualSNUIState uiState) {


    if (mQrcodeDialog != null && mQrcodeDialog.isShowing()) {
      mQrcodeDialog.dismiss();
      mQrcodeDialog = null;

    }
    mQrcodeDialog = DialogUtil.showBottomQrcodeDialog(this.getActivity(), null, null, null);
  }

  public void goToNextPage(DeviceInfo deviceInfo) {
    TracePoints.addDeviceWithInputSN(getContext(), UserInfo.get().getEmail(),deviceInfo.getSn());
    String netModes = deviceInfo.getCommunicateMode();
    if (netModes != null) {
      if ("BLE".equalsIgnoreCase(netModes) || "4G".equalsIgnoreCase(netModes)) {
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        ArrayList arrayList = new ArrayList();
        arrayList.add(deviceInfo);
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_PREV_FRAGMENT, R.id.deviceGuidePageOneFragment);
        bundle.putInt(KEY_FROM, R.id.manualSNFragment);
        bundle.putSerializable(PARAMETER_DEVICD, arrayList);
        controller.navigate(R.id.deviceGuidePageOneFragment, bundle);
        return;
      }
    }
    gotoGuidePageOne(deviceInfo);
  }

  private void gotoGuidePageOne(DeviceInfo deviceInfo) {
    if (deviceInfo != null) {
      Bundle bundle = new Bundle();
      ArrayList arrayList = new ArrayList();
      arrayList.add(deviceInfo);
      bundle.putInt(KEY_PREV_FRAGMENT, R.id.deviceGuidePageOneFragment);
      bundle.putInt(KEY_FROM, R.id.scanCodeFragment);
      bundle.putSerializable(PARAMETER_DEVICD, arrayList);
      goToNextFragment(R.id.deviceGuidePageOneFragment, bundle);
    }

  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate baseUistate) {
    ManualSNUIState uiState = (ManualSNUIState) baseUistate;
    LogUtils.i("lesly sncode onUiLiveDataChanged state = " + uiState.getState());
    if (uiState.getState() == RESPONSE_SUCCESS) {
      deviceInfo = uiState.getDeviceInfo();
      Log.i("lesly ", "lesly sncode  datachange deviceInfo = " + deviceInfo);
      if (deviceInfo != null) {

        //TODO 如果从注册页面过来就跳转回去并携带原有参数
        if (page_from == R.id.deviceRegisterFragment) {
          registerDeviceInfo.setSn(deviceInfo.getSn());
          goToDeviceRegisterPage();
          return;
        }

        //产品类型位空
        if (TextUtils.isEmpty(deviceInfo.getProductType())) {
          mViewModel.bindingNotIotDevice(mViewDataBinding.getUiState().getSn());
          return;
        }
        if (!deviceInfo.getProductType().equals(PRODUCT_IOT)) {
          if ("Wifi+BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode()) ||
            "4G+BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode()) ||
            "BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode()) ||
            "WIFI".equalsIgnoreCase(deviceInfo.getCommunicateMode()) ||
            "4G".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
            goToNextPage(deviceInfo);
          } else {
            mViewModel.bindingNotIotDevice(mViewDataBinding.getUiState().getSn());
          }

        } else {
          mViewModel.bindingNotIotDevice(mViewDataBinding.getUiState().getSn());
        }
      }
    } else if (uiState.getState() == BIND_SUCCESS) {
      //   goToHome();
      goToResultFragment(true, uiState);

    } else if (uiState.getState() == EDIT_CONTENT_IS_NULL) {

      ToastUtils.showLong(LanguageStrings.app_networkconfignotiot_sncode_isnull_textview_text());

    } else if (uiState.getState() == RESPONSE_FAIL) {
      //当sn接口失败且来自于添加设备那么就报sn异常
      if (page_from == R.id.deviceRegisterFragment) {
        ToastUtils.showShort(LanguageStrings.app_device_regist_invalidserialnumber_text());
        return;
      }
      goToResultFragment(false, uiState);

    } else if (uiState.getState() == CONNECT_FAIL) {

      if (!TextUtils.isEmpty(uiState.getMessage())) {
        ToastUtils.showShort(uiState.getMessage());
      }

      goToResultFragment(false, uiState);
    }
    uiState.setState(INIT_STATE);
  }

  private void goToResultFragment(boolean result, ManualSNUIState uiState) {

    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
    Bundle bundle = new Bundle();
    bundle.putInt(KEY_FROM, R.id.manualSNFragment);
    bundle.putBoolean(KEY_ADD_NOT_IOT_RESULT, result);
    bundle.putSerializable(KEY_PREV_DATA, deviceInfo);
    bundle.putSerializable(KEY_ADD_NOT_IOT_SN, mViewDataBinding.editSn.getText().toString());
    bundle.putSerializable(KEY_ADD_NOT_IOT_GEGISTERED, uiState.isDeviceInfoRegistered());
    controller.navigate(R.id.action_manualSNFragment_to_NoIotResultFragment, bundle);
  }

  private void goToDeviceRegisterPage() {
    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE)
      .withInt(KEY_PREV_FRAGMENT, R.id.scanCodeFragment)
      .withSerializable(PARAMETER_DEVICD, registerDeviceInfo)
      .withSerializable(KEY_PREV_DATA, registerUistate)
      .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return this;
  }


  private void initTrace() {
    stayEleId = "3";
    pageId = "13";
    mouduleId = "6";
    nextButtoneleid = "2";
  }

  public void sendNextBottonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }


  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    return false;
  }


}
