package com.chervon.moudleConfigNet.ui;

import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;

import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentBluetoothGuideBinding;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentGuidePageBinding;
import com.chervon.moudleConfigNet.ui.viewmodel.ManualSNViewModel;
import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleScanState;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class BluetoothGuidePageFragment extends BaseEnhanceFragment<ManualSNViewModel, MoudleConfignetFragmentBluetoothGuideBinding>  {
    public static final String TAG = "ScanNearbyDevicesFragment";


    @Override
    protected void initDatas(Bundle savedInstanceState) {
    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_bluetooth_guide;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        //如果扫描中
        if (BleScanState.STATE_SCANNING == BleManager.getInstance().getScanSate()) {
            //停止扫描
          try{
            BleManager.getInstance().cancelScan();
          }catch (Exception e){

          }
        }
        mViewDataBinding.btnNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                  Bundle bundle = getArguments();
                  bundle.putInt(KEY_PREV_FRAGMENT,R.id.deviceConnectionFragment);
                  goToNextFragment(R.id.action_bluetoothGuidePageFragment_to_deviceConnectionFragment,bundle);
            }
        });

    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return ManualSNViewModel.class;
    }


    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }


}
