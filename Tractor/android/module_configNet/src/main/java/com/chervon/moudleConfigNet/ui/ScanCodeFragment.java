package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_GEGISTERED;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_RESULT;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_SN;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_INPUT_MARK;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.BIND_SUCCESS;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Rect;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.model.DeviceRegisterUistate;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.data.model.GuidePageInfoEntity;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentScanCodeBinding;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.moudleConfigNet.ui.state.GuidePageUiState;
import com.chervon.moudleConfigNet.ui.state.ScanCodeUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.ScanCodeViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.huawei.hms.hmsscankit.OnResultCallback;
import com.huawei.hms.hmsscankit.RemoteView;
import com.huawei.hms.ml.scan.HmsScan;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;
import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/20
 * @UpdateRemark: 适配欧洲设备注册跳转
 * @Version: 1.1
 */
public class ScanCodeFragment extends BaseEnhanceFragment<ScanCodeViewModel, MoudleConfignetFragmentScanCodeBinding> implements BaseEnhanceFragment.OnkeyBackListener {
    public static final String TAG = "ScanCodeFragment";
    public static final String PRODUCT_IOT = "notIotDevice";

    public static final int RESPONSE_SUCCESS = 1;
    public static final int INIT_STATE = 0;
    public static final int RESPONSE_FAIL = 2;
    public static final int RESPONSE_FAIL_ALREADY_ADD = 3;
    public static final int REGISTER_SCAN_DEVICE_SUCCESS = 4;

    private boolean isFlashLighton = false;
    private String mNotIotProductId;
    private DeviceInfo deviceInfo;
    DeviceRegisterUistate registerUistate;
    private List<GuidePageInfoEntity> mGuidePageInfoEntityList;
    private static final String[] permissionsGroup = new String[]{Manifest.permission.CAMERA};
    private BottomSheetDialog mQrcodeDialog;

    //扫描相关
    final int SCAN_FRAME_SIZE = 240;
    int mScreenWidth;
    int mScreenHeight;
    private RemoteView remoteView;
    private Bundle savedInstanceState;

    private DeviceInfo registerDeviceInfo;

    @SuppressLint("ResourceAsColor")
    @Override
    protected void initDatas(Bundle savedInstanceState) {
        initTrace();
        this.savedInstanceState = savedInstanceState;

        Bundle bundle = getArguments();
        if (bundle != null) {
            mNotIotProductId = bundle.getString(PARAMETER_PRODUCT_ID);
            if (mFrom == R.id.deviceRegisterMoreFragment ||mFrom == R.id.deviceRegisterFragment|| mFrom == R.id.deviceRegisterEuFragment) {
                registerUistate = (DeviceRegisterUistate) bundle.getSerializable(KEY_PREV_DATA);
                registerDeviceInfo = (DeviceInfo) bundle.getSerializable(PARAMETER_DEVICD);

            }
        }


        mViewDataBinding.setUiState(new ScanCodeUIState());
        mViewDataBinding.setPresenter(mViewModel);
        mViewDataBinding.setController(this);

        mViewDataBinding.ibflashlight.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                switchLight();
            }
        });

        mViewModel.mLiveData.observe(this, new Observer<ManualSNUIState>() {
            @Override
            public void onChanged(ManualSNUIState uiState) {
                if (null == uiState) {
                    return;
                }

                if (uiState.getState() == RESPONSE_SUCCESS) {
                    deviceInfo = uiState.getDeviceInfo();
                    if (deviceInfo != null) {

                        if (TextUtils.isEmpty(deviceInfo.getProductType())) {
                            return;
                        }
                        if (TextUtils.isEmpty(mNotIotProductId) && !deviceInfo.getProductType().equals(PRODUCT_IOT)) {
                            if ("Wifi+BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode()) || "4G+BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode()) || "BLE".equalsIgnoreCase(deviceInfo.getCommunicateMode()) || "WIFI".equalsIgnoreCase(deviceInfo.getCommunicateMode()) || "4G".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                                //goto device init
                                goToNextPage(deviceInfo);
                            } else {
                                mViewModel.bindingNotIotDevice(deviceInfo.getSn());
                            }
                        } else if (!TextUtils.isEmpty(mNotIotProductId) && mNotIotProductId.equalsIgnoreCase(deviceInfo.getProductId())) {
                            mViewModel.bindingNotIotDevice(deviceInfo.getSn());
                        } else if (!TextUtils.isEmpty(mNotIotProductId) && !mNotIotProductId.equalsIgnoreCase(deviceInfo.getProductId())) {
                            mViewModel.getGuidePage(mNotIotProductId + "", 0);
                        } else {

                            mViewModel.bindingNotIotDevice(deviceInfo.getSn());
                        }

                    }


                } else if (uiState.getState() == BIND_SUCCESS) {
                    if (mFrom != R.id.deviceRegisterMoreFragment && mFrom != R.id.deviceRegisterEuFragment) {
                        goToResultFragment(true, uiState);
                    }
                } else if (uiState.getState() == CONNECT_FAIL) {
                    if (!TextUtils.isEmpty(uiState.getMessage())) {
                        ToastUtils.showShort(uiState.getMessage());
                    }
                    if (null != remoteView) {
                        remoteView.onStart();
                    }

                } else if (uiState.getState() == RESPONSE_FAIL) {

                    if (!TextUtils.isEmpty(uiState.getMessage())) {
                        ToastUtils.showShort(uiState.getMessage());
                    }

                    if (mFrom != R.id.deviceRegisterMoreFragment && mFrom != R.id.deviceRegisterEuFragment) {
                        goToResultFragment(false, uiState);
                    } else {
                        if (isAdded()) {
                            if (null != remoteView) {
                                remoteView.onStart();
                            }
                        }
                    }

                } else if (uiState.getState() == RESPONSE_FAIL_ALREADY_ADD) {
                    ToastUtils.showShort(LanguageStrings.appDeviceqrcodescanProductnotsametoast());
                    if (null != remoteView) {
                        remoteView.onStart();
                    }
                } else if (uiState.getState() == REGISTER_SCAN_DEVICE_SUCCESS) {
                    goToDeviceRegister(uiState.getDeviceInfo());
                }

                uiState.setState(INIT_STATE);

            }
        });


        mViewModel.guidePageLiveData.observe(this, new Observer<GuidePageUiState>() {
            @Override
            public void onChanged(GuidePageUiState guidePageUiState) {
                if (guidePageUiState != null && guidePageUiState.getPageInfos() != null) {
                    mGuidePageInfoEntityList = guidePageUiState.getPageInfos();
                    if (isAdded()) {
                        ScanCodeFragment.this.getActivity().runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (mGuidePageInfoEntityList != null && mGuidePageInfoEntityList.size() > 0 && mGuidePageInfoEntityList.get(0) != null) {
                                    showQrNotFind(mGuidePageInfoEntityList.get(0).getIcon());
                                } else {
                                    showQrNotFind(null);
                                }

                            }
                        });
                    }


                }
            }
        });


        mViewDataBinding.tvBarTitle.setText(LanguageStrings.appDeviceqrcodescanTitleTextviewText());


    }


    private void goToResultFragment(boolean result, ManualSNUIState uiState) {

        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM, R.id.manualSNFragment);
        bundle.putBoolean(KEY_ADD_NOT_IOT_RESULT, result);
        bundle.putSerializable(KEY_PREV_DATA, deviceInfo);
        bundle.putSerializable(KEY_ADD_NOT_IOT_SN, uiState.getSn());
        bundle.putSerializable(KEY_ADD_NOT_IOT_GEGISTERED, uiState.isDeviceInfoRegistered());
        //调用navigate跳转之前先判断
        if (controller.getCurrentDestination().getId() == R.id.scanCodeFragment) {
            controller.navigate(R.id.action_manualSNFragment_to_NoIotResultFragment, bundle);
        }

    }

    private void requestPermission() {
        RxPermissions rxPermissions = new RxPermissions(this);
        rxPermissions.setLogging(true);
        rxPermissions.requestEachCombined(permissionsGroup).subscribe(new Consumer<Permission>() {
            @Override
            public void accept(Permission permission) throws Exception {
                if (permission.granted) {
                    //处理允许权限后的操作
                    // mViewDataBinding.zxingView.startCamera();
                    initScan(savedInstanceState);
                    return;
                } else if (permission.shouldShowRequestPermissionRationale) {
                    //禁止，但没有选择“以后不再询问”，以后申请权限，会继续弹出提示
                    //处理用户点击禁止后的操作
                    showPermissionError();
                } else {
                    showPermissionError();
                }

                //场景一：选择“禁止并不再询问”；
                //场景二：用户点击系统申请权限弹出框外部，使对话框消失；
                //场景三：再此之前已经点击过"禁止并不再询问",调用申请权限则直接回调到此处。
                //Toast提示用户前往设置允许权限
            }
        });
    }

    private void initScan(Bundle savedInstanceState) {
        //1. Obtain the screen density to calculate the viewfinder's rectangle.
        DisplayMetrics dm = getResources().getDisplayMetrics();
        float density = dm.density;
        //2. Obtain the screen size.
        mScreenWidth = getResources().getDisplayMetrics().widthPixels;
        mScreenHeight = getResources().getDisplayMetrics().heightPixels;

        int scanFrameSize = (int) (SCAN_FRAME_SIZE * density);

        Rect rect = new Rect();
        rect.left = mScreenWidth / 2 - scanFrameSize / 2;
        rect.right = mScreenWidth / 2 + scanFrameSize / 2;
        rect.top = mScreenHeight / 2 - scanFrameSize / 2;
        rect.bottom = mScreenHeight / 2 + scanFrameSize / 2;


        remoteView = new RemoteView.Builder().setContext(getActivity()).setBoundingBox(rect).setFormat(HmsScan.ALL_SCAN_TYPE).build();

        remoteView.setOnResultCallback(new OnResultCallback() {
            @Override
            public void onResult(HmsScan[] result) {
                //Check the result.
                if (result != null && result.length > 0 && result[0] != null && !TextUtils.isEmpty(result[0].getOriginalValue())) {
                    String sn = result[0].getOriginalValue();


                    if (!TextUtils.isEmpty(sn)) {
                        if (mViewDataBinding != null) {
                            if (mFrom == R.id.deviceRegisterEuFragment) {
                                sendDataBackToDeviceRegister(sn);
                            } else {
                                boolean isRegisterMore = false;
                                if (null != registerUistate) {
                                    isRegisterMore = registerUistate.isRegistMore();
                                }
                                if (mFrom == R.id.deviceRegisterFragment || mFrom == R.id.deviceRegisterMoreFragment) {
                                    sendDataBackToDeviceRegisterNa(sn);
                                } else {
                                    mViewModel.getProductBySn(sn, isRegisterMore);
                                }
                            }

                            remoteView.onStop();
                        }
                    }

                }
            }
        });
        // Load the customized view to the activity.
        remoteView.onCreate(savedInstanceState);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT);
        mViewDataBinding.rim.addView(remoteView, params);
    }

    private void showPermissionError() {
        if (getActivity() != null && !getActivity().isFinishing()) {
            DialogUtil.simpleConfirmDialog2(getContext(), LanguageStrings.getAppDeviceqrcodescanCameraAuthorizedalerttitle(), LanguageStrings.getAppDeviceqrcodescanCameraAuthorizedalertmessage(), LanguageStrings.app_modifyavator_gosetting_textview_text(), LanguageStrings.app_setting_clearcachecancle_button_text(), new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    // onConfirmed 跳转到系统设置
                    goIntentSetting();
                }
            }, new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    //onCanceled
                    goBack();
                }
            });
        }
    }

    /**
     * 跳转到三星系统设置。其他机型暂无适配计划
     */
    private void goIntentSetting() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getActivity().getPackageName(), null);
        intent.setData(uri);
        try {
            getContext().startActivity(intent);
        } catch (Exception e) {

        }
    }


    private void goToDeviceRegister(DeviceInfo deviceInfo) {
//    registerUistate.setRegistMore(true);
        if (deviceInfo != null) {
            registerUistate.setSn(deviceInfo.getSn());
            registerUistate.setDeviceid(deviceInfo.getDeviceId());
            registerDeviceInfo.setDeviceId(deviceInfo.getDeviceId());
        }

        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE).withInt(KEY_FROM, R.id.scanCodeFragment).withInt(KEY_TO, R.id.deviceRegisterFragment).withInt(KEY_PREV_FRAGMENT, R.id.scanCodeFragment).withSerializable(PARAMETER_DEVICD, registerDeviceInfo).withSerializable(KEY_PREV_DATA, registerUistate).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
    }

    /**
     * 将扫码结果返回页面
     *
     * @param sn
     */
    private void sendDataBackToDeviceRegisterNa(String sn) {

        //如果是注册更多，直接返回给注册页面
        if (null != registerUistate) {
            //跳转时要停止
            if (null != remoteView) {
                remoteView.onStop();
            }
            //重置SN
            if (null!=registerUistate){
                if (null!=registerUistate.getDeviceInfo()){
                    registerUistate.getDeviceInfo().setSn(sn);
                }
                registerUistate.setSn(sn);
            }

            if (null!=registerDeviceInfo){
                registerDeviceInfo.setSn(sn);
            }

            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE)
                    .withInt(KEY_FROM, R.id.scanCodeFragment)
                    .withInt(KEY_TO, R.id.deviceRegisterFragment)
                    .withInt(KEY_PREV_FRAGMENT, R.id.scanCodeFragment)
                    .withSerializable(PARAMETER_DEVICD, registerDeviceInfo)
                    .withSerializable(KEY_PREV_DATA, registerUistate)
                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        }
    }

    /**
     * 将扫码结果返回页面
     *
     * @param sn
     */
    private void sendDataBackToDeviceRegister(String sn) {
        if (!TextUtils.isEmpty(sn) && getArguments() != null) {
            int mark = getArguments().getInt(KEY_INPUT_MARK);
            switch (mark) {
                case 0:
                    registerUistate.setSn(sn);
                    registerUistate.getSnValidateStatus().setSn(sn);
                    break;
                default:
                    registerUistate.setKitSN(sn, mark - 1);
                    break;
            }
        }
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE).withInt(KEY_FROM, R.id.scanCodeFragment).withInt(KEY_TO, R.id.deviceRegisterEuFragment).withInt(KEY_PREV_FRAGMENT, R.id.scanCodeFragment).withInt(KEY_INPUT_MARK, getArguments() != null ? getArguments().getInt(KEY_INPUT_MARK) : 0).withSerializable(PARAMETER_DEVICD, registerDeviceInfo).withSerializable(KEY_PREV_DATA, registerUistate).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
        //跳转时要停止
        if (null != remoteView) {
            remoteView.onStop();
        }
    }

    public void goBack() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");

        if (mFrom == R.id.deviceRegisterMoreFragment || mFrom == R.id.deviceRegisterFragment) {
            goToDeviceRegister(registerDeviceInfo);
        } else if (mFrom == R.id.deviceRegisterEuFragment) {
            sendDataBackToDeviceRegister("");
        } else {
            try {
                NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
                controller.popBackStack(R.id.scanNearbyDevicesFragment, true);
                goToNextFragment(R.id.scanNearbyDevicesFragment);
            } catch (Exception e) {
                LogUtils.e(TAG, "goBack is error--->" + e.getMessage());
            }

        }

    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_scan_code;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        AutoSizeCompat.autoConvertDensityOfGlobal(this.getResources());
        if (isAdded()) {
            ((ConfigNetActivity) getActivity()).setKeyBackListener(this);
        }
    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return ScanCodeViewModel.class;
    }


    public void goToSerialNumber(ScanCodeUIState uiState) {
        sendSnBottonClick();
        ConfigNetActivity.add_device_by_sn = "";

        Bundle bundle = new Bundle();
        bundle.putInt(KEY_FROM, R.id.deviceRegisterFragment);
        bundle.putSerializable(PARAMETER_DEVICD, registerDeviceInfo);
        bundle.putSerializable(KEY_PREV_DATA, registerUistate);

        if (null != registerUistate) {
            //区分
            if (mFrom == R.id.deviceRegisterEuFragment) {
                sendDataBackToDeviceRegister("");
            } else {
                goToDeviceRegister(deviceInfo);
            }
        } else {
            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
            controller.navigate(R.id.manualSNFragment);
        }


    }

    @Override
    public void onStart() {
        super.onStart();
        if (getActivity().isFinishing()) {
            return;
        } else {
            requestPermission();
        }
        if (null != remoteView) {
            remoteView.onStart();
        }

    }


    @Override
    public void onStop() {
        super.onStop();

        if (getActivity().isFinishing()) {
            return;
        }
        if (null != remoteView) {
            remoteView.onStop();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        LogUtils.e(TAG,"-----onPause");
        if (getActivity().isFinishing()) {
            return;
        }
        if (null != remoteView) {
            remoteView.onPause();
        }


        if (null != remoteView) {
            boolean lightStatus = remoteView.getLightStatus();
            if (lightStatus) {
                remoteView.switchLight();
            }
        }
    }


    @Override
    public void onDestroy() {
        LogUtils.e(TAG,"-----onDestroy");
        if (null != remoteView) {
            remoteView.onDestroy();
        }
        super.onDestroy();
    }


    public void switchLight() {
        sendFlashlightButtonClick();

        if (remoteView.getLightStatus()) {
            remoteView.switchLight();
        } else {
            remoteView.switchLight();
        }


    }


    public void showQrNotFind() {
        sendQRCodeNotFoundClick();
        if (!TextUtils.isEmpty(mNotIotProductId)) {
            mViewModel.getGuidePage(mNotIotProductId + "", 0);
        } else {
            showQrNotFind(null);
        }

    }

    public void showQrNotFind(String imageUrl) {

        if (mQrcodeDialog != null && mQrcodeDialog.isShowing()) {
            mQrcodeDialog.dismiss();
            mQrcodeDialog = null;

        }
        mQrcodeDialog = DialogUtil.showBottomQrcodeDialog(this.getActivity(), null, null, imageUrl);
    }


    public void goToNextPage(DeviceInfo deviceInfo) {

        TracePoints.addDeviceWithQRCode(getContext(), UserInfo.get().getEmail(), deviceInfo.getSn());

        String netModes = deviceInfo.getCommunicateMode();
        if (netModes != null) {
            if ("BLE".equalsIgnoreCase(netModes) || "4G".equalsIgnoreCase(netModes)) {
                NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);

                ArrayList arrayList = new ArrayList();
                arrayList.add(deviceInfo);
                Bundle bundle = new Bundle();
                bundle.putInt(KEY_PREV_FRAGMENT, R.id.scanNearbyDevicesFragment);
                bundle.putInt(KEY_FROM, R.id.scanCodeFragment);
                bundle.putSerializable(PARAMETER_DEVICD, arrayList);
                controller.navigate(R.id.deviceGuidePageOneFragment, bundle);
                return;
            }

        }
        gotoGuidePageOne(deviceInfo);
    }

    private void gotoGuidePageOne(DeviceInfo deviceInfo) {

        if (deviceInfo != null) {
            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
            ArrayList arrayList = new ArrayList();
            arrayList.add(deviceInfo);
            Bundle bundle = new Bundle();
            bundle.putInt(KEY_PREV_FRAGMENT, R.id.deviceGuidePageOneFragment);
            bundle.putInt(KEY_FROM, R.id.scanCodeFragment);
            bundle.putSerializable(PARAMETER_DEVICD, arrayList);
            controller.navigate(R.id.deviceGuidePageOneFragment, bundle);
        }

    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }

    @Override
    public boolean OnkeyBack() {

        goBack();
        return true;
    }

    @Override
    public void onResume() {
        UiHelper.hideSystemUI(getActivity().getWindow());
        super.onResume();
        LogUtils.e(TAG,"-----onResume");

        if (getActivity().isFinishing()) {
            return;
        }
        if (null != remoteView) {
            remoteView.onResume();
        }

    }


    private void initTrace() {
        stayEleId = "5";
        pageId = "12";
        mouduleId = "6";
        nextButtoneleid = "2";
    }

    private void sendFlashlightButtonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "3", "1");
    }

    private void sendSnBottonClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
    }


    private void sendQRCodeNotFoundClick() {
        sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "4", "1");
    }


    @Override
    public void onDestroyView() {
        // mViewDataBinding.zxingView.setDelegate(null);
        super.onDestroyView();
        LogUtils.e(TAG,"-----onDestroyView");

    }
}
