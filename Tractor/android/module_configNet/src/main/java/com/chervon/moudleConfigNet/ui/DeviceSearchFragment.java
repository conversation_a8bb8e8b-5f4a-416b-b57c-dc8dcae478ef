package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.dialog.ConfirmAlertDialog;
import com.chervon.libBase.utils.DeviceTypeConfig;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentDeviceSearchBinding;
import com.chervon.moudleConfigNet.ui.adapter.DevicesSearchAdapter;
import com.chervon.moudleConfigNet.ui.state.DeviceSearchUIState;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.moudleConfigNet.ui.state.ScanNearByDevicesUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.DeviceSearchViewModel;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceSearchFragment extends BaseEnhanceFragment<DeviceSearchViewModel, MoudleConfignetFragmentDeviceSearchBinding> implements ItemClick, BaseEnhanceFragment.OnkeyBackListener {
    public static final String TAG = "DeviceSearchFragment";
    private DevicesSearchAdapter mAdapter;

    @Override
    protected void initDatas(Bundle savedInstanceState) {
      initTrace();
        mViewDataBinding.setUiState(new ManualSNUIState());
        mViewDataBinding.setPresenter(this);
        mViewModel.searchHistoryDevice();
        mViewModel.mLiveData.observe(this, new Observer<DeviceSearchUIState>() {
            @Override
            public void onChanged(DeviceSearchUIState uiState) {
                List<ProductInfo> deviceInfos = uiState.getCurrentSearchDevices();
                if (deviceInfos != null && deviceInfos.size() > 0) {
                    mAdapter.setData(deviceInfos);
                    mAdapter.notifyDataSetChanged();
                    mViewDataBinding.tvSearchHistory.setVisibility(View.GONE);
                    mViewDataBinding.ivDelete.setVisibility(View.GONE);
                } else {
                    List<ProductInfo> historySearchDeviceInfos = uiState.getHistorySearchDevices();
                    if(historySearchDeviceInfos != null && historySearchDeviceInfos.size() > 0){
                      mAdapter.setData(historySearchDeviceInfos);
                      mAdapter.notifyDataSetChanged();
                      mViewDataBinding.tvSearchHistory.setVisibility(View.VISIBLE);
                      mViewDataBinding.ivDelete.setVisibility(View.VISIBLE);
                    }else{
                      mAdapter.setData(historySearchDeviceInfos);
                      mAdapter.notifyDataSetChanged();
                      mViewDataBinding.tvSearchHistory.setVisibility(View.GONE);
                      mViewDataBinding.ivDelete.setVisibility(View.GONE);
                    }

                }
            }
        });





    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_device_search;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding.rvDeviceList.setLayoutManager(new LinearLayoutManager(this.getActivity(), LinearLayoutManager.VERTICAL, false));
        List<ProductInfo> list = new ArrayList<ProductInfo>();
        mAdapter = new DevicesSearchAdapter(this.getActivity(), this, list);
        mViewDataBinding.rvDeviceList.setAdapter(mAdapter);
        mViewDataBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mViewModel.searchDevice(s.toString());
            }
        });
    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return DeviceSearchViewModel.class;
    }


    @Override
    public void onItemClick(Object uiState) {
        mViewModel.saveSearchToHistory((ProductInfo)uiState);
        gotoGuidePage((ProductInfo)uiState);

    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {
        String str = mViewDataBinding.etSearch.getText().toString();
    }



    public void clearHistory() {
        sendEmptyBottonClick();
      ConfirmAlertDialog.initTrace("3",
      "15",
       "6",
       pageResouce+"_15",
         "1","2");
        ConfirmAlertDialog.show(this.getFragmentManager(),
                 LanguageStrings. getClearsearchhistory() ,
                LanguageStrings.getBaseCancel(),
                LanguageStrings. getSearchdone(), new Consumer<Boolean>() {
                    @Override
                    public void accept(Boolean aBoolean) throws Exception {
                        if (aBoolean) {
                            mViewModel.clearHistory();
                        } else {

                        }

                    }
                });


    }

    public void cancel() {
      sendCancelBottonClick();
        NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
        controller.popBackStack(R.id.scanNearbyDevicesFragment, true);
        goToNextFragment(R.id.scanNearbyDevicesFragment);
    }

    public void clearText() {
        mViewDataBinding.etSearch.setText("");
    }

    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }

    @Override
    public boolean OnkeyBack() {
      sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, "1","1");
        cancel();
        return true;
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    private void gotoGuidePage(ProductInfo productInfo) {

        if (DeviceTypeConfig.NOT_IOT_DEVICE.equals(productInfo.getProductType())
                ||DeviceTypeConfig.GATEWAY_SUB_DEVICE.equalsIgnoreCase(productInfo.getProductType())) {
            goToScanCode(null);
        } else {
            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
            Bundle bundle = new Bundle();
            ArrayList arrayList=new ArrayList();
            arrayList.add(productInfo);
            bundle.putInt(KEY_FROM, R.id.scanCodeFragment);
            bundle.putInt(KEY_PREV_FRAGMENT, R.id.deviceGuidePageOneFragment);
            bundle.putSerializable(PARAMETER_DEVICD, arrayList);
            controller.navigate(R.id.deviceGuidePageOneFragment, bundle);
        }
    }

    public void goToScanCode(ScanNearByDevicesUIState uiState) {
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.navigate(R.id.scanCodeFragment);

    }



  private void initTrace() {
    stayEleId="4";
    pageId="14";
    mouduleId="6";
    nextButtoneleid ="2";
  }

  public void sendCancelBottonClick() {
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, nextButtoneleid,"1");
  }
  public void sendEmptyBottonClick() {
    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, "3","1");
  }

}
