package com.chervon.moudleConfigNet.ui;


import static com.chervon.libBase.utils.APPConstants.IS_RESET_WIFI_CONNECTION;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_SN;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_CONFIG;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_MORE_SOLUTIONS;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD_ID;
import static com.chervon.libRouter.RouterConstants.PARAMETER_PRODUCT_ID;

import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.UnderlineSpan;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.BuildConfig;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentWifiMoreSolutionsBinding;
import com.chervon.moudleConfigNet.ui.viewmodel.WifiMoreSolutionsViewModel;

import java.util.ArrayList;

/**
 * @Author: 184862
 * @description wifi设备离线后，更多解决页面 Jackson需求
 * @CreateDate: 2024/1/15
 * @UpdateDate: 2024/1/15
 */
public class WifiMoreSolutionsFragment extends BaseEnhanceFragment<WifiMoreSolutionsViewModel, MoudleConfignetFragmentWifiMoreSolutionsBinding>
  implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "WifiMoreSolutionsFragment";
  private DeviceInfo deviceInfo;
  private String productId;
  private String deviceId;

  @Override
  protected void initDatas(Bundle savedInstanceState) {


    mViewModel.setFragment(this);
    Bundle bundle = getArguments();
    productId = bundle.getString(PARAMETER_PRODUCT_ID);
    deviceId = bundle.getString(PARAMETER_DEVICD_ID);

    if (!TextUtils.isEmpty(deviceId)) {
      deviceInfo = SoftRoomDatabase.getDatabase(getContext()).deviceDao().getDeviceById(deviceId);
    }

  }


  private void setBttomView() {
    String andStr = LanguageStrings.app_moresolutions_moresolutionsor_textview_text();
    SpannableStringBuilder style = new SpannableStringBuilder();
    style.append(LanguageStrings.app_moresolutions_moresolutions_textview_text());
    style.append(LanguageStrings.app_usermanual_title_textview_text());
    style.append(andStr);
    style.append(LanguageStrings.app_helpcenter_title_textview_text());

    style.setSpan(new UserManualClickableSpan(),
      LanguageStrings.app_moresolutions_moresolutions_textview_text().length(),
      LanguageStrings.app_moresolutions_moresolutions_textview_text().length() + LanguageStrings.app_usermanual_title_textview_text().length(),
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(new HelpCenterClickableSpan(),
      LanguageStrings.app_moresolutions_moresolutions_textview_text().length() +
        LanguageStrings.app_usermanual_title_textview_text().length() + andStr.length(),
      style.length(),
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(new UnderlineSpan() {
                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                      super.updateDrawState(ds);
                      ds.setColor(getResources().getColor(R.color.colorButtonNormal));//设置颜色
                      ds.setUnderlineText(false);//去掉下划线
                    }
                  }, LanguageStrings.app_moresolutions_moresolutions_textview_text().length(),
      LanguageStrings.app_moresolutions_moresolutions_textview_text().length() + LanguageStrings.app_usermanual_title_textview_text().length()
      , Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

    style.setSpan(new UnderlineSpan() {
                    @Override
                    public void updateDrawState(@NonNull TextPaint ds) {
                      super.updateDrawState(ds);
                      ds.setColor(getResources().getColor(R.color.colorButtonNormal));//设置颜色
                      ds.setUnderlineText(false);//去掉下划线
                    }
                  }, LanguageStrings.app_moresolutions_moresolutions_textview_text().length() +
        LanguageStrings.app_usermanual_title_textview_text().length() + andStr.length(), style.length(),
      Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);


    mViewDataBinding.tvSolutionDetail.setMovementMethod(LinkMovementMethod.getInstance());
    mViewDataBinding.tvSolutionDetail.setText(style);
  }

  public void repairDevice(){
    ArrayList<DeviceInfo> list = new ArrayList();
    list.add(deviceInfo);
//    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_CONFIGNET)
//      .withSerializable(PARAMETER_DEVICD, list)
//      .withInt(KEY_PREV_FRAGMENT, MOBLIE_WIFI_CONFIG)
//      .withInt(KEY_FROM, MOBLIE_WIFI_CONFIG)
//      .withInt(KEY_TO, R.id.deviceGuidePageOneFragment)
//      .withInt(IS_RESET_WIFI_CONNECTION, 1)
//      .navigation();

    Bundle bundle = new Bundle();
    bundle.putSerializable(PARAMETER_DEVICD, list);
    bundle.putInt(KEY_PREV_FRAGMENT, MOBLIE_WIFI_MORE_SOLUTIONS);
    bundle.putInt(KEY_FROM, MOBLIE_WIFI_MORE_SOLUTIONS);
    bundle.putInt(KEY_TO, R.id.deviceGuidePageOneFragment);
    bundle.putInt(IS_RESET_WIFI_CONNECTION, 1);
    goToNextFragment(R.id.deviceGuidePageOneFragment, bundle);

//    NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
//    controller.popBackStack(R.id.wifiMoreSolutionsFragment, true);
//    goToNextFragment(R.id.deviceGuidePageOneFragment);
  }

  public class UserManualClickableSpan extends ClickableSpan {
    @Override
    public void onClick(@NonNull View view) {

      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_PRODUCT_ENCYCLOPEDIAS_DETAIL).
        withSerializable(KEY_PREV_DATA, deviceInfo).navigation();

    }
  }


  public static class HelpCenterClickableSpan extends ClickableSpan {
    @Override
    public void onClick(@NonNull View view) {
      //跳转到帮助中心
      ARouter.getInstance().build(RouterConstants.ACTIVITY_HELP_CENTER).navigation();
    }
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_wifi_more_solutions;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    UiHelper.showSystemUI(getActivity().getWindow());
    mViewDataBinding = (MoudleConfignetFragmentWifiMoreSolutionsBinding) viewDataBinding;


    if (BuildConfig.EVN.equals(Utils.NA_TAG)) {
      mViewDataBinding.tvSolutionDetail.setVisibility(View.VISIBLE);
      mViewDataBinding.viewLineTwo.setVisibility(View.VISIBLE);
      setBttomView();
    } else {
      mViewDataBinding.tvSolutionDetail.setVisibility(View.GONE);
      mViewDataBinding.viewLineTwo.setVisibility(View.GONE);
    }

  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class getViewModelClass() {
    return WifiMoreSolutionsViewModel.class;
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate baseUistate) {

  }


  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return this;
  }

  @Override
  public boolean OnkeyBack() {
    if (null!=getActivity()){
      getActivity().finish();
    }
    return true;
  }
}
