package com.chervon.moudleConfigNet.ui;

import android.os.Bundle;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;

import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libBase.ui.BaseFragment;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentCardAddBinding;
import com.chervon.moudleConfigNet.ui.adapter.DevicesCardAdapter;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.adapter.GridSpaceItemDecoration;
import com.chervon.moudleConfigNet.ui.state.DeviceCardAddUIState;
import com.chervon.moudleConfigNet.ui.state.DeviceNotFoundUIState;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.moudleConfigNet.ui.viewmodel.DeviceCardAddViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: DeviceNotFoundFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午6:01
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午6:01
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceCardAddFragment extends BaseFragment<DeviceCardAddViewModel> implements ItemClick {
    public static final String TAG = "DeviceNotFoundFragment";
    private MoudleConfignetFragmentCardAddBinding mViewDataBinding;

    @Override
    protected void initDatas(Bundle savedInstanceState) {
        mViewDataBinding.rvDevices.setLayoutManager(new GridLayoutManager(this.getActivity(), 2, GridLayoutManager.VERTICAL, false));
      //  mViewDataBinding.rvDevices.addItemDecoration(new DividerItemDecoration(this.getActivity(), GridLayoutManager.VERTICAL));
        mViewDataBinding.rvDevices.addItemDecoration(new GridSpaceItemDecoration(2,
                22,
                22));
        List<ProductInfo> list = new ArrayList<ProductInfo>();
//        list.add(new ProductInfo("gechaoji"));
//        list.add(new ProductInfo("gechaoji2"));
        DevicesCardAdapter adapter = new DevicesCardAdapter(this.getActivity(), this, list);
        mViewDataBinding.rvDevices.setAdapter(adapter);
        mViewModel.mLiveData.observe(this, new Observer<DeviceNotFoundUIState>() {
            @Override
            public void onChanged(DeviceNotFoundUIState uiState) {
                if (uiState.getProducts()!=null&&uiState.getProducts().size() > 0) {
                    adapter.setData(uiState.getProducts());
                    adapter.notifyDataSetChanged();

                }
            }
        });


    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_card_add;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        mViewDataBinding = (MoudleConfignetFragmentCardAddBinding) viewDataBinding;
    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return DeviceCardAddViewModel.class;
    }

    public void onItemClick(DeviceCardAddUIState uiState){

    }

    @Override
    public void onItemClick(Object obj) {
        ProductInfo productInfo=(ProductInfo)obj;
//        if(productInfo.isIot()){
//            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
//            controller.navigate(R.id.action_deviceNotFoundFragment_to_deviceInitFragment);
//        }else{
//            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
//            controller.navigate(R.id.action_deviceCardAddFragment_to_scanCodeFragment);
//
//        }
    }

    @Override
    public void onItemClick(Object uiState, DeviceInfo deviceInfo) {

    }
}
