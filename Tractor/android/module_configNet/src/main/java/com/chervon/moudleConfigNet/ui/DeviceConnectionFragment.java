package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_MORE_SOLUTIONS;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_IS_GUIDEPAGE_EMPTY;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentDeviceConnectionBinding;
import com.chervon.moudleConfigNet.ui.state.ScanNearByDevicesUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.DevicesConnectionViewModel;
import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleScanState;
import com.google.gson.Gson;
import java.util.ArrayList;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/7/3
 * @UpdateRemark: 修改欧洲CRM同步设备deviceId匹配
 */
public class DeviceConnectionFragment extends BaseEnhanceFragment<DevicesConnectionViewModel, MoudleConfignetFragmentDeviceConnectionBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "ScanNearbyDevicesFragment";
  private static final int SCAN_SUCCESS = 11;
  private static final int SCAN_FAILED = 12;
  public static final int CONNECT_SUCCESS = 13;
  public static final int BIND_SUCCESS = 14;
  public static final int CONNECT_FAIL = 15;
  public static final int CONNECT_SET_EN = 16;
  public static final int MATCH_DEVICE_ID_FAIL = 17;
  private static final long BINDING_TIME_OUT = 60000;
  public static final String NET_MODE_BLE = "BLE";
  public static final String NET_MODE_4G_BLE = "4G";
  public static final String NET_MODE_WIFI = "wifi";
  private CountDownTimer mDownTimer;
  private DeviceInfo deviceInfo;
  private ProductInfo mProductInfo;
  public static final int MAIN_USER_ALREADY_BIND_FAIL = 18;


  private boolean isConnectFail;
  int from = 0;
  private Runnable mBindingTimeOutCallBack = new Runnable() {
    @Override
    public void run() {
      LogUtils.d("showFailPage_mBindingTimeOutCallBack");
      showConncetionFail();
      isBindingTimeOut = true;
    }
  };
  private boolean isBindingTimeOut = false;
  private boolean isBunding;
  private ArrayList preDataList;
  private boolean isGuidePageEmpty;
  private Object preData;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    mViewModel.setFragment(this);
    isBunding = false;
    liveDatasObserver();
    getDataFromBundle();
    initDownTimer();
    showDeviceConnectionProgress();
  }

  /**
   * @param
   * @return
   * @method getDataFromBundle
   * @description Data transferred from the previous interface
   * @date:
   * @author: wangheng
   */
  private void getDataFromBundle() {
    Bundle bundle = getArguments();
    if (bundle != null) {
      from = bundle.getInt(KEY_PREV_FRAGMENT);
      isGuidePageEmpty = bundle.getBoolean(PARAMETER_IS_GUIDEPAGE_EMPTY);


      preDataList = new ArrayList();
      ArrayList prePageData = (ArrayList) bundle.getSerializable(PARAMETER_DEVICD);
      for (Object object : prePageData) {
        preDataList.add(object);
      }
      if(null !=mViewModel.mLiveData.getValue()) {
        mViewModel.mLiveData.getValue().getFilterMac().clear();
      }
      Object preData = preDataList.get(0);
      singleDeiveConnect(preData);
      this.preData = preData;
      preDataList.remove(0);
    }
  }

  private void singleDeiveConnect(Object preData) {
    LogUtils.d(TAG, TAG + preData.toString());
    //如果存在蓝牙连接的设备，手动断开
    BleManager.getInstance().disconnectAllDevice();


    if (preData instanceof DeviceInfo) {
      deviceInfo = (DeviceInfo) preData;
      if (!TextUtils.isEmpty(deviceInfo.getMac())) {
        if (deviceInfo.getCommunicateMode().equals(NET_MODE_WIFI) && (1 == ((ConfigNetActivity) getActivity()).getIsResetWifi())) {
          if (!TextUtils.isEmpty(deviceInfo.getSn())) {
            String snCode = deviceInfo.getSn().substring(1, 5);
            mProductInfo = SoftRoomDatabase.getDatabase(getContext()).productDao().getProductInfo(snCode);
            mViewModel.scanningNearbyDevicesWifi(mProductInfo, deviceInfo);
          } else {
            //如果SN不存在，那么就从deviceId里面
            if (!TextUtils.isEmpty(deviceInfo.getDeviceId())) {
              String snCode = deviceInfo.getDeviceId().substring(1, 5);
              mProductInfo = SoftRoomDatabase.getDatabase(getContext()).productDao().getProductInfo(snCode);
              mViewModel.scanningNearbyDevicesWifi(mProductInfo, deviceInfo);
            }
          }

        } else {
          String snCode = deviceInfo.getSn();
          mProductInfo = SoftRoomDatabase.getDatabase(getContext()).productDao().getProductInfo(snCode);

          showDeviceConnectionProgress();
          mViewModel.scanningNearbyDevices(mProductInfo);
        }
      } else {
        mProductInfo = new ProductInfo();
        if (!TextUtils.isEmpty(deviceInfo.getSn())) {
          mProductInfo.setProductSnCode(deviceInfo.getSn().substring(1, 5));
          mViewModel.scanningNearbyDevices(mProductInfo);
        } else if(!TextUtils.isEmpty(deviceInfo.getDeviceId())) {
          //如果SN不存在，那么就从deviceId里面
          mProductInfo.setProductSnCode(deviceInfo.getDeviceId().substring(1, 5));
          mViewModel.scanningNearbyDevices(mProductInfo);
        }
      }

    } else if (preData instanceof ProductInfo) {
      mProductInfo = (ProductInfo) preData;
      mViewModel.scanningNearbyDevices(mProductInfo);
    }
  }


  private void showDeviceScanFailed() {
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.tvFailtip1.setVisibility(View.VISIBLE);
      mViewDataBinding.tvFailtip2.setVisibility(View.VISIBLE);
      mViewDataBinding.tvFailtip2.setText(LanguageStrings.app_connect_fail_tips_text());

      Glide.with(this).load(R.drawable.ic_warning).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivLoading);
      mViewDataBinding.tvLoading.setText(LanguageStrings.app_scan_failed_tryagain_text());

      mViewDataBinding.btnRtry.setVisibility(View.VISIBLE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
    }


  }


  private void showDeviceConnectionProgress() {
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.VISIBLE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
    }

  }

  private void showDeviceBindingProgress() {
    if (isAdded() && !isConnectFail) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.VISIBLE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.loading_gray).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivBinding);
    }

  }

  private void showDeviceBindingByWifiProgress() {
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);

      mViewDataBinding.llConnectionSuccessful.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.loading_gray).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivBinding);
    }

  }


  private void showDeviceSetWifiEvnSuccess() {
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);

      mViewDataBinding.btnNext.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);

      mViewDataBinding.llConnectionSuccessful.setVisibility(View.VISIBLE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      Glide.with(this).load(R.drawable.ic_binding_success).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivBinding);
      Glide.with(this).load(R.drawable.ic_binding_success).into(mViewDataBinding.ivLoading);
      mViewDataBinding.tvLoading.setText(LanguageStrings.app_networkconfigwificonnect_connectsuccess_textview_text());

    }

  }

  private void showDeviceBindingSuccess() {
    if (isAdded()) {
      LogUtils.d("showFailPage_showDeviceBindingSuccess");
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.VISIBLE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.VISIBLE);
      // 解决IOT-11656
      mViewDataBinding.tvFailtip1.setVisibility(View.GONE);
      mViewDataBinding.tvFailtip2.setVisibility(View.GONE);

      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.btnRtry.setVisibility(View.GONE);
      mViewDataBinding.tvLoading.setText(LanguageStrings.appNetworkconfigblutoothconnectSuccess());
      Glide.with(this).load(R.drawable.ic_binding_success).into(mViewDataBinding.ivDeviceBindingSuccess);
      Glide.with(this).load(R.drawable.ic_binding_success).into(mViewDataBinding.ivLoading);

      mDownTimer.start();
      deviceModeAddOne();


      //临时需求 2021/2/1
//      if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
//        showOnlyDoneButtonAndTimerDown(mViewDataBinding.btnNext, mViewDataBinding.btnRegistdevice, View.GONE, mViewDataBinding.btNotregistdevice);
//
//      } else {
      if (mViewModel.mLiveData.getValue().isDeviceInfoRegistered()) {
        showOnlyDoneButtonAndTimerDown(mViewDataBinding.btnNext, mViewDataBinding.btnRegistdevice, View.GONE, mViewDataBinding.btNotregistdevice);
      } else {
        showOnlyDoneButtonAndTimerDown(mViewDataBinding.btnRegistdevice, mViewDataBinding.btNotregistdevice, View.VISIBLE, mViewDataBinding.btnNext);
      }
//      }

      //增加逻辑，如果是蓝牙设备，绑定成功后，立即断开，蓝牙连接权限交给RN面板
      if (NET_MODE_BLE.equalsIgnoreCase(deviceInfo.getCommunicateMode())){
        BluetoothConection.getInstance().disConnectDevice();
      }

    }

  }

  private void showOnlyDoneButtonAndTimerDown(TextView mViewDataBinding, TextView mViewDataBinding1, int gone, TextView mViewDataBinding2) {
    mViewDataBinding.setVisibility(View.VISIBLE);
    mViewDataBinding1.setVisibility(gone);
    mViewDataBinding2.setVisibility(View.GONE);
  }

  private void deviceModeAddOne() {
    String deviceName = deviceInfo.getDeviceName();
    if (TextUtils.isEmpty(deviceName)) {
      deviceInfo.setDeviceName("1");
    } else {
      try {
        int modelNo = Integer.parseInt(deviceName);
        deviceInfo.setDeviceName((modelNo + 1) + "");
      } catch (Exception e) {

      }
    }
  }


  private void showConncetionFail() {
    isConnectFail = true;
    mViewModel.disConnectBluetooth();
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.tvFailtip1.setVisibility(View.VISIBLE);
      mViewDataBinding.tvFailtip2.setVisibility(View.VISIBLE);
      Glide.with(this).load(R.drawable.device_not_found).into(mViewDataBinding.ivLoading);
      mViewDataBinding.tvLoading.setText(LanguageStrings.getAppNetworkconfigblutoothconnectConnectfailed());
      mViewDataBinding.btnRtry.setVisibility(View.VISIBLE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
    }


  }

  private void showFailedWhenMainUserAlreadyBind(ScanNearByDevicesUIState uiState) {
    isConnectFail = true;
    mViewModel.disConnectBluetooth();
    if (isAdded()) {
      mViewDataBinding.llDeviceConnection.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBindingSuccess.setVisibility(View.GONE);
      mViewDataBinding.llConnectionSuccessful.setVisibility(View.GONE);
      mViewDataBinding.llDeviceBinding.setVisibility(View.GONE);
      mViewDataBinding.tvFailtip1.setVisibility(View.GONE);
      mViewDataBinding.tvFailtip2.setVisibility(View.VISIBLE);
      mViewDataBinding.tvFailtip2.setText(uiState.getMessage());
      Glide.with(this).load(R.drawable.device_not_found).into(mViewDataBinding.ivLoading);
      mViewDataBinding.tvLoading.setText(LanguageStrings.getAppNetworkconfigblutoothconnectConnectfailed());
      mViewDataBinding.btnRtry.setVisibility(View.VISIBLE);
      mViewDataBinding.btnNext.setVisibility(View.GONE);
    }
  }
  // TODO Z6  判断是否Z6 并进行设备绑定


  private void liveDatasObserver() {
    mViewModel.isScanedZ6.observe(this, new Observer<Boolean>() {
      @Override
      public void onChanged(Boolean aBoolean) {
        if (aBoolean) {
          showDeviceBindingProgress();
          mViewModel.bindingDevice(deviceInfo);
        }
      }
    });
    mViewModel.mScanliveData.observe(this, new Observer<ScanNearByDevicesUIState>() {
      @Override
      public void onChanged(ScanNearByDevicesUIState uiState) {
        if (uiState != null && uiState.isDeviceFound() && !uiState.isScanTimeOut()) {
                    /*
                    Take the matching device and combine it with the first one
                     */
          if (uiState.getDevices() != null && uiState.getDevices().size() > 0 && uiState.getDevices().get(0).size() > 0) {
            deviceInfo = uiState.getDevices().get(0).get(0);
            // TODO Z6  判断z6

            if (mViewModel.connectZ6(deviceInfo)) {
            } else {
              if(mFrom == R.id.deviceListFragment) {
                mViewModel.connectDeviceByBle(deviceInfo,preData instanceof DeviceInfo? ((DeviceInfo) preData).getDeviceId():"");
              } else {
                mViewModel.connectDeviceByBle(deviceInfo,"");
              }
              LogUtils.d("showFailPage_uiState.getDevices().size()" + uiState.getDevices().size());
            }

          } else {
            LogUtils.d("showFailPage_uiState.getDevices() == null");
            showConncetionFail();
            TracePoints.deviceConnectFragmentScanTimeOrNotFindDevice(getContext(), UserInfo.get().getEmail(),"showFailPage_uiState.getDevices() == null");
          }
        } else {
          TracePoints.deviceConnectFragmentScanTimeOrNotFindDevice(getContext(), UserInfo.get().getEmail(),"showFailPage_uiState.getDevices() == null");
          showConncetionFail();
        }

      }
    });


    mViewModel.mLiveData.observe(this, new Observer<ScanNearByDevicesUIState>() {
      @Override
      public void onChanged(ScanNearByDevicesUIState uiState) {
        LogUtils.iTag(TAG, "onChanged——getState" + uiState.getState());
        if (uiState.getState() == SCAN_SUCCESS) {
          showDeviceConnectionProgress();
        } else if (uiState.getState() == SCAN_FAILED) {
          sendBaseTraceClick("6");
          showDeviceScanFailed();
        } else if (!isBunding && uiState.getState() == CONNECT_SUCCESS) {

          if (null==uiState.getDeviceClicked()){
            return;
          }
          if (TextUtils.isEmpty(uiState.getDeviceClicked().getDeviceId())){
            return;
          }

          sendClickTrace(DeviceConnectionFragment.this.getContext(), mouduleId, pageId, pageResouce, "5", "5", "deviceid", uiState.getDeviceClicked().getDeviceId());

          TracePoints.bleAllRight(UserInfo.get().getEmail(),new Gson().toJson(deviceInfo));
          if(null !=mViewModel.mScanliveData.getValue()) {
            mViewModel.mScanliveData.getValue().getFilterMac().clear();
          }
          if(null !=mViewModel.mLiveData.getValue()) {
            mViewModel.mLiveData.getValue().getFilterMac().clear();
          }
          mViewDataBinding.btnRtry.postDelayed(mBindingTimeOutCallBack, BINDING_TIME_OUT);
          DeviceInfo currentDeviceInfo = uiState.getDeviceClicked();
          String netModes = currentDeviceInfo.getCommunicateMode();
          if (netModes != null) {
            if (NET_MODE_BLE.equalsIgnoreCase(netModes) || NET_MODE_4G_BLE.equalsIgnoreCase(netModes)) {
              if (!isBunding) {
                mViewModel.bindingDevice(currentDeviceInfo);
                isBunding = true;
              }

            }
            if (NET_MODE_WIFI.equalsIgnoreCase(netModes)) {
              showDeviceBindingByWifiProgress();
              if (uiState.isWifiEvn()) {
                showDeviceSetWifiEvnSuccess();
                mViewDataBinding.btnRtry.removeCallbacks(mBindingTimeOutCallBack);
                mDownTimer.start();
                showOnlyDoneButtonAndTimerDown(mViewDataBinding.btnNext, mViewDataBinding.btnRegistdevice, View.GONE, mViewDataBinding.btNotregistdevice);
              }
            } else {
              showDeviceBindingProgress();
            }
          }
        } else if (uiState.getState() == BIND_SUCCESS && !isBindingTimeOut) {
          mViewDataBinding.btnRtry.removeCallbacks(mBindingTimeOutCallBack);
          isBunding = true;
          if(null != mViewModel.mLiveData.getValue()) {
            deviceInfo.setMac(mViewModel.mLiveData.getValue().getDeviceClicked().getMac());
          }
          //insert db
          insertDevice(deviceInfo);
          showDeviceBindingSuccess();
        } else if (uiState.getState() == CONNECT_FAIL) {
          if (!TextUtils.isEmpty(uiState.getMessage())){
            ToastUtils.showShort(uiState.getMessage());
          }
          isBunding = false;
          mViewModel.disConnectBluetooth();
          if (preDataList != null && preDataList.size() > 0) {
            Object preData = preDataList.get(0);
            mViewModel.disConnectBluetooth();
            mViewDataBinding.btnRegistdevice.postDelayed(new Runnable() {
              @Override
              public void run() {
                singleDeiveConnect(preData);
              }
            }, 800);

            preDataList.remove(0);
          } else {
            sendBaseTraceClick("6");
            showConncetionFail();
          }
        } else if (uiState.getState() == MAIN_USER_ALREADY_BIND_FAIL){
          mViewModel.disConnectBluetooth();
          if (preDataList != null && preDataList.size() > 0) {
            Object preData = preDataList.get(0);
            mViewDataBinding.btnRegistdevice.postDelayed(new Runnable() {
              @Override
              public void run() {
                isBunding = false;
                singleDeiveConnect(preData);
              }
            }, 800);

            preDataList.remove(0);
          } else {
            isBunding = true;
            sendBaseTraceClick("6");
            if(!isBindingTimeOut) {
              mViewDataBinding.btnRtry.removeCallbacks(mBindingTimeOutCallBack);
              showFailedWhenMainUserAlreadyBind(uiState);
            }
          }
        }else if (uiState.getState() == MATCH_DEVICE_ID_FAIL) {
          mViewModel.disConnectBluetooth();
          //同步过滤mac
          if(null !=mViewModel.mScanliveData.getValue()) {
            mViewModel.mScanliveData.getValue().setFilterMacMap(uiState.getFilterMac());
            if(null !=mViewModel.mScanliveData.getValue().getDevices()) {
              mViewModel.mScanliveData.getValue().getDevices().clear();
            }
          }
          if(null != uiState.getDevices()) {
            uiState.getDevices().clear();
          }
          //匹配deviceId失败重新扫描
          mViewDataBinding.btnRegistdevice.postDelayed(new Runnable() {
            @Override
            public void run() {
              singleDeiveConnect(preData);
            }
          }, 800);

        }
      }
    });


  }

  /**
   * @param
   * @return
   * @method initDownTimer
   * @description Initialize Countdown
   * @date:
   * @author: wangheng
   */
  private void initDownTimer() {
    if (mDownTimer == null) {
      //设置倒计时时间  蓝牙和4G--10s、 WIFI----5s
      long millisInFuture = 11_000;
      if (null != deviceInfo) {
        String communicatemode = deviceInfo.getCommunicateMode();
        if (NET_MODE_WIFI.equalsIgnoreCase(communicatemode)) {
          millisInFuture = 6_000;
        }
      }

      if (null!=mProductInfo){
        String networkModes = mProductInfo.getNetworkModes();
        if (NET_MODE_WIFI.equalsIgnoreCase(networkModes)){
          millisInFuture = 6_000;
        }
      }

      mDownTimer = new CountDownTimer(millisInFuture, 1000) {
        @Override
        public void onTick(long millisUntilFinished) {
          if (isAdded()) {
            mViewDataBinding.btnNext.setText(LanguageStrings.getConnectdone().toString() + " (" + (millisUntilFinished / 1000) + "s)");
            mViewDataBinding.btNotregistdevice.setText(LanguageStrings.getConnectdone().toString() + " (" + (millisUntilFinished / 1000) + "s)");
          }
        }

        @Override
        public void onFinish() {
          goToNextPage();
        }
      };

    }


  }

  public void clickToPanel() {
    mDownTimer.cancel();
    goToNextPage();
  }


  public void clickToRegist() {
    sendNextButtonClick();
    BluetoothConection.getInstance().disConnectDevice();
    mDownTimer.cancel();
    ARouter.getInstance().build(ACTIVITY_URL_DEVICE).withSerializable(KEY_PREV_DATA, deviceInfo).withInt(KEY_PREV_FRAGMENT, 12345)
      .navigation();
    getActivity().finish();
  }

  public void goToNextPage() {
    String netModes = deviceInfo.getCommunicateMode();
    if (netModes != null) {
      if (NET_MODE_BLE.equalsIgnoreCase(netModes) || NET_MODE_4G_BLE.equalsIgnoreCase(netModes)) {
        gotoPanel();
        sendDoneClick();
      } else if (NET_MODE_WIFI.equalsIgnoreCase(netModes)) {
        gotoWifiConfig();
      }
    }
  }

  public void gotoPanel() {
    LogUtils.d("disConnectDevice_gotoPanel");
    BluetoothConection.getInstance().disConnectDevice();

    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_PANEL).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
      .withSerializable(PARAMETER_DEVICD, deviceInfo)
      .navigation();

    upgradeUser();

    TracePoints.bleGotoPanel(UserInfo.get().getEmail(), new Gson().toJson(deviceInfo));

    getActivity().finish();
  }

  public void insertDevice(DeviceInfo deviceInfo) {
    DeviceDao mDeviceDao = SoftRoomDatabase.getDatabase(getContext()).deviceDao();
    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
      DeviceInfo oldDeviceInfo=  mDeviceDao.getDeviceById(deviceInfo.getDeviceId());
      if(null != oldDeviceInfo) {
        oldDeviceInfo.setMac(deviceInfo.getMac());
        mDeviceDao.update(oldDeviceInfo);
      } else {
        mDeviceDao.insert(deviceInfo);
      }
    });
  }

  /**
   * 更新评分系统
   */
  private void upgradeUser(){
    String isAddDevice = "isAddDevice";
    User user = UserInfo.get();
    user.setIsAddDevice(isAddDevice);
    UserInfo.set(user);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_device_connection;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    Glide.with(this).load(R.drawable.loading_gray).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivDeviceConnection);
    Glide.with(this).load(R.drawable.device_conn_anima).diskCacheStrategy(DiskCacheStrategy.ALL).into(mViewDataBinding.ivLoading);
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class getViewModelClass() {
    return DevicesConnectionViewModel.class;
  }


  public void retryAgain() {
    sendRetryButtonClick();
    if (isGuidePageEmpty) {
      cancel();
    } else {
      if (from > 0) {
        NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
        controller.popBackStack(R.id.deviceConnectionFragment, true);
      }
    }


  }

  @Override
  public void onPause() {
    super.onPause();

  }

  private void gotoWifiConfig() {

    Bundle bundle = getBundle();
    bundle.putSerializable(PARAMETER_DEVICD, deviceInfo);
    //欧洲crm同步设备
    if(mFrom == R.id.deviceListFragment) {
      bundle.putInt(KEY_FROM,mFrom);
    }
    goToNextFragment(R.id.wifiInputFragment, bundle);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    LogUtils.d("disConnectDevice_OnkeyBack");
    BluetoothConection.getInstance().disConnectDevice();//断开
    cancel();
    return true;
  }

  public void cancel() {
    if (mViewModel.mLiveData.getValue().getState() == BIND_SUCCESS) {
      if (0 == ((ConfigNetActivity) getActivity()).getIsResetWifi()) {
        mDownTimer.cancel();
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_HOME).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
      } else {
        getActivity().finish();
      }

    } else {
      if (0 == ((ConfigNetActivity) getActivity()).getIsResetWifi()) {
        if (mFrom ==  R.id.deviceListFragment) {
          if (BleScanState.STATE_SCANNING == BleManager.getInstance().getScanSate()) {
            //停止扫描
            try {
              BleManager.getInstance().cancelScan();
            } catch (Exception e) {
              e.printStackTrace();
            }
          }
          getActivity().finish();
          return;
        }
        NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
        controller.popBackStack(R.id.scanNearbyDevicesFragment, true);
        goToNextFragment(R.id.scanNearbyDevicesFragment);
      } else if (mFrom == MOBLIE_WIFI_MORE_SOLUTIONS) {
        //手动断开所有连接设备
        BleManager.getInstance().disconnectAllDevice();
        NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
        controller.popBackStack();
      } else {
        getActivity().finish();
      }
    }

  }


  private void initTrace() {
    stayEleId = "4";
    pageId = "36";
    mouduleId = "6";
    nextButtoneleid = "2";
  }

  public void sendNextButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  public void sendDoneClick() {
//    6_36_0_7_click
    mouduleId = "6";
    pageId = "36";
    String eleId = "7";

    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, eleId, "1");
  }

  public void sendRetryButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "3", "1");
  }


  @Override
  public void onDestroyView() {
    super.onDestroyView();
    isBunding = false;
    if (null != mDownTimer) {
      mDownTimer.cancel();
    }
  }
}
