package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.BluetoothUtils.isDeviceConnected;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_CONFIG;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_MORE_SOLUTIONS;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.libRouter.RouterConstants.PARAMETER_IS_GUIDEPAGE_EMPTY;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.BLUETOOTH_PERMISSON_EXCEPTION;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.BLUETOOTH_PERMISSON_GPS_SWITCH_CLOSE;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.BLUETOOTH_PERMISSON_GPS_SWITCH_OPEN;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.BLUETOOTH_PERMISSON_LOCATION_GRANTED;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.BLUETOOTH_PERMISSON_LOCATION_UNGRANTED;
import static com.chervon.moudleConfigNet.ui.ScanNearbyDevicesFragment.BLUETOOTH_PERMISSON_REZEMU;

import android.app.Dialog;
import android.bluetooth.BluetoothGatt;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.DeviceStatus;
import com.chervon.libBase.model.UpdateAllow;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.model.WifiUpgradeAllow;
import com.chervon.libBase.model.WifiUpgradeAllow2;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.BluetoothUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.FileHelper;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BlueDataAnalysisUtils;
import com.chervon.libBluetooth.BlueToothUtilListener;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libBluetooth.data.BlueDataFormat;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.libNetwork.configs.DeviceUpgradeModel;
import com.chervon.libNetwork.configs.TracePoints;
import com.chervon.libNetwork.event.IotModelEvent;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.data.model.GuidePageInfoEntity;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentGuidePageBinding;
import com.chervon.moudleConfigNet.ui.state.GuidePageUiState;
import com.chervon.moudleConfigNet.ui.state.ScanNearByDevicesUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.GuidePageViewModel;
import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.data.BleScanState;
import com.clj.fastble.exception.BleException;
import com.clj.fastble.utils.HexUtil;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/25
 * @UpdateRemark: 区分欧洲CRM同步设备 修改绑定接口
 * @Version: 1.1
 */
public class DeviceGuidePageOneFragment extends BaseEnhanceFragment<GuidePageViewModel, MoudleConfignetFragmentGuidePageBinding>
  implements BaseEnhanceFragment.OnkeyBackListener, BlueToothUtilListener {
  public static final String TAG = "DeviceGuidePageOneFragment";
  private int currentPageNum;
  private List<GuidePageInfoEntity> mGuidePageInfoEntityList = new ArrayList<GuidePageInfoEntity>();
  ArrayList preDataListTemp;
  private String deviceId;
  private DeviceInfo deviceInfo;
  protected boolean isCanUpdate = false;
  private String jobId;
  private int updateMode = -1;
  private Dialog mProgressDialog;
  private boolean firstEnter = true;
  private boolean firstExposureEnter = true;
  private boolean isGuidePageEmpty = true;
  private String activityName = "com.chervon.moudleConfigNet.ui.ConfigNetActivity";
  private static final String BLANK_SPACE = "$$";
  private MutableLiveData<DeviceStatus> deviceInfoMutableLiveData = new MutableLiveData<>();
  private static final int CONTENT_LINE = 1;
  //WIFI设备在线状态
  private static final int WIFI_DEVICE_ONLINE_STATUS = 1;
  private boolean wifi_device_online = false;

  // 未知原因
  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE1 = 1;
  // 电量不足
  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE2 = 2;
  // 设备工作中
  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE3 = 3;
  // 刹车开关未按下
  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE4 = 4;
  //设备不在充电桩
  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE6 = 6;
  private long lastToastTime = 0;

  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE5 = 5;

  private static final int ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE8 = 8;
  //蓝牙设备可以升级code
  private static final String BLE_ALLOW_UPGRADE_CODE = "00";
  //WIFI升级模式
  private static final int WIFI_UPGRADE_MODEL_CODE = 0;
  //是否可升级
  private static final String WIFI_UPGRADE_ALLOW_CODE = "1001";
  // todo wangheng fix r bug  IOT-10603
  private String curentUpgradeStatus = "";
  private String curentUpgradeStatusV2 = "";

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    if ((mFrom == R.id.activity_device_upgrade)) {
      Bundle bundle = getArguments();
      deviceId = bundle.getString("deviceId");
      jobId = bundle.getString("jobId");
      updateMode = bundle.getInt("upgradeModel");
      if (deviceInfo == null) {
        deviceInfo = SoftRoomDatabase.getDatabase(this.getContext()).deviceDao().getDeviceById(deviceId);
      }
      BaseApplication.setShowDialogMessage(true);
    }

    mViewModel.setFragment(this);


  }

  @Override
  public void onStart() {
    super.onStart();
    //手动取消订阅升级的topic
    if (!TextUtils.isEmpty(deviceId)&&!TextUtils.isEmpty(jobId)){
      String checkUpgradeStatus = String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceId);
      // aws/things/TCH17230000002X/jobs/1753315820404240386/action/result 新版本
      String checkUpgradeStatus2 = String.format(AwsMqttService.WIFI_UPGRADE_RESULT, deviceId, jobId);
      AwsMqttService.unsubscribeTopic(checkUpgradeStatus2);
      AwsMqttService.unsubscribeTopic(checkUpgradeStatus);
    }




    if ((mFrom == R.id.activity_device_upgrade)) {
      if (!EventBus.getDefault().isRegistered(this)) {
        EventBus.getDefault().register(this);
      }
    }
    mViewModel.checkBleStatus();
    mViewModel.observerScanStatus();


  }


  @Override
  public void onResume() {
    super.onResume();
    hideProgress();
    updateUi();
  }


  private void updateUi() {

    if (mGuidePageInfoEntityList.size() == 0 || currentPageNum >= mGuidePageInfoEntityList.size()) {

      if (currentPageNum >= mGuidePageInfoEntityList.size()) {
        currentPageNum = mGuidePageInfoEntityList.size();
      }
      if ((mFrom == R.id.activity_device_upgrade)) {
        mViewDataBinding.btnNext.setText(LanguageStrings.app_OTAInfo_upgrade_button_text());
      } else {
        mViewDataBinding.btnNext.setText(LanguageStrings.getNetworkconfigconfirmed());
      }
      return;
    }
    GuidePageInfoEntity currentPageInfo = mGuidePageInfoEntityList.get(currentPageNum);
    if (!TextUtils.isEmpty(currentPageInfo.getContent().getMessage())) {
      String pageContent = currentPageInfo.getContent().getMessage();
      if (!TextUtils.isEmpty(pageContent)) {
        pageContent = pageContent.replace(BLANK_SPACE, "\n");
        mViewDataBinding.tvLoading.setText(pageContent.replace("\\n", "\n"));
      } else {
        mViewDataBinding.tvLoading.setText("");
      }

      if (!TextUtils.isEmpty(mViewDataBinding.tvLoading.getText())) {
        String text = mViewDataBinding.tvLoading.getText().toString();
        String[] split = text.split("\n");
        if (split.length == CONTENT_LINE) {
          mViewDataBinding.tvLoading.setGravity(Gravity.CENTER);
        } else {
          mViewDataBinding.tvLoading.setGravity(Gravity.LEFT);
        }
      }
    }


    //  if (currentPageInfo.getIconType() == 1) {
    Glide.with(DeviceGuidePageOneFragment.this.getContext()).load(currentPageInfo.getIcon()).placeholder(R.drawable.ic_device_default).into(mViewDataBinding.ivLoading);
    //  }
    if (currentPageNum != 0) {
      sendExposure(this.getContext(), mouduleId, pageId, pageResouce);
    }

    GuidePageUiState uiState = mViewDataBinding.getUiState();
    if (currentPageNum < mGuidePageInfoEntityList.size() - 1) {
      uiState.setPageNum((currentPageNum + 1) + "/" + mGuidePageInfoEntityList.size());
      mViewDataBinding.setUiState(uiState);
      mViewDataBinding.vPageNum.setVisibility(View.VISIBLE);
      mViewDataBinding.btnNext.setText(LanguageStrings.getDevicesNext());
    } else {
      if (currentPageNum != 0) {
        uiState.setPageNum(mGuidePageInfoEntityList.size() + "/" + mGuidePageInfoEntityList.size());
        mViewDataBinding.setUiState(uiState);
        mViewDataBinding.vPageNum.setVisibility(View.VISIBLE);
      } else {
        mViewDataBinding.vPageNum.setVisibility(View.INVISIBLE);
      }

      if ((mFrom == R.id.activity_device_upgrade)) {
        mViewDataBinding.btnNext.setText(LanguageStrings.app_OTAInfo_upgrade_button_text());
      } else {
        mViewDataBinding.btnNext.setText(LanguageStrings.getNetworkconfigconfirmed());
      }
    }
  }

  private void updatePageId() {
    if (mFrom == R.id.activity_device_upgrade) {
      pageId = (43 + currentPageNum) + "";
    } else {
      pageId = (16 + currentPageNum) + "";
    }

  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_guide_page;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    if (isAdded()) {
      ((ConfigNetActivity) getActivity()).setKeyBackListener(this);
    }
    UiHelper.showSystemUI(getActivity().getWindow());
    Bundle bundle = getArguments();
    if (bundle == null) {
      bundle = getActivity().getIntent().getExtras();
    }
    ArrayList preDataList = (ArrayList) bundle.getSerializable(PARAMETER_DEVICD);
    preDataListTemp = new ArrayList();

    if (null == preDataList) {
      return;
    }

    for (int i = 0; i < preDataList.size(); i++) {
      preDataListTemp.add(preDataList.get(i));
    }
    Long productId = 0L;
    Object preData = preDataListTemp.get(0);
    try {
      //解决异常PID为空---配网引导页隐藏了设备IOT-12632
      if (preData instanceof DeviceInfo) {
        deviceInfo = (DeviceInfo) preData;
        productId = Long.parseLong(((DeviceInfo) preData).getProductId());
      } else if (preData instanceof ProductInfo) {
        productId = Long.parseLong(((ProductInfo) preData).getId());
      }
    }catch (Exception e){
      getActivity().finish();
    }

    int type = 1;
    if (mFrom == R.id.activity_device_upgrade) {
      type = 2;
    }
    mProgressDialog = DialogUtil.showRnLoadingDialog(this.getActivity());
    mViewModel.getGuidePage(productId + "", type);
    //如果扫描中
    if (BleScanState.STATE_SCANNING == BleManager.getInstance().getScanSate()) {
      //停止扫描
      try {
        BleManager.getInstance().cancelScan();
      } catch (Exception e) {

      }
    }
    GuidePageUiState pageUiState = new GuidePageUiState();
    mViewDataBinding.setUiState(pageUiState);
    mViewDataBinding.setPresenter(mViewModel);
    mViewModel.mLiveData.observe(this, new Observer<GuidePageUiState>() {
      @Override
      public void onChanged(GuidePageUiState guidePageUiState) {
        if (guidePageUiState != null && guidePageUiState.getPageInfos() != null) {
          mProgressDialog.dismiss();
          mGuidePageInfoEntityList = guidePageUiState.getPageInfos();
          if (mGuidePageInfoEntityList != null && mGuidePageInfoEntityList.size() > 0) {
            isGuidePageEmpty = false;
            mProgressDialog.dismiss();
            if (isAdded()) {
              mViewDataBinding.btnNext.setVisibility(View.VISIBLE);
              DeviceGuidePageOneFragment.this.getActivity().runOnUiThread(new Runnable() {
                @Override
                public void run() {
                  updateUi();
                }
              });
            }

          } else {
            isGuidePageEmpty = true;
            gotoNextPage(mViewDataBinding.getUiState());
            mViewDataBinding.btnNext.setVisibility(View.GONE);
          }

        }
      }
    });
    if ((mFrom == R.id.activity_device_upgrade)) {
      if (isAdded()) {
        ((ConfigNetActivity) getActivity()).setNavTitle(LanguageStrings.app_OTA_upgrade_textview_text());
      }
    } else {
      try {
        BleManager.getInstance().cancelScan();
      } catch (Exception e) {

      }
    }

    initProgress();

    mViewModel.mScanData.observe(this, new Observer<ScanNearByDevicesUIState>() {
      @Override
      public void onChanged(ScanNearByDevicesUIState uiState) {
        if (mViewDataBinding == null) {
          return;
        }
        try {
          if (uiState.isBlePermission() && uiState.isGpsPermission() && uiState.isGpsSwitch()) {

            mViewModel.closeBleEnableDialog();
          } else if (BLUETOOTH_PERMISSON_EXCEPTION == uiState.getState()) {
            LogUtils.e(TAG, "蓝牙权限出现异常");
            //扫描权限异常

            mViewModel.openBleEnableAlertError();
          } else if (BLUETOOTH_PERMISSON_LOCATION_UNGRANTED == uiState.getState()) {
            LogUtils.e(TAG, "定位权限未授予");
            mViewModel.openLocationPermissionDialog();
          } else if (BLUETOOTH_PERMISSON_GPS_SWITCH_CLOSE == uiState.getState()) {
            LogUtils.e(TAG, "GPS未打开");
            mViewModel.openGpsEnableDialog();

          } else if (BLUETOOTH_PERMISSON_LOCATION_GRANTED == uiState.getState()) {
            LogUtils.e(TAG, "定位权限授予");
            mViewModel.closeLocationPermissionDialog();

            mViewModel.checkGPSStatus();
          } else if (BLUETOOTH_PERMISSON_GPS_SWITCH_OPEN == uiState.getState()) {
            LogUtils.e(TAG, "GPS开关打开");
            mViewModel.closeGpsEnableDialog();
            mViewModel.checkBleStatus();
          } else if (BLUETOOTH_PERMISSON_REZEMU == uiState.getState()) {
            LogUtils.e(TAG, "蓝牙权限正常");
            mViewModel.closeBleEnableDialog();
            mViewModel.checkAccessFindLocationPermission();
            //设备引导页面为空&蓝牙为未连接
            connectWithException();
          }
        } catch (Exception e) {
          LogUtils.e(TAG, "exception");

        }

      }
    });
  }

  /**
   * 蓝牙未连接或者异常并且引导页面为空
   */
  private void connectWithException(){
    if (null!=deviceInfo){
      GuidePageUiState guidePageUiState = mViewModel.mLiveData.getValue();
      if (!TextUtils.isEmpty(deviceInfo.getMac()) && guidePageUiState.isHasCheckGuide() && isGuidePageEmpty){
         //蓝牙未连接，去发起连接
        if (!BluetoothUtils.isDeviceConnected(getContext(),deviceInfo.getMac())){
          bleUpgrade();
       }
      }
    }
  }

  private void initProgress() {
    if (mProgressDialog == null) {
      mProgressDialog = DialogUtil.showRnLoadingDialog(this.getActivity());
      mProgressDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
        @Override
        public void onDismiss(DialogInterface dialog) {
          mProgressDialog = null;
        }
      });
    }
    mProgressDialog.setCanceledOnTouchOutside(false);
  }

  private void hideProgress() {
    if (mProgressDialog != null) {
      if (mProgressDialog.isShowing()) {
        mProgressDialog.dismiss();
      }
    }
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class getViewModelClass() {
    return GuidePageViewModel.class;
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  long preNextClickTime = 0;

  public void gotoNextPage(GuidePageUiState uiState) {
    sendNextBottonClick();
    currentPageNum++;
    if (currentPageNum < mGuidePageInfoEntityList.size()) {
      sendStayDuration();
      updatePageId();
      initPageResouce();
    }

    updateUi();

    if (currentPageNum >= mGuidePageInfoEntityList.size()) {
      if (System.currentTimeMillis() - preNextClickTime < 3000) {
        return;
      }

      preNextClickTime = System.currentTimeMillis();
      currentPageNum = mGuidePageInfoEntityList.size();
      if (mFrom == R.id.activity_device_upgrade) {

        Bundle bundle = getArguments();
        int updateMode = bundle.getInt("upgradeModel");

        if (updateMode == 1) {

          if (!mProgressDialog.isShowing()) {
            mProgressDialog.show();
          }
          deviceId = bundle.getString("deviceId");

          bleUpgrade();

        } else {
          publishCheckOrCanUpgrade();
        }
      } else {
        Bundle bundle = getArguments();
        ArrayList temp = new ArrayList();
        for (int i = 0; i < preDataListTemp.size(); i++) {
          temp.add(preDataListTemp.get(i));
        }
        bundle.putSerializable(PARAMETER_DEVICD, temp);
        bundle.putBoolean(PARAMETER_IS_GUIDEPAGE_EMPTY, isGuidePageEmpty);
        if(mFrom == R.id.deviceListFragment) {
          bundle.putInt(KEY_FROM, mFrom);
          bundle.putInt(KEY_PREV_FRAGMENT,R.id.deviceGuidePageOneFragment);
        }
        goToNextFragment(R.id.deviceConnectionFragment, bundle);
        currentPageNum = 0;
        // updatePageId();
      }

    }
  }

  private void publishCheckOrCanUpgrade() {
    if ((mFrom == R.id.activity_device_upgrade)) {

      mProgressDialog = DialogUtil.showRnLoadingDialog(this.getActivity());
      mProgressDialog.setCanceledOnTouchOutside(true);
      Bundle bundle = getArguments();
      deviceId = bundle.getString("deviceId");
      jobId = bundle.getString("jobId");
      updateMode = bundle.getInt("upgradeModel");
      int upgradeModel = bundle.getInt("upgradeModel");

      if (deviceInfo != null && ("4g".equalsIgnoreCase(deviceInfo.getCommunicateMode()) ||
        "wifi".equalsIgnoreCase(deviceInfo.getCommunicateMode()) ||
        "dt".equalsIgnoreCase(deviceInfo.getCommunicateMode()))) {

        // jumpToUpgradePageAndRestore();

        FileHelper.appendToFile(getContext(), deviceInfo.getDeviceId() + "请求升级发送给设备");

        //获取设备的在线状态
        mViewModel.getDeviceStatus(deviceInfoMutableLiveData, deviceId);

        deviceInfoMutableLiveData.observe(this, new Observer<DeviceStatus>() {
          @Override
          public void onChanged(DeviceStatus deviceStatus) {

            if (null != deviceStatus) {
              int isOnline = deviceStatus.getIsOnline();
              FileHelper.appendToFile(getContext(), "设备状态--" + isOnline);

              //设备离线
              if (isOnline == WIFI_DEVICE_ONLINE_STATUS) {

                wifi_device_online = true;

                if (upgradeModel == WIFI_UPGRADE_MODEL_CODE) {

                  // 订阅上报的1001物模型
                  AwsMqttService.subscribeTopic(String.format(AwsMqttService.DEVICE_MODEL_UPDATE_TOPIC, deviceId));
                  // todo  wangheng fix r bug  IOT-10603
                  if ("4g".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                    LogUtils.d("----------------",deviceId+"    "+jobId);
                    curentUpgradeStatus = String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC, deviceId, jobId);
                    curentUpgradeStatusV2 = String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC_V2, deviceId, jobId);
                    AwsMqttService.subscribeTopic(curentUpgradeStatus);
                    AwsMqttService.subscribeTopic(String.format(AwsMqttService.WIFI_UPGRADE_RESULT, deviceId, jobId));

                    //订阅新版本Topic
                    AwsMqttService.subscribeTopic(curentUpgradeStatusV2);
                  } else {
                    //订阅是否可升级新Topic1  订阅上报的1001物模型
                    AwsMqttService.subscribeTopic(String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceId));
                    //订阅是否可升级新Topic2
                    AwsMqttService.subscribeTopic(String.format(AwsMqttService.WIFI_UPGRADE_RESULT, deviceId, jobId));

                    AwsMqttService.subscribeTopic(String.format(AwsMqttService.OTA_JOB_FIRMWARE_STATE_UPDATE_TOPIC, deviceId, jobId));
                  }


                  //升级动作
                  AwsMqttService.publishTopic(String.format(AwsMqttService.OTA_NET_JOB_ACTION_TOPIC, deviceId, jobId), GsonUtils.toJson(new UpdateAllow(true)));

                  if (!mProgressDialog.isShowing()) {
                    mProgressDialog.show();
                  }

                }
              } else {
                wifi_device_online = false;
                ToastUtils.showLong(LanguageStrings.app_OTAguide_offline_textview_text());
              }
            }


          }
        });

      }

    }
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");

    if ((mFrom == R.id.activity_device_upgrade)) {
      if (isAdded()) {
        getActivity().finish();
      }
    } else if(mFrom == R.id.deviceListFragment) {
      if (isAdded()) {
        getActivity().finish();
      }
    } else if (mFrom == MOBLIE_WIFI_MORE_SOLUTIONS) {
      NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
      controller.popBackStack();
    } else if (mFrom == R.id.scanCodeFragment || mFrom == R.id.manualSNFragment) {
      NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
      controller.popBackStack(R.id.deviceGuidePageOneFragment, true);
    } else if (mFrom == MOBLIE_WIFI_CONFIG) {
      getActivity().finish();
    } else {
      NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
      controller.popBackStack(R.id.scanNearbyDevicesFragment, true);
      goToNextFragment(R.id.scanNearbyDevicesFragment);
    }
    return true;
  }


  /**
   * @return void
   * @method connectDevice
   * @description 蓝牙连接升级
   * @date: 2022/8/6 15:38
   * @author: langmeng
   */
  private void bleUpgrade() {

    //查找设备数据
    for (DeviceInfo device : SoftRoomDatabase.getDatabase(this.getContext()).deviceDao().getDevices()) {
      if (device.getDeviceId().equals(deviceId)) {
        deviceInfo = device;
      }
    }

    if (deviceInfo == null || deviceInfo.getMac() == null) {
      LogUtils.eTag(TAG, "设备信息获取失败");
      LogUtils.d("ota_canNotUpdate" + "设备信息获取失败");
      return;
    }

    BluetoothConection.getInstance().setListener(this);

    BluetoothConection.getInstance().connectDevice2(getContext(), deviceInfo.getMac());

    isCanUpdate = false;

  }

  @Override
  public void isBlueEnable(boolean enable) {

  }

  @Override
  public void onBleStartConnect() {
  }

  @Override
  public void onBleConnectFail(BleDevice bleDevice, BleException exception, int time) {
  }

  @Override
  public void onBleConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
  }

  @Override
  public void onMtuChanged(int mtu) {

  }

  @Override
  public void onSetMTUFailure(BleException exception) {

  }

  @Override
  public void onGattStartConnect() {

  }

  @Override
  public void onGattConnectFail(BleDevice bleDevice, BleException exception) {
  }

  @Override
  public void onGattConnectSuccess(BleDevice bleDevice, BluetoothGatt gatt, int status) {
  }

  @Override
  public void onGattDisConnected(boolean isActiveDisConnected, BleDevice device, BluetoothGatt gatt, int status) {

  }

  @Override
  public void onNotifySuccess(BleDevice bleDevice) {
    // 打开通知操作成功

    FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
    if (!topActivity.getClass().getName().equals(activityName)) {
      return;
    }
    LogUtils.eTag(TAG, "bleUpgrade_onNotifySuccess");
    //增加指令认证，覆盖当前如果是蓝牙主动断开，先认证，后发送请求升级
    BluetoothConection.getInstance().writeDevice2(bleDevice, BluetoothConection.toCMD(BluetoothConection.COMMAND_AUTHENTICATION, BluetoothConection.AUTHENTICATION_APP));

  }

  @Override
  public void onNotifyFailure(BleDevice bleDevice, BleException exception) {
    FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
    if (!topActivity.getClass().getName().equals(activityName)) {
      return;
    }
    showErrorTip(1.0);
  }

  @Override
  public void onWriteSuccess(int current, int total, byte[] justWrite) {
    FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();
    if (!topActivity.getClass().getName().equals(activityName)) {
      return;
    }

  }

  @Override
  public void onCharacteristicChanged(byte[] data) {
    try {
      // 打开通知后，设备发过来的数据将在这里出现
      FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();

      if (!topActivity.getClass().getName().equals(activityName)) {
        return;
      }

      String dataHexString = HexUtil.formatHexString(data).toUpperCase();
      LogUtils.eTag(TAG, "writeDevice2---->onCharacteristicChanged-->" + dataHexString);

      String command = dataHexString
        .substring(
          BluetoothConection.DATA_HEAD.length() + BluetoothConection.DATA_VERSION.length(),
          BluetoothConection.DATA_COMMAND_END_INDEX
        );
      //判断指令返回
      switch (command) {
        case BluetoothConection.COMMAND_AUTHENTICATION:
          LogUtils.eTag(TAG, "COMMAND_AUTHENTICATION Success");

          String commandData = "";
          StringBuffer sb = new StringBuffer();
          sb.append(commandData)
                  .append("03e9")
                  .append("FF")
                  .append("0000");
          commandData = sb.toString();
          BluetoothConection.getInstance().writeDevice2(BluetoothConection.getInstance().bleDevice,
                          BluetoothConection.toCMD(BluetoothConection.COMMAND_SEND, commandData));

          break;
        case BluetoothConection.COMMAND_OBSERVER:

          BlueDataFormat model = BlueDataAnalysisUtils.getCanUpgradeModel(BlueDataAnalysisUtils.dealSingleData(dataHexString));
          if (null == model) {
            return;
          }
          if (TextUtils.isEmpty(model.getParam_data_value())) {
            return;
          }
          if (TextUtils.isEmpty(jobId)){
            return;
          }

          if (mFrom != R.id.activity_device_upgrade){
            return;
          }

          if (BLE_ALLOW_UPGRADE_CODE.equals(model.param_data_value)) {
            if (!isCanUpdate) {
              isCanUpdate = true;
              jumpToUpgradePageAndRestore();
              hideProgress();

            }
          } else {
            isCanUpdate = false;
            hideProgress();
            LogUtils.eTag(TAG, "收到设备回复升级错误码---"+model.param_data_value);
            if (TextUtils.isEmpty(model.param_data_value)) {
              showErrorTip(1.0);
            } else {
              showErrorTip(Double.parseDouble(model.param_data_value));
            }
          }
          LogUtils.eTag(TAG, "收到设备回复升级状态---"+isCanUpdate);
          if (isCanUpdate){
            sendTraceUpgradeResult(BLE_ALLOW_UPGRADE_CODE);
          }else {
            sendTraceUpgradeResult(BLE_ALLOW_UPGRADE_CODE+"----errorCode--"+model.param_data_value);
          }

          break;
        default:
          break;
      }
    } catch (Exception e) {
      LogUtils.i(TAG, "onCharacteristicChanged is error");
    }

  }


  @Subscribe(threadMode = ThreadMode.MAIN, sticky = false)
  public void onOTADataEvent(IotModelEvent iotModelEvent) {


    if (!isAdded()) {
      return;
    }

    if ((mFrom == R.id.activity_device_upgrade) && updateMode == WIFI_UPGRADE_MODEL_CODE) {


      String checkUpgradeStatus = String.format(AwsMqttService.DEVICE_MODEL_UPDATE_ACCEPTED_TOPIC, deviceId);

      // aws/things/TCH17230000002X/jobs/1753315820404240386/action/result 新版本
      String checkUpgradeStatus2 = String.format(AwsMqttService.WIFI_UPGRADE_RESULT, deviceId, jobId);

      LogUtils.e(TAG, "topic--->" + iotModelEvent.topic, "mqttdata--->" + ConvertUtils.bytes2String(iotModelEvent.modelData));


      //兼容WIFI或者4G设备丢包---'aws/things/TCH17230000002X/jobs/1753315820404240386/action/result‘
      if (iotModelEvent.topic.equals(curentUpgradeStatusV2)) {
        isCanUpdate = true;
        jumpToUpgradePageAndRestore();
      }
      //旧版本WIFI升级详情
      else if (iotModelEvent.topic.equals(checkUpgradeStatus)) {
        WifiUpgradeAllow allow = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), WifiUpgradeAllow.class);
        String allowString = ConvertUtils.bytes2String(iotModelEvent.modelData);

        if (TextUtils.isEmpty(allowString)) {
          return;
        }
        //如果设备状态是离线那么就跳出下面状态
        if (!wifi_device_online) {
          return;
        }

        if (!allowString.contains(WIFI_UPGRADE_ALLOW_CODE)) {
          return;
        }

        if (allow != null) {
          if (null != allow.getState()) {
            if (null != allow.getState().getReported()) {
              if (allow.getState().getReported().getUpgradeAble() == -1) {
                //upgradeAble为默认值说明未解析到1001
                return;
              }
              hideProgress();
              if (allow.getState().getReported().getUpgradeAble() != 0) {
                showErrorWifiTip(allow.getState().getReported().getUpgradeAble());
                isCanUpdate = false;
                FileHelper.appendToFile(getContext(), "设备返回升级状态--" + isCanUpdate);

              } else {
                sendTraceUpgradeResult(BLE_ALLOW_UPGRADE_CODE);

                isCanUpdate = true;
                FileHelper.appendToFile(getContext(), "设备返回升级状态--" + isCanUpdate);

                //移除topic
                AwsMqttService.unsubscribeTopic(checkUpgradeStatus);
                //todo wangheng R fix bug
                if ("4g".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
                  AwsMqttService.unsubscribeTopic(curentUpgradeStatus);
                  AwsMqttService.unsubscribeTopic(curentUpgradeStatusV2);
                }
                jumpToUpgradePageAndRestore();
              }
            }

          }
        }
      } else if (iotModelEvent.topic.equals(checkUpgradeStatus2)) {
        //TODO 2024/2/29新增WIFI的OTA升级
        WifiUpgradeAllow2 allow2 = GsonUtils.fromJson(ConvertUtils.bytes2String(iotModelEvent.modelData), WifiUpgradeAllow2.class);
        //设备可升级状态
        final String WIFI_DEVICE_CAN_UPGRADE = "0";
        if (null == allow2) {
          //设备端返回数据解析异常
          return;
        }
        //如果设备状态是离线
        if (!wifi_device_online) {
          ToastUtils.showLong(LanguageStrings.app_OTAguide_offline_textview_text());
          return;
        }
        if (TextUtils.isEmpty(allow2.getOtaStatus())) {
          //设备端升级状态数据解析异常
          return;
        }

        hideProgress();

        if (WIFI_DEVICE_CAN_UPGRADE.equals(allow2.getOtaStatus())) {
          //移除topic
          AwsMqttService.unsubscribeTopic(checkUpgradeStatus2);
          jumpToUpgradePageAndRestore();
          isCanUpdate = true;
        } else {
          showErrorWifiTip(Integer.parseInt(allow2.getOtaStatus()));
          isCanUpdate = false;
        }

      } else if ((iotModelEvent.topic.equals(curentUpgradeStatus) || iotModelEvent.topic.equals(curentUpgradeStatusV2)) && "4g".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
        isCanUpdate = true;
        //todo wangheng R fix bug
        //移除topic
        String allowString = ConvertUtils.bytes2String(iotModelEvent.modelData);
        AwsMqttService.unsubscribeTopic(checkUpgradeStatus);
        AwsMqttService.unsubscribeTopic(curentUpgradeStatus);
        AwsMqttService.unsubscribeTopic(curentUpgradeStatusV2);
        jumpToUpgradePageAndRestore();
      }
    }
  }

  private void showErrorTip(Double isCUpdate) {
    LogUtils.e("升级状态---->" + isCUpdate);
    sendTraceUpgradeResult(isCUpdate + "");
    if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE1) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode1_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE2) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode2_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE3) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode3_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE4) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode4_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE6) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode6_textview_text());
    } else if(isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE5){
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode5_textview_text());
    }else if(isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE8){
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode8_textview_text());
    }else {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode1_textview_text());
    }

    guidePageEmptyThenBack();
  }

  private void showErrorWifiTip(int isCUpdate) {
    if(lastToastTime == 0){
      lastToastTime = System.currentTimeMillis();
    }else if(System.currentTimeMillis()-lastToastTime <=2000){
      lastToastTime = System.currentTimeMillis();
      return;
    }
    lastToastTime = System.currentTimeMillis();
    LogUtils.e("升级状态---->" + isCUpdate);
    sendTraceUpgradeResult(isCUpdate + "");
    if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE1) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode1_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE2) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode2_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE3) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode3_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE4) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode4_textview_text());
    } else if (isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE6) {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode6_textview_text());
    }  else if(isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE5){
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode5_textview_text());
    }else if(isCUpdate == ERROR_CODE_APP_OTAGUIDE_CANNOTUPRAGECODE8){
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode8_textview_text());
    }else {
      ToastUtils.showLong(LanguageStrings.app_OTAguide_cannotupragecode1_textview_text());
    }

    guidePageEmptyThenBack();
  }

  /**
   * 上报升级结果
   *
   * @param isCUpdate
   */
  private void sendTraceUpgradeResult(String isCUpdate) {
    DeviceUpgradeModel upgradeModel = new DeviceUpgradeModel(jobId, deviceId, isCUpdate + "");
    TracePoints.deviceCanUpgrade(UserInfo.get().getEmail(), new Gson().toJson(upgradeModel));
  }

  private void jumpToUpgradePageAndRestore() {

    //设备状态置为初始化
    wifi_device_online = false;
    Bundle bundle = getArguments();
    if (null==bundle){
      return;
    }
    if (TextUtils.isEmpty(bundle.getString("jobId"))){
      return;
    }

    ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_UPGRADE_PROCESS).withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
      .withString("deviceId", bundle.getString("deviceId"))
      .withSerializable("deviceInfo", deviceInfo)
      .withString("jobId", bundle.getString("jobId"))
      .withInt("upgradeModel", bundle.getInt("upgradeModel"))
      .withBoolean("singleMcu", bundle.getBoolean("singleMcu"))
      .navigation();
    currentPageNum = 0;
    updatePageId();
    EventBus.getDefault().unregister(this);
    if (mFrom == R.id.activity_device_upgrade) {
      String checkUpdateTopic = String.format(AwsMqttService.DEVICE_MODEL_GET_ACCEPTED_TOPIC, deviceId);
      AwsMqttService.unsubscribeTopic(checkUpdateTopic);
      String checkAllowTopic = String.format(AwsMqttService.OTA_JOB_ACTION_ACCEPTED_TOPIC, deviceId, jobId);
      AwsMqttService.unsubscribeTopic(checkAllowTopic);
      // todo wangheng fix r bug  IOT-10603
      if ("4g".equalsIgnoreCase(deviceInfo.getCommunicateMode())) {
        AwsMqttService.unsubscribeTopic(curentUpgradeStatus);

        AwsMqttService.unsubscribeTopic(curentUpgradeStatusV2);
      }
    }
//    if (isAdded() && isGuidePageEmpty) {
    getActivity().finish();
//    }
  }

  @Override
  public void onPause() {
    super.onPause();

    BaseApplication.setShowDialogMessage(false);

  }

  private void initTrace() {
    stayEleId = "3";
    updatePageId();
    if (mFrom == R.id.activity_device_upgrade) {
      mouduleId = "7";
    } else {
      mouduleId = "6";
    }

    nextButtoneleid = "2";
  }

  public void sendNextBottonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  @Override
  public void onDestroy() {
    super.onDestroy();

    EventBus.getDefault().unregister(this);
    //BluetoothConection.getInstance().removeListener(this);
  }

  @Override
  public void sendStayDuration() {

    if (mFrom == R.id.activity_device_upgrade) {
      if (firstEnter) {
        firstEnter = false;
      } else {
        super.sendStayDuration();
      }
    } else {
      super.sendStayDuration();
    }
  }

  @Override
  public void sendBaseExposure() {

    if (mFrom == R.id.activity_device_upgrade) {
      if (firstExposureEnter) {
        firstExposureEnter = false;
      } else {
        super.sendBaseExposure();
      }
    } else {
      super.sendBaseExposure();
    }
  }

  public void guidePageEmptyThenBack() {
    hideProgress();
    //  OnkeyBack();
  }
}
