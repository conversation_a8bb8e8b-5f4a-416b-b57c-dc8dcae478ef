package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import static com.chervon.moudleConfigNet.ui.WifiSelectFragment.WIFI_INFO;

import android.Manifest;
import android.annotation.SuppressLint;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.DialogFragment;
import com.chervon.libBase.utils.LocationUtils;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.data.model.WifiInfoEntity;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentWifiInputBinding;
import com.chervon.moudleConfigNet.ui.state.Sites;
import com.chervon.moudleConfigNet.ui.state.WifiInputUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.WifiInputViewModel;
import com.chervon.moudleConfigNet.utils.WifiUtil;
import com.tbruyelle.rxpermissions2.RxPermissions;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/25
 * @UpdateRemark: 区分欧洲CRM同步设备跳转
 * @Version: 1.1
 */
public class WifiInputFragment extends BaseEnhanceFragment<WifiInputViewModel, MoudleConfignetFragmentWifiInputBinding>
        implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "WifiInputFragment";
  public DeviceInfo mDeviceInfo;
  private final int MAX_EDIT_LENGTH = 31;
  private Drawable etShapeNomarl;
  private Drawable etShapeFocus;
  private Drawable etShapeError;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    Bundle bundle = getArguments();
    checkGPSStatus();
    WifiInputUIState uiSate = mViewDataBinding.getUiState();
    if (uiSate == null) {
      uiSate = new WifiInputUIState();
      mViewDataBinding.setUiState(uiSate);
    }
    if (bundle != null) {
      mDeviceInfo = (DeviceInfo) bundle.getSerializable(PARAMETER_DEVICD);
      WifiInfoEntity wifiInfo = (WifiInfoEntity) bundle.get(WIFI_INFO);
      if (wifiInfo != null) {
        uiSate.setWifiName(wifiInfo.getSsid());
      } else {
        setWifiManager();
      }

    }
    mViewDataBinding.btnJoinWifi.setEnabled(false);
    mViewDataBinding.btnJoinWifi.setClickable(false);
    mViewDataBinding.setUiState(uiSate);
    setTitle(LanguageStrings.app_networkconfigwifiinput_title_textview_text());
    addExitTextListener();

    etShapeFocus = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_focus);
    etShapeNomarl = getResources().getDrawable(com.chervon.libBase.R.drawable.module_login_et_shape);
    etShapeError = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_error);
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_wifi_input;
  }

  private void addExitTextListener(){

    mViewDataBinding.etWifi.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus){
          mViewDataBinding.llWifi.setBackground(etShapeFocus);
        }else {
          String etWifiStr = mViewDataBinding.etWifi.getText().toString();

          if (TextUtils.isEmpty(etWifiStr)){
            mViewDataBinding.tvTipSsid.setVisibility(View.GONE);
            mViewDataBinding.llWifi.setBackground(etShapeNomarl);
          }

          if (etWifiStr.length()>MAX_EDIT_LENGTH){
            mViewDataBinding.llWifi.setBackground(etShapeError);
          }else {
            mViewDataBinding.llWifi.setBackground(etShapeNomarl);
          }
        }
      }
    });

    mViewDataBinding.etWifi.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {

      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {

      }

      @Override
      public void afterTextChanged(Editable s) {
        String etWifiStr = mViewDataBinding.etWifi.getText().toString();
        String etPasswordStr = mViewDataBinding.etPassword.getText().toString();

        if (etWifiStr.length()>MAX_EDIT_LENGTH){
          mViewDataBinding.tvTipSsid.setVisibility(View.VISIBLE);
          mViewDataBinding.llWifi.setBackground(etShapeError);
          mViewDataBinding.btnJoinWifi.setEnabled(false);
          mViewDataBinding.btnJoinWifi.setClickable(false);
        }else {
          mViewDataBinding.tvTipSsid.setVisibility(View.GONE);
          mViewDataBinding.llWifi.setBackground(etShapeNomarl);

          if (etPasswordStr.length()>MAX_EDIT_LENGTH){
            mViewDataBinding.btnJoinWifi.setEnabled(false);
            mViewDataBinding.btnJoinWifi.setClickable(false);
          }else {
            if (TextUtils.isEmpty(etWifiStr)||TextUtils.isEmpty(etPasswordStr)){
              mViewDataBinding.btnJoinWifi.setEnabled(false);
              mViewDataBinding.btnJoinWifi.setClickable(false);
            }else {
              mViewDataBinding.btnJoinWifi.setEnabled(true);
              mViewDataBinding.btnJoinWifi.setClickable(true);
            }
          }

        }
      }
    });

    mViewDataBinding.etPassword.setOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus){
          mViewDataBinding.llWifiPassword.setBackground(etShapeFocus);
        }else {
          String etWifiStr = mViewDataBinding.etPassword.getText().toString();

          if (TextUtils.isEmpty(etWifiStr)){
            mViewDataBinding.tvTipSsid.setVisibility(View.GONE);
            mViewDataBinding.llWifiPassword.setBackground(etShapeNomarl);
          }

          if (etWifiStr.length()>MAX_EDIT_LENGTH){
            mViewDataBinding.llWifiPassword.setBackground(etShapeError);
          }else {
            mViewDataBinding.llWifiPassword.setBackground(etShapeNomarl);
          }
        }
      }
    });
    mViewDataBinding.etPassword.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {

      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {

      }

      @Override
      public void afterTextChanged(Editable s) {
        String etPasswordStr = mViewDataBinding.etPassword.getText().toString();
        String etWifiStr = mViewDataBinding.etWifi.getText().toString();

        if (etPasswordStr.length()>MAX_EDIT_LENGTH){
          mViewDataBinding.tvWifiPwdTip.setVisibility(View.VISIBLE);
          mViewDataBinding.llWifiPassword.setBackground(etShapeError);
          mViewDataBinding.btnJoinWifi.setEnabled(false);
          mViewDataBinding.btnJoinWifi.setClickable(false);
        }else {
          mViewDataBinding.tvWifiPwdTip.setVisibility(View.GONE);
          mViewDataBinding.llWifiPassword.setBackground(etShapeNomarl);

          if (etWifiStr.length()>MAX_EDIT_LENGTH){
            mViewDataBinding.btnJoinWifi.setEnabled(false);
            mViewDataBinding.btnJoinWifi.setClickable(false);
          }else {
            if (TextUtils.isEmpty(etWifiStr)||TextUtils.isEmpty(etPasswordStr)){
              mViewDataBinding.btnJoinWifi.setEnabled(false);
              mViewDataBinding.btnJoinWifi.setClickable(false);
            }else {
              mViewDataBinding.btnJoinWifi.setEnabled(true);
              mViewDataBinding.btnJoinWifi.setClickable(true);
            }
          }
        }
      }
    });
  }
  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {
    mViewDataBinding.ibPassword.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        UiHelper.switchPasswordVisibility(mViewDataBinding.ibPassword, mViewDataBinding.etPassword, view);
      }
    });
  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }


  @Override
  protected Class getViewModelClass() {
    return WifiInputViewModel.class;
  }


  private void checkGPSStatus() {
    if (isAdded()) {
      if (Build.VERSION.SDK_INT >= 31) {
        if (!LocationUtils.isGpsEnabled()) {
          showPermissionDialog();
        }
      } else {

        if (!LocationUtils.isGpsEnabled()) {

          showPermissionDialog();

        }

        BaseApplication.gpsResult.observe(this, new Observer<Boolean>() {
          @Override
          public void onChanged(Boolean result) {
            if (result) {
              showPermissionDialog();
            } else {
              DialogFragment.getInstance(LanguageStrings.app_base_gpsswitchalert_textview_text()).dismiss();

            }
          }
        });
      }
    }

  }

  private void showPermissionDialog() {
    DialogFragment.getInstance(LanguageStrings.app_base_gpsswitchalert_textview_text(), new DialogFragment.DialogClickListener() {
      @Override
      public void clickComfirm() {
        Utils.goIntentSetting(getActivity());
      }
    }).showDialog(getFragmentManager(), TAG);
  }


  public void goToSelectWifi() {
    RxPermissions rxPermissions = new RxPermissions(this);
    rxPermissions.request(new String[]{Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION})
      .subscribe(permission -> {
        if (permission) {
          Bundle bundle = getBundle();
          bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
          //欧洲crm同步设备
          if(mFrom == R.id.deviceListFragment) {
            bundle.putInt(KEY_FROM,mFrom);
          }
          goToNextFragment(R.id.wifiSelectFragment, bundle);

        }

      });
    traceDeviceSelectWifi();

  }

  public void goToWifiConnect(WifiInputUIState uiState) {
    traceDeviceConnectWithJoin();
    Bundle bundle = getBundle();
    bundle.putSerializable(WIFI_INFO, uiState);
    bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
    //欧洲crm同步设备
    if(mFrom == R.id.deviceListFragment) {
      bundle.putInt(KEY_FROM,mFrom);
    }
    goToNextFragment(R.id.wifiConnectionFragment, bundle);
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    cancel();
    return true;
  }

  public void cancel() {
    if (0 == ((ConfigNetActivity) getActivity()).getIsResetWifi()) {
      mViewModel.disConnectBluetooth();
      if (mFrom ==  R.id.deviceListFragment) {
        getActivity().finish();
        return;
      }
      NavController controller = Navigation.findNavController(getActivity(), com.chervon.libBase.R.id.nav_host_fragment_content_main);
      controller.popBackStack(R.id.scanNearbyDevicesFragment, true);
      goToNextFragment(R.id.scanNearbyDevicesFragment);
    } else {
      getActivity().finish();
    }

  }


  private void initTrace() {
    stayEleId = "2";
    pageId = "37";
    mouduleId = "6";
    nextButtoneleid = "2";
  }

  /**
   * 点击join埋点
   */
  private void traceDeviceConnectWithJoin(){

    String module_id = "6";
    String page_id = "37";
    String mod_id = "0";
    String ele_id = "3";
    String pageRes = pageResouce;
    sendClickTraceNew(this.getContext(), module_id, page_id, pageRes, ele_id,mod_id);
  }

  private void traceDeviceSelectWifi(){

    String module_id = "6";
    String page_id = "37";
    String mod_id = "0";
    String ele_id = "4";
    String pageRes = pageResouce;
    sendClickTraceNew(this.getContext(), module_id, page_id, pageRes, ele_id,mod_id);
  }

  @SuppressLint({"CheckResult", "NotifyDataSetChanged"})
  private void setWifiManager() {
    RxPermissions rxPermissions = new RxPermissions(this);
    rxPermissions.requestEach(Manifest.permission.ACCESS_FINE_LOCATION)
      .subscribe(permission -> {
        if (permission.name.equalsIgnoreCase(Manifest.permission.ACCESS_FINE_LOCATION)) {
          Sites<WifiInfoEntity> datas = new Sites<>();
          Sites<WifiInfoEntity> otherWifis = new Sites<>();


          WifiUtil wifiUtil = new WifiUtil(this.getContext());
          WifiInfoEntity myNet = null;
          List<WifiInfoEntity> list = wifiUtil.getAroundWifiDeviceInfo();
          for (WifiInfoEntity entity : list) {
            if (entity.isConnected()) {
              myNet = entity;

              break;
            }
          }

          if (myNet != null) {
            WifiInputUIState uiState = mViewDataBinding.getUiState();
            uiState.setWifiName(myNet.getSsid());
            mViewDataBinding.setUiState(uiState);
          }
        }
      });
  }

}
