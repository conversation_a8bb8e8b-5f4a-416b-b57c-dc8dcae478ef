package com.chervon.moudleConfigNet.ui;

import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_DEVICE;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_GEGISTERED;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_RESULT;
import static com.chervon.libRouter.RouterConstants.KEY_ADD_NOT_IOT_SN;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.BIND_SUCCESS;
import static com.chervon.moudleConfigNet.ui.DeviceConnectionFragment.CONNECT_FAIL;
import static com.chervon.moudleConfigNet.ui.ScanCodeFragment.INIT_STATE;

import android.app.Dialog;
import android.os.Bundle;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.NoIotResultUIState;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentNotIotResultBinding;
import com.chervon.moudleConfigNet.ui.viewmodel.NoIotResultViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 非iot设备注册结果页面
 * @Author: name
 * @CreateDate: 2023/10/25
 * @UpdateUser: hyman
 * @UpdateDate: 2023/10/25
 * @UpdateRemark: 更新说明 新增非iot设备注册结果页面--jackson需求
 * @Version: 1.0
 */
public class NoIotResultFragment extends BaseEnhanceFragment<NoIotResultViewModel, MoudleConfignetFragmentNotIotResultBinding>
        implements BaseEnhanceFragment.OnkeyBackListener {
    public static final String TAG = "NoIotResultFragment";
    private String sn;
    private boolean isAdded = false;

    public static final int RETRY_ADD = 3;
    public static final int ADD_IOT_DONE = 4;
    public static final int ADD_IOT_REGISTER = 5;

    public static final int RESPONSE_SUCCESS = 1;
    private DeviceInfo deviceInfo;
    private boolean isRegistered;
    private int from;
    private Dialog mProgressDialog;

    @Override
    protected void initDatas(Bundle savedInstanceState) {

    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_not_iot_result;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        UiHelper.showSystemUI(getActivity().getWindow());
    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {
        mViewModel.setFragment(this);

        Bundle bundle = getArguments();
        if (null == bundle) {
            return;
        }
        isAdded = bundle.getBoolean(KEY_ADD_NOT_IOT_RESULT, false);
        isRegistered = bundle.getBoolean(KEY_ADD_NOT_IOT_GEGISTERED, false);
        sn = bundle.getString(KEY_ADD_NOT_IOT_SN);
        from = bundle.getInt(KEY_FROM);
        deviceInfo = (DeviceInfo) bundle.getSerializable(KEY_PREV_DATA);

        NoIotResultUIState state = new NoIotResultUIState();
        state.setNaEvn((BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA) ? true : false));
        state.setDeviceInfo(deviceInfo);
        state.setRegistered(isRegistered);
        state.setSn(sn);
        state.setAdded(isAdded);


        mViewDataBinding.setUiState(state);
        mViewModel.setState(state);
        mViewDataBinding.setViewModel(mViewModel);

        if (isAdded) {
            upgradeUser();
        }
    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return NoIotResultViewModel.class;
    }


    @Override
    protected void onUiLiveDataChanged(BaseUistate baseUistate) {

        if (null != mProgressDialog) {
            if (mProgressDialog.isShowing()) {
                mProgressDialog.dismiss();
            }

        }

        NoIotResultUIState uiState = (NoIotResultUIState) baseUistate;
        if (uiState.getState() == RESPONSE_SUCCESS) {
            deviceInfo = uiState.getDeviceInfo();
            mViewModel.bindingNotIotDevice();
        } else if (uiState.getState() == CONNECT_FAIL) {

        } else if (uiState.getState() == RETRY_ADD) {

            exitCurrentPage();
        } else if (uiState.getState() == ADD_IOT_DONE) {
            goToHome();
        } else if (uiState.getState() == BIND_SUCCESS) {

            mViewDataBinding.setUiState(uiState);
            mViewModel.setState(uiState);


        } else if (uiState.getState() == ADD_IOT_REGISTER) {
            registerDevice(deviceInfo);
        }
        uiState.setState(INIT_STATE);
    }

    private void registerDevice(DeviceInfo deviceInfo) {
        //进入注册流程
        ARouter.getInstance().build(ACTIVITY_URL_DEVICE)
                .withSerializable(KEY_PREV_DATA, deviceInfo)
                .withInt(KEY_PREV_FRAGMENT, 1234)
                .navigation();

        getActivity().finish();

    }

    /**
     * 更新评分系统
     */
    private void upgradeUser() {
        String isAddDevice = "isAddDevice";
        User user = UserInfo.get();
        user.setIsAddDevice(isAddDevice);
        UserInfo.set(user);
    }

    private void exitCurrentPage() {
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.popBackStack();
        ConfigNetActivity.add_device_by_sn = sn;
    }

    private void goToHome() {

        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE_NON_IOT_PANEL)
                .withInt(KEY_FROM, R.layout.moudle_confignet_fragment_not_iot_result)
                .withSerializable(KEY_PREV_DATA, deviceInfo)
                .navigation();

        getActivity().finish();

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return this;
    }

    @Override
    public boolean OnkeyBack() {
        return false;
    }
}
