package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.APPConstants.IS_RESET_WIFI_CONNECTION;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.KEY_TO;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_CONFIG;
import static com.chervon.libRouter.RouterConstants.MOBLIE_WIFI_MORE_SOLUTIONS;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.NavDestination;
import androidx.navigation.NavGraph;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.chervon.libBase.model.ToolbarData;
import com.chervon.libBase.ui.BaseActivity;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetActivityConfignetBinding;
import com.chervon.moudleConfigNet.ui.viewmodel.ConfigNetViewModel;
import com.clj.fastble.BleManager;

import java.util.ArrayList;

import me.jessyan.autosize.AutoSize;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ConfigNetActivity
 * @Description: Main activity of ConfigNet moudle
 * @Author: wangheng
 * @CreateDate: 2022/5/10 下午5:31
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/24
 * @UpdateRemark: 欧洲CRM需求 首页点击跳转配网引导
 * @Version: 1.1
 */
@Route(path = RouterConstants.ACTIVITY_URL_CONFIGNET)
public class ConfigNetActivity extends BaseActivity<ConfigNetViewModel> {
  private MoudleConfignetActivityConfignetBinding mBinding;
  private ToolbarData mToolbarData;
  private int isResetWifi = 0;

  //保存输入SN码
  public static String add_device_by_sn = "";
  //  private  NavController navController;
  // private int navId;
  @Override
  protected void onUnRegister() {
  }

  @Override
  protected void onRegister() {

  }


  @Override
  protected Integer getLayoutId() {
    return R.layout.moudle_confignet_activity_confignet;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {

    mBinding = (MoudleConfignetActivityConfignetBinding) viewDataBinding;

    mBinding.toolbar.setNavigationOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });
    mToolbarData = new ToolbarData("", new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        goBack();
      }
    });
    mBinding.setToolbarData(mToolbarData);
    initNavigation();
  }

  private void initNavigation() {
    NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
    navController.addOnDestinationChangedListener(new NavController.OnDestinationChangedListener() {
      @Override
      public void onDestinationChanged(@NonNull NavController controller, @NonNull NavDestination destination, @Nullable Bundle arguments) {

        @SuppressLint("RestrictedApi")
        int size = controller.getBackStack().size();
        if (size==0){
          finish();
        }

        mBinding.clBar.getRoot().setVisibility(View.VISIBLE);
        //    mToolbarData.setTitle(getTitle().toString());
        setKeyBackListener(null);

        if (R.id.scanCodeFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.appDeviceqrcodescanTitleTextviewText());
          mBinding.clBar.getRoot().setVisibility(View.GONE);
        } else if (R.id.manualSNFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.getDevicesninput());
        } else if (R.id.deviceCardAddFragment == destination.getId()) {
          //     mToolbarData.setTitle(getString(com.chervon.libBase.R.string.device_add));
        } else if (R.id.deviceSearchFragment == destination.getId()) {
          mBinding.clBar.getRoot().setVisibility(View.GONE);
        } else if (R.id.deviceConnectionFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.getBlutoothconnectTitle());
        } else if (R.id.wifiInputFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.app_networkconfigwifiinput_title_textview_text());
        } else if (R.id.wifiSelectFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.app_base_selectwifi_button_text());
        } else if (R.id.wifiConnectionFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.app_networkconfigwificonnect_title_textview_text());
        } else if (R.id.scanNearbyDevicesFragment == destination.getId()) {
          mToolbarData.setTitle("");
        } else if (R.id.deviceGuidePageOneFragment == destination.getId()
          || R.id.deviceGuidePagetwoFragment == destination.getId()
          || R.id.deviceGuidePageThreeFragment == destination.getId()) {
          mToolbarData.setTitle(LanguageStrings.getConfiginitialize());
        } else if (R.id.noIotResultFragment == destination.getId()){
          mToolbarData.setTitle(LanguageStrings.app_notiotbindresult_title_textview_text());
        } else if (R.id.wifiMoreSolutionsFragment == destination.getId()){
          mToolbarData.setTitle(LanguageStrings.app_moresolutions_title_textview_text());
        }else {
          //  mToolbarData.setTitle("");
        }
        mBinding.setToolbarData(mToolbarData);
      }
    });
  }

  private void goToNextPage(int page, Bundle bundle) {
    NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
    controller.navigate(page, bundle);
  }

  private void goBack() {
    if (mkeyBackListener != null) {
      if (mkeyBackListener.OnkeyBack()) {
        mkeyBackListener = null;
        return;
      }
    }
    int size = Navigation.findNavController(this, R.id.nav_host_fragment_content_main).getBackStack().size();
    if (size == 0) {
      finish();
    } else {

      int id = Navigation.findNavController(this, R.id.nav_host_fragment_content_main).getCurrentDestination().getId();
      Navigation.findNavController(this, R.id.nav_host_fragment_content_main).popBackStack();

    }
  }

  private static final String[] PERMISSIONSGROUP = new String[]{Manifest.permission.INTERNET,
    Manifest.permission.SYSTEM_ALERT_WINDOW,
    Manifest.permission.BLUETOOTH,
    Manifest.permission.BLUETOOTH_ADMIN

  };

  @Override
  protected void initData(Bundle savedInstanceState) {
    ActivityCompat.requestPermissions(this, PERMISSIONSGROUP, 321);
    mViewModel.initBluetooth(this.getApplication());
    Bundle bundle = getIntent().getExtras();
    if (bundle != null) {
      int fragmentId = getIntent().getExtras().getInt(KEY_PREV_FRAGMENT);
      int from = getIntent().getExtras().getInt(KEY_FROM);
      int pageTo = getIntent().getExtras().getInt(KEY_TO);
      isResetWifi = getIntent().getExtras().getInt(IS_RESET_WIFI_CONNECTION);
      // TODO 72002 适配
      if(isResetWifi==1){
        BluetoothConection.getInstance().disConnectDevice();
      }


      if (fragmentId == R.id.deviceListFragment) {
        if (pageTo == R.id.deviceGuidePageOneFragment) {
          Bundle bundle1 = new Bundle();
          bundle1.putSerializable(PARAMETER_DEVICD, bundle.getSerializable(PARAMETER_DEVICD));
          bundle1.putInt(KEY_FROM, R.id.deviceListFragment);
          goToNextPage(pageTo,bundle1);
          return;
        }
        goToNextPage(R.id.bluetoothGuidePageFragment, bundle);
      }

      if (from == MOBLIE_WIFI_MORE_SOLUTIONS){

        goToNextPage(pageTo, bundle);
      }

      //WIFI设备offline后RN面板跳转至配网页面
      if (fragmentId == MOBLIE_WIFI_CONFIG){
        goToNextPage(pageTo, bundle);
      }

      if (from == R.id.deviceRegisterMoreFragment) {
        goToNextPage(pageTo, bundle);
      }

      if (pageTo > 0) {
        goToNextPage(pageTo, bundle);
      }
    }
  }

  @Override
  protected Class<? extends ConfigNetViewModel> getViewModelClass() {
    return ConfigNetViewModel.class;
  }
  @Override
  public void setTitleName(String string) {
    mToolbarData.setTitle(string);
    mBinding.setToolbarData(mToolbarData);
  }

  @Override
  public void onConfigurationChanged(Configuration newConfig) {
    // TODO Auto-generated method stub
    super.onConfigurationChanged(newConfig);
    DialogUtil.clearDialog();
    //  if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
    AutoSize.autoConvertDensityOfGlobal(this);
    //    }
  }


  @Override
  public boolean onKeyDown(int keyCode, KeyEvent event) {
    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
      goBack();

    }
    return false;
  }

  public void setNavTitle(String title) {
    mToolbarData.setTitle(title);
    mBinding.setToolbarData(mToolbarData);
  }




  public int getIsResetWifi() {
    return isResetWifi;
  }


}
