package com.chervon.moudleConfigNet.ui;

import static com.chervon.libBase.utils.DialogFragment.GPS_ENABLE_DIALOG_KEY;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_FRAGMENT;
import static com.chervon.libRouter.RouterConstants.PARAMETER_DEVICD;
import android.Manifest;
import android.annotation.SuppressLint;
import android.os.Bundle;
import android.view.View;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.utils.DialogFragment;
import com.chervon.libBase.utils.LocationUtils;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.data.model.WifiInfoEntity;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentWifiSelectBinding;
import com.chervon.moudleConfigNet.ui.adapter.SitesAdapter;
import com.chervon.moudleConfigNet.ui.adapter.WifiListAdapter;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.moudleConfigNet.ui.state.Sites;
import com.chervon.moudleConfigNet.ui.viewmodel.ManualSNViewModel;
import com.chervon.moudleConfigNet.utils.WifiUtil;
import com.tbruyelle.rxpermissions2.RxPermissions;
import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/25
 * @UpdateRemark: 区分欧洲CRM同步设备跳转
 * @Version: 1.1
 */
public class WifiSelectFragment extends BaseEnhanceFragment<ManualSNViewModel, MoudleConfignetFragmentWifiSelectBinding>
  implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "ScanNearbyDevicesFragment";
  public static final String WIFI_INFO = "wifiInfo";
  private WifiListAdapter mWifiListAdapter;
  private DeviceInfo mDeviceInfo;


  @Override
  protected void initDatas(Bundle savedInstanceState) {
    initTrace();
    Bundle bundle = getArguments();
    if (bundle != null) {
      mDeviceInfo = (DeviceInfo) bundle.getSerializable(PARAMETER_DEVICD);
    }

    mViewDataBinding.setUiState(new ManualSNUIState());
    mViewDataBinding.setPresenter(mViewModel);

    mViewModel.mLiveData.observe(this, new Observer<ManualSNUIState>() {
      @Override
      public void onChanged(ManualSNUIState manualSNUIState) {
        DeviceInfo deviceInfo = manualSNUIState.getDeviceInfo();
        if (deviceInfo != null) {
          if ("2".equals(deviceInfo.getProductType())) {
//                        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
//                        controller.navigate(R.id.action_manualSNFragment_to_deviceInitFragment);
          } else {
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_DEVICE).withInt(KEY_PREV_FRAGMENT, R.id.manualSNFragment)
              .withSerializable(KEY_PREV_DATA, deviceInfo).navigation();
          }
        }
      }
    });


    List<Object> items = new ArrayList<>();
    mWifiListAdapter = new WifiListAdapter(getActivity(), items);
    LinearLayoutManager gridLayoutManager = new LinearLayoutManager(getActivity());
    mViewDataBinding.rvWifiList.setLayoutManager(gridLayoutManager);
    mViewDataBinding.rvWifiList.setAdapter(mWifiListAdapter);
    mWifiListAdapter.setOnItemClickListener(new WifiListAdapter.OnItemClickListener() {
      @Override
      public void onClick(View itemview, int position) {
        switch (mWifiListAdapter.getItemViewType(position)) {
          case SitesAdapter.SITE:
            WifiInfoEntity wifiInfo = (WifiInfoEntity) mWifiListAdapter.getItems().get(position);
            Bundle bundle = new Bundle();
            bundle.putSerializable(WIFI_INFO, wifiInfo);
            bundle.putSerializable(PARAMETER_DEVICD, mDeviceInfo);
            //欧洲crm同步设备
            if(mFrom == R.id.deviceListFragment) {
              bundle.putInt(KEY_FROM,mFrom);
            }
            goToNextFragment(R.id.wifiInputFragment, bundle);
            break;
          case SitesAdapter.SITES:
            //   ToastUtils.showToast(getActivity(), ((Sites) items.get(position)).getName());
            break;
        }
      }
    });
    setTitle(LanguageStrings.app_base_selectwifi_button_text());



    checkGpsSwitch();
  }

  private void checkGpsSwitch(){
    if (isAdded()){
      if (LocationUtils.isGpsEnabled()){
        LogUtils.e("ScanNearbyDevicesFragment","GPS开关状态---开");
        setWifiManager();
      }else {
        showPermissionDialog();
      }

      BaseApplication.gpsResult.observe(this, new Observer<Boolean>() {
        @Override
        public void onChanged(Boolean result) {
          if (result) {
            showPermissionDialog();
          } else {
            DialogFragment.getInstance(LanguageStrings.app_base_gpsswitchalert_textview_text()).dismiss();
          }
        }
      });
    }

  }


  private void showPermissionDialog() {
    DialogFragment.getInstance(LanguageStrings.app_base_gpsswitchalert_textview_text(), new DialogFragment.DialogClickListener() {
      @Override
      public void clickComfirm() {
        Utils.goIntentSetting(getActivity());
      }
    }).showDialog(getFragmentManager(), TAG);
  }



  @Override
  protected int getLayoutId() {
    return R.layout.moudle_confignet_fragment_wifi_select;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {


  }

  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class getViewModelClass() {
    return ManualSNViewModel.class;
  }


  @SuppressLint({"CheckResult", "NotifyDataSetChanged"})
  private void setWifiManager() {
    RxPermissions rxPermissions = new RxPermissions(this);
    rxPermissions.requestEach(Manifest.permission.ACCESS_FINE_LOCATION)
      .subscribe(permission -> {
        if (permission.name.equalsIgnoreCase(Manifest.permission.ACCESS_FINE_LOCATION)) {
          Sites<WifiInfoEntity> datas = new Sites<>();
          Sites<WifiInfoEntity> otherWifis = new Sites<>();
          WifiUtil wifiUtil = new WifiUtil(this.getContext());
          WifiInfoEntity myNet = null;
          List<WifiInfoEntity> list = wifiUtil.getAroundWifiDeviceInfo();
          for (WifiInfoEntity entity : list) {
            if (entity.isConnected()) {
              myNet = entity;
              myNet.setConnected(true);
              list.remove(entity);
              break;
            }
          }

          List<Sites> sites = new ArrayList<>();
          if (myNet != null) {
            datas.setName(LanguageStrings.app_selectwifi_mynetworks_textview_text());
            List<WifiInfoEntity> myWifiList = new ArrayList<WifiInfoEntity>();
            myWifiList.add(myNet);
            datas.setSites(myWifiList);
            sites.add(datas);
          }

          otherWifis.setName(LanguageStrings.app_selectwifi_othernetworks_textview_text());
          otherWifis.setSites(list);
          sites.add(otherWifis);
          List<Object> items = new ArrayList<>();
          //数据获取之后  将数据循环遍历，放进items集合中，至于服务器返回什么格式的数据，我想看下实体类就应该明白了
          for (int i = 0; i < sites.size(); i++) {
            items.add(sites.get(i));
            for (int k = 0; k < sites.get(i).getSites().size(); k++) {
              items.add(sites.get(i).getSites().get(k));
            }
          }
          mWifiListAdapter.setItems(items);
          mWifiListAdapter.notifyDataSetChanged();
        }
      });
  }


  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }


  private void initTrace() {
    stayEleId = "2";
    pageId = "37";
    mouduleId = "6";
    nextButtoneleid = "2";
  }

  public void sendConnectBottonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }


  @Override
  public boolean OnkeyBack() {
    sendBackTrace();
    return false;
  }
}
