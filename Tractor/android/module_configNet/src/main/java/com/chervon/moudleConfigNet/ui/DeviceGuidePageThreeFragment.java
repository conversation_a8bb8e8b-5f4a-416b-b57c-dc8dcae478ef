package com.chervon.moudleConfigNet.ui;

import android.os.Bundle;
import android.view.View;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;

import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.moudleConfigNet.R;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentGuidePageBinding;
import com.chervon.moudleConfigNet.databinding.MoudleConfignetFragmentManualSnBinding;
import com.chervon.libBase.model.ManualSNUIState;
import com.chervon.moudleConfigNet.ui.viewmodel.ManualSNViewModel;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui
 * @ClassName: ScanNearbyDevicesFragment
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午5:54
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午5:54
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceGuidePageThreeFragment extends BaseEnhanceFragment<ManualSNViewModel, MoudleConfignetFragmentGuidePageBinding> {
    public static final String TAG = "ScanNearbyDevicesFragment";


    @Override
    protected void initDatas(Bundle savedInstanceState) {
      //  mViewDataBinding.setUiState(new ManualSNUIState());
     //   mViewDataBinding.setPresenter(mViewModel);
        mViewModel.mLiveData.observe(this, new Observer<ManualSNUIState>() {
            @Override
            public void onChanged(ManualSNUIState manualSNUIState) {

            }
        });

   //     mViewDataBinding.ivGuidPage.setImageResource(R.drawable.guide_page3);
        mViewDataBinding.btnNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Bundle bundle = getArguments();
                goToNextFragment(R.id.action_deviceGuidePageThreeFragment_to_deviceConnectionFragment,bundle);
            }
        });

    }

    @Override
    protected int getLayoutId() {
        return R.layout.moudle_confignet_fragment_guide_page;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {


    }

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {

    }

    @Override
    protected void onLowMemoryProcessed() {

    }

    @Override
    protected Class getViewModelClass() {
        return ManualSNViewModel.class;
    }


    @Override
    protected void onUiLiveDataChanged(BaseUistate uiState) {

    }

    @Override
    protected LifecycleOwner getLifecycleOwner() {
        return null;
    }
}
