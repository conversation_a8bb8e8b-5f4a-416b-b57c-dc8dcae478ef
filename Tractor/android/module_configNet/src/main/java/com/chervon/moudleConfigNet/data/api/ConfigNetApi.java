package com.chervon.moudleConfigNet.data.api;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.libNetwork.http.RetrofitMethod;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libBase.model.ProductsParams;
import com.chervon.libBase.model.ProductEntry;
import com.chervon.libDB.entities.ProductInfo;
import com.chervon.moudleConfigNet.data.model.BindingEntry;
import com.chervon.libBase.model.SnScanEntry;
import com.chervon.moudleConfigNet.data.request.DeviceDetailBySnReq;
import com.chervon.moudleConfigNet.data.request.ProductBySnReq;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;

import java.util.HashMap;

import io.reactivex.Observable;
import okhttp3.MediaType;
import okhttp3.RequestBody;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.data.api
 * @ClassName: ConfigNetApi
 * @Description: the Application Program Interface of Login moudle
 * @Author: wangheng
 * @CreateDate: 2022/4/25 下午5:37
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/25 下午5:37
 * @UpdateRemark: wangheng
 * @Version: 1.0
 */
public class ConfigNetApi extends RetrofitMethod {
  private ConfigNetService service;

  public ConfigNetApi() {
    service = createService(ConfigNetService.class);
  }

  public Observable<HttpResponse<DeviceInfo>> getDetailBySn(String deviceId) {
    String body = new Gson().toJson(new DeviceDetailBySnReq(deviceId));
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getDeviceBySn(requestBody);
  }


  public Observable<HttpResponse<ProductInfo>> getProductDetail(String req) {
    String body = "{ \"req\": \"" + req + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getProductDetail(requestBody);
  }

  public Observable<HttpResponse<ProductInfo>> getProductDetailByPid(String pid) {
    String body = "{ \"req\": \"" + pid + "\"}";
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getProductDetailByPid(requestBody);
  }



  public Observable<HttpResponse<ProductEntry>> getProducts(ProductsParams params) {

    ObjectMapper objectMapper = new ObjectMapper();
    String elementStr = null;
    try {
      elementStr = objectMapper.writeValueAsString(params);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), elementStr);

    return service.getProducts(requestBody);
  }

  public Observable<HttpResponse<SnScanEntry>> getProductBySn(String sn) {
//        String body = "{ \"req\": \"" + sn + "\"}";
    String body = new Gson().toJson(new ProductBySnReq(sn));
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), body);
    return service.getProductBySn(requestBody);
  }

  public Observable<HttpResponse<BindingEntry>> addDevice(String deviceID, String mac) {
    HashMap<String, String> stringStringHashMap = new HashMap<>();
    stringStringHashMap.put("deviceId", deviceID);
    stringStringHashMap.put("mac", mac);
    String s = new Gson().toJson(stringStringHashMap);
    LogUtils.d("Http_Binding_request" + s);
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), s);
    return service.bindDevice(requestBody);
  }


  public Observable<HttpResponse<BindingEntry>> bindDeviceWithSn(String sn, String mac) {
    HashMap<String, String> stringStringHashMap = new HashMap<>();
    stringStringHashMap.put("mac", mac);
    stringStringHashMap.put("sn", sn);
    String s = new Gson().toJson(stringStringHashMap);
    RequestBody requestBody = RequestBody.create(MediaType.parse("application/json"), s);
    return service.bindDeviceWithSn(requestBody);
  }


  interface ConfigNetService {

    @POST("/app-hmac/device/getProductIdBySn")
    Observable<HttpResponse<SnScanEntry>> getProductBySn(@Body RequestBody sn);


    @POST("/app-hmac/device/detailBySn")
    Observable<HttpResponse<DeviceInfo>> getDeviceBySn(@Body RequestBody sn);

    @POST("/app-hmac/product/list")
    Observable<HttpResponse<ProductEntry>> getProducts(@Body RequestBody params);

    @POST(" /app-hmac/device/detail")
    Observable<HttpResponse<DeviceInfo>> getDeviceDetail(@Body RequestBody deviceId);

    @POST("/app-hmac/product/detail")
    Observable<HttpResponse<ProductInfo>> getProductDetail(@Body RequestBody req);

    @POST("/app-hmac/device/bind")
    Observable<HttpResponse<BindingEntry>> bindDevice(@Body RequestBody body);

    @POST("/app-hmac/device/bindBySn")
    Observable<HttpResponse<BindingEntry>> bindDeviceWithSn(@Body RequestBody requestBody);

    @POST("/app-hmac/product/detail/pid")
    Observable<HttpResponse<ProductInfo>> getProductDetailByPid(@Body RequestBody params);
  }

}
