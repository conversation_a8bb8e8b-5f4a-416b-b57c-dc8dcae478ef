package com.chervon.libDB.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.chervon.libDB.entities.AppSettingEntry;
import com.chervon.libDB.entities.AppVersionEntry;

/**
 * @ProjectName: app
 * @Package: com.chervon.libDB.dao
 * @ClassName: AppSettingDao
 * @Description: Dao  of AppSettingDao
 * @Author: hyman
 * @CreateDate: 2023/9/25
 * @UpdateUser: hyman
 * @UpdateDate: 2023/9/25
 * @UpdateRemark: new
 * @Version: 1.0
 */
@Dao
public interface AppVersionEntryDao {
  @Insert(onConflict = OnConflictStrategy.REPLACE)
  void insertAppVersion(AppVersionEntry user);


  @Query("SELECT * FROM cvn_app_version" )
  AppVersionEntry getAppVersion();

  @Update(onConflict = OnConflictStrategy.REPLACE)
  void updateAppVersion(AppVersionEntry appSetting);

}
