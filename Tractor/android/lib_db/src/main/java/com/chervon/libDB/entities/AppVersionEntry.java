package com.chervon.libDB.entities;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "cvn_app_version")
public class AppVersionEntry {
    @NonNull
    @PrimaryKey
    @ColumnInfo(name = "id" )
    private String id;

    @ColumnInfo(name = "versionName")
    private String versionName;

    @ColumnInfo(name = "versionCode", defaultValue = "0")
    private int versionCode;

    @ColumnInfo(name = "forceDialog", defaultValue = "0")
    private int forceDialog;

    @ColumnInfo(name = "lastCheckVersionTime")
    private String lastCheckVersionTime;

    public AppVersionEntry() {
    }

    public AppVersionEntry(@NonNull String id, String versionName, int versionCode, int forceDialog, String lastCheckVersionTime) {
        this.id = id;
        this.versionName = versionName;
        this.versionCode = versionCode;
        this.forceDialog = forceDialog;
        this.lastCheckVersionTime = lastCheckVersionTime;
    }

    @NonNull
    public String getId() {
        return id;
    }

    public void setId(@NonNull String id) {
        this.id = id;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public int getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }

    public int getForceDialog() {
        return forceDialog;
    }

    public void setForceDialog(int forceDialog) {
        this.forceDialog = forceDialog;
    }

    public String getLastCheckVersionTime() {
        return lastCheckVersionTime;
    }

    public void setLastCheckVersionTime(String lastCheckVersionTime) {
        this.lastCheckVersionTime = lastCheckVersionTime;
    }
}
