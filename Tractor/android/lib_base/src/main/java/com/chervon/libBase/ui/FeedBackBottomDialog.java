package com.chervon.libBase.ui;

import android.content.Context;
import android.preference.PreferenceManager;

import androidx.annotation.NonNull;

import com.google.android.material.bottomsheet.BottomSheetDialog;

public class FeedBackBottomDialog extends BottomSheetDialog {
  public FeedBackBottomDialog(@NonNull Context context) {
    super(context);
  }

  public FeedBackBottomDialog(@NonNull Context context, int theme) {
    super(context, theme);
  }

  protected FeedBackBottomDialog(@NonNull Context context, boolean cancelable, OnCancelListener cancelListener) {
    super(context, cancelable, cancelListener);
  }

}
