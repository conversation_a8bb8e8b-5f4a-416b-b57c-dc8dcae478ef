package com.chervon.libBase.utils;


/**
 * 设备类型集合
 */
public class DeviceTypeConfig {
    // 设备类型 WIFI+BLE
    public static final String WIFI_AND_BLE = "Wifi+BLE";
    // 设备类型 4G+BLE
    public static final String G4_AND_BLE = "4G+BLE";
    // 设备类型非IOT设备
    public static final String NOT_IOT_DEVICE = "notIotDevice";
    // 网关子设备--通过DT脚上报
    public static final String GATEWAY_SUB_DEVICE = "gatewaySubDevice";

    public static final String DEVICE_TYPE_BLE = "BLE";

    public static final String DEVICE_TYPE_WIFI = "WIFI";

    public static final String DEVICE_TYPE_G4 = "4G";

}
