package com.chervon.libBase.utils;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;

import static com.chervon.libBase.model.AppConstants.PERMISSION_ALBUM_UNAUTHORIZED;
import static com.chervon.libBase.model.AppConstants.PERMISSION_CAMERA_UNAUTHORIZED;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.pdf.PdfRenderer;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.appcompat.widget.AppCompatButton;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.afollestad.materialdialogs.MaterialDialog;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.BaseDialogBottomFeedbackReplyBinding;
import com.chervon.libBase.databinding.BaseDialogSimpleCheckboxBinding;
import com.chervon.libBase.databinding.BaseDialogSimpleTipBinding;
import com.chervon.libBase.databinding.BaseDialogVideoFullScreenBinding;
import com.chervon.libBase.databinding.DialogSimpleConfirm2Binding;
import com.chervon.libBase.databinding.DialogSimpleConfirm3Binding;
import com.chervon.libBase.databinding.DialogSimpleConfirmBinding;
import com.chervon.libBase.databinding.DialogSimpleConfirmShareBinding;
import com.chervon.libBase.model.FeedBackReplyRequest;
import com.chervon.libBase.ui.PhotoSelectInterface;
import com.chervon.libBase.ui.PhotoSelectItemClick;
import com.chervon.libBase.ui.ResultListenner;
import com.chervon.libBase.ui.adapter.DialogBottomListAdapter;
import com.chervon.libBase.ui.adapter.DialogBottomListAdapter2;
import com.chervon.libBase.ui.adapter.PhotoSelectAdapter;
import com.chervon.libBase.ui.widget.RnDialog;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import me.jessyan.autosize.utils.AutoSizeUtils;


/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils
 * @ClassName: DialogUtil
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/4/28 上午11:30
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/4/28 上午11:30
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DialogUtil {
    private static Dialog mNewDialog;
    private static MaterialDialog mDialog;

    private static Dialog mFullScreenVideo;
    private BottomSheetDialog bottomSheetDialog;
    DialogBottomListAdapter adapter;
    DialogBottomListAdapter2 adapter2;
    private static Dialog toastDialog;

    public static BottomSheetDialog showBottomQrcodeDialog(Context context, String title, DialogBottomListAdapter.OnItemClickListener listener, String imageUrl) {
        final BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
        bottomSheetDialog.setContentView(R.layout.base_dialog_bottom_qrcode);
        bottomSheetDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
        ImageView imageView = bottomSheetDialog.findViewById(R.id.ivLoading);
        if (!TextUtils.isEmpty(imageUrl)) {
            Glide.with(context).load(imageUrl).placeholder(R.drawable.ic_dialog_bottom_qrcode).into(imageView);
        }

        TextView tvContent = bottomSheetDialog.findViewById(R.id.tvContent);
        tvContent.setText(LanguageStrings.deviceQrcodeScanHelpDefaultText());
        AppCompatButton btnConfirm = bottomSheetDialog.findViewById(R.id.btnConfirm);
        btnConfirm.setText(LanguageStrings.app_deviceqrcodescan_helpok_button_text());
        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheetDialog.cancel();
            }
        });

        bottomSheetDialog.show();
        return bottomSheetDialog;
    }

    public static void dismiss() {
        if (mFullScreenVideo != null) {
            mFullScreenVideo.dismiss();
            mFullScreenVideo = null;
        }
    }

    BaseDialogBottomFeedbackReplyBinding feedbackReplyBinding;
    private final String MAX_SIZE_FEEDBACK_PHOTO = "/4";

    public BottomSheetDialog showBottomReplyDialog(Context context,
                                                   DialogDismissListener dismissListener,
                                                   PhotoSelectInterface photoSelectInterface,
                                                   FeedBackReplyRequest feedBackReplyRequest,
                                                   int type) {
        if (bottomSheetDialog == null) {
            bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle2);
            feedbackReplyBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.base_dialog_bottom_feedback_reply, null, false);
            bottomSheetDialog.setContentView(feedbackReplyBinding.getRoot());
            bottomSheetDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);

            feedbackReplyBinding.setPresenter(photoSelectInterface);
            feedbackReplyBinding.setUiState(feedBackReplyRequest);

            TextView tvImageNum = bottomSheetDialog.findViewById(R.id.tvImageNum);
            RecyclerView recyclerView = bottomSheetDialog.findViewById(R.id.rvImageOption);
            EditText editText = bottomSheetDialog.findViewById(R.id.editText);

            List<String> url = new ArrayList<>();
            if (null != feedBackReplyRequest) {
                if (null != feedBackReplyRequest.getReplyPictures()) {
                    for (String replyPicture : feedBackReplyRequest.getReplyPictures()) {
                        if (!TextUtils.isEmpty(replyPicture)){
                            url.add(replyPicture);
                        }
                    }
                }
            }

            editText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    LogUtils.i("feedbackDialog--onTextChanged--",editText.getText().toString());

                }

                @Override
                public void afterTextChanged(Editable s) {
                    LogUtils.i("feedbackDialog--afterTextChanged--",editText.getText().toString());

                    checkFeedBackButtonClickable(context,feedbackReplyBinding.tvDone,editText.getText().toString());

                }
            });


            checkFeedBackButtonClickable(context,feedbackReplyBinding.tvDone,feedBackReplyRequest.getReplyContent());

            tvImageNum.setText(url.size() + MAX_SIZE_FEEDBACK_PHOTO);
            int default_size = 1;
            List<String> finalUrl = url;
            PhotoSelectAdapter photoAdapter = initPhotoSelectAdapter(context, recyclerView, url,
                    new PhotoSelectItemClick() {
                @Override
                public void onItemClick(Object v, PhotoSelectAdapter adapter) {
                    int id = ((View) v).getId();
                    if (id == R.id.ivfooter) {
                        uploadReceip(context, photoSelectInterface, finalUrl, adapter,editText.getText().toString(),
                                tvImageNum);
                    } else if (id == R.id.ivDeviceIcon) {
                        int index = (int) ((View) v).getTag();
                        photoSelectInterface.previewPhoto(finalUrl.get(index));
                    }

                }

                @Override
                public void onItemDelClick(Object uiState) {
                    tvImageNum.setText((finalUrl.size() - default_size) + MAX_SIZE_FEEDBACK_PHOTO);
                }

                @Override
                public void onItemClick(Object v) {

                }
            });

            feedbackReplyBinding.tvDone.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    photoSelectInterface.replyFeedBack(feedbackReplyBinding.getUiState(), photoAdapter.getDatas(),type);
                }
            });


            bottomSheetDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    if (null != dismissListener) {
                        FeedBackReplyRequest uiState = feedbackReplyBinding.getUiState();
                        uiState.setReplyPictures(finalUrl.toArray(new String[0]));
                        dismissListener.dialogResult(uiState);
                    }
                }
            });
        }
        if (!bottomSheetDialog.isShowing()) {
            bottomSheetDialog.show();
        }

        return bottomSheetDialog;
    }

    /**
     * 检查输入内容和图片是否为空
     * @param editContent
     */
    private void checkFeedBackButtonClickable(Context context,TextView textView,String editContent){
        boolean editIsEmpty = true;
        if (null == textView){
            return;
        }
        if (TextUtils.isEmpty(editContent)){
            editIsEmpty = true;
        }else {
            if (TextUtils.isEmpty(editContent.trim())){
                editIsEmpty = true;
            }else {
                editIsEmpty = false;
            }
        }

        if (editIsEmpty){
            textView.setClickable(false);
            textView.setEnabled(false);
            textView.setTextColor(context.getResources().getColor(R.color.colorGreenGray));
        }else {
            textView.setClickable(true);
            textView.setEnabled(true);
            textView.setTextColor(context.getResources().getColor(R.color.colorButtonNormal));
        }
    }

    public interface DialogDismissListener {
        /**
         * 点击Dismiss记录当前用户输入内容
         *
         * @param request
         */
        void dialogResult(FeedBackReplyRequest request);
    }

    public void uploadReceip(Context context, PhotoSelectInterface photoSelectInterface,
                             List<String> url,
                             PhotoSelectAdapter photoSelectAdapter,
                             String feedbackContent, TextView tvImageNum) {
        photoSelectInterface.setResultListenner(new ResultListenner() {

            @Override
            public void onActivityResult(int requestCode, int resultCode, Intent data, String imgUrl) {
                //  url.clear();
                if (url.size() > 3) {
                    url.add(3, imgUrl);
                } else {
                    url.add(url.size() - 1, imgUrl);
                }
                photoSelectAdapter.setDatas(url);
                photoSelectAdapter.notifyDataSetChanged();
                int photoSize = 0;
                if (url.size() != 0) {
                    photoSize = (url.size() - 1);
                }
                tvImageNum.setText(photoSize + MAX_SIZE_FEEDBACK_PHOTO);
            }
        });

        DialogUtil.showBottomCardDialog(context, new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (view.getId() == R.id.llTakePhoto) {
                    photoSelectInterface.goToTakePhotoPage();
                } else if (view.getId() == R.id.llGallery) {
                    photoSelectInterface.openGalley();
                }

            }
        });
    }

    private PhotoSelectAdapter initPhotoSelectAdapter(Context context, RecyclerView recyclerView, List<String> url, PhotoSelectItemClick itemClick) {
        GridLayoutManager gridLayoutManager = new GridLayoutManager(context, 4, RecyclerView.VERTICAL, false);
        recyclerView.setLayoutManager(gridLayoutManager);
        if (url.size() < 5) {
            url.add("");
        }
        PhotoSelectAdapter mReceiptAdapter = new PhotoSelectAdapter(context, itemClick, url);
        mReceiptAdapter.setHasStableIds(true);
        recyclerView.setAdapter(mReceiptAdapter);
        return mReceiptAdapter;
    }

    public void showBottomListDialog(Context context, String title, DialogBottomListAdapter.OnItemClickListener listener, Object[] dataList) {

        if (bottomSheetDialog == null) {
            bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
            bottomSheetDialog.setContentView(R.layout.base_dialog_bottom_list);
            bottomSheetDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
            adapter = new DialogBottomListAdapter(dataList);

            RecyclerView recyclerView = bottomSheetDialog.findViewById(R.id.recyclerview);
            recyclerView.setLayoutManager(new LinearLayoutManager(context));
            recyclerView.addItemDecoration(new DividerItemDecoration(context, LinearLayoutManager.VERTICAL));
            recyclerView.setAdapter(adapter);
            TextView tvTitle = bottomSheetDialog.findViewById(R.id.tvDialogTitle);
            tvTitle.setText(title);
            TextView tvDone = bottomSheetDialog.findViewById(R.id.tvDone);
            tvDone.setText(LanguageStrings.getDeviceRegistDone());
            adapter.setOnItemClickListener(listener);
            bottomSheetDialog.findViewById(R.id.ivCancel).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    bottomSheetDialog.cancel();
                }
            });

            bottomSheetDialog.findViewById(R.id.tvDone).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (adapter.getPosition() >= 0) {
                        v.setTag(adapter.getPosition());
                        listener.onClick(v, adapter.getContent());
                    }

                    bottomSheetDialog.cancel();
                }
            });

        } else {

            adapter.setDataList(dataList);
            adapter.notifyDataSetChanged();

        }
        bottomSheetDialog.show();


    }

    public void showBottomListDialog2(Context context, String title, DialogBottomListAdapter2.OnItemClickListener listener, Object[] dataList) {

        if (bottomSheetDialog == null) {
            bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
            bottomSheetDialog.setContentView(R.layout.base_dialog_bottom_list);
            bottomSheetDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
            adapter2 = new DialogBottomListAdapter2(dataList);

            RecyclerView recyclerView = bottomSheetDialog.findViewById(R.id.recyclerview);
            recyclerView.setLayoutManager(new LinearLayoutManager(context));

            // 创建 DividerItemDecoration 实例
            DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(context, LinearLayoutManager.VERTICAL);
            Drawable dividerDrawable = ContextCompat.getDrawable(context, R.drawable.base_divider_line_bg);
            if (dividerDrawable != null) {
                dividerItemDecoration.setDrawable(dividerDrawable);
            }

            recyclerView.addItemDecoration(dividerItemDecoration);

            recyclerView.setAdapter(adapter2);
            TextView tvTitle = bottomSheetDialog.findViewById(R.id.tvDialogTitle);
            tvTitle.setText(title);
            TextView tvDone = bottomSheetDialog.findViewById(R.id.tvDone);
            tvDone.setText(LanguageStrings.getDeviceRegistDone());
            adapter2.setOnItemClickListener(listener);
            bottomSheetDialog.findViewById(R.id.ivCancel).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    bottomSheetDialog.cancel();
                }
            });

            bottomSheetDialog.findViewById(R.id.tvDone).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {


                    if (adapter2.getSelectPosition() != -1) {
                        v.setTag(adapter2.getPosition());
                        listener.onClick(v, adapter2.getContent());
                        bottomSheetDialog.cancel();
                    } else {
                        bottomSheetDialog.cancel();
                    }

                }
            });

        } else {

            adapter2.setDataList(dataList);
            adapter2.notifyDataSetChanged();

        }
        bottomSheetDialog.show();


    }

    /**
     * 默认选中
     *
     * @param context
     * @param title
     * @param listener
     * @param dataList
     * @param selectPosition 默认-1
     */
    public void showBottomListDialog2WithSelectPosition(Context context, String title, DialogBottomListAdapter2.OnItemClickListener listener, Object[] dataList, int selectPosition) {

        if (bottomSheetDialog == null) {
            bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
            bottomSheetDialog.setContentView(R.layout.base_dialog_bottom_list);
            bottomSheetDialog.getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
            adapter2 = new DialogBottomListAdapter2(dataList);
            RecyclerView recyclerView = bottomSheetDialog.findViewById(R.id.recyclerview);
            recyclerView.setLayoutManager(new LinearLayoutManager(context));
            recyclerView.addItemDecoration(new DividerItemDecoration(context, LinearLayoutManager.VERTICAL));
            recyclerView.setAdapter(adapter2);
            adapter2.setSelectPosition(selectPosition);
            adapter2.notifyDataSetChanged();
            TextView tvTitle = bottomSheetDialog.findViewById(R.id.tvDialogTitle);
            tvTitle.setText(title);
            TextView tvDone = bottomSheetDialog.findViewById(R.id.tvDone);
            tvDone.setText(LanguageStrings.getDeviceRegistDone());
            adapter2.setOnItemClickListener(listener);
            bottomSheetDialog.findViewById(R.id.ivCancel).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    bottomSheetDialog.cancel();
                }
            });
            bottomSheetDialog.findViewById(R.id.tvDone).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (adapter2.getSelectPosition() != -1) {
                        v.setTag(adapter2.getPosition());
                        listener.onClick(v, adapter2.getContent());
                        bottomSheetDialog.cancel();
                    } else {
                        bottomSheetDialog.cancel();
                    }
                }
            });
        } else {
            adapter2.setDataList(dataList);
            adapter2.setSelectPosition(selectPosition);
            adapter2.notifyDataSetChanged();
        }
        bottomSheetDialog.show();
    }


    public static void showBottomCardDialogEditProfile(Context context, View.OnClickListener listener) {
        final BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
        bottomSheetDialog.setContentView(R.layout.base_dialog_bottom_edit_profile);
        bottomSheetDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
        View takePhoto = bottomSheetDialog.findViewById(R.id.llTakePhoto);
        View gallery = bottomSheetDialog.findViewById(R.id.llGallery);
        //  View browse = bottomSheetDialog.findViewById(R.id.llBrowse);
        TextView cancel = bottomSheetDialog.findViewById(R.id.tvCancel);

        ((TextView) bottomSheetDialog.findViewById(R.id.tvAddDevice)).setText(LanguageStrings.appUserDetailTakephotoTextviewText());
        ((TextView) bottomSheetDialog.findViewById(R.id.tvGallery)).setText(LanguageStrings.appUserDetailAlbumTextviewText());
        takePhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });
        gallery.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });
        //   browse.setOnClickListener(listener);
        cancel.setText(LanguageStrings.getBaseCancel());
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheetDialog.cancel();
            }
        });
        bottomSheetDialog.show();
    }

    public static void showDeviceRegisterBottomCardDialog(Context context, View.OnClickListener listener) {
        final BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
        bottomSheetDialog.setContentView(R.layout.base_deviceregister_dialog_bottom_card);
        bottomSheetDialog.getWindow().findViewById(R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
        TextView tvOpenCamera = bottomSheetDialog.findViewById(R.id.tvOpenCamera);
        tvOpenCamera.setText(LanguageStrings.appUserDetailTakephotoTextviewText());

        TextView tvOpenAlbum = bottomSheetDialog.findViewById(R.id.tvOpenAlbum);
        tvOpenAlbum.setText(LanguageStrings.appUserDetailAlbumTextviewText());

        TextView tvOpenFiler = bottomSheetDialog.findViewById(R.id.tvOpenFiler);
        tvOpenFiler.setText(LanguageStrings.app_deviceregist_choosefile_button_text());

        TextView cancel = bottomSheetDialog.findViewById(R.id.tvCancel);

        tvOpenCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });

        tvOpenAlbum.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });

        tvOpenFiler.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });

        cancel.setText(LanguageStrings.getBaseCancel());

        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheetDialog.cancel();
            }
        });

        bottomSheetDialog.show();
    }

    public static void showBottomCardDialog(Context context, View.OnClickListener listener) {
        final BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
        bottomSheetDialog.setContentView(R.layout.base_dialog_bottom_card);
        bottomSheetDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);


        TextView tvPhotograph = bottomSheetDialog.findViewById(R.id.tvPhotograph);
        tvPhotograph.setText(LanguageStrings.appUserDetailTakephotoTextviewText());
        TextView tvBrowsePhoto = bottomSheetDialog.findViewById(R.id.tvBrowsePhoto);
        tvBrowsePhoto.setText(LanguageStrings.appUserDetailAlbumTextviewText());
        View takePhoto = bottomSheetDialog.findViewById(R.id.llTakePhoto);
        View gallery = bottomSheetDialog.findViewById(R.id.llGallery);
        //  View browse = bottomSheetDialog.findViewById(R.id.llBrowse);
        TextView cancel = bottomSheetDialog.findViewById(R.id.tvCancel);
        takePhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });
        gallery.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onClick(view);
                bottomSheetDialog.cancel();
            }
        });
        //   browse.setOnClickListener(listener);
        cancel.setText(LanguageStrings.getBaseCancel());
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                bottomSheetDialog.cancel();
            }
        });
        bottomSheetDialog.show();
    }


    public static void showPhotoDialog(Context context, String uri, View.OnClickListener listener) {
        final Dialog bottomSheetDialog = new Dialog(context);
        final String MEDIA_PDF = ".pdf";
        bottomSheetDialog.setContentView(R.layout.base_dialog_photo_full_screen);
        bottomSheetDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.BLACK));
        bottomSheetDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

        ImageView ivPreview = bottomSheetDialog.findViewById(R.id.ivPreview);

        if (uri.contains(MEDIA_PDF)) {
            File urlFile = new File(uri);
            try {
                PdfRenderer renderer = new PdfRenderer(ParcelFileDescriptor.open(urlFile, ParcelFileDescriptor.MODE_READ_ONLY));
                PdfRenderer.Page page = renderer.openPage(0);
                Bitmap bitmap = Bitmap.createBitmap(page.getWidth(), page.getHeight(), Bitmap.Config.ARGB_8888);
                page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY);
                page.close();
                renderer.close();

                Glide.with(context).load(bitmap).into(ivPreview);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }else {
            Glide.with(context).load(uri).into(ivPreview);
        }



        View clPreview = bottomSheetDialog.findViewById(R.id.ivPreview);
        clPreview.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                bottomSheetDialog.cancel();
            }
        });
        bottomSheetDialog.show();
    }


    public static void showFullScreenVideoDialog(Context context, Uri uri, View.OnClickListener onConfirmed, View.OnClickListener onCanceled, long seek, SimpleExoPlayer mplayer, PlayerView playerView) {
        mFullScreenVideo = new Dialog(context);
        BaseDialogVideoFullScreenBinding binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.base_dialog_video_full_screen, null, false);

        mFullScreenVideo.setContentView(binding.getRoot());
        mFullScreenVideo.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        mFullScreenVideo.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
//    binding.setUiData(new ToolbarData(
//      LanguageStrings.appDevicerenameTitleText(),
//      new View.OnClickListener() {
//        @Override
//        public void onClick(View view) {
//       //   onCanceled.onClick(view);
//        //  bottomSheetDialog.dismiss();
//        }
//      }));
        // playerView.setLayoutParams( );
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT, Gravity.CENTER);

        binding.clContainer.addView(playerView, layoutParams);

        //  initializePlayer(binding.videoView,context,uri,seek,mplayer);

        ImageButton imageButton = binding.getRoot().findViewById(R.id.exo_fullscreen_button);
        imageButton.setVisibility(View.INVISIBLE);

        ImageButton imageButtonExt = binding.getRoot().findViewById(R.id.exo_fullscreen_ext);
        imageButtonExt.setVisibility(View.VISIBLE);
        imageButtonExt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                binding.clContainer.removeAllViews();
                ((Activity) context).setRequestedOrientation(SCREEN_ORIENTATION_PORTRAIT);
                if (mFullScreenVideo != null) {
                    mFullScreenVideo.dismiss();
                }

                if (onCanceled != null) {
                    onCanceled.onClick(v);
                }

            }
        });
        mFullScreenVideo.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                    binding.clContainer.removeAllViews();
                    //   binding.videoView.setPlayer(null);
                    if (mplayer != null) {
                        mplayer.pause();
                    }

                    if (onCanceled != null) {
                        onCanceled.onClick(imageButtonExt);
                    }

                }
                return false;
            }
        });
        mFullScreenVideo.setOnDismissListener(new DialogInterface.OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {
                if (mplayer != null) {
                    mplayer.pause();
                    binding.clContainer.removeAllViews();
                }
                mFullScreenVideo = null;
            }
        });
        mFullScreenVideo.show();
    }


    public static void showEditDialog(Context context, String title, String editString, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        newdialogViewShow(context, R.layout.dialog_confirm_with_title_edit, null, null, null, title, editString, onConfirmed, onCanceled);
    }

    private static void dialogViewShow(Context context, int layout, String confirmText, String cancelText, String message, String title, String editString, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        // if (mDialog == null) {
        mDialog = new RnDialog.Builder(context).customView(layout, false).build();

        if (cancelText != null) {
            ((TextView) mDialog.findViewById(R.id.btnCancel)).setText(cancelText);
        } else {
            ((TextView) mDialog.findViewById(R.id.btnCancel)).setText(LanguageStrings.getBaseCancel());
        }
        if (confirmText != null) {
            ((TextView) mDialog.findViewById(R.id.btnConfirm)).setText(confirmText);
        } else {
            ((TextView) mDialog.findViewById(R.id.btnConfirm)).setText(LanguageStrings.getConfirm());
        }
        if (message != null) {
            ((TextView) mDialog.findViewById(R.id.tvMessage)).setText(message);
        }
        if (editString != null) {
            ((TextView) mDialog.findViewById(R.id.et_dialog)).setText(editString);
        } else {
            View etView = mDialog.findViewById(R.id.et_dialog);
            if (etView != null) {
                etView.setVisibility(View.GONE);
            }

        }

        if (onCanceled != null) {
            mDialog.findViewById(R.id.btnCancel).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onCanceled.onClick(v);
                    mDialog.dismiss();
                    mDialog = null;
                }
            });
        }

        mDialog.findViewById(R.id.btnConfirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                EditText editText = (EditText) mDialog.findViewById(R.id.et_dialog);
                if (null != editText) {
                    String str = editText.getText().toString();
                    v.setTag(str);
                }
                if (simpleConfirmDialog != null) {
                    simpleConfirmDialog.dismiss();
                    simpleConfirmDialog = null;
                }
                mDialog = null;
            }
        });

        TextView tvTitle = ((TextView) mDialog.findViewById(R.id.tvTitle));
        if (tvTitle != null) {
            tvTitle.setText(title);
        }
        mDialog.show();

    }


    private static void newdialogViewShow(Context context, int layout, String confirmText, String cancelText, String message, String title, String editString, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        if (mNewDialog == null) {
            mNewDialog = new Dialog(context, R.style.MyDialog3);
            View view = LayoutInflater.from(context).inflate(layout, null);

            mNewDialog.setContentView(view, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            mNewDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    mNewDialog = null;
                }
            });
        }


        if (cancelText != null) {
            ((TextView) mNewDialog.findViewById(R.id.btnCancel)).setText(cancelText);
        } else {
            ((TextView) mNewDialog.findViewById(R.id.btnCancel)).setText(LanguageStrings.getBaseCancel());
        }
        if (confirmText != null) {
            ((TextView) mNewDialog.findViewById(R.id.btnConfirm)).setText(confirmText);
        } else {
            ((TextView) mNewDialog.findViewById(R.id.btnConfirm)).setText(LanguageStrings.getConfirm());
        }
        if (message != null) {
            ((TextView) mNewDialog.findViewById(R.id.tvMessage)).setText(message);
        }
        if (editString != null) {
            ((TextView) mNewDialog.findViewById(R.id.et_dialog)).setText(editString);
        } else {
            View etView = mNewDialog.findViewById(R.id.et_dialog);
            if (etView != null) {
                etView.setVisibility(View.GONE);
            }

        }

        if (onCanceled != null) {
            mNewDialog.findViewById(R.id.btnCancel).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onCanceled.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                }
            });
        }

        mNewDialog.findViewById(R.id.btnConfirm).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String str = ((EditText) mNewDialog.findViewById(R.id.et_dialog)).getText().toString();
                v.setTag(str);
                onConfirmed.onClick(v);
                if (simpleConfirmDialog != null) {
                    simpleConfirmDialog.dismiss();
                    simpleConfirmDialog = null;
                }
            }
        });

        TextView tvTitle = ((TextView) mNewDialog.findViewById(R.id.tvTitle));
        if (tvTitle != null) {
            tvTitle.setText(title);
        }
        mNewDialog.show();
//        } else {
//            mNewDialog.show();
//        }
    }

    public static void showPromptDialog(Context context, String confirmText, String cancelText, String title, String message, String defaultValue, View.OnClickListener onClickListener, View.OnClickListener onClickListener1) {
        dialogViewShow(context, R.layout.base_dialog_edit, confirmText, cancelText, message, title, defaultValue, onClickListener, onClickListener1);

    }


    private static Dialog simpleConfirmDialog;
    private static DialogSimpleConfirmBinding simpleConfirmDialogBinding;
    private static DialogSimpleConfirm2Binding simpleConfirmDialog2Binding;
    private static DialogSimpleConfirm3Binding simpleConfirmDialog3Binding;
    private static DialogSimpleConfirmShareBinding simpleConfirmDialogShareBinding;

    //72002 新增方法
    public static Dialog simpleConfirmDialog(Context context, String title, String msg, String cancel, String confirm, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        //  dialogViewShow(context, R.layout.base_dialog_edit, null, null, msg, title, null, onConfirmed, onCanceled);
        try {
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialogBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialogBinding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialogBinding = null;
                    }
                });
            }

            simpleConfirmDialogBinding.tvContent.setText(msg);
            simpleConfirmDialogBinding.tvCancel.setText(cancel);
            simpleConfirmDialogBinding.tvConfirm.setText(confirm);
            simpleConfirmDialogBinding.tvConfirm.setTextColor(ContextCompat.getColor(context, com.chervon.libBase.R.color.colorButtonNormal));
            if (onCanceled != null) {
                simpleConfirmDialogBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (simpleConfirmDialog != null) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialogBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }

                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }

    public static Dialog simpleConfirmDialog(Context context, String title, String msg, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        //  dialogViewShow(context, R.layout.base_dialog_edit, null, null, msg, title, null, onConfirmed, onCanceled);
        try {
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialogBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialogBinding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialogBinding = null;
                    }
                });
            }

            simpleConfirmDialogBinding.tvContent.setText(msg);
            simpleConfirmDialogBinding.tvCancel.setText(LanguageStrings.getBaseCancel());
            simpleConfirmDialogBinding.tvConfirm.setText(LanguageStrings.getConfirm());
            if (onCanceled != null) {
                simpleConfirmDialogBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (simpleConfirmDialog != null) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialogBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }

                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }

    public static Dialog simpleConfirmDialogRN(Context context, String title, String msg, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        //  dialogViewShow(context, R.layout.base_dialog_edit, null, null, msg, title, null, onConfirmed, onCanceled);
        try {
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialogBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialogBinding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialogBinding = null;
                    }
                });
            }
            //TODO 给主页升级弹窗出定制处理------点击key返回相当于弹窗单击cancle按钮
            if (TextUtils.isEmpty(title.trim())) {
                simpleConfirmDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                    @Override
                    public boolean onKey(DialogInterface dialogInterface, int i, KeyEvent keyEvent) {
                        if (keyEvent.getKeyCode() == KeyEvent.KEYCODE_BACK) {
                            if (null != onCanceled) {
                                onCanceled.onClick(null);
                                if (simpleConfirmDialog != null) {
                                    simpleConfirmDialog.dismiss();
                                    simpleConfirmDialog = null;
                                }
                            }
                        }
                        return true;
                    }
                });
            }


            simpleConfirmDialogBinding.tvContent.setText(msg);
            simpleConfirmDialogBinding.tvCancel.setText(LanguageStrings.getBaseCancel());
            simpleConfirmDialogBinding.tvConfirm.setText(LanguageStrings.getConfirm());
            if (onCanceled != null) {
                simpleConfirmDialogBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (simpleConfirmDialog != null) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialogBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }

                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }

    public static Dialog simpleConfirmDialog2(Context context, String title, String msg, String comfirm, String cancle, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        //  dialogViewShow(context, R.layout.base_dialog_edit, null, null, msg, title, null, onConfirmed, onCanceled);
        try {
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialog2Binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm2, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialog2Binding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialog2Binding = null;
                    }
                });
            }

            simpleConfirmDialog2Binding.tvContent.setText(msg);
            simpleConfirmDialog2Binding.tvCancel.setText(cancle);
            simpleConfirmDialog2Binding.tvConfirm.setText(comfirm);
            simpleConfirmDialog2Binding.tvTitle.setText(title);
            if (onCanceled != null) {
                simpleConfirmDialog2Binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (simpleConfirmDialog != null) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialog2Binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }


    public static Dialog simpleConfirmDialog3(Context context, String title,
                                              String msg, String comfirm, String cancle,
                                              View.OnClickListener onConfirmed,
                                              View.OnClickListener onCanceled,String pageSource) {
        try {

            String pageId = "10";
            String moduleId = "5";

            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {
                if (!TextUtils.isEmpty(pageSource)){
                    Utils.sendExposure(context,moduleId,pageId,pageSource);
                }
                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialog3Binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm3, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialog3Binding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialog3Binding = null;
                    }
                });
            }

            simpleConfirmDialog3Binding.tvContent.setText(msg);
            simpleConfirmDialog3Binding.tvCancel.setText(cancle);
            simpleConfirmDialog3Binding.tvConfirm.setText(comfirm);
            simpleConfirmDialog3Binding.tvTitle.setText(title);
            if (onCanceled != null) {
                simpleConfirmDialog3Binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (null != simpleConfirmDialog) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialog3Binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    // 解决IOT-11724
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }

    public static Dialog simpleConfirmDialog4(Context context, String title,
                                              String msg, String comfirm, String cancle,
                                              View.OnClickListener onConfirmed,
                                              View.OnClickListener onCanceled,String pageSource) {
        try {

            String pageId = "10";
            String moduleId = "5";

            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {
                if (!TextUtils.isEmpty(pageSource)){
                    Utils.sendExposure(context,moduleId,pageId,pageSource);
                }
                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialog3Binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm3, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialog3Binding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialog3Binding = null;
                    }
                });
            }

            simpleConfirmDialog3Binding.tvContent.setText(msg);
            simpleConfirmDialog3Binding.tvCancel.setText(cancle);
            simpleConfirmDialog3Binding.tvConfirm.setText(comfirm);
            simpleConfirmDialog3Binding.tvTitle.setText(title);
            if (onCanceled != null) {
                simpleConfirmDialog3Binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (null != simpleConfirmDialog) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialog3Binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    // 解决IOT-11724
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }

    public static Dialog simpleConfirmDialogForShare(Context context, String title, String msg, String comfirm, String cancle, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        try {
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialogShareBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm_share, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialogShareBinding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialogShareBinding = null;
                    }
                });
            }

            simpleConfirmDialogShareBinding.tvContent.setText(msg);
            simpleConfirmDialogShareBinding.tvCancel.setText(cancle);
            simpleConfirmDialogShareBinding.tvConfirm.setText(comfirm);
            simpleConfirmDialogShareBinding.tvTitle.setText(title);
            if (onCanceled != null) {
                simpleConfirmDialogShareBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (simpleConfirmDialog != null) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialogShareBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }


    public static Dialog simpleConfirmCopyDialog(Context context, String title, String msg, View.OnClickListener onConfirmed, View.OnClickListener onCanceled) {
        try {
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialogBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialogBinding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialogBinding = null;
                    }
                });
            }

            simpleConfirmDialogBinding.tvContent.setText(msg);
            simpleConfirmDialogBinding.tvCancel.setText(LanguageStrings.getBaseCancel());
            simpleConfirmDialogBinding.tvConfirm.setText(LanguageStrings.app_base_paste_button_text());
            if (onCanceled != null) {
                simpleConfirmDialogBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                });
            }

            simpleConfirmDialogBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    simpleConfirmDialog.dismiss();
                    simpleConfirmDialog = null;

                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }


    /**
     * 相机和相册权限Alert专用
     * @param context
     * @param errorCode
     * @param confirm
     * @param cancel
     * @param onConfirmed
     * @param onCanceled
     * @return
     */
    public static Dialog cameraAndalbumAlertDialog(Context context, int errorCode,
                                                   String confirm, String cancel,
                                                   View.OnClickListener onConfirmed,
                                                   View.OnClickListener onCanceled) {
        try {
            if (PERMISSION_ALBUM_UNAUTHORIZED == errorCode || PERMISSION_CAMERA_UNAUTHORIZED == errorCode) {

            }
            String title = (PERMISSION_ALBUM_UNAUTHORIZED == errorCode)?LanguageStrings.app_base_albumAuthorizedAlertTitle_textview_text()
                    :LanguageStrings.app_base_cameraAuthorizedAlertTitle_textview_text();
            String content =  (PERMISSION_ALBUM_UNAUTHORIZED == errorCode)?LanguageStrings.app_base_albumAuthorizedAlertContent_textview_text():
                    LanguageStrings.app_base_cameraAuthorizedAlertContent_textview_text();
            if (simpleConfirmDialog == null || simpleConfirmDialogBinding == null) {

                simpleConfirmDialog = new Dialog(context);

                simpleConfirmDialog2Binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.dialog_simple_confirm2, null, false);

                simpleConfirmDialog.setContentView(simpleConfirmDialog2Binding.getRoot());
                simpleConfirmDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                simpleConfirmDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                simpleConfirmDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                    @Override
                    public void onDismiss(DialogInterface dialog) {
                        simpleConfirmDialog = null;
                        simpleConfirmDialog2Binding = null;
                    }
                });
            }
            simpleConfirmDialog2Binding.tvTitle.setText(title);
            simpleConfirmDialog2Binding.tvContent.setText(content);
            simpleConfirmDialog2Binding.tvCancel.setText(cancel);
            simpleConfirmDialog2Binding.tvConfirm.setText(confirm);

            if (onCanceled != null) {
                simpleConfirmDialog2Binding.tvCancel.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onCanceled.onClick(v);
                        if (simpleConfirmDialog != null) {
                            simpleConfirmDialog.dismiss();
                            simpleConfirmDialog = null;
                        }
                    }
                });
            }

            simpleConfirmDialog2Binding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    onConfirmed.onClick(v);
                    if (simpleConfirmDialog != null) {
                        simpleConfirmDialog.dismiss();
                        simpleConfirmDialog = null;
                    }
                }
            });
            simpleConfirmDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }


        return simpleConfirmDialog;
    }

    public static void closeSimpleDialog() {

        try {
            if (null != simpleConfirmDialog) {
                if (simpleConfirmDialog.isShowing()) {
                    simpleConfirmDialog.dismiss();
                }

            }
        } catch (Exception e) {

        }

    }

    public static void simpleTipDialog(Context context, String message, View.OnClickListener onConfirmed) {
        //  dialogViewShow(context, R.layout.base_dialog_simple_tip, null, null, message, null, null, onConfirmed, null);
        BaseDialogSimpleTipBinding baseDialogSimpleTipBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.base_dialog_simple_tip, null, false);

        Dialog simpleDialog = new Dialog(context);
        try {
            simpleDialog.setContentView(baseDialogSimpleTipBinding.getRoot());
            simpleDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            simpleDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

            simpleDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {

                }
            });
            baseDialogSimpleTipBinding.tvMessage.setText(message);
            baseDialogSimpleTipBinding.btnConfirm.setText(LanguageStrings.getConfirm());

            baseDialogSimpleTipBinding.btnConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onConfirmed.onClick(v);
                    simpleDialog.dismiss();

                }
            });
            simpleDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }

    }

    public static void simpleTipCheckDialog(Context context, String title, String message, String hint, String confirm, OnConfirmedWithCheckbox onConfirmed) {
        BaseDialogSimpleCheckboxBinding baseDialogSimpleCheckBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.base_dialog_simple_checkbox, null, false);

        Dialog simpleDialog = new Dialog(context);
        try {
            simpleDialog.setContentView(baseDialogSimpleCheckBinding.getRoot());
            simpleDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            simpleDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

            simpleDialog.setOnDismissListener(dialog -> {
                onConfirmed.onDismiss();
            });
            if (title.isEmpty()) {
                baseDialogSimpleCheckBinding.tvTitle.setVisibility(View.GONE);
            } else {
                baseDialogSimpleCheckBinding.tvTitle.setVisibility(View.VISIBLE);
                baseDialogSimpleCheckBinding.tvTitle.setText(title);
            }
            baseDialogSimpleCheckBinding.tvMessage.setText(message);
            baseDialogSimpleCheckBinding.btnConfirm.setText(confirm);
            baseDialogSimpleCheckBinding.tvHint.setText(hint);
            baseDialogSimpleCheckBinding.btnConfirm.setOnClickListener(v -> {
                onConfirmed.onClick(v, baseDialogSimpleCheckBinding.cbHint.isChecked());
                simpleDialog.dismiss();
            });
            simpleDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
            onConfirmed.onDismiss();
        }

    }

    public interface OnConfirmedWithCheckbox {
        /**
         * 确认回调接口
         *
         * @param v
         * @param b
         */
        void onClick(View v, boolean b);

        /**
         * 取消
         */
        void onDismiss();
    }


    public static void simpleTipForBleEnableDialog(Context context, String message, String btnText, View.OnClickListener onConfirmed) {
        //  dialogViewShow(context, R.layout.base_dialog_simple_tip, null, null, message, null, null, onConfirmed, null);
        BaseDialogSimpleTipBinding baseDialogSimpleTipBinding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.base_dialog_simple_tip, null, false);

        Dialog simpleDialog = new Dialog(context);
        try {
            simpleDialog.setContentView(baseDialogSimpleTipBinding.getRoot());
            simpleDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            simpleDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

            simpleDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {

                }
            });
            baseDialogSimpleTipBinding.tvMessage.setText(message);
            baseDialogSimpleTipBinding.btnConfirm.setText(btnText);

            baseDialogSimpleTipBinding.btnConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onConfirmed.onClick(v);
                    simpleDialog.dismiss();

                }
            });
            simpleDialog.show();
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }

    }

    public static void showFeedBackSuccessDialog(Context context, String message) {
        showFeedBackToastDialog(context, R.layout.base_toast_success, message, AutoSizeUtils.mm2px(context, 300), AutoSizeUtils.mm2px(context, 300));
    }

    public static void showRegisterErrorDialog(Context context, String message) {
        showToastDialog(context, R.layout.base_toast_register_error, message, AutoSizeUtils.mm2px(context, 300), AutoSizeUtils.mm2px(context, 300));
    }

    public static void showWarningDialog(Context context, String message) {
        showToastDialog(context, R.layout.base_toast_warnning, message, AutoSizeUtils.mm2px(context, 300), AutoSizeUtils.mm2px(context, 300));
    }

    public static void showFaceBackWarningDialog(Context context, String message) {
        showToastDialog(context, R.layout.base_toast_warnning, message, AutoSizeUtils.mm2px(context, 443), AutoSizeUtils.mm2px(context, 300));
    }

    public static void showSuccessDialog(Context context, String message) {
        showToastDialog(context, R.layout.base_toast_success, message, AutoSizeUtils.mm2px(context, 300), AutoSizeUtils.mm2px(context, 300));
    }

    public static void showFeedBackToastDialog(Context context, int layout, String message, int with, int height) {
        toastDialog = new Dialog(context, R.style.MyDialog);
        View view = LayoutInflater.from(context).inflate(layout, null);
        ((TextView) view.findViewById(R.id.tvContent)).setText(message);
        toastDialog.setContentView(view, new ConstraintLayout.LayoutParams(with, height));
        toastDialog.show();
    }

    public static void showToastDialog(Context context, int layout, String message, int with, int height) {
        toastDialog = new Dialog(context, R.style.MyDialog);
        View view = LayoutInflater.from(context).inflate(layout, null);
        ((TextView) view.findViewById(R.id.tvContent)).setText(message);
        toastDialog.setContentView(view, new ConstraintLayout.LayoutParams(with, height));
        toastDialog.show();
        view.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (null != toastDialog) {
                    toastDialog.hide();
                }

            }
        }, 1000);
    }


    public static void clearDialog() {
        if (mDialog != null) {
            mDialog.dismiss();
            mDialog = null;
        }
        if (toastDialog != null) {
            toastDialog.dismiss();
            toastDialog = null;
        }
    }


    public static Dialog showRnLoadingDialog(Activity context) {

        Dialog dialog = new Dialog(context, R.style.MyDialog);
        View view = LayoutInflater.from(context).inflate(R.layout.base_progress_loading, null);
        dialog.setCancelable(false);
        dialog.setContentView(view, new ConstraintLayout.LayoutParams(AutoSizeUtils.mm2px(context, 300), AutoSizeUtils.mm2px(context, 300)));
        if (context != null && !context.isFinishing()) {
            dialog.show();
        }

        return dialog;
    }
}
