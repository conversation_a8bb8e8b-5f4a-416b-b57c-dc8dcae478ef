package com.chervon.libBase.ui.widget;


import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.R;

import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Locale;

/**
 * 时间选择器
 * 选择只支持当前时间以下
 */
public class CustomDatePicker {

  /**
   * 定义结果回调接口
   */
  public interface ResultHandler {
    void handle(String time, long timeStamp);
  }

  private OnDateClickListener mOnDateClickListener;

  public enum SCROLL_TYPE {
    HOUR(1),
    MINUTE(2);

    SCROLL_TYPE(int value) {
      this.value = value;
    }

    public int value;
  }

  private int scrollUnits = SCROLL_TYPE.HOUR.value + SCROLL_TYPE.MINUTE.value;
  private ResultHandler handler;
  private Context context;
  private boolean canAccess = false;

  private Dialog datePickerDialog;
  private DatePickerView year_pv, month_pv, day_pv, hour_pv, minute_pv;

  private static final int MAX_MINUTE = 59;
  private static final int MAX_HOUR = 23;
  private static final int MIN_MINUTE = 0;
  private static final int MIN_HOUR = 0;
  private static final int MAX_MONTH = 12;

  private ArrayList<String> year, month, day, hour, minute;

  private final static ArrayList<String> shortMonthStringArray = new ArrayList<>();
  private int startYear, startMonth, startDay, startHour, startMinute, endYear, endMonth, endDay, endHour, endMinute, endDay2;
  private boolean spanYear, spanMon, spanDay, spanHour, spanMin;
  private Calendar selectedCalender, startCalendar, endCalendar;
  private TextView tv_select;
  private ImageView tv_cancle;
  private int lastSelectedDay;

  {
    shortMonthStringArray.add("null");
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month1_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month2_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month3_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month4_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month5_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month6_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month7_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month8_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month9_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month10_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month11_textview_text());
    shortMonthStringArray.add(LanguageStrings.app_datepicker_month12_textview_text());
  }

  public CustomDatePicker(Context context, ResultHandler resultHandler,
                          String startDate, String endDate,
                          String currentTime) {
    if (isValidDate(startDate, "yyyy-MM-dd HH:mm") && isValidDate(endDate, "yyyy-MM-dd HH:mm")) {
      canAccess = true;
      this.context = context;
      this.handler = resultHandler;
      selectedCalender = Calendar.getInstance();
      startCalendar = Calendar.getInstance();
      endCalendar = Calendar.getInstance();
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.CHINA);
      try {
        startCalendar.setTime(sdf.parse(startDate));
        endCalendar.setTime(sdf.parse(endDate));
        selectedCalender.setTime(sdf.parse(currentTime));
      } catch (ParseException e) {
        e.printStackTrace();
      }
      initDialog();
      initView();
    }
  }

  private void initDialog() {
    if (datePickerDialog == null) {
      datePickerDialog = new BottomSheetDialog(context, R.style.BottomSheetDialogStyle);
      if (Utils.getBuildEvn().equals(Utils.EVN_NA)) {
        datePickerDialog.setContentView(R.layout.base_custom_dialog_datapicker_mmddyyyy);
      } else {
        datePickerDialog.setContentView(R.layout.base_custom_dialog_datapicker_ddmmyyyy);
      }

      datePickerDialog.getWindow().findViewById(com.google.android.material.R.id.design_bottom_sheet).setBackgroundColor(Color.TRANSPARENT);
      datePickerDialog.setCancelable(false);
    }
  }

  private void initView() {
    year_pv = (DatePickerView) datePickerDialog.findViewById(R.id.year_pv);
    month_pv = (DatePickerView) datePickerDialog.findViewById(R.id.month_pv);
    day_pv = (DatePickerView) datePickerDialog.findViewById(R.id.day_pv);
    hour_pv = (DatePickerView) datePickerDialog.findViewById(R.id.hour_pv);
    minute_pv = (DatePickerView) datePickerDialog.findViewById(R.id.minute_pv);
    tv_cancle = (ImageView) datePickerDialog.findViewById(R.id.ivCancel);
    tv_select = (TextView) datePickerDialog.findViewById(R.id.tv_select);
    TextView tv_title = (TextView) datePickerDialog.findViewById(R.id.tv_title);
    tv_title.setText(LanguageStrings.getDeviceRegistSuccessDate());
    tv_select.setText(LanguageStrings.app_base_done_button_text());


    tv_cancle.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if(mOnDateClickListener!=null){
          mOnDateClickListener.onCancelClick("");
        }
        datePickerDialog.dismiss();
      }
    });

    tv_select.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        SimpleDateFormat sdf = new SimpleDateFormat(Utils.getBuildEvn().equals(Utils.EVN_NA) ? "MM/dd/yyyy" : "dd/MM/yyyy", Locale.CHINA);
        handler.handle(sdf.format(selectedCalender.getTime()), selectedCalender.getTime().getTime());

        if(mOnDateClickListener!=null) {
          SimpleDateFormat rnSdf = new SimpleDateFormat("yyyy/MM/dd", Locale.CHINA);
          mOnDateClickListener.onConfirmClick(rnSdf.format(selectedCalender.getTime()));
        }
        datePickerDialog.dismiss();
      }
    });
  }

  private void initParameter() {
    startYear = startCalendar.get(Calendar.YEAR);
    startMonth = startCalendar.get(Calendar.MONTH);
    startDay = startCalendar.get(Calendar.DAY_OF_MONTH);
    lastSelectedDay = startDay;
    startHour = startCalendar.get(Calendar.HOUR_OF_DAY);
    startMinute = startCalendar.get(Calendar.MINUTE);
    endYear = endCalendar.get(Calendar.YEAR);
    endMonth = endCalendar.get(Calendar.MONTH);
    endDay = endCalendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    endDay2 = endCalendar.get(Calendar.DAY_OF_MONTH);
    endHour = endCalendar.get(Calendar.HOUR_OF_DAY);
    endMinute = endCalendar.get(Calendar.MINUTE);
    spanYear = startYear != endYear;
    spanMon = (!spanYear) && (startMonth != endMonth);
    spanDay = (!spanMon) && (startDay != endDay);
    spanHour = (!spanDay) && (startHour != endHour);
    spanMin = (!spanHour) && (startMinute != endMinute);
  }

  private void initTimer() {
    initArrayList();
    if (spanYear) {
      for (int i = startYear; i <= endYear; i++) {
        year.add(String.valueOf(i) + "");
      }
      for (int i = startMonth; i <= MAX_MONTH; i++) {
        month.add(shortMonthStringArray.get(i));
      }
      for (int i = startDay; i <= startCalendar.getActualMaximum(Calendar.DAY_OF_MONTH); i++) {
        day.add(formatTimeUnit(i) + "");
      }

      if ((scrollUnits & SCROLL_TYPE.HOUR.value) != SCROLL_TYPE.HOUR.value) {
        hour.add(formatTimeUnit(startHour));
      } else {
        for (int i = startHour; i <= MAX_HOUR; i++) {
          hour.add(formatTimeUnit(i));
        }
      }

      if ((scrollUnits & SCROLL_TYPE.MINUTE.value) != SCROLL_TYPE.MINUTE.value) {
        minute.add(formatTimeUnit(startMinute));
      } else {
        for (int i = startMinute; i <= MAX_MINUTE; i++) {
          minute.add(formatTimeUnit(i));
        }
      }
    } else if (spanMon) {
      year.add(String.valueOf(startYear) + "");
      for (int i = startMonth; i <= endMonth; i++) {
        month.add(shortMonthStringArray.get(i));
      }
      for (int i = startDay; i <= startCalendar.getActualMaximum(Calendar.DAY_OF_MONTH); i++) {
        day.add(formatTimeUnit(i) + "");
      }

      if ((scrollUnits & SCROLL_TYPE.HOUR.value) != SCROLL_TYPE.HOUR.value) {
        hour.add(formatTimeUnit(startHour));
      } else {
        for (int i = startHour; i <= MAX_HOUR; i++) {
          hour.add(formatTimeUnit(i));
        }
      }

      if ((scrollUnits & SCROLL_TYPE.MINUTE.value) != SCROLL_TYPE.MINUTE.value) {
        minute.add(formatTimeUnit(startMinute));
      } else {
        for (int i = startMinute; i <= MAX_MINUTE; i++) {
          minute.add(formatTimeUnit(i));
        }
      }
    } else if (spanDay) {
      year.add(String.valueOf(startYear) + "");
      month.add(shortMonthStringArray.get(startMonth));
      for (int i = startDay; i <= endDay; i++) {
        day.add(formatTimeUnit(i) + "");
      }

      if ((scrollUnits & SCROLL_TYPE.HOUR.value) != SCROLL_TYPE.HOUR.value) {
        hour.add(formatTimeUnit(startHour));
      } else {
        for (int i = startHour; i <= MAX_HOUR; i++) {
          hour.add(formatTimeUnit(i));
        }
      }

      if ((scrollUnits & SCROLL_TYPE.MINUTE.value) != SCROLL_TYPE.MINUTE.value) {
        minute.add(formatTimeUnit(startMinute));
      } else {
        for (int i = startMinute; i <= MAX_MINUTE; i++) {
          minute.add(formatTimeUnit(i));
        }
      }
    } else if (spanHour) {
      year.add(String.valueOf(startYear) + "");
      month.add(shortMonthStringArray.get(startMonth));
      day.add(formatTimeUnit(startDay) + "");

      if ((scrollUnits & SCROLL_TYPE.HOUR.value) != SCROLL_TYPE.HOUR.value) {
        hour.add(formatTimeUnit(startHour));
      } else {
        for (int i = startHour; i <= endHour; i++) {
          hour.add(formatTimeUnit(i));
        }
      }

      if ((scrollUnits & SCROLL_TYPE.MINUTE.value) != SCROLL_TYPE.MINUTE.value) {
        minute.add(formatTimeUnit(startMinute));
      } else {
        for (int i = startMinute; i <= MAX_MINUTE; i++) {
          minute.add(formatTimeUnit(i));
        }
      }
    } else if (spanMin) {
      year.add(String.valueOf(startYear) + "");
      month.add(shortMonthStringArray.get(startMonth));
      day.add(formatTimeUnit(startDay) + "");
      hour.add(formatTimeUnit(startHour));

      if ((scrollUnits & SCROLL_TYPE.MINUTE.value) != SCROLL_TYPE.MINUTE.value) {
        minute.add(formatTimeUnit(startMinute));
      } else {
        for (int i = startMinute; i <= endMinute; i++) {
          minute.add(formatTimeUnit(i));
        }
      }
    }
    loadComponent();
  }

  /**
   * 将“0-9”转换为“00-09”
   */
  private String formatTimeUnit(int unit) {
    return unit < 10 ? "0" + String.valueOf(unit) : String.valueOf(unit);
  }

  private void initArrayList() {
    if (year == null) year = new ArrayList<>();
    if (month == null) month = new ArrayList<>();
    if (day == null) day = new ArrayList<>();
    if (hour == null) hour = new ArrayList<>();
    if (minute == null) minute = new ArrayList<>();
    year.clear();
    month.clear();
    day.clear();
    hour.clear();
    minute.clear();
  }

  private void loadComponent() {
    year_pv.setData(year);
    month_pv.setData(month);
    day_pv.setData(day);
    hour_pv.setData(hour);
    minute_pv.setData(minute);
    year_pv.setSelected(0);
    month_pv.setSelected(0);
    day_pv.setSelected(0);
    hour_pv.setSelected(0);
    minute_pv.setSelected(0);
    executeScroll();
  }

  private void addListener() {
    year_pv.setOnSelectListener(new DatePickerView.onSelectListener() {
      @Override
      public void onSelect(String year) {

        //如果当前滚动的年份和选中的不一致
        if (Integer.parseInt(year) != selectedCalender.get(Calendar.YEAR)) {
          //闰年变平年
          if (isLeapYear(selectedCalender.get(Calendar.YEAR))){
            //如果选择是2月且是最后一天
            int monthOfYear = 1;
            int selectDay = selectedCalender.get(Calendar.DAY_OF_MONTH);
            int monthOfDays = queryEndDayOfMonth(selectedCalender.get(Calendar.YEAR),selectedCalender.get(Calendar.MONTH));
            int lastOfleapYearAndMonth2 = 28;
            if (selectedCalender.get(Calendar.MONTH) == monthOfYear && selectDay == monthOfDays){

              selectedCalender.set(Calendar.MONTH,monthOfYear);
              selectedCalender.set(Calendar.DAY_OF_MONTH,lastOfleapYearAndMonth2 -1);
              selectedCalender.set(Calendar.YEAR, Integer.parseInt(year));
            }else {
              selectedCalender.set(Calendar.YEAR, Integer.parseInt(year));
            }
          }else {
            selectedCalender.set(Calendar.YEAR, Integer.parseInt(year));
          }
          //检查月份
          monthChange();

        }
      }
    });

    month_pv.setOnSelectListener(new DatePickerView.onSelectListener() {
      @Override
      public void onSelect(String monthStr) {
        int month = shortMonthStringArray.indexOf(monthStr);

        int dayOfMonth = queryEndDayOfMonth(selectedCalender.get(Calendar.YEAR), month - 1);
        int selectedDay = selectedCalender.get(Calendar.DAY_OF_MONTH);
        if (selectedDay >= dayOfMonth) {
          selectedCalender.set(Calendar.DAY_OF_MONTH, dayOfMonth);
        }

        selectedCalender.set(Calendar.MONTH, month - 1);

        dayChange();

      }
    });

    day_pv.setOnSelectListener(new DatePickerView.onSelectListener() {
      @Override
      public void onSelect(String day) {

        selectedCalender.set(Calendar.DAY_OF_MONTH, Integer.parseInt(day));
        lastSelectedDay = Integer.parseInt(day);
      }
    });

  }

  private void monthChange() {
    month.clear();
    int selectedYear = selectedCalender.get(Calendar.YEAR);
    selectedCalender.get(Calendar.DAY_OF_MONTH);
    int selectedMonth = selectedCalender.get(Calendar.MONTH);

    if (selectedYear == startYear) {
      for (int i = startMonth + 1; i <= MAX_MONTH; i++) {
        month.add(shortMonthStringArray.get(i));
      }
    } else if (selectedYear >= endYear) {
      for (int i = 1; i <= endMonth + 1; i++) {
        month.add(shortMonthStringArray.get(i));
      }
    } else {
      for (int i = 1; i <= MAX_MONTH; i++) {
        month.add(shortMonthStringArray.get(i));
      }
    }
    month_pv.setData(month);
    //如果当前年份等于最大年份，
    //判断当前月份是否大于当今最大月份，大于就选到最大月份，否则就不动
    if (selectedYear == endYear) {
      if (selectedMonth >= endMonth) {
        selectedCalender.set(Calendar.MONTH, endMonth);
      } else {
        selectedCalender.set(Calendar.MONTH, selectedMonth);
      }
    } else {
      selectedCalender.set(Calendar.MONTH, selectedMonth);
    }
    month_pv.setSelected(selectedCalender.get(Calendar.MONTH));
//    executeAnimator(month_pv);
    month_pv.postDelayed(new Runnable() {
      @Override
      public void run() {
        dayChange();
      }
    }, 100);
    executeScroll();
  }

  private void dayChange() {
    day.clear();

    int selectedYear = selectedCalender.get(Calendar.YEAR);
    int selectedMonth = selectedCalender.get(Calendar.MONTH);
    int selectDay = selectedCalender.get(Calendar.DAY_OF_MONTH);


    if (selectedYear == startYear && selectedMonth == startMonth) {
      for (int i = startDay; i <= selectedCalender.getActualMaximum(Calendar.DAY_OF_MONTH); i++) {
        day.add(formatTimeUnit(i) + "");
      }
    } else if (selectedYear >= endYear && selectedMonth >= endMonth) {
      for (int i = 1; i <= endDay2; i++) {
        day.add(formatTimeUnit(i) + "");
      }
    } else {
      int monthOfDays = queryEndDayOfMonth(selectedCalender.get(Calendar.YEAR), selectedCalender.get(Calendar.MONTH));
      for (int i = 1; i <= monthOfDays; i++) {

        day.add(formatTimeUnit(i) + "");
      }

    }

    day_pv.setData(day);
    //如果选择当前年份 月份为最大值，那么就天数必须等于或者小于当天
    if (selectedYear == endYear && selectedMonth == endMonth - 1 && selectDay >= endDay2) {
      selectedCalender.set(Calendar.DAY_OF_MONTH, endDay2);
    } else if (lastSelectedDay >= day.size()) {
      //如上次选择31号，切换后只有30号，那么选中最大日30
      selectedCalender.set(Calendar.DAY_OF_MONTH, day.size());
    } else {
      selectedCalender.set(Calendar.DAY_OF_MONTH, selectDay);
    }
    day_pv.setSelected(selectedCalender.get(Calendar.DAY_OF_MONTH) - 1);
    lastSelectedDay = selectedCalender.get(Calendar.DAY_OF_MONTH);
    executeScroll();
//    executeAnimator(day_pv);

  }


  private void executeAnimator(View view) {
    PropertyValuesHolder pvhX = PropertyValuesHolder.ofFloat("alpha", 1f, 0f, 1f);
    PropertyValuesHolder pvhY = PropertyValuesHolder.ofFloat("scaleX", 1f, 1.3f, 1f);
    PropertyValuesHolder pvhZ = PropertyValuesHolder.ofFloat("scaleY", 1f, 1.3f, 1f);
    ObjectAnimator.ofPropertyValuesHolder(view, pvhX, pvhY, pvhZ).setDuration(200).start();
  }

  private void executeScroll() {
    year_pv.setCanScroll(year.size() > 1);
    month_pv.setCanScroll(month.size() > 1);
    day_pv.setCanScroll(day.size() > 1);
    hour_pv.setCanScroll(hour.size() > 1 && (scrollUnits & SCROLL_TYPE.HOUR.value) == SCROLL_TYPE.HOUR.value);
    minute_pv.setCanScroll(minute.size() > 1 && (scrollUnits & SCROLL_TYPE.MINUTE.value) == SCROLL_TYPE.MINUTE.value);
  }

  private int disScrollUnit(SCROLL_TYPE... scroll_types) {
    if (scroll_types == null || scroll_types.length == 0) {
      scrollUnits = SCROLL_TYPE.HOUR.value + SCROLL_TYPE.MINUTE.value;
    } else {
      for (SCROLL_TYPE scroll_type : scroll_types) {
        scrollUnits ^= scroll_type.value;
      }
    }
    return scrollUnits;
  }

  public void show(String time) {
    if (canAccess) {
      if (isValidDate(time, "yyyy-MM-dd")) {

        if (startCalendar.getTime().getTime() < endCalendar.getTime().getTime()) {
          canAccess = true;
          initParameter();
          initTimer();
          //滑动监听
          addListener();
          setSelectedTime(time);
          datePickerDialog.show();
        }
      } else {
        canAccess = false;
      }
    }
  }

  /**
   * 设置期控件是否显示时和分
   */
  public void showSpecificTime(boolean show) {
    if (canAccess) {
      if (show) {
        disScrollUnit();
        hour_pv.setVisibility(View.VISIBLE);

        minute_pv.setVisibility(View.VISIBLE);

      } else {
        disScrollUnit(SCROLL_TYPE.HOUR, SCROLL_TYPE.MINUTE);
        hour_pv.setVisibility(View.GONE);
        minute_pv.setVisibility(View.GONE);
      }
    }
  }

  /**
   * 设置期控件是否可以循环滚动
   */
  public void setIsLoop(boolean isLoop) {
    if (canAccess) {
      this.year_pv.setIsLoop(isLoop);
      this.month_pv.setIsLoop(isLoop);
      this.day_pv.setIsLoop(isLoop);
      this.hour_pv.setIsLoop(isLoop);
      this.minute_pv.setIsLoop(isLoop);
    }
  }

  /**
   * 设置期控件默认选中的时间
   */
  public void setSelectedTime(String time) {
    if (canAccess) {
      String[] str = time.split(" ");
      String data = str[0];
      year_pv.setSelected(data.substring(0, 4) + "");
      selectedCalender.set(Calendar.YEAR, Integer.parseInt(data.substring(0, 4)));
      month.clear();
      int selectedYear = selectedCalender.get(Calendar.YEAR);
      if (selectedYear == startYear) {
        for (int i = startMonth + 1; i <= MAX_MONTH; i++) {
          month.add(shortMonthStringArray.get(i));
        }
      } else if (selectedYear == endYear) {
        for (int i = 1; i <= endMonth + 1; i++) {
          month.add(shortMonthStringArray.get(i));
        }
      } else {
        for (int i = 1; i <= MAX_MONTH; i++) {
          month.add(shortMonthStringArray.get(i));
        }
      }
      month_pv.setData(month);
      selectedCalender.set(Calendar.MONTH, Integer.parseInt(data.substring(5, 7)) - 1);
      month_pv.setSelected(selectedCalender.get(Calendar.MONTH));

      executeAnimator(month_pv);
      day.clear();
      int selectedMonth = selectedCalender.get(Calendar.MONTH);
      if (selectedYear == startYear && selectedMonth == startMonth) {
        for (int i = startDay; i <= selectedCalender.getActualMaximum(Calendar.DAY_OF_MONTH); i++) {
          day.add(formatTimeUnit(i) + "");
        }
      } else if (selectedYear == endYear && selectedMonth == endMonth) {
        for (int i = 1; i <= endDay2; i++) {
          day.add(formatTimeUnit(i) + "");
        }
      } else {
        for (int i = 1; i <= selectedCalender.getActualMaximum(Calendar.DAY_OF_MONTH); i++) {
          day.add(formatTimeUnit(i) + "");
        }
      }
      day_pv.setData(day);
      day_pv.setSelected(data.substring(8, data.length()) + "");
      selectedCalender.set(Calendar.DAY_OF_MONTH, Integer.parseInt(data.substring(8, data.length())));
      executeAnimator(day_pv);
      if (str.length == 2) {
        String[] timeStr = str[1].split(":");
        if ((scrollUnits & SCROLL_TYPE.HOUR.value) == SCROLL_TYPE.HOUR.value) {
          hour.clear();
          int selectedDay = selectedCalender.get(Calendar.DAY_OF_MONTH);
          if (selectedYear == startYear && selectedMonth == startMonth && selectedDay == startDay) {
            for (int i = startHour; i <= MAX_HOUR; i++) {
              hour.add(formatTimeUnit(i));
            }
          } else if (selectedYear == endYear && selectedMonth == endMonth && selectedDay == endDay) {
            for (int i = MIN_HOUR; i <= endHour; i++) {
              hour.add(formatTimeUnit(i));
            }
          } else {
            for (int i = MIN_HOUR; i <= MAX_HOUR; i++) {
              hour.add(formatTimeUnit(i));
            }
          }
          hour_pv.setData(hour);
          hour_pv.setSelected(timeStr[0]);
          selectedCalender.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeStr[0]));
//          executeAnimator(hour_pv);
        }

        if ((scrollUnits & SCROLL_TYPE.MINUTE.value) == SCROLL_TYPE.MINUTE.value) {
          minute.clear();
          int selectedDay = selectedCalender.get(Calendar.DAY_OF_MONTH);
          int selectedHour = selectedCalender.get(Calendar.HOUR_OF_DAY);
          if (selectedYear == startYear && selectedMonth == startMonth && selectedDay == startDay && selectedHour == startHour) {
            for (int i = startMinute; i <= MAX_MINUTE; i++) {
              minute.add(formatTimeUnit(i));
            }
          } else if (selectedYear == endYear && selectedMonth == endMonth && selectedDay == endDay && selectedHour == endHour) {
            for (int i = MIN_MINUTE; i <= endMinute; i++) {
              minute.add(formatTimeUnit(i));
            }
          } else {
            for (int i = MIN_MINUTE; i <= MAX_MINUTE; i++) {
              minute.add(formatTimeUnit(i));
            }
          }
          minute_pv.setData(minute);
          minute_pv.setSelected(timeStr[1]);
          selectedCalender.set(Calendar.MINUTE, Integer.parseInt(timeStr[1]));
//          executeAnimator(minute_pv);
        }
      }
      executeScroll();
    }
  }

  /**
   * 验证字符串是否是一个合法的期格式
   */
  private boolean isValidDate(String date, String template) {
    boolean convertSuccess = true;
    // 指定期格式
    SimpleDateFormat format = new SimpleDateFormat(template, Locale.CHINA);
    try {
      // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证期，比如2015/02/29会被接受，并转换成2015/03/01
      format.setLenient(false);
      format.parse(date);
    } catch (Exception e) {
      // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
      convertSuccess = false;
    }
    return convertSuccess;
  }

  public int queryEndDayOfMonth(Integer year, int month) {

    Calendar c = Calendar.getInstance();
    c.clear();
    c.set(Calendar.YEAR, year);
    c.set(Calendar.MONTH, month);
    LogUtils.i("年份--->" + year,
            "月份----" + month,
            "月份天数----" + c.getActualMaximum(Calendar.DAY_OF_MONTH),
            "日----" + selectedCalender.get(Calendar.DAY_OF_MONTH));
    return c.getActualMaximum(Calendar.DAY_OF_MONTH);
  }

  private boolean isLeapYear(int year) {
    int leapYearofMonth_2 = 29;
    Calendar calendar = Calendar.getInstance();
    calendar.set(year, 2, 1);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    int day = calendar.get(Calendar.DAY_OF_MONTH);
    return day == leapYearofMonth_2;
  }

  public interface OnDateClickListener {
    /**
     * Called when a view has been clicked.
     */
    void onConfirmClick(String date);
    void onCancelClick(String date);
  }

  public void setmOnDateClickListener(OnDateClickListener mOnDateClickListener) {
    this.mOnDateClickListener = mOnDateClickListener;
  }
  public void setTitle(String title){
    if(datePickerDialog != null){
      TextView tv_title = (TextView) datePickerDialog.findViewById(R.id.tv_title);
      tv_title.setText(title);
    }

  }
  public void dissMiss(){
    datePickerDialog.dismiss();
  }

}
