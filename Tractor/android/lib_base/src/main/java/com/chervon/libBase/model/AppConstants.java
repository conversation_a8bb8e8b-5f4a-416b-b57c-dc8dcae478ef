package com.chervon.libBase.model;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.model
 * @ClassName: AppConstants
 * @Description: 全局静态变量
 * @Author: langmeng
 * @CreateDate: 2022/8/27 16:23
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/8/27 16:23
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class AppConstants {

  public static final String APP = "app";
  public static final String RN = "rn";
  //系统通知渠道
  public static final String CHANNELID_SYSTEM = "system";
  //营销通知渠道
  public static final String CHANNELID_MARKETING = "marketing";
  //设备通知渠道
  public static final String CHANNELID_DEVICE = "device";
  //客服反馈
  public static final String CHANNELID_CUSTOMER = "customer";

  public static final String NET_WORK_EXCEPTION = "network exception";

  public static final String DEVICE_MODULE = "DeviceModule";
  // TODO Z6

  public static final int Z6_BLENAME_LENGTH = 22;
  public static final String Z6_HEAD = "X_";
  public static final String ZT_01 = "zt01";
  public static final String ZT_02 = "zt02";
  public static final String ZT_03 = "zt03";

  public static final String RM_01 = "RM01";
  public static final String RM_02 = "RM02";

  public static final String ZTX4800R = "ZTX4800R";
  public static final String ZTX5400R = "ZTX5400R";
  public static final String ZTX6000R = "ZTX6000R";
  public static final int LOCK_POSITION = 6;
  public static final String ADD_0 = "0";
  public static final int DEVICEID_END_LENGTH = 6;
  public static final int BIT_LENGTH = 8;
  public static final String DATA_LENGTH = "19";
  public static final String PACKAGE_NUBER = "00";


  //权限相关错误码
  //相机权限未授予
  public static final int  PERMISSION_CAMERA_UNAUTHORIZED = 1001001;
  //相册权限未授予
  public static final int  PERMISSION_ALBUM_UNAUTHORIZED = 1001002;

  public static final String MEDIA_TYPE_PDF = "application/pdf";
  public static final String MEDIA_TYPE_DOC = "application/msword";
  public static final String MEDIA_TYPE_DOCX = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
  public static final String MEDIA_TYPE_PNG = "image/png";

}
