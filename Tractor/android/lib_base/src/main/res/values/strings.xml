<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="action_update">action_update</string>
    <string name="action_control">action_control</string>
    <string name="action_del">action_del</string>
    <string name="action_device_detail">action_device_detail</string>
    <string name="a_half">1/2</string>
    <string name="two_seconds">2/2</string>
    <string name="star">"* "</string>
    <string name="PLUS">+</string>
    <string name="SPACE">&#160;</string>


    <string name="app_regist_firstnameinputerror_textview_text">No Spaces are allowed before or after the content</string>
    <string name="app_networkconfigblutoothconnect_connectsuccess_textview_text">Device added successfully</string>
    <string name="app_networkconfigwifiinput_password_inputview_placeholder">Password</string>
    <string name="app_loginguide_login_button_text">Log In</string>
    <string name="app_login_title_textview_text">Log in</string>
    <string name="app_login_forget_button_text">Forgot Password?</string>
    <string name="app_login_regist_button_text">Sign Up</string>
    <string name="app_regist_title_textview_text">Sign up</string>
    <string name="app_base_confirm_button_text">confirm</string>
    <string name="app_login_account_inputview_placeholder">Email address</string>
    <string name="app_regist_email_inputview_placeholder">Email address</string>
    <string name="app_forgetpassword_email_inputview_placeholder">Email address</string>
    <string name="app_regist_next_button_text">Next</string>
    <string name="app_forgetpassword_next_button_text">Next</string>
    <string name="app_devicesninput_helpnext_button_text">Next</string>
    <string name="app_loginguide_regist_button_text">Sign Up</string>
    <string name="app_registverifycode_done_button_text">Done</string>
    <string name="app_registverifycode_done_button_title">Done</string>
    <string name="app_forgetpasswordreset_done_button_text">Done</string>
    <string name="app_deviceproductsearch_done_button_text">Done</string>
    <string name="app_networkconfigblutoothconnect_done_button_text">Done</string>
    <string name="app_modifypassword_done_button_text">Done</string>
    <string name="app_OTA_done_button_text">Done</string>
    <!--  <string name="title_shopping_mall">Shopping Mall</string>-->
    <string name="app_deviceproductsearch_cancel_button_text">cancel</string>
    <string name="app_userDetail_cancel_textview_text">cancel</string>
    <string name="app_devicesninput_sn_inputview_placeholder">Enter Serial Number</string>
    <string name="app_deviceqrcodescan_inputsn_button_text">Enter serial number manually</string>
    <string name="app_deviceqrcodescan_notice_textview_text">Please scan the QR code on the device</string>

    <string name="app_deviceqrcodescan_title_textview_text">Scan</string>
    <string name="app_devicesearch_notice_textview_text">Scanning for nearby Bluetooth device…</string>
    <string name="app_deviceadd_useranothermethod_textview_text">Use another method.</string>
    <string name="app_deviceadd_scanqrcode_button_text">Scan QR Code</string>
    <string name="app_login_privacy_textview_text">Privacy Policy</string>
    <string name="app_login_agreement_textview_text">User Agreement</string>
    <string name="app_login_agreementprivacypart_textview_text">I have read and agreed to the</string>
    <string name="app_login_agreementprivacyand_textview_text">and</string>
    <string name="app_checkpassword_inputpassword_inputview_placeholder">Enter the original password</string>
    <string name="app_regist_login_button_text">You already have an account to log in</string>
    <string name="app_login_account_textview_text">Please enter the correct mailbox</string>
    <string name="app_devicelist_emptywelcome_textview_text">WELCOME</string>
    <string name="app_loginguide_wellcom_textview_text">WELCOME</string>
    <string name="app_loginguide_copyright_textview_text">to POWER BEYOND BELIEF.™</string>

    <string name="app_loginguide_guide_textview_text">You can use your existing EGO POWER+ account to log in.</string>
    <string name="app_login_checkagreement_textview_text">Please check before sign in</string>
    <string name="app_regist_agreementprivacy_textview_text">Please check before log in</string>
    <string name="app_devicesninput_title_textview_text">Enter Sn code</string>
    <string name="app_deviceqrcodescan_help_button_text">QR code not found?</string>
    <string name="app_devicesninput_help_button_text">QR code not found?</string>
    <string name="app_deviceproductsearch_history_textview_text">Search history</string>
    <string name="app_deviceproductsearch_deletealert_textview_text">Clear search history</string>
    <string name="app_networkconfigblutoothconnect_stepconnecting_textview_text">Device Connecting</string>
    <string name="app_networkconfigblutoothconnect_stepconnectsuccess_textview_text">Device Connection successful</string>
    <string name="app_networkconfigblutoothconnect_stepbinding_textview_text">Device pairing</string>
    <string name="app_networkconfigwificonnect_failed_textview_text">Please check the following problems and try again</string>
    <string name="app_networkconfigwificonnect_faileddescription_textview_text">1. Please keep the phone close to the device during the adding process$$2. Please check whether the device is connected via Bluetooth$$3. Please make sure the Wi-Fi password is correct</string>
    <string name="app_networkconfigwificonnect_noticeconnectfaied_textview_text">Add failed</string>
    <string name="app_OTA_retry_button_text">Retry</string>
    <string name="app_networkconfigblutoothconnect_retry_button_text">Retry</string>
    <string name="app_networkconfigwificonnect_retry_button_text">Retry</string>
    <string name="app_networkconfigblutoothconnect_title_textview_text">Bluetooth Connection</string>
    <string name="app_networkconfigblutoothconnect_notice_textview_text">Please keep the Bluetooth on and keep the phone close to the device</string>
    <string name="app_networkconfigwificonnect_description_textview_text">Please keep the Bluetooth on and keep the phone as close to the device as possible Please make sure the WiFi signal is good</string>
    <string name="app_networkconfigwifiinput_join_button_text">Join</string>
    <string name="app_networkconfigwificonnect_noticeconnectsuccess_textview_text">Connection successful</string>
    <string name="app_networkconfigwificonnect_noticeconnecting_textview_text">Connecting</string>
    <string name="app_networkconfigguide_confirmed_button_text">Confirm</string>
    <string name="app_base_selectwifi_button_text">Select WiFi</string>
    <string name="app_networkconfigguide_initialize_textview_text">Initialize</string>
    <string name="app_devicesearchresult_founded_textview_text">Device(s) found</string>
    <string name="app_devicesearchresult_description_textview_text">If your device can not be found, Please use another method.</string>
    <string name="app_deviceregist_title_textview_text">Device Registation</string>
    <string name="app_deviceregist_sn_inputview_placeholder">Enter Serial Number</string>
    <string name="app_deviceregist_done_button_text">Done</string>
    <string name="app_deviceregist_devicesn_textview_text">SN.</string>
    <string name="app_deviceregist_receipt_inputview_placeholder">Receipt</string>
    <string name="app_deviceregist_receiptupload_textview_text">Upload Receipt</string>
    <string name="app_deviceregist_receiptuploadalbum_button_text">Album</string>
    <string name="app_deviceregist_receiptuploadcamera_button_text">Camera</string>
    <string name="app_deviceregist_receiptuploadcancel_button_text">Cancel</string>
    <string name="app_deviceregist_location_inputview_placeholder">Place of purchase</string>
    <string name="app_deviceregist_date_inputview_placeholder">Date of Purchase</string>
    <string name="app_deviceregist_use_inputview_placeholder">Purpose</string>
    <string name="app_deviceregist_survey_textview_text">Enjoy an additional 2 years warranty</string>
    <string name="app_deviceregist_surveydetail_textview_text">Upload your receipt (required) and answer these questions.$$if you register your battery within 90 days, we’ll extend your 10.0Ah/12.0Ah battery warranty from 3 years to 5 years—with our thanks!</string>

    <string name="app_deviceregist_surveypurchaser_textview_text">PURCHASER</string>
    <string name="app_regist_firstname_inputview_placeholder">"First name (required)"</string>
    <string name="app_regist_lastname_inputview_placeholder">"Last name (required)"</string>
    <string name="app_deviceregist_city_textview_text">City*</string>
    <string name="app_deviceregist_surveypurchaseroption_countrytitle_textview_text">Country</string>
    <string name="app_deviceregist_surveypurchaseroption_provincetitle_textview_text">State/Province</string>
    <string name="app_deviceregist_surveycontact_textview_text">CONTACT INFORMATION</string>
    <string name="app_deviceregist_surveyagree_textview_text">"YES, I'd like to sign up to participate in EGO product research."</string>
    <string name="app_deviceregist_surveybuyreason_textview_text">Please indicate the top 2 reasons that you purchased this product:</string>

    <string name="app_forgetpasswordverifycode_title_textview_text"> Forget password</string>
    <string name="app_deviceregist_surveybuyreasonoption1_textview_text">Received good ratings</string>
    <string name="app_deviceregist_surveybuyreasonoption2_textview_text">56 Volt battery power meets my needs</string>
    <string name="app_deviceregist_surveybuyreasonoption3_textview_text">Product features meet my needs</string>
    <string name="app_deviceregist_surveybuyreasonoption4_textview_text">56 Volt battery compatible with all other EGO outdoor power tools</string>
    <string name="app_deviceregist_surveybuyreasonoption5_textview_text">Premium product design</string>
    <string name="app_deviceregist_surveybuyreasonoption6_textview_text">5 Year limited warranty</string>
    <string name="app_deviceregist_surveybuyreasonoption7_textview_text">Environmentally friendly device</string>
    <string name="app_deviceregist_surveybuyreasonoption8_textview_text">Other</string>
    <string name="app_deviceregist_surveyotherproduct_textview_text">What other EGO products do you own? Please select all that apply:</string>
    <string name="app_deviceregist_surveyotherproductoption1_textview_text">Riding Mower</string>
    <string name="app_deviceregist_surveyotherproductoption2_textview_text">Lawn Mower</string>
    <string name="app_deviceregist_surveyotherproductoption3_textview_text">Leaf Blower</string>
    <string name="app_deviceregist_surveyotherproductoption4_textview_text">String Trimmer</string>
    <string name="app_deviceregist_surveyotherproductoption5_textview_text">Snow Blower</string>
    <string name="app_deviceregist_surveyotherproductoption6_textview_text">Chain Saw</string>
    <string name="app_deviceregist_surveyotherproductoption7_textview_text">Hedge Trimmer</string>
    <string name="app_deviceregist_surveyotherproductoption8_textview_text">Power Station (Inverter)</string>
    <string name="app_deviceregist_surveyotherproductoption9_textview_text">Other</string>
    <string name="app_deviceregist_surveyotherproductoption10_textview_text">This is my first EGO product</string>
    <string name="app_deviceregist_surveywantbuy_textview_text">Which other products are you considering buying in the next 12 months? Please select all that apply.</string>
    <string name="app_deviceregist_surveywantbuyoption1_textview_text">Riding Mower</string>
    <string name="app_deviceregist_surveywantbuyoption2_textview_text">Lawn Mower</string>
    <string name="app_deviceregist_surveywantbuyoption3_textview_text">Leaf Blower</string>
    <string name="app_deviceregist_surveywantbuyoption4_textview_text">String Trimmer</string>
    <string name="app_deviceregist_surveywantbuyoption5_textview_text">Snow Blower</string>
    <string name="app_deviceregist_surveywantbuyoption6_textview_text">Chain Saw</string>
    <string name="app_deviceregist_surveywantbuyoption7_textview_text">Hedge Trimmer</string>
    <string name="app_deviceregist_surveywantbuyoption8_textview_text">Power Station (Inverter)</string>
    <string name="app_deviceregist_surveywantbuyoption9_textview_text">Presure Washer</string>
    <string name="app_deviceregist_surveywantbuyoption10_textview_text">Wet/Dry Vac</string>
    <string name="app_deviceregist_surveywantbuyoption11_textview_text">Ice/Earth Auger</string>
    <string name="app_deviceregist_surveywantbuyoption12_textview_text">Portable Cooler</string>
    <string name="app_deviceregist_surveywantbuyoption13_textview_text">Portable Speaker</string>
    <string name="app_deviceregist_surveywantbuyoption14_textview_text">Portable Light</string>
    <string name="app_deviceregist_surveywantbuyoption15_textview_text">Portable Fan</string>
    <string name="app_deviceregist_surveywantbuyoption16_textview_text">UTV</string>
    <string name="app_deviceregist_surveywantbuyoption17_textview_text">E-bike/Scooter</string>
    <string name="app_deviceregist_surveywantbuyoption18_textview_text">Other</string>
    <string name="app_deviceregist_surveywantbuyoption19_textview_text">Do not know</string>
    <string name="app_deviceregist_surveyotherband_textview_text">What brand did you use before this EGO product? Select one.</string>
    <string name="app_deviceregist_surveyotherbandoption1_textview_text">John Deere</string>
    <string name="app_deviceregist_surveyotherbandoption2_textview_text">Honda</string>
    <string name="app_deviceregist_surveyotherbandoption3_textview_text">Ryobi</string>
    <string name="app_deviceregist_surveyotherbandoption4_textview_text">Craftsman</string>
    <string name="app_deviceregist_surveyotherbandoption5_textview_text">Cub Cadet</string>
    <string name="app_deviceregist_surveyotherbandoption6_textview_text">Husqvarna</string>
    <string name="app_deviceregist_surveyotherbandoption7_textview_text">Toro</string>
    <string name="app_deviceregist_surveyotherbandoption8_textview_text">Stihl</string>
    <string name="app_deviceregist_surveyotherbandoption9_textview_text">Ariens</string>
    <string name="app_deviceregist_surveyotherbandoption10_textview_text">Other</string>
    <string name="app_deviceregist_surveyotherbandoption11_textview_text">None, this is my first product of this kind</string>
    <string name="app_deviceregistactionsheet_location_textview_text">Place of Purchase</string>
    <string name="app_deviceregistactionsheet_use_textview_text">Purpose</string>
    <string name="app_deviceregistactionsheet_date_textview_text">Date of Purchase</string>
    <string name="app_deviceregistsuccess_title_textview_text">Device Registation</string>
    <string name="app_deviceregistsuccess_use_textview_text">Purpose</string>
    <string name="app_deviceregistsuccess_location_textview_text">Place of purchase</string>
    <string name="app_deviceregistsuccess_date_textview_text">Date of Purchase</string>
    <string name="app_deviceregistsuccess_sn_textview_text">Sn</string>
    <string name="app_deviceregistsuccess_done_button_text">Done</string>
    <string name="app_deviceregistsuccess_more_button_text">Register More</string>
    <string name="app_systemauthorization_locationtitle_textview_text">Access Geographic Location</string>
    <string name="app_systemauthorization_locationdescription_textview_text">Scan WLAN list, search nearby Bluetooth devices and device related functions</string>
    <string name="app_systemauthorization_bluetoothtitle_textview_text">Bluetooth Access Permissions</string>
    <string name="app_systemauthorization_bluetoothdescription_textview_text">Access to Bluetooth is required to discover nearby Bluetooth devices</string>
    <string name="app_systemauthorization_cameratitle_textview_text">Access Camera Permissions</string>
    <string name="app_systemauthorization_cameradescription_textview_text">Code scanning and other functions</string>
    <string name="app_systemauthorization_albumtitle_textview_text">Access to Photo Permissions</string>
    <string name="app_systemauthorization_albumdescription_textview_text">Uploading pictures, feedback and other functions</string>
    <string name="app_systemauthorization_allowed_textview_text">Allowed</string>
    <string name="app_systemauthorization_grant_textview_text">Grant</string>
    <string name="app_privacyagreementrevoke_title_textview_text">Revoking authorization</string>
    <string name="app_deviceqrcodescan_helpdefault_textview_text">Equipment QR code: QR code is pasted next to the SN code</string>
    <string name="app_privacyagreementdetail_date_textview_text">Our privacy policy is updated on</string>
    <string name="app_container_devicedonotusetoast_textview_text">This device has been stopped service or abandoned</string>
    <string name="app_devicelist_addtool_button_text">+ ADD</string>
    <string name="app_checkpassword_title_textview_text">Change Password</string>
    <string name="app_modifypassword_title_textview_text">Change Password</string>
    <string name="app_privacyagreementuser_date_textview_text">Our user agreement is updated on</string>
    <string name="app_forgetpasswordsuccess_subtext_textview_text">You have reset your password. Please log in</string>
    <string name="app_networkconfigblutoothconnect_registdevice_button_text">Register Now</string>
    <string name="app_networkconfigblutoothconnect_notregistdevice_button_text">Done</string>
    <string name="app_forgetpasswordsuccess_maintext_textview_text">Successfully</string>
    <string name="app_login_password_textview_text">The password is 8-16 bits, case sensitive, and must contain numbers and letters，cannot contain spaces</string>
    <string name="app_regist_email_textview_text"> Please enter the correct mailbox</string>
    <string name="app_devicesearch_notopenbluetoothdescription_textview_text">Please turn on Bluetooth</string>
    <string name="app_deviceadd_selectbycategory_textview_text">Select by category</string>
    <string name="app_devicelist_emptydescription_textview_text">No products here, please add products first.</string>
    <string name="app_deviceregistactionsheet_receipt_textview_text">Receipt Information</string>
    <string name="app_deviceregist_register_button_text">Register</string>
    <string name="app_deviceregistfailed_failed_textview_text">Registration failed (ERROR002) \n Please try again!</string>
    <string name="app_deviceregistfailed_retry_button_text">Retry</string>
    <string name="app_deviceregistfailed_back_button_text">Back</string>
    <string name="app_networkconfigblutoothconnect_connectfaileddescription_textview_text">1. Please keep the mobile phone Bluetooth on and close to the device as it is being added$$2. Please check if the device is connected by another user</string>
    <string name="app_base_next_button_text">Next</string>
    <string name="app_helpcenter_question_textview_text">Enter your question</string>
    <string name="app_helpcenter_title_textview_text">Help Center</string>
    <string name="app_helpcenterdetail_title_textview_text">Help Center</string>
    <string name="app_helpcenterdetail_recommen_textview_text">Related recommendations</string>
    <string name="app_helpcentersearch_cancel_button_text">Cancel</string>
    <string name="app_helpcentersearch_question_inputview_placeholder">Enter your question</string>
    <string name="app_helpcentersearch_nodata_textview_text">No corresponding problem found!</string>
    <string name="app_messagecenter_devicemessageerror_textview_text">Error code：</string>
    <string name="app_productdealers_locationtips_textview_text">Open the location authority of ego app to find dealers near you</string>
    <string name="app_productdealers_dataempty_textview_text">No match result</string>
    <string name="app_productdealers_dataempty_toast_text">No dealer</string>
    <string name="app_devicelist_hi_textview_text">Hi</string>
    <string name="app_base_havenotnetwork_textview_text"> Connection failed,please check your network！</string>
    <string name="app_privacyagreementrevoke_revoke_button_text">Revoke</string>
    <string name="app_privacyagreementrevoke_cancel_button_text">Cancel</string>
    <string name="app_privacyagreementuser_title_textview_text">Legal Information</string>
    <string name="app_privacyagreementuser_privacyagreement_textview_text">Privacy Policy</string>
    <string name="app_privacyagreementuser_useragreement_textview_text">User Agreement</string>
    <string name="app_privacyagreementuser_revokeagreement_textview_text">Revoke Authorization</string>
    <string name="app_privacyagreementuser_cancelaccount_textview_text">Cancel Services</string>
    <string name="app_privacyagreementdevice_title_textview_text">Privacy</string>
    <string name="app_deviceqrcodescan_productnotsametoast_textview_text">Device already exists</string>
    <string name="app_networkconfigwifiinput_account_inputview_placeholder">Name</string>
    <string name="app_base_logininvalid_textview_text">Login status invalid</string>
    <string name="app_devicelist_deviceoffline_textview_text">Offline</string>
    <string name="app_userDetail_takephoto_textview_text">Take Photo</string>
    <string name="app_userDetail_album_textview_text">Choose from Album</string>

    <string name="app_OTAguide_cannotupragecode1_textview_text">Device can\'t upgrade now</string>
    <string name="app_OTAguide_cannotupragecode2_textview_text">The battery of the device is low and cannot be updated</string>
    <string name="app_OTAguide_cannotupragecode3_textview_text">The device is working and cannot be updated</string>
    <string name="app_OTAguide_cannotupragecode4_textview_text">The brake is not on and cannot be updated.</string>
    <string name="app_OTAguide_cannotupragecode6_textview_text">The Device is not in the charging pile and cannot be updated.</string>
    <string name="app_OTAguide_cannotupragecode5_textview_text">Do not sit in the seat.</string>
    <string name="app_OTAguide_cannotupragecode8_textview_text">Please pull up PTO.</string>

    <string name="app_systemauthorization_receive_textview_text">Receive content</string>
    <string name="app_usercenter_acount_textview_text">Account</string>
    <string name="app_productdealers_title_textview_text">Dealer Locations</string>
    <string name="app_usercenter_help_textview_text">Help Center</string>
    <string name="app_userDetail_changePassword_textview_text">Change Password</string>
    <string name="app_usercenter_message_textview_text">Messages</string>
    <string name="app_setting_language_textview_text">Language</string>
    <string name="app_language_title_textview_text">Language</string>
    <string name="app_setting_cache_textview_text">Clear Cache</string>
    <string name="app_setting_version_textview_text">Version</string>
    <string name="app_logoff_logoff_textview_text">Cancel services</string>
    <string name="app_setting_privacy_textview_text">Legal Information</string>
    <string name="app_privacy_title_textview_text">Privacy</string>
    <string name="app_setting_findDevice_textview_text">Smart Connection</string>
    <string name="app_setting_finddevicedescribe_textview_text">When the mobile phone is close to the Bluetooth device to be added, a window will pop up automatically to prompt that it can be connected.</string>
    <string name="app_userDetail_accountsettings_textview_text">Account Settings</string>
    <string name="app_privacy_cancelServices_textview_text">Cancel services</string>
    <string name="app_usercenter_setting_textview_text">Settings</string>
    <string name="app_setting_title_textview_text">Settings</string>
    <string name="app_userinfo_account_textview_text">Account</string>
    <string name="app_OTA_upgrade_textview_text">Update</string>
    <string name="app_OTAInfo_upgrade_textview_text">Update</string>
    <string name="app_OTAguide_upgrade_button_text">Update</string>
    <string name="app_OTAguide_upgrade_textview_text">Update</string>
    <string name="app_OTAguide_upgradetip_textview_text">Please keep the equipment well connected  keep the equipment well connected</string>
    <string name="app_OTAhistory_upgradehistory_textview_text">Update History</string>
    <string name="app_OTA_equipmentupgrade_textview_text">Equipment update</string>
    <string name="app_language_done_button_text">Done</string>
    <string name="app_logoff_description_textview_text">After cancellation, all user data under this account will also be permanently deleted.</string>
    <string name="app_logoff_confirm_button_text">Confirm Cancel</string>
    <string name="app_usercenter_services_textview_text">My services</string>
    <string name="app_usercenter_dealer_textview_text">Dealer Locations</string>
    <string name="app_privacy_privacyPolicy_textview_text">Privacy policy</string>
    <string name="app_privacy_userAgreement_textview_text">User agreement</string>
    <string name="app_userDetail_profile_textview_text">Profile</string>
    <string name="app_userDetail_name_textview_text">Name</string>
    <string name="app_userinfo_firstname_inputview_placeholder">First name (required)</string>
    <string name="app_userinfo_lastname_inputview_placeholder">Last name (required)</string>
    <string name="app_userinfo_nickname_inputview_placeholder">Nickname</string>
    <string name="app_userinfo_age_inputview_placeholder">Age</string>
    <string name="app_userinfo_gender_inputview_placeholder">Gender</string>
    <string name="app_userinfo_mobilenumber_inputview_placeholder">Mobile number</string>
    <string name="app_userinfo_address_inputview_placeholder">Address</string>
    <string name="app_userinfo_country_inputview_placeholder">Country</string>
    <string name="app_userinfo_zipcode_inputview_placeholder">Zip code</string>
    <string name="app_userinfo_areacode_textview_text">Select Your Country Calling Code</string>
    <string name="app_userinfo_selectgender_textview_text">Select gender</string>
    <string name="app_base_datapickerok_button_text">Done</string>
    <string name="app_setting_logoutalert_textview_text">Are you sure to exit account?</string>
    <string name="app_setting_cancel_button_text">Cancel</string>
    <string name="app_setting_logout_button_text">Log Out</string>
    <string name="app_logoff_confirm_textview_text">Are you sure to Cancel services?</string>
    <string name="app_logoff_cancel_button_text">Cancel</string>
    <string name="app_base_cancel_button_text">Cancel</string>
    <string name="app_OTAInfo_cancel_button_text">Cancel</string>
    <string name="app_logoff_ok_button_text">OK</string>
    <string name="app_deviceqrcodescan_helpok_button_text">OK</string>
    <string name="app_devicelist_deletedevicealertok_button_text">OK</string>
    <string name="app_setting_success_textview_text">Successfully</string>
    <string name="app_setting_success_text">Success</string>
    <string name="app_userinfo_save_button_text">Save</string>
    <string name="app_modifyavator_modify_textview_text">Modify</string>
    <string name="app_userinfo_male_textview_text">Male</string>
    <string name="app_userinfo_female_textview_text">Female</string>
    <string name="app_smartadddevice_descriptionfirst_textview_text">The device</string>
    <string name="app_smartadddevice_descriptionsecond_textview_text">near you has been found, do you want connect?</string>
    <string name="app_smartadddevice_connect_button_text">Connect</string>
    <string name="app_smartadddevice_cancel_button_text">Cancel</string>
    <string name="app_OTA_back_button_text">Back</string>
    <string name="app_OTA_upgradesuccessful_textview_text">Upgrade successful</string>
    <string name="app_OTA_upgradefailed_textview_text">Upgrade failed</string>
    <string name="app_OTA_newversion_textview_text">New version number is </string>
    <string name="app_OTA_cancelUpgrade_textview_text">Device is being upgraded. Leaving the current page may cause the upgrade to fail</string>
    <string name="app_OTAInfo_cancelUpgrade_textview_text">If you do not upgrade the device will not work properly,\nPlease upgrade now</string>
    <string name="app_OTAhistory_versionhistoryfirst_textview_text">Updated from</string>
    <string name="app_OTAhistory_versionhistorysecond_textview_text">to</string>
    <string name="app_privacy_withdraw_authorize_textview_text">Withdrawal of consent</string>
    <string name="app_messagesetting_receive_textview_text">Receive content</string>
    <string name="app_messagesetting_systemmessages_textview_text">System Notifications</string>
    <string name="app_messagesetting_systemtip_textview_text">Receive notification from ego app system</string>
    <string name="app_messagesetting_permissiontip_textview_text">Enable the permission for EGO notifications to receive notifications immediately.</string>
    <string name="app_messagecenter_title_textview_text">Notifications</string>
    <string name="app_messagecenter_devicemessagecontent_textview_text">No message</string>
    <string name="app_messagecenter_messagelistdelete_textview_text">Delete</string>
    <string name="app_messagecenter_messagelistsystemtitle_textview_text">System notifications</string>
    <string name="app_messagecenter_messagelistmarketingtitle_textview_text">Marketing message</string>
    <string name="app_messagecenter_messagedetailtitle_textview_text">Notification Details</string>
    <string name="app_channelname_system_textview_text">System notifications</string>
    <string name="app_channelname_marketing_textview_text">Marketing notifications</string>
    <string name="app_channelname_device_textview_text">Device notifications</string>
    <string name="app_version_upgradetitle_textview_text">Upgrade content</string>
    <string name="app_version_upgrade_button_text">Version update</string>
    <string name="app_version_title_textview_text">Version</string>
    <string name="app_about_subtitle_textview_text">Report Mailbox</string>
    <string name="app_about_tip_textview_text">©2023 EGO CONNECT. All rights reserved.</string>
    <string name="app_version_latestversion_textview_text">Latest version</string>
    <string name="app_version_currentversion_textview_text">Current Version</string>
    <string name="app_about_currentversion_textview_text">Current version</string>
    <string name="app_about_content_textview_text">For complaints and reports of personal information security, please contact us at the email address below:$$<EMAIL></string>
    <string name="app_OTAInfo_currentversion_textview_text">Current version</string>
    <string name="app_OTAInfo_latestversion_textview_text">Latest version</string>
    <string name="app_OTAInfo_upgrade_button_text">Upgrade</string>
    <string name="app_setting_clearcache_textview_text">Are you sure you want to clear the cache?</string>
    <string name="app_setting_clearcachecancle_button_text">Cancel</string>
    <string name="app_setting_clearcacheclearnow_button_text">Clear now</string>
    <string name="app_setting_clearcacheloading_textview_text">Cleaning</string>
    <string name="app_OTAhistory_currentversion_textview_text">Current version</string>
    <string name="app_userinfo_inputnickname_textview_text">Nickname</string>
    <string name="app_userinfo_inputage_textview_text">Age</string>
    <string name="app_userinfo_choosegender_textview_text">Gender</string>
    <string name="app_userinfo_chooseareacode_textview_text">Please choose zip code</string>
    <string name="app_userinfo_inputmobilephone_textview_text">Mobile number</string>
    <string name="app_userinfo_inputaddress_textview_text">Address</string>
    <string name="app_userinfo_inputcountry_textview_text">Country</string>
    <string name="app_userinfo_inputzipcode_textview_text">Zip code</string>
    <string name="app_OTAhistory_note_textview_text">Release note</string>
    <string name="app_OTAInfo_versionhistory_button_text">Version History</string>
    <string name="app_OTAInfo_upgradecontent_textview_text">Upgrade content</string>
    <string name="app_OTA_download_textview_text">Firmware download in progress…</string>
    <string name="app_OTA_upgrading_textview_text">Device upgrading</string>
    <string name="app_OTA_keepconnected_textview_text">Please keep the device network connected well</string>
    <string name="app_messagelist_nomessage_textview_text">No message</string>

    <string name="app_fittinghelp_buy_button_text">Where to Buy</string>
    <string name="app_producthelp_title_textview_text">Product Information</string>
    <string name="app_producthelp_manual_button_text">VIEW THE MANUAL</string>
    <string name="app_producthelp_video_button_text">HOW-TO VIDEOS</string>
    <string name="app_producthelp_detail_button_text">DETAILS</string>
    <string name="app_producthelp_techspec_button_text">TECH SPECS</string>
    <string name="app_producthelp_faq_button_text">FAQ</string>
    <string name="app_devicemoreinfo_name_textview_text">Equipment name</string>
    <string name="app_devicemoreinfo_regist_textview_text">Device registration</string>
    <string name="app_devicemoreinfo_product_textview_text">Product Encyclopedia</string>
    <string name="app_devicecode_title_textview_text">Device Information</string>
    <string name="app_fittinghelp_title_textview_text">Accessories</string>
    <string name="app_deviceboard_manual_button_text">Manual</string>
    <string name="app_deviceboard_info_button_text">Equipment Info</string>
    <string name="app_devicecode_assemblyserialnumber_textview_text">Assembly serial number</string>
    <string name="app_devicerename_toast_textview_text">Cannot contain special characters</string>
    <string name="app_devicemoreinfo_title_textview_text">Device Details</string>
    <string name="app_devicerename_title_textview_text">Rename Device</string>

    <string name="app_fittinglist_title_textview_text">Accessories</string>
    <string name="app_usercenter_feedback_textview_text">Feedback</string>
    <string name="app_feedback_problems_textview_text">"Any problems with your devices?"</string>
    <string name="app_feedback_appissues_textview_text">App issues</string>
    <string name="app_feedbackhistory_title_textview_text">"Feedback History"</string>
    <string name="app_feedbackhistory_edit_button_text">Edit</string>
    <string name="app_feedbackhistory_cancel_button_text">Cancel</string>
    <string name="app_feedbackhistory_delete_button_text">Delete</string>
    <string name="app_feedbackhistory_nocontent_textview_text">No content</string>
    <string name="app_feedbackquestions_entermobilephone_textview_text">Please enter your mobile number</string>
    <string name="app_feedbackquestions_submit_button_text">Submit</string>
    <string name="app_feedbackquestions_mobilephone_textview_text">Mobile phone number (optional)</string>
    <string name="app_feedbackquestions_imageoptional_textview_text">Image(optional)</string>
    <string name="app_feedbackquestions_questiondescriptionhint_textview_text">"Please describe your problem specifically and add the necessary information such as the name of the equipment "</string>
    <string name="app_feedbackquestions_questiondescription_textview_text">Question Description</string>
    <string name="app_feedbackquestions_classification_textview_text">Classification of Issues</string>
    <string name="app_feedbackquestions_title_textview_text">Feedback Questions</string>
    <string name="app_feedbackdetails_imageoptional_textview_text">Image（optional）</string>
    <string name="app_feedbackdetails_whatreplyto_textview_text">Please enter what you want to reply to!</string>
    <string name="app_feedbackdetails_publish_button_text">Publish</string>
    <string name="app_feedbackdetails_title_textview_text">Feedback Details</string>
    <string name="app_feedbackdetails_reply_button_text">Reply</string>
    <string name="app_feedbackhistory_confirmdelete_textview_text">Confirm to delete the selected  content</string>
    <string name="app_feedback_noequipment_textview_text">No equipment</string>

    <string name="app_feedbackquestions_enteradescriptiontip_textview_text">Submission failed$$please try again</string>
    <string name="app_feedbackquestions_submitfailed_textview_text">Submission failed$$ please try again</string>
    <string name="app_feedback_more_button_text">More</string>
    <string name="app_base_showfile_textview_text">User Manual</string>
    <string name="app_commom_log_out">Press again to exit the program</string>
    <string name="app_common_foreground_service_running">Service Running</string>
    <string name="app_messagedetail_suggestiontitle_textview_text">Solution suggestions</string>
    <string name="app_messagedetail_viewmore_textview_text">View more</string>
    <string name="app_deviceqrcodescan_cameraAuthorizedalerttitle_textview_text">Unauthorized access to camera</string>
    <string name="app_networkconfigblutoothconnect_stepbindsuccess_textview_text">Device pairing successful</string>
    <string name="app_networkconfigwifiinput_location_alert_title">The current APP positioning is not enabled</string>
    <string name="app_networkconfigblutoothconnect_connectfailed_textview_text">Device connection failed; please check the following issues and try again</string>
    <string name="app_networkconfigwificonnect_stepsynchronizesuccess_textview_text">Successfully synchronized WiFi information</string>
    <string name="app_networkconfigwificonnect_stepconnectsuccess_textview_text">Successfully connected to the network</string>
    <string name="app_networkconfigwificonnect_stepbindsuccess_textview_text">Device account pairing succeeded</string>
    <string name="app_messagecenter_dialog_button_close_text">Close</string>
    <string name="app_setting_unit_textview_text">Unit</string>
    <string name="app_unit_metric_textview_text">Metric Units</string>
    <string name="app_unit_imperial_textview_text">Imperial Units</string>
    <string name="app_base_paste_button_text">Paste</string>
    <string name="app_devicemoreinfo_unregisted_textview_text">Unregistered</string>
    <string name="app_devicemoreinfo_registed_textview_text">Registered</string>
    <string name="app_networkconfigwifiinput_notice_textview_text">Please select a 2.4GHz Wi-Fi network</string>
    <string name="app_notiotbindresult_title_textview_text">Add Device</string>
    <string name="app_notiotbindresult_sucess_textview_text">Device added successfully</string>
    <string name="app_networkconfignotiot_sncode_isnull_textview_text">Please enter a SN code</string>
    <string name="app_notiotbindresult_fail_textview_text">Failed to add device. Please check the following issues and try again</string>
    <string name="app_notiotbindresult_failnotice_textview_text">Scan the QR code on the device, or type in the SN code manually. Make sure you enter the correct sn code</string>
    <string name="app_networkconfigwificonnect_connectsuccess_textview_text">Device connected successfully</string>
    <string name="app_deviceboard_registinfo_button_text">Registration Info</string>
    <string name="app_notiotbindresult_register_button_text">Register Now</string>
    <string name="app_notiotbindresult_done_button_text">Done</string>
    <string name="app_notiotbindresult_retry_button_text">Retry</string>
    <string name="app_base_nodata_textview_text">No Content</string>
    <string name="app_base_nonetwork_textview_text">Network connection exception, please refresh and try again</string>
    <string name="app_messagedetail_error_textview_text">Error code:</string>
    <string name="app_deviceregist_lost_receipt_textview_text">You will need to provide a valid receipt to enjoy the warranty extension from 3 to 5 years.</string>
    <string name="app_feedback_historyunread_textview_text">New</string>
    <string name="app_devicerename_rule_textview_text">Please enter 1-20 characters</string>
    <string name="app_no_iot_panel_rename_success">Success</string>
    <string name="app_no_iot_panel_rename_fail">Failed</string>
    <string name="app_networkconfigwificonnect_stepsynchronizing_textview_text">Syncing Wi-Fi information with device</string>
    <string name="app_networkconfigwificonnect_stepconnecting_textview_text">Connecting device to network$$Note:connection could take up to 2 minutes depending on your network environment</string>
    <string name="app_networkconfigwificonnect_stepbinding_textview_text">Device binding with phone</string>
    <!--  新增-->
    <string name="app_networkconfigwificonnect_stepsynchronized_textview_text">Wi-Fi information successfully synced</string>
    <!--  新增-->
    <string name="app_networkconfigwificonnect_stepconnected_textview_text">Device connected to network</string>
    <!--  新增-->
    <string name="app_networkconfigwificonnect_stepbinded_textview_text">Device bound to phone</string>
    <!--  新增-->
    <string name="app_oobe_sign_up_password_char_and_spaces">At least 8 characters, cannot contain space</string>
    <!--  新增-->
    <string name="app_oobe_sign_up_password_confirm_numbers_and_letters">Must contain 1 number, 1 letter &#038; 1 special character</string>
    <!--修改-->
    <string name="app_regist_password_inputview_placeholder">Password(required)</string>
    <!--修改-->
    <string name="app_regist_passwordconfirm_inputview_placeholder">Confirm password(required)</string>
    <!--  新增-->
    <string name="app_regist_password_error_worning_header">Password cannot contain: </string>
    <!--  遗漏-->
    <string name="app_deviceregistsuccess_success_textview_text">Registration was successful!</string>
    <!--修改-->
    <string name="app_logoff_content_textview_text">Are you sure to cancel account?</string>
    <!--  新增-->
    <string name="app_userDetail_accountcancel_textview_text">Account Cancellation</string>
    <!--  新增-->
    <string name="app_usercenter_profile_textview_text">Profile</string>
    <!--  新增-->
    <string name="app_usercenter_usernamedefault_textview_text">Welcome!</string>
    <!--  新增-->
    <string name="app_product_only_available_ego_fleet_app">This product is only available on the EGO Fleet App</string>
    <!--修改-->
    <string name="app_userDetail_email_textview_text">Email Address</string>
    <!--修改-->
    <string name="app_setting_logout_textview_text">Log Out</string>
    <!--修改-->
    <string name="app_forgetpassword_description_textview_text">Please ensure that your email address is confirmed.</string>
    <!--修改-->
    <string name="app_OTAguide_offline_textview_text">Device is offline now</string>


    <!--  新增 72002 配件-->

    <string name="app_devicemanagepartdetail_confirm_button_text">OK</string>
    <string name="app_devicemanagepartdetail_dialog_textview_text">Do you confirm this accessory has been replaced?</string>
    <string name="app_devicemanagepartdetail_nextmaintenance_textview_text">Next maintenance in</string>
    <string name="app_devicemanagepartdetail_maintenancetip_textview_text">Please perform maintenance on the accessories based on actual working conditions</string>
    <string name="app_devicemanagepartdetail_reset_button_text">Reset</string>
    <string name="app_devicemanagepartdetail_detail_textview_text">Details</string>
    <string name="app_deviceregist_devicemodelno_textview_text">Model Number</string>
    <string name="app_devicemanageaccessorie_maintenance_textview_text">Need  maintenance immediately !</string>
    <string name="app_devicemanageaccessorie_maintenancein_textview_text">Maintenance in</string>
    <string name="app_devicemanageaccessorie_workinghours_textview_text">working hour(s)</string>
    <string name="app_devicemanageaccessorie_days_textview_text">day(s)</string>
    <!--  新增 新增 72002 快捷按钮-->
    <string name="app_devicelist_turnonheat_button_text">Turning on handle heating...</string>
    <string name="app_devicelist_turnoffheat_button_text">Turning off handle heating...</string>

    <!--  新增-->
    <string name="app_accountverification_title_textview_text">Account verification</string>
    <string name="app_signup_passwordcheckemail_textview_text">Cannot contain your email</string>

    <!--  1月15日 新增词条-->
    <string name="app_moresolutions_title_textview_text">"More solutions"</string>
    <string name="app_moresolutions_subtitle_textview_text">"If the device is still offline, please try the following solutions"</string>
    <string name="app_moresolutions_solution1_textview_text">"1.Please check if the router is powered on and connected to the network, or restart the router"</string>
    <string name="app_moresolutions_solution2_textview_text">"2. Re-pair with the device"</string>
    <string name="app_moresolutions_moresolutions_textview_text">"If the issue is still nor resolved, please refer to "</string>
    <string name="app_usermanual_title_textview_text">"User Manual"</string>
    <string name="app_moresolutions_moresolutionsor_textview_text">" or "</string>
    <string name="app_base_ok_button_text">"OK"</string>
    <string name="app_base_notopenbluetoothalert_textview_text">"Please turn on your cell phone's Bluetooth"</string>
    <string name="app_networkconfigwifiinput_title_textview_text">Input Network</string>
    <string name="app_networkconfigwificonnect_title_textview_text">Connect to Network</string>
    <string name="app_messagesetting_devicesubtitle_textview_text">Receive device message</string>
    <string name="app_messagesetting_title_textview_text">Message Settings</string>
    <string name="app_systemauthorization_title_textview_text">System Permission Management</string>
    <string name="app_devicelist_night_textview_text">Good night</string>
    <string name="app_devicelist_evening_textview_text">Good evening</string>
    <string name="app_devicelist_afternoon_textview_text">Good afternoon</string>
    <string name="app_devicelist_morning_textview_text">Good morning</string>
    <string name="app_deviceregist_surveycontactoption_phone_textview_text">Phone number*</string>
    <string name="app_networkconfigwificonnect_notregistdevice_button_text">Done</string>
    <string name="app_messagelist_systemmessage_textview_text">System Notifications</string>
    <string name="app_devicemoreinfo_deletedevice_textview_text">Delete Device</string>
    <string name="app_forgetpassword_title_textview_text">Forget password</string>
    <string name="app_forgetpasswordsuccess_textview_text">Forget password</string>
    <string name="app_forgetpassword_textview_text">Forget password</string>
    <string name="app_forgetpasswordverifycode_sendagain_button_text">Send again</string>
    <string name="app_messagealert_tosee_button_text">View</string>
    <string name="app_setting_aboutus_textview_text">About EGO</string>
    <string name="app_forgetpasswordreset_passwordnotsame_textview_text">Two inputs are inconsistent</string>
    <string name="app_login_login_button_text">Log In</string>
    <string name="app_fittinglist_dataempty_textview_text">No recommendation</string>
    <string name="app_deviceregist_surveypurchaseroption_province_textview_text">State/Province*</string>
    <string name="app_deviceregist_surveypurchaseroption_address_textview_text">Address</string>
    <string name="app_setting_message_textview_text">Message Settings</string>
    <string name="app_deviceboard_fitting_button_text">Accessories</string>
    <string name="app_modifyavator_save_button_text">Save</string>
    <string name="app_devicesearch_closetodevice_textview_text">Place the phone as close to the target device as possible</string>
    <string name="app_tabbar_user_textview_text">My Account</string>
    <string name="app_forgetpasswordsuccess_title_textview_text">Forgot password</string>
    <string name="app_base_servererror_textview_text">Server error</string>
    <string name="app_devicelist_deletedevicealerttitle_textview_text">Are you sure you want to delete this device?</string>
    <string name="app_setting_systempermissionimanagement_textview_text">System Permissions Management</string>
    <string name="app_devicecode_modeno_textview_text">Model Number:</string>
    <string name="app_tabbar_home_textview_text">Home</string>
    <string name="app_deviceproductsearch_history_inputview_placeholder">Enter product name</string>
    <string name="app_base_refreshheaderloading_textview_text">LOADING...</string>
    <string name="app_devicemoreinfo_deletedevicetitle_textview_text">Are you sure you want to delete this device?</string>
    <string name="app_messagesetting_system_textview_text">System Notifications</string>
    <string name="app_login_password_inputview_placeholder">Password</string>
    <string name="app_deviceregist_surveypurchaseroption_country_textview_text">Country*</string>
    <string name="app_deviceregist_another_inputview_placeholder">Enter another address</string>
    <string name="app_deviceregist_surveycontactoption_email_textview_text">Email</string>
    <string name="app_deviceregist_phone_textview_text">Phone number*</string>
    <string name="app_checkpassword_done_button_text">Change Password</string>
    <string name="app_deviceregist_surveypurchaseroption_lastname_textview_text">Last name*</string>
    <string name="app_about_title_textview_text">About EGO</string>
    <string name="app_messagesetting_device_textview_text">Device Messages</string>
    <string name="app_devicesearch_notfounddescription_textview_text">No equipment found. Please scan the QR code or find the product card to add</string>
    <string name="app_deviceregist_surveypurchaseroption_postcode_textview_text">Zip code/Post code*</string>
    <string name="app_modifyavator_gosetting_textview_text">Go to Settings</string>
    <string name="app_deviceregist_surveypurchaseroption_firstname_textview_text">First name*</string>
    <string name="app_networkconfig_bluetooth_permission_tip">Please open Bluetooth permission</string>
    <string name="app_deviceqrcodescan_flashlight_button_text">Flashlight</string>
    <string name="app_modifyavator_title_textview_text">Edit Profile</string>
    <string name="app_privacycollction_account_textview_text">Account information</string>
    <string name="app_messagesetting_devicemessagesreceive_textview_text">Receive device message</string>
    <string name="app_privacyagreementrevoke_confirmtdetail_textview_text">Confirm cancelation of authorization. After  authorization is revoked, all devices will be unpaired from your current login account and the data previously stored on the server will be cleared. This data cannot be recovered after revocation. To reuse the device, you will need to pair the device again and re-authorize it.</string>
    <string name="app_devicemoreinfo_deletedevicemessage_textview_text">Your device settings will be deleted if this device is removed.</string>
    <string name="app_deivcelist_resetpassword_textview_text">Your device settings will be deleted if this device is removed. The device\'s password will be reset.</string>
    <string name="app_forgetpasswordverifycode_havesendfirst_textview_text">We just emailed you a six-digit code</string>
    <string name="app_registverifycode_havesendsecond_textview_text">. Enter the code below to confirm your email address.</string>
    <string name="app_privacyagreementrevoke_noticedetail_textview_text">After you revoke your authorization, we will not be able to continue providing you with the services
	corresponding to the revocation of authorization, nor will we process your corresponding personal information.
	However, your decision to revoke your authorization will not affect any previous processing of
	personal data based on your consent or authorization. After you revoke your authorization, you need to re-enter your account
	and password before entering the function page again, and click “agree” to our Privacy Policy and User Agreement again.
	Your consent indicates that you voluntarily agree to our use and processing of your personal information.</string>
    <string name="app_privacyagreementrevoke_confirmtitle_textview_text">Confirm cancellation of authorization</string>
    <string name="app_productdealers_visitwebsite_button_text">Visit website</string>
    <string name="app_productdealers_directions_button_text">Direction</string>
    <string name="app_deviceregist_surveynavtitle_textview_text">Extend Warranty</string>
    <string name="app_devicecode_deviceid_textview_text">Device ID</string>
    <string name="app_networkconfigwificonnect_registdevice_button_text">Register Now</string>
    <string name="app_base_done_button_text">Done</string>
    <string name="app_devicecode_firmwareversion_textview_text">Firmware Version</string>
    <string name="app_modifypassword_passwordsame_textview_text">The new password must be different from the old one.</string>
    <string name="app_devicecode_serialnumber_textview_text">Serial Number</string>
    <!--  修改-->
    <string name="app_scan_failed_tryagain_text">Scan failed, the device was not found Please troubleshoot the following issues and try again</string>
    <string name="app_connect_fail_tips_text">2. Please check whether the device is powered on</string>
    <string name="app_checkverifycode_is_invalid_text">The code is invalid. Please try again</string>
    <string name="app_password_comfirm_mismatch_text">Password mismatch</string>
    <string name="app_base_email_format_text">Confirm email format</string>
    <string name="app_base_pictureselect_other_text">Other</string>
    <string name="app_verifycode_sendtoomuch_text">Sending mail frequently, please try again later</string>
    <string name="app_forget_confirm_email_text">Mailbox verification</string>
    <string name="app_select_wifistatus_connected_text">connected</string>
    <string name="app_messagecenter_detail_processingsuggestions_text">Processing suggestions</string>
    <string name="app_privacyagreementrevoke_noticedetailtitle_textview">The revocation of authorization will have the following effects on you:</string>
    <string name="app_messagecenterbanner_now_button_text">Now</string>
    <!--  2月需求-->
    <string name="app_base_inputrequirederror_textview_text">This field is required</string>
    <string name="app_base_nameinputerror_textview_text">Numbers and special characters are not allowed in name fields</string>

    <string name="app_deviceboard_moretocome_button_text">More to come...</string>
    <string name="app_device_regist_invalidserialnumber_text">Invalid Serial Number</string>
    <string name="app_deviceregistsuccess_ifkit_textview_text">Was this tool part of a kit?$$To register your kit more quickly, after finishing this tool registration, we recommend to tap “register more” and rescanning a new tool QR code.</string>

    <!--  4月需求-->
    <string name="app_base_locationpermissionalert_textview_text">"Please grant your phone's location permission to the App for Bluetooth scan"</string>
    <string name="app_base_gpsswitchalert_textview_text">"Please turn on your cell phone’s Location"</string>
    <!--  ios未使用-->
    <string name="app_selectwifi_mynetworks_textview_text">"My Networks"</string>
    <string name="app_selectwifi_othernetworks_textview_text">"Other Networks"</string>

    <string name="app_messagecenter_systemmessage_textview_text">System Notifications</string>
    <string name="app_messagecenter_marketingmessage_textview_text">Offers</string>
    <string name="app_messagesetting_marketing_textview_text">Offers</string>
    <string name="app_messagesetting_marketingsubtitle_textview_text">Receive offers from EGO</string>

    <string name="app_container_rnbundlenameerror_textview_text">Please update your EGO Connect to latest version</string>


    <!--  5月需求-->
    <string name="app_deviceregist_giftwithresidential_textview_text">Receipt is required for full warranty coverage, if no receipt is available warranty coverage is limited.</string>


    <string name="app_evaluatedialog_no_textview_text">No</string>
    <string name="app_evaluatedialog_yes_textview_text">Yes</string>
    <string name="app_evaluatedialog_content_textview_text">Do you like EGO Connect so far?</string>

    <!--  6月需求-->
    <string name="app_regist_emailisexists_textview_text">Mailbox already exists. Please log in.</string>
    <string name="app_upgradedialog_content_textview_text">Please update the app to the latest version to use this device.</string>
    <string name="app_upgradedialog_later_textview_text">Later</string>
    <string name="app_upgradedialog_upgrade_textview_text">Update</string>
    <string name="app_OTA_failedinfo_textview_text">Sorry, update has failed. please make sure that you have an active connection to your device and try again.</string>
    <!-- EU_CRM -->
    <string name="app_registaccountype_domestictitle_textview_text">Domestic Registration</string>
    <string name="app_registaccountype_domesticdescription_textview_text">If you are a domestic or recreational user, please start here.</string>
    <string name="app_registaccountype_commercialtitle_textview_text">Commercial Registration</string>
    <string name="app_registaccountype_commercialdescription_textview_text">If you are a professional user or retailer, please start here.</string>
    <string name="app_registcommercialtype_done_button_text">Go to Website</string>
    <string name="app_registcommercialtype_description_textview_text">For commercial users, please register and login your account on the official website:</string>
    <string name="app_registselectcountry_title_textview_text">Pick a country from the options below</string>
    <string name="app_registselectcountry_selectcountry_textview_text">Select your country of residence</string>
    <string name="app_registaddressinfo_street_inputview_placeholder">Street/No (required)</string>
    <string name="app_registaddressinfo_city_inputview_placeholder">City (required)</string>
    <string name="app_registaddressinfo_county_inputview_placeholder">County (required)</string>
    <string name="app_registaddressinfo_postcode_inputview_placeholder">Postcode (required)</string>
    <string name="app_registeu_firstname_inputview_placeholder">First name (required)</string>
    <string name="app_registeu_lastname_inputview_placeholder">Last name (required)</string>
    <string name="app_registeu_email_inputview_placeholder">Email address (required)</string>
    <string name="app_registeu_password_inputview_placeholder">Password(required)</string>
    <string name="app_registeu_passwordconfirm_inputview_placeholder">Confirm password(required)</string>
    <string name="app_regist_phone_inputview_placeholder">Phone (optional)</string>
    <string name="app_registeu_email_textview_text">Please enter the correct mailbox</string>
    <string name="app_registeu_login_button_text">I already have an account to log in</string>
    <string name="app_logineu_description_textview_text">For commercial users, please login to your account on the EGO official website.</string>
    <string name="app_logineu_link_textview_text">EGO official website</string>
    <string name="app_deviceregisteu_receipt_input_placeholder">Receipt</string>
    <string name="app_deviceregisteu_purchasedate_input_placeholder">Purchase date</string>
    <string name="app_deviceregisteu_kit_textview_text">Battery &amp; Charger Included?</string>
    <string name="app_deviceregisteu_yes_textview_text">Yes</string>
    <string name="app_deviceregisteu_no_textview_text">No</string>
    <string name="app_deviceregisteu_modelnumber_textview_text">Model Number: </string>
    <string name="app_deviceregisteu_devicesn_textview_text">Serial Number: </string>
    <string name="app_deviceregisteu_sn_input_placeholder">Enter Serial Number</string>
    <string name="app_deviceregisteu_sninputerror_textview_text">Please enter at least one product Serial Number within the kit</string>
    <string name="app_deviceregisteu_regist_button_text">Register</string>
    <string name="app_devicelist_alertmessage_textview_text">This product has been registered via kit registration or website. Please click the card and follow the instructions to pair the device for connection.</string>
    <string name="app_devicelist_alerttip_textview_text">Don’t show this again</string>
    <string name="app_devicelist_alerttip_button_text">OK</string>
    <string name="app_devicelist_toconnect_textview_text">To be connected</string>
    <!--  8月需求-->
    <string name="app_deviceinfo_copytoast_textview_text">Copied</string>
    <string name="app_dealer_findstoresnear_textview_text">Find stores near this location</string>
    <string name="app_dealer_findstoresnearzipcode_textview_text">Zip code</string>
    <string name="app_dealer_distance_textview_text">Distance：</string>
    <string name="app_dealer_zipcodenotfound_textview_text">This Zip code was not found.</string>

    <!--9月需求-->
    <string name="app_devicelist_mytool_textview_text">My Device</string>
    <string name="app_devicesearch_add_textview_text">Add a device</string>
    <string name="app_devicelist_emptyadd_button_text">ADD A Device</string>
    <string name="app_noiotpanelch7000orpowerstationtips_textview_text">EGO Connect app only supports the non-connectivity functions of this product, if you want to connect this device via Bluetooth or Wi-Fi to your phone, please use EGO Power+ app.</string>
    <string name="app_feedbackdetails_submittedpatient_textview_text">Feedback has been submitted$$ please be patient...</string>
    <string name="app_networkconfigwifiinput_ssidoverflow_textview_text">The name of the selected Wi-Fi exceeds 31 characters. Please select another Wi-Fi network.</string>
    <string name="app_networkconfigwifiinput_wifipasswordoverflow_textview_text">Password length exceeds 31 characters. Please change the password or choose another Wi-Fi network.</string>
    <!-- 9月5号词条变更与iOS统一-->
    <string name="app_registverifycode_havesendfirst_textview_text">We just emailed you a six-digit code to your mailbox</string>

    <!--12月需求-->
    <string name="app_deviceregist_choosefile_button_text">Choose from File</string>
    <string name="app_deviceregist_illegalfile_textview_text">Only supports uploading PDF, Word and Pages files</string>
    <string name="app_deviceadd_inputserialnumber_textview_text">Enter Serial Number Manually</string>
    <string name="app_deviceregist_snregistered_textview_text">This serial number has been registered.</string>
    <string name="app_base_cameraAuthorizedAlertTitle_textview_text">Unauthorized access to camera</string>
    <string name="app_base_cameraAuthorizedAlertContent_textview_text">EGO Connect requires your permission to access the camera to take pictures</string>
    <string name="app_base_albumAuthorizedAlertTitle_textview_text">Unauthorized access to album</string>
    <string name="app_base_albumAuthorizedAlertContent_textview_text">EGO Connect requires your permission to access the album to upload pictures</string>
    <string name="app_deviceqrcodescan_cameraAuthorizedalertmessage_textview_text">EGO Connect requires your permission to access the camera to scan the code</string>

    <!--2025-2需求-->
    <string name="app_deviceadd_productscompatibledescribe_textview_text">Only Bluetooth-enabled EGO products appear below. Other EGO products can be added in “Use another method”.</string>
    <string name="app_intentFeedbackAlertContent_textview_text">You have a feedback dialog thread that is still active. Please close that dialog thread if that issue is resolved before starting a new one, or add to it if related to the same topic.</string>
    <string name="app_intentFeedbackAlertContinue_textview_text">Continue</string>
    <string name="app_intentFeedbackAlertCancel_textview_text">Cancel</string>
    <string name="app_feedbackDetail_edit_textview_text">Edit</string>
    <string name="app_feedbackDetail_closethisthread_textview_text">Close This Thread</string>


    <!-- Hd新增功能设备分享 -->
    <string name="app_usercenter_sharedevice_textview_text">Share Device</string>
    <string name="app_sharedevice_message_textview_text">Bluetooth device can connect to EGO Connect directly and do not need to be shared to use</string>
    <string name="app_sharedevice_share_textview_text">Share</string>
    <string name="app_sharedevice_accept_textview_text">Accept</string>
    <string name="app_sharedevice_sharewith_textview_text">Shared with</string>
    <string name="app_sharedevice_users_textview_text">user(s)</string>
    <string name="app_sharedevice_notshared_textview_text">Not shared</string>
    <string name="app_sharedevice_sharedby_textview_text">Shared by</string>
    <string name="app_sharedevice_accepted_textview_text">Accepted</string>
    <string name="app_sharedevice_pending_textview_text">Pending</string>
    <string name="app_sharedevice_expired_textview_text">Expired</string>
    <string name="app_sharedevice_expirein_textview_text">Expire in</string>
    <string name="app_sharedevice_days_textview_text">days</string>
    <string name="app_sharedevice_deviceempty_textview_text">There is no device</string>
    <string name="app_sharedevice_shareempty_textview_text">No invitation received</string>
    <string name="app_sharedevice_shared_textview_text">This device has been shared with:</string>
    <string name="app_sharedevice_noshared_textview_text">Your device has not been shared yet.</string>
    <string name="app_sharedevice_enteremail_textview_text">Please enter the EGO Connect email account of the person being shared</string>
    <string name="app_sharedevice_emailhint_textview_text">EGO Connect email account</string>
    <string name="app_sharedevice_emailerror1_textview_text">Please enter the correct email address</string>
    <string name="app_sharedevice_emailerror2_textview_text">This account has already been shared</string>
    <string name="app_sharedevice_emailerror3_textview_text">Please do not enter your own account email address</string>
    <string name="app_sharedevice_invitemessage_textview_text">hasn\'t signed up for EGO CONNECT yet,invite your friends to sign up now!</string>
    <string name="app_sharedevice_invite_textview_text">Invite</string>
    <string name="app_sharedevice_dialog1_textview_text">Do you want to stop sharing this device?</string>
    <string name="app_sharedevice_dialog2_textview_text">Your device settings will be deleted if this device is removed</string>
    <string name="app_sharedevice_dialog3_textview_text">Your device settings will be deleted and all shared accounts will be removed</string>
    <string name="app_devicelist_shared_textview_text">Shared</string>
    <string name="app_sharedevice_accept_button_text">Accept</string>
    <string name="app_sharedevice_empty_button_text">Share device</string>
    <string name="app_sharedevice_sharesuccess_textview_text">Success</string>
    <string name="app_sharedevice_invitesuccess_textview_text">Invitation successful</string>

    <!--2025-3需求-->
    <string name="app_tabbar_explore_textview_text">Explore</string>
    <string name="app_deviceregistpromote_title_textview_text">Recommend Parts</string>
    <string name="app_deviceregistpromote_shopnow_textview_text">Shop Now</string>



    <!-- 时间选择弹窗月份简写词条 -->
    <string name="app_datepicker_month1_textview_text">Jan</string>
    <string name="app_datepicker_month2_textview_text">Feb</string>
    <string name="app_datepicker_month3_textview_text">Mar</string>
    <string name="app_datepicker_month4_textview_text">Apr</string>
    <string name="app_datepicker_month5_textview_text">May</string>
    <string name="app_datepicker_month6_textview_text">Jun</string>
    <string name="app_datepicker_month7_textview_text">Jul</string>
    <string name="app_datepicker_month8_textview_text">Aug</string>
    <string name="app_datepicker_month9_textview_text">Sep</string>
    <string name="app_datepicker_month10_textview_text">Oct</string>
    <string name="app_datepicker_month11_textview_text">Nov</string>
    <string name="app_datepicker_month12_textview_text">Dec</string>

    <!-- 2025-5 -->
    <string name="app_appversiondialog_content_textview_text">A new version is available — update now to enjoy the latest features!</string>
    <string name="app_appversiondialog_later_textview_text">Later</string>
    <string name="app_appversiondialog_upgrade_textview_text">Update</string>

    <!-- 2025-6 -->
    <string name="app_closefeedbackdialog_title_textview_text">Close This Thread?</string>
    <string name="app_closefeedbackdialog_content_textview_text">You won’t be able to reply after closing. Are you sure you want to close this feedback thread?</string>
    <string name="app_closefeedbackdialog_cancel_textview_text">Cancel</string>
    <string name="app_closefeedbackdialog_close_textview_text">Close</string>
</resources>

