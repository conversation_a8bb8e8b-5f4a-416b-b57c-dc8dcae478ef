apply from: "../common_lib_build.gradle"

android {
    signingConfigs {
//    debug {
//      storeFile file('/home/<USER>/iotkey.jks')
//      storePassword '2022iot'
//      keyAlias 'iot2022'
//      keyPassword '2022iot'
//    }
    }
    defaultConfig {
        targetSdkVersion 33
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }


        if (isDebug.toBoolean()) {
            buildConfigField "boolean", "IS_DEBUG", "true"
        } else {
            buildConfigField("boolean", "IS_DEBUG", "false")
        }
        // buildConfigField ("String","VERSION_NAME", "\""+rootProject.ext.android.versionName+"\"" )//自定义参数


    }
    buildFeatures {
        dataBinding = true
    }

}

dependencies {
    implementation 'com.alibaba:arouter-api:1.5.2'
    implementation project(path: ':lib_router')
    annotationProcessor 'com.alibaba:arouter-compiler:1.5.2'

    //添加material-dialogs依赖
    //核心模块的依赖
    implementation 'com.afollestad.material-dialogs:core:0.9.1.0'
    //公共模块的依赖
    //公共模块包含不是每个人都需要的扩展库。 这包括 ColorChooserDialog、FolderChooserDialog、Material Preference 类和 MaterialSimpleListAdapter / MaterialSimpleListItem
    implementation 'com.afollestad.material-dialogs:commons:0.9.1.0'
    api "com.squareup.retrofit2:converter-jackson:2.3.0"

    // debugApi 'com.squareup.leakcanary:leakcanary-android:2.7'

    api libs.rxandroid
    api libs.rxjava
    api libs.rxkotlin
    api libs.autodispose
    api libs.autodispose_android
    api libs.gson
    api libs.autosize
    api libs.utilcode
    api libs.glide
    api libs.eventbus
    api libs.jsoup
    api 'com.github.zcweng:switch-button:0.0.3@aar'
    api project(path: ':lib_db')
    implementation 'org.jetbrains:annotations:15.0'
    implementation 'androidx.navigation:navigation-runtime:2.3.5'
    implementation 'androidx.navigation:navigation-ui:2.3.5'
    implementation 'androidx.navigation:navigation-fragment:2.3.5'
    api 'androidx.lifecycle:lifecycle-service:2.3.1'

    implementation this.rootProject.ext.dependencies.roomruntime
    annotationProcessor this.rootProject.ext.dependencies.roomcompiler


    api 'com.luck.picture.lib:selector_cvn:1.0.1@aar'
    api 'top.zibin.luban:compress:1.0.0@aar'
    api 'com.yalantis.ucrop:ucrop:1.0.0@aar'
    api project(path: ':FastBleLib')
    //  api 'com.clj.fastble:fastble_cvn:1.0.2@aar'


    api 'com.timecat.component:MLang:2.0.2'
    api 'com.github.mcxtzhang:SwipeDelMenuLayout:V1.3.0'
    api 'org.bouncycastle:bcprov-jdk15on:1.55'

    implementation 'com.google.android.exoplayer:exoplayer:2.17.1'

//  debugApi 'io.github.didi.dokit:dokitx:*******'
//  releaseApi 'io.github.didi.dokit:dokitx-no-op:*******'

    def aws_version = "2.12.7"
    implementation "com.amazonaws:aws-android-sdk-iot:$aws_version"
    implementation "com.amazonaws:aws-android-sdk-mobile-client:$aws_version"
    api "com.amazonaws:aws-android-sdk-s3:$aws_version"


    //FCM
    api 'com.google.firebase:firebase-core:17.0.0'
    api 'com.google.firebase:firebase-messaging:20.0.0'

     //google
    api 'com.google.android.gms:play-services-auth:15.0.1'
    //Google Play服务依赖项
    api 'com.google.android.gms:play-services-maps:18.2.0'
    api 'com.google.android.gms:play-services-location:21.0.1'
    //地图操作工具类，添加标记等
    api 'com.google.maps.android:android-maps-utils:3.8.2'

    api 'com.google.android.libraries.places:places:2.4.0'



    // 添加依赖
    debugImplementation 'com.github.coder-pig:CpNetworkCapture:0.0.11'

    api 'com.chervon.trace:trace_data:1.2.0@aar'

//    api 'com.github.Jasonchenlijian:FastBle:2.4.0'


    api 'com.github.NightWhistler:HtmlSpanner:0.4'

    implementation "androidx.window:window-java:1.0.0"
    //自定义LiveDataBus 防止数据倒灌
    api 'com.kunminx.arch:unpeek-livedata:7.8.0'


    //评价系统
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'com.google.android.play:review-ktx:2.0.1'



}
